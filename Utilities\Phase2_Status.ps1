# Phase 2 Simple Test - Check what we have so far

Write-Host "=== Phase 2 Debug System - Status Check ===" -ForegroundColor Green

# Test 1: Check if LoggingService has DebugMode property
Write-Host "Test 1: Check LoggingService enhancements" -ForegroundColor Cyan
$loggingServiceLines = Select-String -Path "/mnt/c/Users/<USER>/OneDrive - JERA Global Markets/Azure Devops/it-endusersupport-automation/scripts/Utilities/OnboardingFromJiraGUI_v2.ps1" -Pattern "DebugMode.*=.*true"
if ($loggingServiceLines) {
    Write-Host "✅ LoggingService has DebugMode property" -ForegroundColor Green
    Write-Host "Found: $($loggingServiceLines.Line)" -ForegroundColor Yellow
} else {
    Write-Host "❌ LoggingService DebugMode property not found" -ForegroundColor Red
}

# Test 2: Check if SetDebugMode method exists
Write-Host "`nTest 2: Check SetDebugMode method" -ForegroundColor Cyan
$setDebugLines = Select-String -Path "/mnt/c/Users/<USER>/OneDrive - JERA Global Markets/Azure Devops/it-endusersupport-automation/scripts/Utilities/OnboardingFromJiraGUI_v2.ps1" -Pattern "SetDebugMode"
if ($setDebugLines) {
    Write-Host "✅ SetDebugMode method exists" -ForegroundColor Green
    Write-Host "Found $($setDebugLines.Count) references" -ForegroundColor Yellow
} else {
    Write-Host "❌ SetDebugMode method not found" -ForegroundColor Red
}

# Test 3: Check if Debug ComboBox was added to status bar
Write-Host "`nTest 3: Check Debug ComboBox in status bar" -ForegroundColor Cyan
$debugComboLines = Select-String -Path "/mnt/c/Users/<USER>/OneDrive - JERA Global Markets/Azure Devops/it-endusersupport-automation/scripts/Utilities/OnboardingFromJiraGUI_v2.ps1" -Pattern "debugCombo"
if ($debugComboLines) {
    Write-Host "✅ Debug ComboBox found in UI" -ForegroundColor Green
    Write-Host "Found $($debugComboLines.Count) references" -ForegroundColor Yellow
} else {
    Write-Host "❌ Debug ComboBox not found" -ForegroundColor Red
}

# Test 4: Check if some debug statements were replaced
Write-Host "`nTest 4: Check debug statement replacements" -ForegroundColor Cyan
$newDebugLines = Select-String -Path "/mnt/c/Users/<USER>/OneDrive - JERA Global Markets/Azure Devops/it-endusersupport-automation/scripts/Utilities/OnboardingFromJiraGUI_v2.ps1" -Pattern "log\.Debug\("
if ($newDebugLines) {
    Write-Host "✅ New debug statements found" -ForegroundColor Green
    Write-Host "Found $($newDebugLines.Count) new debug statements" -ForegroundColor Yellow
} else {
    Write-Host "❌ No new debug statements found" -ForegroundColor Red
}

# Test 5: Count remaining old debug statements
Write-Host "`nTest 5: Count remaining old debug statements" -ForegroundColor Cyan
$oldDebugLines = Select-String -Path "/mnt/c/Users/<USER>/OneDrive - JERA Global Markets/Azure Devops/it-endusersupport-automation/scripts/Utilities/OnboardingFromJiraGUI_v2.ps1" -Pattern "Write-Host.*DEBUG:"
Write-Host "Remaining old debug statements: $($oldDebugLines.Count)" -ForegroundColor Yellow
if ($oldDebugLines.Count -lt 158) {
    $reduced = 158 - $oldDebugLines.Count
    Write-Host "✅ Reduced by $reduced debug statements" -ForegroundColor Green
} else {
    Write-Host "❌ No reduction in debug statements" -ForegroundColor Red
}

Write-Host "`n=== Phase 2 Status Summary ===" -ForegroundColor Green
Write-Host "🔧 LoggingService enhanced with debug mode control" -ForegroundColor Cyan
Write-Host "🎛️ Debug ComboBox added to status bar" -ForegroundColor Cyan
Write-Host "🔄 Some debug statements converted to centralized logging" -ForegroundColor Cyan
Write-Host "📊 Debug system infrastructure is in place!" -ForegroundColor Yellow