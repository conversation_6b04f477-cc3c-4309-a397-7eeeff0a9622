﻿{
    "Duration":  3.210477,
    "StartTime":  {
                      "value":  "\/Date(1751501586526)\/",
                      "DisplayHint":  2,
                      "DateTime":  "Thursday, July 3, 2025 1:13:06 AM"
                  },
    "SkippedTests":  0,
    "FailedTests":  3,
    "Errors":  [
                   "Script Loading: Script loading failed: FAILED: A parameter cannot be found that matches parameter name \u0027WhatIf\u0027.",
                   "Region Structure: Unbalanced regions: 31 regions, 30 endregions",
                   "Documentation Coverage: Low documentation: 7.05% comment lines"
               ],
    "PassedTests":  24,
    "TestDetails":  [
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751501586963)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Thursday, July 3, 2025 1:13:06 AM"
                                          },
                            "Name":  "File Existence",
                            "Message":  "Script file found",
                            "Details":  null
                        },
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751501587010)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Thursday, July 3, 2025 1:13:07 AM"
                                          },
                            "Name":  "File Size",
                            "Message":  "Script size: 0.35 MB (368360 bytes)",
                            "Details":  null
                        },
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751501587117)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Thursday, July 3, 2025 1:13:07 AM"
                                          },
                            "Name":  "Line Count",
                            "Message":  "Total lines: 9872",
                            "Details":  null
                        },
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751501587323)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Thursday, July 3, 2025 1:13:07 AM"
                                          },
                            "Name":  "PowerShell Parser",
                            "Message":  "No syntax errors detected",
                            "Details":  null
                        },
                        {
                            "Status":  "FAILED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751501588950)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Thursday, July 3, 2025 1:13:08 AM"
                                          },
                            "Name":  "Script Loading",
                            "Message":  "Script loading failed: FAILED: A parameter cannot be found that matches parameter name \u0027WhatIf\u0027.",
                            "Details":  null
                        },
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751501588973)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Thursday, July 3, 2025 1:13:08 AM"
                                          },
                            "Name":  "Class Definition: AdvancedLoggingManager",
                            "Message":  "Class definition found",
                            "Details":  null
                        },
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751501588983)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Thursday, July 3, 2025 1:13:08 AM"
                                          },
                            "Name":  "Class Definition: VersionManager",
                            "Message":  "Class definition found",
                            "Details":  null
                        },
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751501588994)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Thursday, July 3, 2025 1:13:08 AM"
                                          },
                            "Name":  "Class Definition: TestingFramework",
                            "Message":  "Class definition found",
                            "Details":  null
                        },
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751501589012)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Thursday, July 3, 2025 1:13:09 AM"
                                          },
                            "Name":  "Class Definition: DocumentationGenerator",
                            "Message":  "Class definition found",
                            "Details":  null
                        },
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751501589027)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Thursday, July 3, 2025 1:13:09 AM"
                                          },
                            "Name":  "Function Definition: Write-StructuredLog",
                            "Message":  "Function definition found",
                            "Details":  null
                        },
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751501589047)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Thursday, July 3, 2025 1:13:09 AM"
                                          },
                            "Name":  "Function Definition: Get-ScriptVersion",
                            "Message":  "Function definition found",
                            "Details":  null
                        },
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751501589060)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Thursday, July 3, 2025 1:13:09 AM"
                                          },
                            "Name":  "Function Definition: Register-Test",
                            "Message":  "Function definition found",
                            "Details":  null
                        },
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751501589076)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Thursday, July 3, 2025 1:13:09 AM"
                                          },
                            "Name":  "Function Definition: New-Documentation",
                            "Message":  "Function definition found",
                            "Details":  null
                        },
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751501589090)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Thursday, July 3, 2025 1:13:09 AM"
                                          },
                            "Name":  "Function Definition: Show-ScriptHelp",
                            "Message":  "Function definition found",
                            "Details":  null
                        },
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751501589114)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Thursday, July 3, 2025 1:13:09 AM"
                                          },
                            "Name":  "Phase 1 Integration: ConfigurationManager",
                            "Message":  "Feature preserved",
                            "Details":  null
                        },
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751501589128)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Thursday, July 3, 2025 1:13:09 AM"
                                          },
                            "Name":  "Phase 1 Integration: Get-ConfigurationValue",
                            "Message":  "Feature preserved",
                            "Details":  null
                        },
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751501589142)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Thursday, July 3, 2025 1:13:09 AM"
                                          },
                            "Name":  "Phase 1 Integration: Write-ErrorLog",
                            "Message":  "Feature preserved",
                            "Details":  null
                        },
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751501589158)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Thursday, July 3, 2025 1:13:09 AM"
                                          },
                            "Name":  "Phase 2 Integration: AutoCompletionHistory",
                            "Message":  "Feature preserved",
                            "Details":  null
                        },
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751501589180)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Thursday, July 3, 2025 1:13:09 AM"
                                          },
                            "Name":  "Phase 2 Integration: Apply-ModernUIIntegration",
                            "Message":  "Feature preserved",
                            "Details":  null
                        },
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751501589194)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Thursday, July 3, 2025 1:13:09 AM"
                                          },
                            "Name":  "Phase 2 Integration: Show-ProgressDialog",
                            "Message":  "Feature preserved",
                            "Details":  null
                        },
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751501589211)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Thursday, July 3, 2025 1:13:09 AM"
                                          },
                            "Name":  "Phase 3 Integration: OrganizationalDataManager",
                            "Message":  "Feature preserved",
                            "Details":  null
                        },
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751501589228)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Thursday, July 3, 2025 1:13:09 AM"
                                          },
                            "Name":  "Phase 3 Integration: BatchOperationManager",
                            "Message":  "Feature preserved",
                            "Details":  null
                        },
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751501589246)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Thursday, July 3, 2025 1:13:09 AM"
                                          },
                            "Name":  "Phase 3 Integration: EnhancedJiraManager",
                            "Message":  "Feature preserved",
                            "Details":  null
                        },
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751501589262)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Thursday, July 3, 2025 1:13:09 AM"
                                          },
                            "Name":  "Phase 3 Integration: AdvancedADGroupManager",
                            "Message":  "Feature preserved",
                            "Details":  null
                        },
                        {
                            "Status":  "FAILED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751501589319)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Thursday, July 3, 2025 1:13:09 AM"
                                          },
                            "Name":  "Region Structure",
                            "Message":  "Unbalanced regions: 31 regions, 30 endregions",
                            "Details":  null
                        },
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751501589336)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Thursday, July 3, 2025 1:13:09 AM"
                                          },
                            "Name":  "Error Handling",
                            "Message":  "Good error handling coverage: 127 try blocks",
                            "Details":  null
                        },
                        {
                            "Status":  "FAILED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751501589735)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Thursday, July 3, 2025 1:13:09 AM"
                                          },
                            "Name":  "Documentation Coverage",
                            "Message":  "Low documentation: 7.05% comment lines",
                            "Details":  null
                        }
                    ],
    "EndTime":  {
                    "value":  "\/Date(1751501589736)\/",
                    "DisplayHint":  2,
                    "DateTime":  "Thursday, July 3, 2025 1:13:09 AM"
                },
    "TotalTests":  27
}
