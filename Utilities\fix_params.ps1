# Fix parameter description syntax issues
$scriptPath = "OnboardingFromJiraGUI.ps1"
$content = Get-Content $scriptPath -Raw

Write-Host "Fixing parameter description syntax..." -ForegroundColor Yellow

# Fix the quoted domain example that's causing parsing issues
$content = $content -replace '\(e\.g\., "jeragm\.com"\)', '(e.g., jeragm.com)'

# Fix parentheses in parameter descriptions that PowerShell might interpret as code
$content = $content -replace '\(Development, Testing, Production\)', '[Development, Testing, Production]'

$content | Set-Content $scriptPath -Encoding UTF8

Write-Host "Fixed parameter description syntax" -ForegroundColor Green