try {
    $errors = $null
    $tokens = $null
    $ast = [System.Management.Automation.Language.Parser]::ParseFile('Utilities\OnboardingFromJiraGUI.ps1', [ref]$tokens, [ref]$errors)
    
    if ($errors.Count -eq 0) {
        Write-Host "SUCCESS: No syntax errors found" -ForegroundColor Green
        Write-Host "Total tokens: $($tokens.Count)" -ForegroundColor Cyan
        Write-Host "AST nodes: $($ast.FindAll({$true}, $true).Count)" -ForegroundColor Cyan
    } else {
        Write-Host "FAILED: Found $($errors.Count) syntax errors:" -ForegroundColor Red
        foreach ($err in $errors) {
            Write-Host "  Line $($err.Extent.StartLineNumber): $($err.Message)" -ForegroundColor Red
        }
    }
} catch {
    Write-Host "ERROR: Exception during parsing: $($_.Exception.Message)" -ForegroundColor Red
}
