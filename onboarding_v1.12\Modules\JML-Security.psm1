#Requires -Version 5.1

<#
.SYNOPSIS
JML Security Management Module

.DESCRIPTION
This module provides security functionality for the JML (Jo<PERSON>, Mover, Leaver) 
admin account management script. It handles data redaction, credential management,
hashing, and input validation with enterprise-grade security practices.

.NOTES
Version:        2.0
Author:         <PERSON>
Creation Date:  2025-01-09
Security Level: Enhanced with comprehensive data protection

Dependencies:
- PowerShell 5.1 or higher
- System.Web assembly for security functions
#>

# Import required assemblies
Add-Type -AssemblyName System.Web

# Import configuration module
Import-Module (Join-Path $PSScriptRoot "JML-Configuration.psm1") -Force

<#
.SYNOPSIS
Redacts sensitive information from text for secure logging.

.DESCRIPTION
Implements comprehensive data redaction for sensitive information including UPNs,
email addresses, distinguished names, and server names. Uses configurable redaction
patterns and hashing for audit trail purposes.

.PARAMETER Text
The text to redact sensitive information from.

.PARAMETER RedactionType
Type of redaction: UPN, Email, DistinguishedName, ServerName, or All.

.EXAMPLE
Protect-SensitiveData -Text "<EMAIL>" -RedactionType "Email"
Returns: "user***@domain.com"

.NOTES
Security: Uses SHA256 hashing with salt for audit trail purposes.
#>
function Protect-SensitiveData {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true, ValueFromPipeline = $true)]
        [AllowEmptyString()]
        [string]$Text,

        [Parameter(Mandatory = $false)]
        [ValidateSet('UPN', 'Email', 'DistinguishedName', 'ServerName', 'All')]
        [string]$RedactionType = 'All'
    )

    if ([string]::IsNullOrEmpty($Text)) {
        return $Text
    }

    $redactedText = $Text

    try {
        # Get configuration
        $config = Get-ModuleConfiguration

        # Only redact if redaction is enabled in configuration
        if ($config -and $config.Logging.EnableDataRedaction) {

            # UPN and Email redaction (user***@domain.com)
            if ($RedactionType -eq 'UPN' -or $RedactionType -eq 'Email' -or $RedactionType -eq 'All') {
                if ($config.Logging.DataRedaction.RedactUPNs -or $config.Logging.DataRedaction.RedactEmailAddresses) {
                    # Pattern for email/UPN: <EMAIL> -> user***@domain.com
                    $redactedText = $redactedText -replace '(\w+)@(\w+\.\w+)', '$1***@$2'
                }
            }

            # Distinguished Name redaction
            if ($RedactionType -eq 'DistinguishedName' -or $RedactionType -eq 'All') {
                if ($config.Logging.DataRedaction.RedactDistinguishedNames) {
                    # Hash DN for audit trail while maintaining privacy
                    $dnPattern = 'CN=([^,]+),.*?DC=\w+,DC=\w+'
                    $redactedText = $redactedText -replace $dnPattern, '[DN-HASH:' + (Get-StringHash -InputString $Text) + ']'
                }
            }

            # Server name redaction
            if ($RedactionType -eq 'ServerName' -or $RedactionType -eq 'All') {
                if ($config.Logging.DataRedaction.RedactServerNames) {
                    # Replace server URLs with generic placeholders
                    $redactedText = $redactedText -replace 'https?://[^/\s]+', '[SERVER]'
                    if ($config.Jira.ServerUrl) {
                        $redactedText = $redactedText -replace [regex]::Escape($config.Jira.ServerUrl), '[JIRA-INSTANCE]'
                    }
                    if ($config.Email.SmtpServer) {
                        $redactedText = $redactedText -replace [regex]::Escape($config.Email.SmtpServer), '[SMTP-SERVER]'
                    }
                }
            }
        }

        return $redactedText
    }
    catch {
        # If redaction fails, return original text to avoid breaking functionality
        Write-Warning "Data redaction failed: $($_.Exception.Message)"
        return $Text
    }
}

<#
.SYNOPSIS
Generates a secure hash of a string for audit purposes.

.DESCRIPTION
Creates a SHA256 hash with salt for sensitive data that needs to be tracked
but not exposed in logs.

.PARAMETER InputString
The string to hash.

.EXAMPLE
Get-StringHash -InputString "sensitive data"

.NOTES
Security: Uses configurable salt from configuration for consistent hashing.
#>
function Get-StringHash {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [string]$InputString
    )

    $hasher = $null
    try {
        # Get configuration
        $config = Get-ModuleConfiguration
        
        $salt = if ($config -and $config.Logging.DataRedaction.HashSalt) {
            $config.Logging.DataRedaction.HashSalt
        } else {
            "DefaultSalt2024"
        }

        $saltedString = $InputString + $salt
        $hasher = [System.Security.Cryptography.SHA256]::Create()
        $hashBytes = $hasher.ComputeHash([System.Text.Encoding]::UTF8.GetBytes($saltedString))
        $hashString = [System.BitConverter]::ToString($hashBytes) -replace '-', ''

        # Return first 8 characters for brevity in logs
        return $hashString.Substring(0, 8)
    }
    catch {
        Write-Warning "Hash generation failed: $($_.Exception.Message)"
        return "HASH-ERROR"
    }
    finally {
        if ($hasher) {
            $hasher.Dispose()
        }
    }
}

<#
.SYNOPSIS
Retrieves credentials using the configured secure storage method.

.DESCRIPTION
Implements a fallback hierarchy for credential retrieval:
1. PowerShell SecretManagement module
2. Windows Credential Manager
3. Encrypted credential files
4. Secure prompt as last resort

.PARAMETER CredentialName
Name/identifier for the credential.

.PARAMETER Purpose
Description of what the credential is used for (for prompting).

.EXAMPLE
$cred = Get-SecureCredential -CredentialName "JiraApiToken" -Purpose "Jira API access"

.NOTES
Security: Implements multiple secure storage methods with graceful fallback.
#>
function Get-SecureCredential {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [string]$CredentialName,

        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [string]$Purpose
    )

    $credential = $null
    $methods = @()

    # Get configuration
    $config = Get-ModuleConfiguration

    # Build list of available methods based on configuration and availability
    if ($config -and $config.Security.CredentialStorage.PrimaryMethod) {
        $methods += $config.Security.CredentialStorage.PrimaryMethod
        $methods += $config.Security.CredentialStorage.FallbackMethods
    } else {
        $methods = @('SecretManagement', 'CredentialManager', 'EncryptedFile', 'SecurePrompt')
    }

    foreach ($method in $methods) {
        try {
            switch ($method) {
                'SecretManagement' {
                    # Check if SecretManagement module is available
                    if (Get-Module -ListAvailable -Name "Microsoft.PowerShell.SecretManagement") {
                        Write-Host "Attempting to retrieve credential using SecretManagement..." -ForegroundColor Cyan
                        $vaultName = if ($config) { $config.Security.CredentialStorage.SecretVaultName } else { "AdminAccountVault" }

                        # Check if vault exists
                        $vault = Get-SecretVault -Name $vaultName -ErrorAction SilentlyContinue
                        if ($vault) {
                            $credential = Get-Secret -Name $CredentialName -Vault $vaultName -ErrorAction Stop
                            Write-Host "Credential retrieved from SecretManagement vault." -ForegroundColor Green
                            break
                        }
                    }
                }

                'CredentialManager' {
                    Write-Host "Attempting to retrieve credential from Windows Credential Manager..." -ForegroundColor Cyan
                    # Implementation would use Windows Credential Manager APIs
                    # This is a placeholder for the actual implementation
                    Write-Warning "Windows Credential Manager integration not yet implemented."
                }

                'EncryptedFile' {
                    Write-Host "Attempting to retrieve credential from encrypted file..." -ForegroundColor Cyan
                    $filePath = if ($config) { $config.Security.CredentialStorage.EncryptedFileSettings.FilePath } else { ".\SecureCredentials.xml" }
                    if (Test-Path $filePath) {
                        $encryptedData = Import-Clixml -Path $filePath -ErrorAction Stop
                        if ($encryptedData.ContainsKey($CredentialName)) {
                            $credential = $encryptedData[$CredentialName]
                            Write-Host "Credential retrieved from encrypted file." -ForegroundColor Green
                            break
                        }
                    }
                }

                'SecurePrompt' {
                    Write-Host "Prompting for credential: $Purpose" -ForegroundColor Yellow
                    $credential = Get-Credential -Message "Enter credentials for: $Purpose" -UserName $CredentialName
                    if ($credential) {
                        Write-Host "Credential provided via secure prompt." -ForegroundColor Green
                        break
                    }
                }
            }

            if ($credential) {
                break
            }
        }
        catch {
            Write-Warning "Failed to retrieve credential using method '$method': $($_.Exception.Message)"
            continue
        }
    }

    if (-not $credential) {
        throw "Failed to retrieve credential '$CredentialName' using any available method."
    }

    return $credential
}

# Export functions
Export-ModuleMember -Function Protect-SensitiveData, Get-StringHash, Get-SecureCredential
