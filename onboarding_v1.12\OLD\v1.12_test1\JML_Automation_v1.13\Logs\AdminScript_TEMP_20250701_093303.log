2025-07-01 09:33:03.383 | akinje          | INFO     | JML Automation Script v1.13 starting...
2025-07-01 09:33:03.391 | akinje          | DEBUG    | Script path: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13
2025-07-01 09:33:03.411 | akinje          | DEBUG    | PowerShell version: 7.5.1
2025-07-01 09:33:04.542 | akinje          | DEBUG    | Execution policy: Bypass
2025-07-01 09:33:04.560 | akinje          | DEBUG    | User: akinje
2025-07-01 09:33:04.578 | akinje          | DEBUG    | Computer: JGM-CG7THR3
2025-07-01 09:33:04.671 | akinje          | INFO     | === JML ENVIRONMENT VALIDATION ===
2025-07-01 09:33:04.692 | akin<PERSON>          | INFO     | Verifying PowerShell Version...
2025-07-01 09:33:04.722 | akinje          | INFO     | === JML ENVIRONMENT VALIDATION ===
2025-07-01 09:33:04.748 | akinje          | INFO     | Verifying PowerShell Version...
2025-07-01 09:33:04.788 | akinje          | INFO     | PowerShell 7+ detected (version 7.5.1). Checking for updates...
2025-07-01 09:33:04.812 | akinje          | INFO     | PowerShell 7+ detected (version 7.5.1)
2025-07-01 09:33:04.845 | akinje          | INFO     | Checking for PowerShell updates online...
2025-07-01 09:33:04.876 | akinje          | DEBUG    | Checking for PowerShell updates online...
2025-07-01 09:33:05.188 | akinje          | INFO     | A newer version of PowerShell (7.5.2) is available.
2025-07-01 09:33:05.206 | akinje          | INFO     | Current version: 7.5.1
2025-07-01 09:33:05.228 | akinje          | INFO     | Download from: https://github.com/PowerShell/PowerShell/releases
2025-07-01 09:33:05.260 | akinje          | INFO     | === CHECKING REQUIRED MODULES ===
2025-07-01 09:33:05.290 | akinje          | INFO     | === CHECKING REQUIRED MODULES ===
2025-07-01 09:33:05.339 | akinje          | INFO     | Checking for required modules and available updates...
2025-07-01 09:33:17.387 | akinje          | INFO     | Checking module: Microsoft.PowerShell.SecretManagement...
2025-07-01 09:33:17.549 | akinje          | INFO     |   Found installed version 1.1.2
2025-07-01 09:33:18.367 | akinje          | INFO     |   Already up to date
2025-07-01 09:33:18.386 | akinje          | INFO     | Checking module: Microsoft.PowerShell.SecretStore...
2025-07-01 09:33:18.641 | akinje          | INFO     |   Found installed version 1.0.6
2025-07-01 09:33:19.187 | akinje          | INFO     |   Already up to date
2025-07-01 09:33:19.214 | akinje          | INFO     | Checking module: Microsoft.Graph...
2025-07-01 09:33:19.437 | akinje          | INFO     |   Found installed version 2.28.0
2025-07-01 09:33:20.091 | akinje          | INFO     |   Already up to date
2025-07-01 09:33:20.110 | akinje          | INFO     | Checking module: JiraPS...
2025-07-01 09:33:20.355 | akinje          | INFO     |   Found installed version 2.14.7
2025-07-01 09:33:20.905 | akinje          | INFO     |   Already up to date
2025-07-01 09:33:20.929 | akinje          | INFO     | Checking module: ImportExcel...
2025-07-01 09:33:21.201 | akinje          | INFO     |   Found installed version 7.8.10
2025-07-01 09:33:21.818 | akinje          | INFO     |   Already up to date
2025-07-01 09:33:21.839 | akinje          | INFO     | Checking module: PSWriteHTML...
2025-07-01 09:33:22.760 | akinje          | INFO     |   Found installed version 1.36.0
2025-07-01 09:33:23.668 | akinje          | INFO     |   Already up to date
2025-07-01 09:33:23.737 | akinje          | INFO     | All modules are up to date.
2025-07-01 09:33:23.762 | akinje          | INFO     | Verifying module availability...
2025-07-01 09:33:23.869 | akinje          | INFO     |   [VERIFIED] Microsoft.PowerShell.SecretManagement v1.1.2 is available.
2025-07-01 09:33:23.970 | akinje          | INFO     |   [VERIFIED] Microsoft.PowerShell.SecretStore v1.0.6 is available.
2025-07-01 09:33:24.054 | akinje          | INFO     |   [VERIFIED] Microsoft.Graph v2.28.0 is available.
2025-07-01 09:33:24.155 | akinje          | INFO     |   [VERIFIED] JiraPS v2.14.7 is available.
2025-07-01 09:33:24.265 | akinje          | INFO     |   [VERIFIED] ImportExcel v7.8.10 is available.
2025-07-01 09:33:24.383 | akinje          | INFO     |   [VERIFIED] PSWriteHTML v1.36.0 is available.
2025-07-01 09:33:24.415 | akinje          | INFO     | All required modules are successfully installed and available.
2025-07-01 09:33:24.454 | akinje          | INFO     | === JML ENVIRONMENT VALIDATION COMPLETE ===
2025-07-01 09:33:24.497 | akinje          | DEBUG    | [DEBUG] Starting JML script execution...
2025-07-01 09:33:24.532 | akinje          | DEBUG    | [DEBUG] Script directory: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13
2025-07-01 09:33:24.577 | akinje          | DEBUG    | [DEBUG] Checking ActiveDirectory module availability...
2025-07-01 09:33:24.675 | akinje          | INFO     | [INFO] ActiveDirectory module is not available - running in UAT mode
2025-07-01 09:33:24.714 | akinje          | INFO     | [INFO] This is normal for UAT/test environments
2025-07-01 09:33:24.769 | akinje          | DEBUG    | [DEBUG] Initializing JML paths...
2025-07-01 09:33:24.814 | akinje          | DEBUG    | [DEBUG] JML Paths initialized globally.
2025-07-01 09:33:24.867 | akinje          | DEBUG    | [DEBUG] Project Root: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13
2025-07-01 09:33:24.912 | akinje          | DEBUG    | [DEBUG] Modules Root: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Modules
2025-07-01 09:33:24.956 | akinje          | DEBUG    | [DEBUG] Config Dir: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Config
2025-07-01 09:33:24.993 | akinje          | DEBUG    | [DEBUG] Updated ConfigPath to use Config directory: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Config\AdminAccountConfig.psd1
2025-07-01 09:33:25.022 | akinje          | DEBUG    | [DEBUG] Starting enhanced module loading...
2025-07-01 09:33:25.064 | akinje          | INFO     | Loading JML modules from nested structure...
2025-07-01 09:33:25.106 | akinje          | INFO     | Modules Root: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Modules
2025-07-01 09:33:25.139 | akinje          | INFO     |   Loading: JML-Core/JML-Utilities [CRITICAL]
2025-07-01 09:33:25.168 | akinje          | INFO     |     Description: General utility functions
2025-07-01 09:33:25.198 | akinje          | INFO     |     Path: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Modules\JML-Core\JML-Utilities.psd1 (manifest)
2025-07-01 09:33:25.228 | akinje          | INFO     |     Attempting to import module using manifest...
2025-07-01 09:33:25.486 | akinje          | WARNING  | WARNING: JML-Security: JML-Configuration module functions not available. Some features may be limited.
2025-07-01 09:33:25.703 | akinje          | WARNING  | WARNING: JML-Logging: Missing dependencies: Get-ModuleConfiguration (JML-Configuration), Protect-SensitiveData (JML-Security). Some features may be limited.
2025-07-01 09:33:25.727 | akinje          | INFO     |     [SUCCESS] Module loaded: JML-Core/JML-Utilities
2025-07-01 09:33:25.834 | akinje          | INFO     |     [WARNING] Critical function Get-StringHash not available after import
2025-07-01 09:33:25.948 | akinje          | INFO     |     [WARNING] Critical function Test-ModuleAvailability not available after import
2025-07-01 09:33:25.978 | akinje          | INFO     |   Loading: JML-Core/JML-Configuration [CRITICAL]
2025-07-01 09:33:26.010 | akinje          | INFO     |     Description: Core configuration management
2025-07-01 09:33:26.056 | akinje          | INFO     |     Path: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Modules\JML-Core\JML-Configuration.psd1 (manifest)
2025-07-01 09:33:26.086 | akinje          | INFO     |     Attempting to import module using manifest...
2025-07-01 09:33:26.162 | akinje          | INFO     |     [SUCCESS] Module loaded: JML-Core/JML-Configuration
2025-07-01 09:33:26.188 | akinje          | INFO     |     [VERIFIED] Function Initialize-SmartConfiguration is available
2025-07-01 09:33:26.214 | akinje          | INFO     |     [VERIFIED] Function Get-ModuleConfiguration is available
2025-07-01 09:33:26.248 | akinje          | INFO     |   Loading: JML-Core/JML-Security [CRITICAL]
2025-07-01 09:33:26.286 | akinje          | INFO     |     Description: Security and credential management
2025-07-01 09:33:26.316 | akinje          | INFO     |     Path: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Modules\JML-Core\JML-Security.psd1 (manifest)
2025-07-01 09:33:26.357 | akinje          | INFO     |     Attempting to import module using manifest...
2025-07-01 09:33:26.402 | akinje          | INFO     |     [SUCCESS] Module loaded: JML-Core/JML-Security
2025-07-01 09:33:26.429 | akinje          | INFO     |     [VERIFIED] Function Get-SecureCredential is available
2025-07-01 09:33:26.462 | akinje          | INFO     |     [VERIFIED] Function Set-SecureCredential is available
2025-07-01 09:33:26.499 | akinje          | INFO     |   Loading: JML-Core/JML-Logging [CRITICAL]
2025-07-01 09:33:26.533 | akinje          | INFO     |     Description: Secure logging functionality
2025-07-01 09:33:26.571 | akinje          | INFO     |     Path: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Modules\JML-Core\JML-Logging.psd1 (manifest)
2025-07-01 09:33:26.612 | akinje          | INFO     |     Attempting to import module using manifest...
2025-07-01 09:33:26.684 | akinje          | INFO     |     [SUCCESS] Module loaded: JML-Core/JML-Logging
2025-07-01 09:33:26.724 | akinje          | INFO     |     [VERIFIED] Function Write-SecureLog is available
2025-07-01 09:33:26.851 | akinje          | INFO     |     [WARNING] Critical function Initialize-LoggingSystem not available after import
2025-07-01 09:33:26.889 | akinje          | INFO     |   Loading: JML-Features/JML-ActiveDirectory [OPTIONAL]
2025-07-01 09:33:26.931 | akinje          | INFO     |     Description: Active Directory operations
2025-07-01 09:33:26.976 | akinje          | INFO     |     Path: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Modules\JML-Features\JML-ActiveDirectory.psd1 (manifest)
2025-07-01 09:33:27.013 | akinje          | INFO     |     Attempting to import module using manifest...
2025-07-01 09:33:27.173 | akinje          | INFO     |     [SUCCESS] Module loaded: JML-Features/JML-ActiveDirectory
2025-07-01 09:33:27.200 | akinje          | INFO     |   Loading: JML-Features/JML-Email [OPTIONAL]
2025-07-01 09:33:27.224 | akinje          | INFO     |     Description: Email notification functionality
2025-07-01 09:33:27.247 | akinje          | INFO     |     Path: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Modules\JML-Features\JML-Email.psd1 (manifest)
2025-07-01 09:33:27.271 | akinje          | INFO     |     Attempting to import module using manifest...
2025-07-01 09:33:27.361 | akinje          | INFO     |     [SUCCESS] Module loaded: JML-Features/JML-Email
2025-07-01 09:33:27.388 | akinje          | INFO     |   Loading: JML-Features/JML-Jira [OPTIONAL]
2025-07-01 09:33:27.420 | akinje          | INFO     |     Description: Jira integration
2025-07-01 09:33:27.471 | akinje          | INFO     |     Path: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Modules\JML-Features\JML-Jira.psd1 (manifest)
2025-07-01 09:33:27.538 | akinje          | INFO     |     Attempting to import module using manifest...
2025-07-01 09:33:27.668 | akinje          | INFO     |     [SUCCESS] Module loaded: JML-Features/JML-Jira
2025-07-01 09:33:27.705 | akinje          | INFO     |   Loading: JML-Infrastructure/JML-Setup [OPTIONAL]
2025-07-01 09:33:27.739 | akinje          | INFO     |     Description: Setup and environment validation
2025-07-01 09:33:27.768 | akinje          | INFO     |     Path: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Modules\JML-Infrastructure\JML-Setup.psd1 (manifest)
2025-07-01 09:33:27.795 | akinje          | INFO     |     Attempting to import module using manifest...
2025-07-01 09:33:28.237 | akinje          | INFO     |     [SUCCESS] Module loaded: JML-Infrastructure/JML-Setup
2025-07-01 09:33:28.271 | akinje          | INFO     |   Loading: JML-Infrastructure/JML-Monitoring [OPTIONAL]
2025-07-01 09:33:28.324 | akinje          | INFO     |     Description: System monitoring
2025-07-01 09:33:28.413 | akinje          | INFO     |     Path: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Modules\JML-Infrastructure\JML-Monitoring.psd1 (manifest)
2025-07-01 09:33:28.455 | akinje          | INFO     |     Attempting to import module using manifest...
2025-07-01 09:33:28.612 | akinje          | INFO     |     [SUCCESS] Module loaded: JML-Infrastructure/JML-Monitoring
2025-07-01 09:33:28.657 | akinje          | INFO     |   Loading: JML-Infrastructure/JML-UIManager [OPTIONAL]
2025-07-01 09:33:28.693 | akinje          | INFO     |     Description: Modern hybrid dashboard UI with real-time status
2025-07-01 09:33:28.743 | akinje          | INFO     |     Path: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Modules\JML-Infrastructure\JML-UIManager.psd1 (manifest)
2025-07-01 09:33:28.794 | akinje          | INFO     |     Attempting to import module using manifest...
2025-07-01 09:33:28.955 | akinje          | INFO     |     [SUCCESS] Module loaded: JML-Infrastructure/JML-UIManager
2025-07-01 09:33:28.993 | akinje          | INFO     | Module Loading Summary:
2025-07-01 09:33:29.041 | akinje          | INFO     |   Successfully loaded: 10/10
2025-07-01 09:33:29.093 | akinje          | DEBUG    | [DEBUG] Enhanced module loading completed successfully
2025-07-01 09:33:29.129 | akinje          | DEBUG    | [DEBUG] Performing post-loading verification...
2025-07-01 09:33:29.163 | akinje          | DEBUG    | [DEBUG] ✓ Initialize-SmartConfiguration is available
2025-07-01 09:33:29.214 | akinje          | DEBUG    | [DEBUG] ✓ Get-ModuleConfiguration is available
2025-07-01 09:34:05.155 | akinje          | INFO     | Configuration loaded from: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Config\AdminAccountConfig.psd1
2025-07-01 09:34:05.177 | akinje          | INFO     | [SUCCESS] Configuration initialized successfully
2025-07-01 09:34:05.210 | akinje          | DEBUG    | [DEBUG] Initializing credential storage selection...
2025-07-01 09:34:05.307 | akinje          | INFO     | ================================================================================
2025-07-01 09:34:05.331 | akinje          | INFO     |                     JML Credential Storage Selection                      
2025-07-01 09:34:05.361 | akinje          | INFO     | ================================================================================
2025-07-01 09:34:05.395 | akinje          | INFO     | Please select your preferred credential storage method:
2025-07-01 09:34:05.426 | akinje          | INFO     |   1. Encrypted File (Recommended for Portability)
2025-07-01 09:34:05.455 | akinje          | INFO     |      - Stores credentials in an encrypted XML file
2025-07-01 09:34:05.485 | akinje          | INFO     |      - Portable across different machines
2025-07-01 09:34:05.515 | akinje          | INFO     |      - Uses Windows DPAPI for encryption
2025-07-01 09:34:05.549 | akinje          | INFO     |   2. PowerShell Secret Management / SecretStore
2025-07-01 09:34:05.588 | akinje          | INFO     |      - Uses Microsoft.PowerShell.SecretStore module
2025-07-01 09:34:05.621 | akinje          | INFO     |      - Requires vault password for access
2025-07-01 09:34:05.655 | akinje          | INFO     |      - More secure but requires setup
2025-07-01 09:34:05.700 | akinje          | INFO     |   3. Windows Credential Manager
2025-07-01 09:34:05.746 | akinje          | INFO     |      - Uses Windows built-in credential storage
2025-07-01 09:34:05.785 | akinje          | INFO     |      - Integrated with Windows security
2025-07-01 09:34:05.831 | akinje          | INFO     |      - Machine-specific storage
2025-07-01 09:34:05.881 | akinje          | INFO     | Enter your choice (1-3) [Default: 1 in 10 seconds]: 
2025-07-01 09:34:05.939 | akinje          | INFO     | 2
2025-07-01 09:34:05.979 | akinje          | INFO     | Selected: PowerShell Secret Management
2025-07-01 09:34:06.017 | akinje          | INFO     | Credentials will be stored using Microsoft.PowerShell.SecretStore.
2025-07-01 09:34:06.056 | akinje          | INFO     | Credential storage method set for this session: SecretManagement
2025-07-01 09:34:06.097 | akinje          | INFO     | Press any key to continue...
2025-07-01 09:34:06.166 | akinje          | DEBUG    | [DEBUG] Credential storage method selected: SecretManagement
2025-07-01 09:34:06.213 | akinje          | DEBUG    | [DEBUG] Using credential method for status check: SecretManagement
2025-07-01 09:34:06.268 | akinje          | DEBUG    | [DEBUG] Checking credential setup status for method: SecretManagement
2025-07-01 09:34:06.308 | akinje          | DEBUG    | [DEBUG] Checking SecretManagement credential storage
2025-07-01 09:34:07.636 | akinje          | DEBUG    | [DEBUG] Missing Jira credentials in vault: JiraUsername, JiraApiToken, JiraServerUrl - setup needed
2025-07-01 09:34:07.661 | akinje          | DEBUG    | [DEBUG] Starting automated setup for method: SecretManagement
2025-07-01 09:34:07.694 | akinje          | INFO     | === AUTOMATED CREDENTIAL SETUP (SecretManagement) ===
2025-07-01 09:34:07.738 | akinje          | INFO     | This appears to be the first run of the JML system.
2025-07-01 09:34:07.775 | akinje          | INFO     | Setting up automated credential management using Microsoft.PowerShell.SecretStore...
2025-07-01 09:34:34.184 | akinje          | INFO     | Starting SecretManagement setup...
2025-07-01 09:34:34.258 | akinje          | INFO     | [Progress] JML Admin Account Script Setup Utility
2025-07-01 09:34:34.407 | akinje          | INFO     | [Information] Version 1.12 - Enhanced Security Edition
2025-07-01 09:34:34.507 | akinje          | INFO     | [Progress] Checking SecretStore modules...
2025-07-01 09:34:34.609 | akinje          | INFO     | [Progress] Checking SecretStore modules...
2025-07-01 09:34:34.791 | akinje          | INFO     | [Success] Microsoft.PowerShell.SecretManagement: Available (v1.1.2 1.1.2)
2025-07-01 09:34:34.977 | akinje          | INFO     | [Success] Microsoft.PowerShell.SecretStore: Available (v1.0.6 1.0.6)
2025-07-01 09:34:35.067 | akinje          | INFO     | [Success] All SecretStore modules are available
2025-07-01 09:34:35.128 | akinje          | INFO     | [Progress] Starting credential setup...
2025-07-01 09:34:35.199 | akinje          | INFO     | [Progress] Initializing comprehensive SecretStore credential management...
2025-07-01 09:34:35.278 | akinje          | INFO     | [Information] Credential setup already completed.
2025-07-01 09:34:35.355 | akinje          | INFO     | Existing credential setup detected.
2025-07-01 09:34:35.388 | akinje          | INFO     | This may be due to missing credentials (like JiraServerUrl) that were added in recent updates.
2025-07-01 09:34:35.445 | akinje          | INFO     | Options:
2025-07-01 09:34:35.498 | akinje          | INFO     | 1. Force re-run setup (recommended - will add any missing credentials)
2025-07-01 09:34:35.560 | akinje          | INFO     | 2. Skip setup (use existing credentials as-is)
