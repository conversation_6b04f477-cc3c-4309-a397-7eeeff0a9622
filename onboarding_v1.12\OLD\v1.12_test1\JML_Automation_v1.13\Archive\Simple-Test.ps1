Write-Host "Testing module loading..." -ForegroundColor Cyan

# Import modules
Import-Module .\Modules\JML-Configuration.psd1 -Force -Global
Import-Module .\Modules\JML-Security.psd1 -Force -Global
Import-Module .\Modules\JML-Utilities.psd1 -Force -Global
Import-Module .\Modules\JML-Logging.psd1 -Force -Global

Write-Host "Loaded modules:" -ForegroundColor Yellow
Get-Module | Where-Object {$_.Name -like "*JML*"} | ForEach-Object {
    Write-Host "  $($_.Name) - Version: $($_.Version) - Functions: $($_.ExportedFunctions.Count)" -ForegroundColor Green
}

Write-Host "Testing function availability:" -ForegroundColor Yellow
$functions = @("Initialize-SmartConfiguration", "Get-SecureCredential", "Get-ValidatedUPN", "Write-SecureLog")
foreach ($func in $functions) {
    $cmd = Get-Command $func -ErrorAction SilentlyContinue
    if ($cmd) {
        Write-Host "  $func - Found in module: $($cmd.Module.Name)" -ForegroundColor Green
    } else {
        Write-Host "  $func - NOT FOUND" -ForegroundColor Red
    }
}
