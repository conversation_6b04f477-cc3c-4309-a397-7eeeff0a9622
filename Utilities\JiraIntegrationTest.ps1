#!/usr/bin/env pwsh

# Test script to validate Jira integration with real API token
# This tests the core business logic without the WPF GUI

Write-Host "=== JIRA INTEGRATION TEST ===" -ForegroundColor Green
Write-Host "Testing with real API token..." -ForegroundColor Cyan

# Import JiraPS module
try {
    Import-Module JiraPS -Force
    Write-Host "✅ JiraPS module loaded successfully" -ForegroundColor Green
} catch {
    Write-Host "❌ Failed to import JiraPS: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Test Jira connection
try {
    Write-Host "Testing Jira connection..." -ForegroundColor Cyan
    
    # Check if session exists
    $session = Get-JiraSession
    if ($session) {
        Write-Host "✅ Existing Jira session found: $($session.WebSession.BaseAddress)" -ForegroundColor Green
    } else {
        Write-Host "⚠️  No existing Jira session. Please authenticate first:" -ForegroundColor Yellow
        Write-Host "   Set-JiraConfigServer -Server 'https://jeragm.atlassian.net'" -ForegroundColor White
        Write-Host "   New-JiraSession -Credential (Get-Credential)" -ForegroundColor White
        exit 1
    }
} catch {
    Write-Host "❌ Jira connection test failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Test fetching a real ticket
$testTicketId = "TESTIT-49342"
Write-Host "Fetching test ticket: $testTicketId" -ForegroundColor Cyan

try {
    $issue = Get-JiraIssue -Key $testTicketId -ErrorAction Stop
    Write-Host "✅ Successfully fetched ticket: $($issue.Key)" -ForegroundColor Green
    Write-Host "   Summary: $($issue.Summary)" -ForegroundColor White
    Write-Host "   Status: $($issue.Status.Name)" -ForegroundColor White
    Write-Host "   Created: $($issue.Created)" -ForegroundColor White
    
    # Test custom field extraction
    Write-Host "`n=== CUSTOM FIELD EXTRACTION TEST ===" -ForegroundColor Green
    
    $ticketData = @{}
    $customFieldsFound = 0
    
    # Expected custom fields for JERAGM
    $expectedFields = @{
        "customfield_10304" = "FirstName"
        "customfield_10305" = "LastName"
        "customfield_10238" = "JobTitle"
        "customfield_10120" = "Department"
        "customfield_10343" = "ModelAccount"
        "customfield_10115" = "OfficeLocation"
    }
    
    # Method 1: Check CustomFields property
    if ($issue.CustomFields) {
        Write-Host "📋 CustomFields property exists with $($issue.CustomFields.Count) items" -ForegroundColor Cyan
        foreach ($field in $issue.CustomFields) {
            $fieldName = if ($expectedFields[$field.Id]) { $expectedFields[$field.Id] } else { $field.Id }
            Write-Host "   $fieldName ($($field.Id)): $($field.Value)" -ForegroundColor Yellow
            $ticketData[$field.Id] = $field.Value
            $customFieldsFound++
        }
    }
    
    # Method 2: Check direct properties
    $customFieldProperties = $issue.PSObject.Properties | Where-Object { $_.Name -like "customfield_*" }
    if ($customFieldProperties) {
        Write-Host "🔍 Found $($customFieldProperties.Count) direct custom field properties" -ForegroundColor Cyan
        foreach ($prop in $customFieldProperties) {
            $fieldName = if ($expectedFields[$prop.Name]) { $expectedFields[$prop.Name] } else { $prop.Name }
            Write-Host "   $fieldName ($($prop.Name)): $($prop.Value)" -ForegroundColor Yellow
            $ticketData[$prop.Name] = $prop.Value
            $customFieldsFound++
        }
    }
    
    # Method 3: Check Fields property
    if ($issue.Fields) {
        Write-Host "📁 Fields property exists" -ForegroundColor Cyan
        $fieldsCustomFields = $issue.Fields.PSObject.Properties | Where-Object { $_.Name -like "customfield_*" }
        foreach ($prop in $fieldsCustomFields) {
            $fieldName = if ($expectedFields[$prop.Name]) { $expectedFields[$prop.Name] } else { $prop.Name }
            Write-Host "   $fieldName ($($prop.Name)): $($prop.Value)" -ForegroundColor Yellow
            $ticketData[$prop.Name] = $prop.Value
            $customFieldsFound++
        }
    }
    
    # Test specific fields we need
    Write-Host "`n=== REQUIRED FIELD VALIDATION ===" -ForegroundColor Green
    $requiredFields = @("customfield_10304", "customfield_10305", "customfield_10238", "customfield_10120", "customfield_10343")
    $foundRequiredFields = 0
    
    foreach ($fieldId in $requiredFields) {
        $fieldName = $expectedFields[$fieldId]
        if ($ticketData[$fieldId]) {
            Write-Host "✅ $fieldName ($fieldId): '$($ticketData[$fieldId])'" -ForegroundColor Green
            $foundRequiredFields++
        } else {
            Write-Host "❌ $fieldName ($fieldId): NOT FOUND" -ForegroundColor Red
        }
    }
    
    # Generate username based on extracted data
    if ($ticketData["customfield_10304"] -and $ticketData["customfield_10305"]) {
        $firstName = $ticketData["customfield_10304"]
        $lastName = $ticketData["customfield_10305"]
        $username = ($firstName.Substring(0,1) + $lastName).ToLower() -replace '[^a-z0-9]', ''
        Write-Host "`n=== USERNAME GENERATION ===" -ForegroundColor Green
        Write-Host "✅ Generated username: '$username' from '$firstName $lastName'" -ForegroundColor Green
    } else {
        Write-Host "❌ Cannot generate username - missing FirstName or LastName" -ForegroundColor Red
    }
    
    # Summary
    Write-Host "`n=== TEST SUMMARY ===" -ForegroundColor Green
    Write-Host "📊 Total custom fields found: $customFieldsFound" -ForegroundColor Cyan
    Write-Host "📊 Required fields found: $foundRequiredFields/5" -ForegroundColor Cyan
    
    if ($foundRequiredFields -ge 3) {
        Write-Host "✅ JIRA INTEGRATION TEST PASSED" -ForegroundColor Green
        Write-Host "   Ready for production use!" -ForegroundColor White
    } else {
        Write-Host "⚠️  PARTIAL SUCCESS - Some required fields missing" -ForegroundColor Yellow
        Write-Host "   Application will use fallback parsing" -ForegroundColor White
    }
    
} catch {
    Write-Host "❌ Failed to fetch ticket $testTicketId" -ForegroundColor Red
    Write-Host "   Error: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "   This could mean:" -ForegroundColor Yellow
    Write-Host "   - Ticket doesn't exist" -ForegroundColor Yellow
    Write-Host "   - Authentication expired" -ForegroundColor Yellow
    Write-Host "   - Insufficient permissions" -ForegroundColor Yellow
}

Write-Host "`n=== TEST COMPLETE ===" -ForegroundColor Green