

# JML Automation Project Refactoring Plan v3.0

## 1. Critical Assessment of the Restructuring Plan

The proposed restructuring plan is a significant improvement over the current flat structure. It correctly identifies the need for logical separation of components. However, a deeper analysis of the `JML_v1.12.ps1` script and the proposed plan reveals several areas that need more detailed consideration to ensure a robust and seamless transition.

### Potential Issues & Missing Considerations:

*   **Module Discovery and Loading**: The current script likely uses a simple `Get-ChildItem` on a single `Modules` folder. The new nested structure (`Modules/JML-Core`, `Modules/JML-Features`) will break this logic. The plan must specify *how* the main script will discover and import modules from these subdirectories reliably. A simple hardcoded list of imports is not scalable.
*   **Inter-Module Dependencies**: Modules may depend on each other (e.g., a feature module in `JML-Features` might call a function from a `JML-Utilities` module in `JML-Core`). The plan does not explicitly address how these dependencies will be resolved. Relying on global imports in the main script can work, but it's not a robust solution.
*   **Test Script Adaptability**: The `Tests/` directory will contain dozens of scripts. Each of these scripts will need to know how to find and import the refactored modules. Manually updating every test script is error-prone and inefficient. A centralized test helper or bootstrap script is needed.
*   **Configuration and Credential Paths**: The plan correctly moves configuration files to a `Config/` directory. However, it needs to provide a foolproof method for the scripts to find this directory, regardless of where the script is executed from. Using `$PSScriptRoot` is the right approach, but it must be implemented consistently everywhere.
*   **Environment Consistency**: The script needs to run in different environments (dev, test, prod). The plan should consider how configuration might be managed across these environments. While not part of the immediate file structure refactoring, it's a key consideration for maintainability.

## 2. Implementation Recommendations

To address the issues identified above, the following improvements should be integrated into the plan.

*   **Implement a Dynamic Module Loader**:
    *   Create a dedicated function within the main script (`Start-JMLOnboarding.ps1`) that recursively scans the `Modules` directory for all `*.psd1` or `*.psm1` files and imports them.
    *   This loader should use `-Force` to ensure the latest versions are always loaded and `-Global` so that all functions are available to each other, simplifying inter-module dependencies.
    *   Wrap the `Import-Module` calls in a `try/catch` block to provide detailed error messages if a module fails to load.

*   **Create a Centralized Path Management System**:
    *   At the very beginning of the main script and the setup script, define a set of variables that hold the absolute paths to all key directories (`$ProjectRoot`, `$ModulesDir`, `$ConfigDir`, `$LogsDir`).
    *   These variables should be derived from `$PSScriptRoot` to ensure they are always correct relative to the script's location.

*   **Develop a Test Bootstrap File**:
    *   Create a `Tests\Test-Bootstrap.ps1` script.
    *   This script will contain the logic to define the path variables and import all the necessary modules, just like the main script.
    *   Each individual test script (`Test-Jira.ps1`, etc.) will then dot-source this bootstrap file at the beginning, ensuring a consistent test environment.

*   **Add Pre-flight Checks**:
    *   Before executing the main logic, the script should perform a series of pre-flight checks to validate the environment.
    *   This includes verifying that the `Config` directory and the `AdminAccountConfig.psd1` file exist, and that the `Modules` directory is not empty. This prevents cryptic errors later on.

## 3. Code Examples and Implementation Guidance

The following snippets demonstrate how to implement the recommendations.

### A. Robust Path Resolution

```powershell
# --- Path Initialization ---
# Use $PSScriptRoot for scripts, fallback to current location for interactive sessions
if ($PSScriptRoot) {
    $ProjectRoot = $PSScriptRoot
} else {
    $ProjectRoot = Get-Location
}

# Define all critical paths relative to the project root
$ModulesDir = Join-Path $ProjectRoot "Modules"
$ConfigDir  = Join-Path $ProjectRoot "Config"
$LogsDir    = Join-Path $ProjectRoot "Logs"

# --- Pre-flight Check ---
if (-not (Test-Path $ModulesDir)) {
    throw "Critical Error: Modules directory not found at '$ModulesDir'. Cannot proceed."
}
if (-not (Test-Path $ConfigDir)) {
    throw "Critical Error: Config directory not found at '$ConfigDir'. Cannot proceed."
}
```

### B. Dynamic and Safe Module Importing

```powershell
# --- Module Discovery and Loading ---
Write-Verbose "Searching for modules in '$ModulesDir'..."
$moduleFiles = Get-ChildItem -Path $ModulesDir -Recurse -Filter "*.psm1"

if ($null -eq $moduleFiles) {
    throw "No PowerShell modules (.psm1) found in the Modules directory."
}

foreach ($moduleFile in $moduleFiles) {
    try {
        Write-Verbose "Importing module: $($moduleFile.FullName)"
        Import-Module $moduleFile.FullName -Force -Global -ErrorAction Stop
    }
    catch {
        Write-Error "Failed to import module '$($moduleFile.Name)'. Error: $_"
        # Depending on criticality, you might want to exit here
        # exit 1
    }
}
```

### C. Safe Configuration File Loading

```powershell
# --- Configuration Loading ---
$configFile = Join-Path $ConfigDir "AdminAccountConfig.psd1"

if (-not (Test-Path $configFile)) {
    throw "Configuration file 'AdminAccountConfig.psd1' not found in '$ConfigDir'."
}

try {
    $Global:Config = Import-PowerShellDataFile -Path $configFile -ErrorAction Stop
    Write-Verbose "Configuration loaded successfully."
}
catch {
    throw "Failed to load or parse the configuration file at '$configFile'. Error: $_"
}
```

## 4. Risk Analysis

*   **Primary Risk - Broken Paths**: The most significant risk is that hardcoded relative paths (`..\Modules`, `.\AdminAccountConfig.psd1`) exist within the modules themselves or in the numerous test scripts. A global search for patterns like `Join-Path`, `..`, and `.\` is necessary to identify and remediate these.
*   **Execution Policy and Permissions**: The refactoring involves moving files. This could encounter issues in environments with strict execution policies or file system permissions.
*   **Dependency Changes**: Renaming the main script and moving modules fundamentally changes how the system is invoked and how it operates. Any external orchestration (e.g., Task Scheduler, CI/CD pipelines) that calls `JML_v1.12.ps1` will break and must be updated.

## 5. Enhanced Restructuring and Phased Implementation Plan

This enhanced plan incorporates all the recommendations for a more robust and manageable system.

### Final Proposed Structure

```
JML_Automation_v1.13/
|
+--- Start-JMLOnboarding.ps1        # Main executable script
+--- Setup-JMLAutomation.ps1        # Setup script
+--- README.md                      # High-level project documentation
|
+--- Config/
|    +--- AdminAccountConfig.psd1    # Centralized configuration
|    +--- SecureCredentials.xml      # Legacy credential store
|
+--- Modules/
|    +--- JML-Core/
|    |    +--- JML-Configuration.psm1, .psd1
|    |    +--- JML-Logging.psm1, .psd1
|    |    +--- JML-Utilities.psm1, .psd1
|    +--- JML-Features/
|    |    +--- JML-ActiveDirectory.psm1, .psd1
|    |    +--- JML-Email.psm1, .psd1
|    |    +--- JML-Jira.psm1, .psd1
|    +--- JML-Security/
|         +--- JML-Security.psm1, .psd1
|         +--- JML-VaultManagement.psm1
|
+--- Tests/
|    +--- Test-Bootstrap.ps1         # Centralized test setup script
|    +--- ... (all other test scripts)
|
+--- Docs/
|    +--- ... (all .md documentation files)
|
+--- Archive/
|    +--- ... (all backups and old files)
|
+--- Logs/                          # Created at runtime
```

### Phased Implementation Approach

**Phase 1: Setup and Scaffolding (No Code Changes)**
1.  **Create Root Directory**: Create the `JML_Automation_v1.13` directory.
2.  **Create Subdirectories**: Create the `Config`, `Modules`, `Tests`, `Docs`, and `Archive` directories.
3.  **Create Module Subdirectories**: Create `JML-Core`, `JML-Features`, and `JML-Security` inside `Modules`.
4.  **Copy Files**: *Copy* (do not move) all the files from the old structure into their new locations as defined in the plan. This preserves the working original version as a fallback.

**Phase 2: Core Script Refactoring**
1.  **Implement Path Management**: Add the robust path resolution logic to the top of `Start-JMLOnboarding.ps1`.
2.  **Implement Module Loader**: Replace the existing module import logic with the new dynamic, recursive loader.
3.  **Implement Config Loader**: Update the script to load the configuration from the `Config` directory.
4.  **Initial Testing**: Run `Start-JMLOnboarding.ps1` in simulation mode to ensure it can load all modules and configurations without errors.

**Phase 3: Test Framework Refactoring**
1.  **Create Bootstrap**: Create the `Tests\Test-Bootstrap.ps1` file containing the path and module loading logic.
2.  **Update a Single Test**: Modify one or two simple test scripts to dot-source the bootstrap file and remove their old path/import logic.
3.  **Run Tests**: Execute the updated tests to validate that the bootstrap approach works.
4.  **Update All Tests**: Once validated, update the remaining test scripts.

**Phase 4: Finalization and Cleanup**
1.  **Full Regression Test**: Run the entire test suite to ensure all functionality is working as expected.
2.  **Update Documentation**: Update the `README.md` to reflect the new structure and execution instructions.
3.  **Archive Old Project**: Once the new version is confirmed to be stable and fully functional, the original `v1.12_test1` directory can be zipped and moved to the `Archive` folder or deleted.
