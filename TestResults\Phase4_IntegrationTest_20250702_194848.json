﻿{
    "Duration":  5.3590002,
    "StartTime":  {
                      "value":  "\/Date(1751482122489)\/",
                      "DisplayHint":  2,
                      "DateTime":  "Wednesday, July 2, 2025 7:48:42 PM"
                  },
    "SkippedTests":  0,
    "FailedTests":  5,
    "Errors":  [
                   "PowerShell Parser: Found 3 syntax errors: Cannot assign property, use \u0027$this.VersionInfo\u0027.; Variable is not assigned in the method.; Variable is not assigned in the method.",
                   "Script Loading: Script loading failed: FAILED: At C:\\Users\\<USER>\\OneDrive - JERA Global Markets\\Azure Devops\\it-endusersupport-automation\\scripts\\Utilities\\OnboardingFromJiraGUI.ps1:7033 char:17 +                 $versionInfo = $this.CompatibilityMatrix.PowerShell[$ ... +                 ~~~~~~~~~~~~ Cannot assign property, use \u0027$this.VersionInfo\u0027.  At C:\\Users\\<USER>\\OneDrive - JERA Global Markets\\Azure Devops\\it-endusersupport-automation\\scripts\\Utilities\\OnboardingFromJiraGUI.ps1:6418 char:25 +             ProcessId = $PID +                         ~~~~ Variable is not assigned in the method.  At C:\\Users\\<USER>\\OneDrive - JERA Global Markets\\Azure Devops\\it-endusersupport-automation\\scripts\\Utilities\\OnboardingFromJiraGUI.ps1:7021 char:22 +         $psVersion = $PSVersionTable.PSVersion.ToString() +                      ~~~~~~~~~~~~~~~ Variable is not assigned in the method.",
                   "Phase 1 Integration: Write-ErrorLog: Feature missing",
                   "Region Structure: Unbalanced regions: 31 regions, 23 endregions",
                   "Documentation Coverage: Low documentation: 6.97% comment lines"
               ],
    "PassedTests":  22,
    "TestDetails":  [
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751482122987)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Wednesday, July 2, 2025 7:48:42 PM"
                                          },
                            "Name":  "File Existence",
                            "Message":  "Script file found",
                            "Details":  null
                        },
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751482123071)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Wednesday, July 2, 2025 7:48:43 PM"
                                          },
                            "Name":  "File Size",
                            "Message":  "Script size: 0.35 MB (367809 bytes)",
                            "Details":  null
                        },
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751482123371)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Wednesday, July 2, 2025 7:48:43 PM"
                                          },
                            "Name":  "Line Count",
                            "Message":  "Total lines: 9842",
                            "Details":  null
                        },
                        {
                            "Status":  "FAILED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751482123907)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Wednesday, July 2, 2025 7:48:43 PM"
                                          },
                            "Name":  "PowerShell Parser",
                            "Message":  "Found 3 syntax errors: Cannot assign property, use \u0027$this.VersionInfo\u0027.; Variable is not assigned in the method.; Variable is not assigned in the method.",
                            "Details":  null
                        },
                        {
                            "Status":  "FAILED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751482126152)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Wednesday, July 2, 2025 7:48:46 PM"
                                          },
                            "Name":  "Script Loading",
                            "Message":  "Script loading failed: FAILED: At C:\\Users\\<USER>\\OneDrive - JERA Global Markets\\Azure Devops\\it-endusersupport-automation\\scripts\\Utilities\\OnboardingFromJiraGUI.ps1:7033 char:17 +                 $versionInfo = $this.CompatibilityMatrix.PowerShell[$ ... +                 ~~~~~~~~~~~~ Cannot assign property, use \u0027$this.VersionInfo\u0027.  At C:\\Users\\<USER>\\OneDrive - JERA Global Markets\\Azure Devops\\it-endusersupport-automation\\scripts\\Utilities\\OnboardingFromJiraGUI.ps1:6418 char:25 +             ProcessId = $PID +                         ~~~~ Variable is not assigned in the method.  At C:\\Users\\<USER>\\OneDrive - JERA Global Markets\\Azure Devops\\it-endusersupport-automation\\scripts\\Utilities\\OnboardingFromJiraGUI.ps1:7021 char:22 +         $psVersion = $PSVersionTable.PSVersion.ToString() +                      ~~~~~~~~~~~~~~~ Variable is not assigned in the method.",
                            "Details":  null
                        },
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751482126201)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Wednesday, July 2, 2025 7:48:46 PM"
                                          },
                            "Name":  "Class Definition: AdvancedLoggingManager",
                            "Message":  "Class definition found",
                            "Details":  null
                        },
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751482126228)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Wednesday, July 2, 2025 7:48:46 PM"
                                          },
                            "Name":  "Class Definition: VersionManager",
                            "Message":  "Class definition found",
                            "Details":  null
                        },
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751482126261)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Wednesday, July 2, 2025 7:48:46 PM"
                                          },
                            "Name":  "Class Definition: TestingFramework",
                            "Message":  "Class definition found",
                            "Details":  null
                        },
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751482126277)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Wednesday, July 2, 2025 7:48:46 PM"
                                          },
                            "Name":  "Class Definition: DocumentationGenerator",
                            "Message":  "Class definition found",
                            "Details":  null
                        },
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751482126295)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Wednesday, July 2, 2025 7:48:46 PM"
                                          },
                            "Name":  "Function Definition: Write-StructuredLog",
                            "Message":  "Function definition found",
                            "Details":  null
                        },
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751482126329)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Wednesday, July 2, 2025 7:48:46 PM"
                                          },
                            "Name":  "Function Definition: Get-ScriptVersion",
                            "Message":  "Function definition found",
                            "Details":  null
                        },
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751482126361)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Wednesday, July 2, 2025 7:48:46 PM"
                                          },
                            "Name":  "Function Definition: Register-Test",
                            "Message":  "Function definition found",
                            "Details":  null
                        },
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751482126396)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Wednesday, July 2, 2025 7:48:46 PM"
                                          },
                            "Name":  "Function Definition: New-Documentation",
                            "Message":  "Function definition found",
                            "Details":  null
                        },
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751482126416)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Wednesday, July 2, 2025 7:48:46 PM"
                                          },
                            "Name":  "Function Definition: Show-ScriptHelp",
                            "Message":  "Function definition found",
                            "Details":  null
                        },
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751482126445)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Wednesday, July 2, 2025 7:48:46 PM"
                                          },
                            "Name":  "Phase 1 Integration: ConfigurationManager",
                            "Message":  "Feature preserved",
                            "Details":  null
                        },
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751482126469)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Wednesday, July 2, 2025 7:48:46 PM"
                                          },
                            "Name":  "Phase 1 Integration: Get-ConfigurationValue",
                            "Message":  "Feature preserved",
                            "Details":  null
                        },
                        {
                            "Status":  "FAILED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751482126489)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Wednesday, July 2, 2025 7:48:46 PM"
                                          },
                            "Name":  "Phase 1 Integration: Write-ErrorLog",
                            "Message":  "Feature missing",
                            "Details":  null
                        },
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751482126500)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Wednesday, July 2, 2025 7:48:46 PM"
                                          },
                            "Name":  "Phase 2 Integration: AutoCompletionHistory",
                            "Message":  "Feature preserved",
                            "Details":  null
                        },
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751482126524)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Wednesday, July 2, 2025 7:48:46 PM"
                                          },
                            "Name":  "Phase 2 Integration: Apply-ModernUIIntegration",
                            "Message":  "Feature preserved",
                            "Details":  null
                        },
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751482126547)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Wednesday, July 2, 2025 7:48:46 PM"
                                          },
                            "Name":  "Phase 2 Integration: Show-ProgressDialog",
                            "Message":  "Feature preserved",
                            "Details":  null
                        },
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751482126561)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Wednesday, July 2, 2025 7:48:46 PM"
                                          },
                            "Name":  "Phase 3 Integration: OrganizationalDataManager",
                            "Message":  "Feature preserved",
                            "Details":  null
                        },
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751482126578)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Wednesday, July 2, 2025 7:48:46 PM"
                                          },
                            "Name":  "Phase 3 Integration: BatchOperationManager",
                            "Message":  "Feature preserved",
                            "Details":  null
                        },
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751482126602)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Wednesday, July 2, 2025 7:48:46 PM"
                                          },
                            "Name":  "Phase 3 Integration: EnhancedJiraManager",
                            "Message":  "Feature preserved",
                            "Details":  null
                        },
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751482126627)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Wednesday, July 2, 2025 7:48:46 PM"
                                          },
                            "Name":  "Phase 3 Integration: AdvancedADGroupManager",
                            "Message":  "Feature preserved",
                            "Details":  null
                        },
                        {
                            "Status":  "FAILED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751482126812)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Wednesday, July 2, 2025 7:48:46 PM"
                                          },
                            "Name":  "Region Structure",
                            "Message":  "Unbalanced regions: 31 regions, 23 endregions",
                            "Details":  null
                        },
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751482126831)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Wednesday, July 2, 2025 7:48:46 PM"
                                          },
                            "Name":  "Error Handling",
                            "Message":  "Good error handling coverage: 127 try blocks",
                            "Details":  null
                        },
                        {
                            "Status":  "FAILED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751482127845)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Wednesday, July 2, 2025 7:48:47 PM"
                                          },
                            "Name":  "Documentation Coverage",
                            "Message":  "Low documentation: 6.97% comment lines",
                            "Details":  null
                        }
                    ],
    "EndTime":  {
                    "value":  "\/Date(1751482127848)\/",
                    "DisplayHint":  2,
                    "DateTime":  "Wednesday, July 2, 2025 7:48:47 PM"
                },
    "TotalTests":  27
}
