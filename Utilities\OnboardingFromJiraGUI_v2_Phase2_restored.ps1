#Requires -Version 5.1

# Load WPF Assemblies - MUST be at the top before any WPF types are referenced
try {
    Add-Type -AssemblyName PresentationFramework -ErrorAction Stop
    Add-Type -AssemblyName PresentationCore -ErrorAction Stop  
    Add-Type -AssemblyName WindowsBase -ErrorAction Stop
    Add-Type -AssemblyName System.Windows.Forms -ErrorAction Stop
} catch {
    Write-Error "Failed to load required WPF assemblies: $($_.Exception.Message)"
    exit 1
}

# OnboardingFromJiraGUI_v2.ps1
# Complete implementation with PowerShell-specific WPF solutions
# Enhanced UI with modern grid layout and professional styling

# PowerShell version and compatibility check for icon support
if ($PSVersionTable.PSVersion.Major -lt 5) {
    Write-Host "ERROR: This script requires PowerShell 5.1 or higher. Current version: $($PSVersionTable.PSVersion)" -ForegroundColor Red
    Write-Host "Please upgrade PowerShell or use PowerShell Core 6+" -ForegroundColor Yellow
    exit 1
}

# Dynamic icon selection based on PowerShell version - using character codes to avoid parsing issues
$script:UseUnicode = $PSVersionTable.PSVersion.Major -ge 7

function Get-Icons {
    if ($script:UseUnicode) {
        # Unicode emojis for PowerShell 7+
        return @{
            Connection = [char]0x1F510  # Lock emoji
            Login = [char]0x1F511       # Key emoji
            Logout = [char]0x1F6AA      # Door emoji
            Ticket = [char]0x1F3AB      # Ticket emoji
            Fetch = [char]0x1F4CB       # Clipboard emoji
            Refresh = [char]0x1F504     # Refresh emoji
            Validate = [char]0x2705     # Check mark emoji
            Create = [char]0x1F477      # Construction worker emoji
            Comment = [char]0x1F4AC     # Speech bubble emoji
            Export = [char]0x1F4BE      # Floppy disk emoji
            Copy = [char]0x1F4CB        # Clipboard emoji
            Browser = [char]0x1F310     # Globe emoji
            User = [char]0x1F464        # User emoji
            Success = [char]0x1F389     # Party emoji
            Error = [char]0x26A0        # Warning emoji
            Info = [char]0x2139         # Information emoji
            Settings = [char]0x2699     # Gear emoji
            Wizard = [char]0x1F9D9      # Wizard emoji
            Steps = [char]0x1F4CB       # Steps emoji
        }
    } else {
        # ASCII alternatives for PowerShell 5.x
        return @{
            Connection = "[*]"
            Login = "[+]"
            Logout = "[-]"
            Ticket = "[T]"
            Fetch = "[F]"
            Refresh = "[R]"
            Validate = "[V]"
            Create = "[C]"
            Comment = "[M]"
            Export = "[E]"
            Copy = "[X]"
            Browser = "[B]"
            User = "[U]"
            Success = "[OK]"
            Error = "[!]"
            Info = "[i]"
            Settings = "[S]"
            Wizard = "[W]"
            Steps = "[#]"
        }
    }
}

$script:Icons = Get-Icons

Write-Host "PowerShell Version: $($PSVersionTable.PSVersion) - UI Enhanced" -ForegroundColor Green
Write-Host "Icon Support: $(if ($script:UseUnicode) { 'Unicode (Emojis)' } else { 'ASCII Fallback' })" -ForegroundColor Cyan

# CENTRALIZED CONFIGURATION
$script:AppConfig = @{
    Jira = @{
        DefaultUrl = "https://jeragm.atlassian.net"
        FieldMappings = @{
            FirstName = @{ CustomFieldId = "customfield_10304"; DisplayName = "First Name" }
            LastName = @{ CustomFieldId = "customfield_10305"; DisplayName = "Last Name" }
            JobTitle = @{ CustomFieldId = "customfield_10238"; DisplayName = "Job Title" }
            Department = @{ CustomFieldId = "customfield_10120"; DisplayName = "Department" }
            ModelAccount = @{ CustomFieldId = "customfield_10343"; DisplayName = "Model Account" }
            OfficeLocation = @{ CustomFieldId = "customfield_10115"; DisplayName = "Office Location" }
            EmployeeType = @{ CustomFieldId = "customfield_10342"; DisplayName = "Employee Type" }
            EffectiveDate = @{ CustomFieldId = "customfield_10344"; DisplayName = "Effective Date" }
        }
        KnownCustomFields = @("customfield_10304", "customfield_10305", "customfield_10238", "customfield_10120", "customfield_10343", "customfield_10115")
    }
    ActiveDirectory = @{
        OUMappings = @{
            "Singapore" = "OU=Users,OU=Singapore,DC=jeragm,DC=com"
            "United Kingdom" = "OU=Users,OU=London,DC=jeragm,DC=com"
            "Japan" = "OU=Tokyo,OU=Users,OU=Accounts,OU=JeraGM,DC=JERAGM,DC=COM"
            "Tokyo" = "OU=Tokyo,OU=Users,OU=Accounts,OU=JeraGM,DC=JERAGM,DC=COM"
            "USA" = "OU=Users,OU=USA,DC=jeragm,DC=com"
            "United States" = "OU=Users,OU=USA,DC=jeragm,DC=com"
        }
        DefaultOU = "OU=Users,OU=Singapore,DC=jeragm,DC=com"
        ValidationRules = @{
            FirstName = @{
                Required = $true; MinLength = 2; MaxLength = 50
                Pattern = '^[a-zA-Z\s\-\.]+$'
                ErrorMessage = "First name must be 2-50 characters, letters only"
            }
            LastName = @{
                Required = $true; MinLength = 2; MaxLength = 50
                Pattern = '^[a-zA-Z\s\-\.]+$'
                ErrorMessage = "Last name must be 2-50 characters, letters only"
            }
            SamAccountName = @{
                Required = $true; MinLength = 3; MaxLength = 20
                Pattern = '^[a-zA-Z0-9\.]+$'
                ErrorMessage = "Username must be 3-20 characters, alphanumeric and dots only"
            }
        }
        Company = "JERA Global Markets"
        DefaultPassword = "TempPass123!"
        Domain = "jeragm.com"
    }
    UI = @{
        WindowTitle = "JERA Global Markets - Onboarding System v3.0"
        WindowSize = @{ Width = 1000; Height = 700; MinWidth = 800; MinHeight = 600 }
        SessionTimeoutMinutes = 60
        Colors = @{
            # JERA Global Markets Official Color Palette
            Primary = "#2D2D30"          # JERA Dark Navy (header/primary buttons)
            Secondary = "#666666"        # JERA Grey (secondary text)
            Success = "#28A745"          # Green for success states
            Warning = "#FF9800"          # Orange for warnings
            Error = "#DC3545"            # Red for errors
            JERAOrange = "#FF6B35"       # JERA Brand Orange
            JERABlue = "#0066CC"         # JERA Bright Blue  
            Disabled = "#CCCCCC"         # Light Gray for disabled
            Background = "#F8F9FA"       # Light background
            ContentBackground = "#FFFFFF" # Pure white for content
            HeaderBackground = "#2D2D30" # JERA Dark Navy header
            StatusSuccess = "#D4EDDA"    # Light green status
            StatusWarning = "#FFF3CD"    # Light orange status  
            StatusError = "#F8D7DA"      # Light red status
            BorderColor = "#DEE2E6"      # Subtle border gray
            TextPrimary = "#212529"      # Dark text
            TextSecondary = "#6C757D"    # Secondary text
            TextMuted = "#ADB5BD"        # Muted text
        }
        Spacing = @{
            DefaultMargin = 15
            FieldSpacing = 10
            ButtonHeight = 40           # Increased for JERA style
            SeparatorSpacing = 20       # More space for professional look
            PanelPadding = 20           # More generous padding
            StatusBarHeight = 30        # Taller status bar
            NavigationHeight = 50       # Taller navigation
        }
        Layout = @{
            LeftPanelWidth = 350        # Wider left panel
            LeftPanelMinWidth = 300
            SplitterWidth = 5
            ScrollViewerHeight = 520    # Adjusted for new dimensions
        }
        Typography = @{
            HeaderFontSize = 18
            SectionFontSize = 16
            LabelFontSize = 14
            BodyFontSize = 12
            StatusFontSize = 11
        }
    }
    Validation = @{
        RequiredFields = @('FirstName', 'LastName', 'Department', 'JobTitle')
        UsernameMaxLength = 20
        CommentTemplate = @"
User onboarding completed:
- Name: {FirstName} {LastName}
- Username: {Username}
- Department: {Department}
- Job Title: {JobTitle}
- OU: {OU}

Account has been created and is ready for use.
"@
    }
}

# Configuration accessor function
function Get-AppConfig([string]$Path) {
    $parts = $Path -split '\.'
    $current = $script:AppConfig
    foreach ($part in $parts) {
        if ($current.ContainsKey($part)) { 
            $current = $current[$part] 
        } else { 
            return $null 
        }
    }
    return $current
}

# Standardized Error Handling Helper
class ErrorHelper {
    static [hashtable] Success([object]$data, [string]$operation = "") {
        $result = @{ Success = $true; Data = $data }
        if ($operation) { $result.Operation = $operation }
        return $result
    }
    
    static [hashtable] Failure([string]$error, [string]$operation = "", [System.Exception]$exception = $null) {
        $result = @{ Success = $false; ErrorMessage = $error }
        if ($operation) { $result.Operation = $operation }
        if ($exception) { $result.Exception = $exception }
        return $result
    }
    
    static [hashtable] SafeExecute([scriptblock]$operation, [string]$operationName, $logger = $null) {
        try {
            if ($logger) { $logger.Debug($operationName, "Starting operation") }
            $result = & $operation
            if ($logger) { $logger.Debug($operationName, "Operation completed successfully") }
            return [ErrorHelper]::Success($result, $operationName)
        } catch {
            if ($logger) { $logger.Error("$operationName failed", $_.Exception) }
            return [ErrorHelper]::Failure($_.Exception.Message, $operationName, $_.Exception)
        }
    }
}

# SECTION 0: POWERSHELL WPF INFRASTRUCTURE

# Custom Property Change Notification System for PowerShell Classes
class ObservableProperty {
    [string]$Name
    [object]$Value
    [scriptblock]$OnChanged

    ObservableProperty([string]$name, [object]$initialValue, [scriptblock]$onChanged) {
        $this.Name = $name
        $this.Value = $initialValue
        $this.OnChanged = $onChanged
    }

    [void] SetValue([object]$newValue) {
        if ($this.Value -ne $newValue) {
            $oldValue = $this.Value
            $this.Value = $newValue
            if ($this.OnChanged) {
                & $this.OnChanged $this.Name $oldValue $newValue
            }
        }
    }
}

# PowerShell-compatible Observable Object Base Class
class ObservableObject {
    hidden [hashtable]$_properties = @{}
    hidden [System.Collections.Generic.List[scriptblock]]$_propertyChangedHandlers = [System.Collections.Generic.List[scriptblock]]::new()

    # Register a property for observation
    hidden [void] RegisterProperty([string]$name, [object]$initialValue) {
        $this._properties[$name] = [ObservableProperty]::new($name, $initialValue, {
            param($propName, $oldValue, $newValue)
            $this.NotifyPropertyChanged($propName)
        })
    }

    # Add property changed event handler
    [void] AddPropertyChangedHandler([scriptblock]$handler) {
        $this._propertyChangedHandlers.Add($handler)
    }

    # Notify all handlers of property change
    hidden [void] NotifyPropertyChanged([string]$propertyName) {
        foreach ($handler in $this._propertyChangedHandlers) {
            & $handler $propertyName
        }
    }

    # Get property value
    [object] GetProperty([string]$name) {
        if ($this._properties.ContainsKey($name)) {
            return $this._properties[$name].Value
        }
        return $null
    }

    # Set property value with change notification
    [void] SetProperty([string]$name, [object]$value) {
        if ($this._properties.ContainsKey($name)) {
            $this._properties[$name].SetValue($value)
        }
    }
}

# PowerShell-compatible Value Converter
class BooleanToVisibilityConverter {
    [object] Convert([object]$value, [type]$targetType, [object]$parameter, [System.Globalization.CultureInfo]$culture) {
        if ($value -is [bool] -and $value) {
            return 'Visible'
        }
        return 'Collapsed'
    }

    [object] ConvertBack([object]$value, [type]$targetType, [object]$parameter, [System.Globalization.CultureInfo]$culture) {
        return $value -eq 'Visible'
    }
}

class InverseBooleanConverter {
    [object] Convert([object]$value, [type]$targetType, [object]$parameter, [System.Globalization.CultureInfo]$culture) {
        if ($value -is [bool]) { return -not $value }
        return $true
    }

    [object] ConvertBack([object]$value, [type]$targetType, [object]$parameter, [System.Globalization.CultureInfo]$culture) {
        if ($value -is [bool]) { return -not $value }
        return $false
    }
}

class BooleanToOppositeBooleanConverter {
    [object] Convert([object]$value, [type]$targetType, [object]$parameter, [System.Globalization.CultureInfo]$culture) { 
        if ($value -is [bool]) { return -not $value }
        return $true
    }
    [object] ConvertBack([object]$value, [type]$targetType, [object]$parameter, [System.Globalization.CultureInfo]$culture) { 
        if ($value -is [bool]) { return -not $value }
        return $false
    }
}

# PowerShell Threading Helper
class AsyncOperationHelper {
    static [void] RunOnUIThread([object]$dispatcher, [scriptblock]$action) {
        if ($dispatcher.CheckAccess()) {
            & $action
        } else {
            $dispatcher.Invoke($action)
        }
    }

    static [void] RunAsync([scriptblock]$backgroundWork, [scriptblock]$onComplete, [object]$uiDispatcher, [object[]]$argumentList = @()) {
        Write-Host "DEBUG: AsyncOperationHelper.RunAsync called" -ForegroundColor Cyan
        try {
            Write-Host "DEBUG: Creating runspace" -ForegroundColor Cyan
            $runspace = [runspacefactory]::CreateRunspace()
            $runspace.Open()
            
            Write-Host "DEBUG: Creating PowerShell instance" -ForegroundColor Cyan
            $powershell = [powershell]::Create()
            $powershell.Runspace = $runspace
            
            # Add the background work
            Write-Host "DEBUG: Adding script to PowerShell instance" -ForegroundColor Cyan
            $powershell.AddScript({
                param($work, $args)
                try {
                    Write-Host "DEBUG: Background script executing" -ForegroundColor Cyan
                    $result = & $work @args
                    Write-Host "DEBUG: Background script completed successfully" -ForegroundColor Cyan
                    return @{ Success = $true; Result = $result }
                } catch {
                    Write-Host "DEBUG: Background script failed: $($_.Exception.Message)" -ForegroundColor Red
                    return @{ Success = $false; Error = $_.Exception.Message }
                }
            }).AddArgument($backgroundWork).AddArgument($argumentList) | Out-Null

            # Start async execution
            Write-Host "DEBUG: Starting async execution" -ForegroundColor Cyan
            $handle = $powershell.BeginInvoke()

            # Schedule completion check
            Write-Host "DEBUG: Setting up completion timer" -ForegroundColor Cyan
            $timer = New-Object System.Windows.Threading.DispatcherTimer
            $timer.Interval = [timespan]::FromMilliseconds(100)
            $timer.Add_Tick({
                Write-Host "DEBUG: Timer tick, checking if completed" -ForegroundColor Gray
                if ($handle.IsCompleted) {
                    Write-Host "DEBUG: Async operation completed" -ForegroundColor Green
                    $timer.Stop()
                    try {
                        $result = $powershell.EndInvoke($handle)
                        Write-Host "DEBUG: Got result, about to dispatch to UI" -ForegroundColor Green
                        if ($uiDispatcher -and $onComplete) {
                            $uiDispatcher.InvokeAsync({
                                Write-Host "DEBUG: UI dispatcher executing completion callback" -ForegroundColor Green
                                & $onComplete $result[0]
                            }.GetNewClosure())
                        }
                    } catch {
                        Write-Warning "Async operation failed: $($_.Exception.Message)"
                    } finally {
                        if ($powershell) { $powershell.Dispose() }
                        if ($runspace) { $runspace.Close() }
                    }
                }
            }.GetNewClosure())
            $timer.Start()
            Write-Host "DEBUG: Timer started" -ForegroundColor Cyan
        } catch {
            Write-Error "Failed to start async operation: $($_.Exception.Message)"
            if ($onComplete) {
                & $onComplete @{ Success = $false; Error = $_.Exception.Message }
            }
        }
    }
}

# SECTION 1: SERVICE CLASSES
class LoggingService {
    [string]$LogLevel = 'INFO'
    [bool]$DebugMode = $true  # Default ON as requested
    [System.Collections.Generic.List[string]]$LogHistory

    LoggingService() {
        $this.LogHistory = [System.Collections.Generic.List[string]]::new()
    }

    hidden [int] GetLevelValue([string]$level) {
        switch ($level.ToUpper()) {
            'DEBUG'   { return 0 }
            'VERBOSE' { return 1 }
            'INFO'    { return 2 }
            'WARN'    { return 3 }
            'ERROR'   { return 4 }
            'SECURITY'{ return 5 }
            default   { return 2 }
        }
        return 2 # Fallback return
    }

    hidden WriteLog([string]$level, [string]$message) {
        if ($this.GetLevelValue($level) -ge $this.GetLevelValue($this.LogLevel)) {
            $logEntry = "[{0}] [{1}] {2}" -f (Get-Date -Format 'yyyy-MM-dd HH:mm:ss'), $level.ToUpper(), $message
            Write-Host $logEntry
            $this.LogHistory.Add($logEntry)
            
            # Keep only last 1000 entries to prevent memory issues
            if ($this.LogHistory.Count -gt 1000) {
                $this.LogHistory.RemoveAt(0)
            }
        }
    }

    [void] Info([string]$Message) { $this.WriteLog('INFO', $Message) }
    [void] Warn([string]$Message) { $this.WriteLog('WARN', $Message) }
    [void] Error([string]$Message, [System.Exception]$Exception) { 
        $errMsg = $Message
        if ($Exception) { $errMsg += " | Exception: $($Exception.Message)" }
        $this.WriteLog('ERROR', $errMsg)
    }
    [void] Verbose([string]$Message) { $this.WriteLog('VERBOSE', $Message) }
    [void] SecurityAudit([string]$action, [string]$result) { $this.WriteLog('SECURITY', "Action: $action, Result: $result") }
    
    # Enhanced Debug method with operation context and mode control
    [void] Debug([string]$operation, [string]$message) { 
        if ($this.DebugMode) {
            $this.WriteLog('DEBUG', "[$operation] $message")
        }
    }
    
    # Backward compatibility - single parameter debug
    [void] Debug([string]$message) { 
        if ($this.DebugMode) {
            $this.WriteLog('DEBUG', $message)
        }
    }
    
    # Debug mode control
    [void] SetDebugMode([bool]$enabled) {
        $this.DebugMode = $enabled
        $this.Info("Debug mode " + $(if ($enabled) { "enabled" } else { "disabled" }))
    }
    
    [bool] GetDebugMode() {
        return $this.DebugMode
    }
    
    [string[]] GetRecentLogs([int]$count = 50) {
        $start = [Math]::Max(0, $this.LogHistory.Count - $count)
        $end = $this.LogHistory.Count - $start
        return $this.LogHistory.GetRange($start, $end).ToArray()
    }
}

class ConfigurationService {
    [string[]] GetOUList() {
        $ouMappings = Get-AppConfig -Path "ActiveDirectory.OUMappings"
        return $ouMappings.Values
    }
    
    [string] GetOUForLocation([string]$officeLocation) {
        $ouMappings = Get-AppConfig -Path "ActiveDirectory.OUMappings"
        if ($ouMappings.ContainsKey($officeLocation)) {
            return $ouMappings[$officeLocation]
        }
        return Get-AppConfig -Path "ActiveDirectory.DefaultOU"
    }

    [hashtable] GetJiraFieldMappings() {
        return Get-AppConfig -Path "Jira.FieldMappings"
    }

    [hashtable] GetValidationRules() {
        return Get-AppConfig -Path "ActiveDirectory.ValidationRules"
    }

    [hashtable] ValidateField([string]$fieldName, [string]$value) {
        $rules = $this.GetValidationRules()
        if (-not $rules.ContainsKey($fieldName)) {
            return @{ IsValid = $true; ErrorMessage = "" }
        }

        $rule = $rules[$fieldName]
        
        # Check required
        if ($rule.Required -and [string]::IsNullOrWhiteSpace($value)) {
            return @{ IsValid = $false; ErrorMessage = "$fieldName is required" }
        }

        $trimmedValue = $value.Trim()

        # Check length and pattern
        if ($rule.ContainsKey('MinLength') -and $trimmedValue.Length -lt $rule.MinLength) {
            return @{ IsValid = $false; ErrorMessage = "$fieldName must be at least $($rule.MinLength) characters" }
        }

        if ($rule.ContainsKey('Pattern') -and $trimmedValue -notmatch $rule.Pattern) {
            return @{ IsValid = $false; ErrorMessage = $rule.ErrorMessage }
        }

        return @{ IsValid = $true; ErrorMessage = "" }
    }

    [int] GetSessionTimeoutMinutes() { 
        return Get-AppConfig -Path "UI.SessionTimeoutMinutes"
    }
    
    [string] GetWindowTitle() { 
        return Get-AppConfig -Path "UI.WindowTitle"
    }
    
    [string] GetDefaultJiraUrl() { 
        return Get-AppConfig -Path "Jira.DefaultUrl"
    }
}

# JiraRestClient - Unified REST API client for all Jira operations
class JiraRestClient {
    $log
    
    JiraRestClient($loggingService) {
        $this.log = $loggingService
    }
    
    # Unified REST API method with operation-specific debug context
    [hashtable] InvokeJiraAPI([hashtable]$authInfo, [string]$endpoint, [string]$method, [object]$body, [string]$operation) {
        return [ErrorHelper]::SafeExecute({
            $this.log.Debug("REST-$operation", "Starting $method request to $endpoint")
            
            # Create auth headers using ErrorHelper
            $headersResult = $this.CreateAuthHeaders($authInfo)
            if (-not $headersResult.Success) {
                throw "Failed to create auth headers: $($headersResult.ErrorMessage)"
            }
            
            $headers = $headersResult.Data
            $fullUrl = "$($authInfo.Url)$endpoint"
            
            $this.log.Debug("REST-$operation", "URL: $fullUrl")
            $this.log.Debug("REST-$operation", "Method: $method")
            
            # Prepare request parameters
            $requestParams = @{
                Uri = $fullUrl
                Headers = $headers
                Method = $method
                ErrorAction = 'Stop'
            }
            
            # Add body if provided (for POST requests)
            if ($body) {
                # Add Content-Type header when we have a body
                $headers["Content-Type"] = "application/json"
                
                if ($body -is [string]) {
                    $requestParams['Body'] = $body
                    $this.log.Debug("REST-$operation", "Body length: $($body.Length) chars")
                } else {
                    $jsonBody = $body | ConvertTo-Json -Depth 10
                    $requestParams['Body'] = $jsonBody
                    $this.log.Debug("REST-$operation", "JSON body length: $($jsonBody.Length) chars")
                }
            }
            
            # Execute the REST API call
            $this.log.Debug("REST-$operation", "Executing REST API call...")
            $response = Invoke-RestMethod @requestParams
            
            $this.log.Debug("REST-$operation", "REST API call completed successfully")
            
            # Return the response
            return $response
            
        }, "REST-$operation", $this.log)
    }
    
    # Helper method to create auth headers (same as JiraService but isolated)
    hidden [hashtable] CreateAuthHeaders([hashtable]$authInfo) {
        return [ErrorHelper]::SafeExecute({
            $apiToken = [Runtime.InteropServices.Marshal]::PtrToStringAuto([Runtime.InteropServices.Marshal]::SecureStringToBSTR($authInfo.ApiToken))
            $base64AuthInfo = [Convert]::ToBase64String([Text.Encoding]::ASCII.GetBytes("$($authInfo.Username):$apiToken"))
            return @{
                "Authorization" = "Basic $base64AuthInfo"
                "Accept" = "application/json"
            }
        }, "CreateAuthHeaders", $this.log)
    }
}

class JiraService {
    $log
    $restClient
    [bool]$UseJiraPS = $false
    
    JiraService($loggingService) { 
        $this.log = $loggingService 
        $this.restClient = [JiraRestClient]::new($loggingService)
        # Check if JiraPS module is available
        try {
            $jiraPSModule = Get-Module -ListAvailable -Name JiraPS
            if ($jiraPSModule) {
                $this.UseJiraPS = $true
                $this.log.Info("JiraPS module detected, will use JiraPS commands")
            } else {
                $this.log.Info("JiraPS module not found, using mock data")
            }
        } catch {
            $this.log.Warn("Could not check for JiraPS module: $($_.Exception.Message)")
        }
    }
    
    [hashtable] TestAuthentication([hashtable]$authInfo) {
        return [ConnectionResilienceManager]::ExecuteWithRetry({
            [PerformanceMonitor]::StartOperation("JiraAuthentication")
            [AdvancedLogger]::Debug("Authentication", "Testing Jira authentication for $($authInfo.Username)")
            
            # Check cache first for recent authentication
            $cacheKey = "auth_$($authInfo.Username)_$($authInfo.Url)"
            $cachedAuth = [CacheManager]::Get($cacheKey)
            if ($cachedAuth) {
                [AdvancedLogger]::Debug("Authentication", "Using cached authentication result")
                [PerformanceMonitor]::EndOperation("JiraAuthentication")
                return $cachedAuth
            }
            
            # Use simple direct REST API approach (proven to work with SSO)
            [AdvancedLogger]::Debug("Authentication", "Using direct REST API for authentication")
            
            $jiraUrl = $authInfo.Url
            $username = $authInfo.Username
            $apiTokenSecure = $authInfo.ApiToken
            $apiToken = [Runtime.InteropServices.Marshal]::PtrToStringAuto([Runtime.InteropServices.Marshal]::SecureStringToBSTR($apiTokenSecure))
            
            # Create basic auth header (same approach as FIXED version)
            $base64AuthInfo = [Convert]::ToBase64String([Text.Encoding]::ASCII.GetBytes("$username`:$apiToken"))
            $headers = @{
                "Authorization" = "Basic $base64AuthInfo"
                "Accept" = "application/json"
            }
            
            # Test authentication with /myself endpoint (same as FIXED version)
            $response = Invoke-RestMethod -Uri "$jiraUrl/rest/api/3/myself" -Headers $headers -Method Get -ErrorAction Stop
            
            if ($response -and $response.accountId) {
                $this.log.Debug("Authentication", "Success! User: $($response.displayName), Email: $($response.emailAddress)")
                $result = @{
                    Success = $true
                    User = $response
                    DisplayName = $response.displayName
                    Email = $response.emailAddress
                    AccountId = $response.accountId
                }
                
                # Cache successful authentication for 5 minutes
                [CacheManager]::Set($cacheKey, $result, 300)
                [PerformanceMonitor]::EndOperation("JiraAuthentication")
                return $result
            } else {
                [PerformanceMonitor]::EndOperation("JiraAuthentication")
                throw "Invalid response from Jira authentication endpoint"
            }
        }, "JiraAuthentication", $this.log)
    }
    
    [hashtable] AuthenticateAndStore([string]$url, [string]$username, [string]$apiToken) {
        return [ErrorHelper]::SafeExecute({
            $this.log.Debug("Authentication", "Full authentication flow for $username")
            
            # Create secure string from API token
            $secureApiToken = ConvertTo-SecureString $apiToken -AsPlainText -Force
            
            # Create auth info structure
            $authInfo = @{
                Url = $url
                Username = $username
                ApiToken = $secureApiToken
            }
            
            # Test the authentication
            $testResult = $this.TestAuthentication($authInfo)
            if (-not $testResult.Success) {
                throw "Authentication test failed: $($testResult.ErrorMessage)"
            }
            
            return @{
                AuthInfo = $authInfo
                UserDetails = $testResult.Data
            }
        }, "AuthenticateAndStore", $this.log)
    }

    [hashtable] GetTicketDetails([string]$ticketId, [hashtable]$authInfo) {
        return [ErrorHelper]::SafeExecute({
            [PerformanceMonitor]::StartOperation("TicketFetch")
            $this.log.Info("Fetching ticket details for $ticketId")
            
            # Check cache first for recent ticket data
            $cacheKey = "ticket_$ticketId"
            $cachedTicket = [CacheManager]::Get($cacheKey)
            if ($cachedTicket) {
                $this.log.Debug("TicketFetch", "Using cached ticket data for $ticketId")
                [PerformanceMonitor]::EndOperation("TicketFetch")
                return $cachedTicket
            }
            
            if ($authInfo -and $authInfo.ApiToken) {
                $result = $this.GetTicketUsingRestAPI($ticketId, $authInfo)
            } elseif ($this.UseJiraPS) {
                $result = $this.GetTicketUsingJiraPS($ticketId)
            } else {
                $result = $this.GetTicketUsingMockData($ticketId)
            }
            
            # If the sub-method already returned error format, extract data or re-throw
            if ($result.Success -eq $false) {
                [PerformanceMonitor]::EndOperation("TicketFetch")
                throw $result.ErrorMessage
            }
            
            # Cache successful ticket data for 2 minutes (tickets change more frequently)
            [CacheManager]::Set($cacheKey, $result.Data, 120)
            [PerformanceMonitor]::EndOperation("TicketFetch")
            return $result.Data
        }, "TicketFetch", $this.log)
    }
    
    [hashtable] GetTicketUsingRestAPI([string]$ticketId, [hashtable]$authInfo) {
        return [ErrorHelper]::SafeExecute({
            $this.log.Info("Using REST API to fetch ticket $ticketId")
            
            # Use unified REST client for ticket fetch
            $result = $this.restClient.InvokeJiraAPI($authInfo, "/rest/api/3/issue/$ticketId", "Get", $null, "TicketFetch")
            
            if (-not $result.Success) {
                throw "Ticket fetch REST call failed: $($result.ErrorMessage)"
            }
            
            $response = $result.Data
            if ($response) {
                $this.log.Debug("REST-TicketFetch", "Successfully fetched issue: $($response.key)")
                $this.log.Debug("REST-TicketFetch", "Summary: $($response.fields.summary)")
                
                # Convert to our format
                $ticketData = @{
                    "summary" = $response.fields.summary
                    "description" = $response.fields.description
                }
                
                # Extract custom fields
                $customFieldsFound = 0
                $customFieldProperties = $response.fields.PSObject.Properties | Where-Object { $_.Name -like "customfield_*" }
                
                foreach ($prop in $customFieldProperties) {
                    $value = $prop.Value
                    if ($value -and $value -ne $null) {
                        # Handle different value types
                        $displayValue = if ($value -is [string]) { 
                            $value 
                        } elseif ($value -is [System.Collections.Hashtable] -or $value.PSObject) {
                            if ($value.value) { $value.value }
                            elseif ($value.displayName) { $value.displayName }
                            elseif ($value.name) { $value.name }
                            else { $value.ToString() }
                        } else { 
                            $value.ToString() 
                        }
                        
                        $ticketData[$prop.Name] = $displayValue
                        $customFieldsFound++
                        $this.log.Debug("REST-TicketFetch", "Custom field $($prop.Name) = '$displayValue'")
                    }
                }
                
                $this.log.Debug("REST-TicketFetch", "Found $customFieldsFound custom fields via REST API")
                return $ticketData
            } else {
                throw "No data returned from Jira API"
            }
        }, "REST-TicketFetch", $this.log)
    }
    
    [hashtable] GetTicketUsingJiraPS([string]$ticketId) {
        try {
            $this.log.Info("Using JiraPS to fetch ticket $ticketId")
            
            # Import JiraPS module
            Import-Module JiraPS -Force
            
            # Check if we have an active session
            $session = Get-JiraSession -ErrorAction SilentlyContinue
            if (-not $session) {
                Write-Host "DEBUG: No active JiraPS session found. Authentication required." -ForegroundColor Yellow
                $this.log.Warn("No active JiraPS session. Please authenticate first with: Set-JiraConfigServer and New-JiraSession")
                throw "No active JiraPS session. Please authenticate first."
            }
            
            Write-Host "DEBUG: Active JiraPS session found for server: $($session.Server)" -ForegroundColor Green
            
            # Get the Jira issue using JiraPS
            Write-Host "DEBUG: Calling Get-JiraIssue for $ticketId" -ForegroundColor Cyan
            $issue = Get-JiraIssue -Key $ticketId -ErrorAction Stop
            
            if (-not $issue) {
                throw "No issue returned from Get-JiraIssue"
            }
            
            Write-Host "DEBUG: Successfully retrieved issue object" -ForegroundColor Green
            
            # Convert to our standard format
            $ticketData = @{
                summary = if ($issue.Summary) { $issue.Summary } else { "" }
                description = if ($issue.Description) { $issue.Description } else { "" }
            }
            
            Write-Host "DEBUG: Issue object retrieved successfully" -ForegroundColor Cyan
            
            # Try multiple ways to get custom fields
            $customFieldsFound = 0
            
            # Method 1: Check CustomFields property
            if ($issue.PSObject.Properties['CustomFields']) {
                Write-Host "DEBUG: CustomFields property exists" -ForegroundColor Cyan
                $customFields = $issue.CustomFields
                if ($customFields) {
                    Write-Host "DEBUG: CustomFields has $($customFields.Count) items" -ForegroundColor Cyan
                    foreach ($field in $customFields) {
                        if ($field -and $field.PSObject.Properties['Id'] -and $field.PSObject.Properties['Value']) {
                            Write-Host "DEBUG: CustomField - Id: $($field.Id), Value: $($field.Value)" -ForegroundColor Yellow
                            $ticketData[$field.Id] = $field.Value
                            $customFieldsFound++
                        }
                    }
                }
            } else {
                Write-Host "DEBUG: No CustomFields property found" -ForegroundColor Red
            }
            
            # Method 2: Check direct properties on issue object
            $customFieldProperties = $issue.PSObject.Properties | Where-Object { $_.Name -like "customfield_*" }
            Write-Host "DEBUG: Found $($customFieldProperties.Count) direct custom field properties" -ForegroundColor Cyan
            foreach ($prop in $customFieldProperties) {
                if ($prop.Value) {
                    Write-Host "DEBUG: Direct CustomField - Name: $($prop.Name), Value: $($prop.Value)" -ForegroundColor Yellow
                    $ticketData[$prop.Name] = $prop.Value
                    $customFieldsFound++
                }
            }
            
            # Method 3: Check Fields property
            if ($issue.PSObject.Properties['Fields'] -and $issue.Fields) {
                Write-Host "DEBUG: Fields property exists" -ForegroundColor Cyan
                $fieldsCustomFields = $issue.Fields.PSObject.Properties | Where-Object { $_.Name -like "customfield_*" }
                Write-Host "DEBUG: Found $($fieldsCustomFields.Count) custom fields in Fields property" -ForegroundColor Cyan
                foreach ($prop in $fieldsCustomFields) {
                    if ($prop.Value) {
                        Write-Host "DEBUG: Fields CustomField - Name: $($prop.Name), Value: $($prop.Value)" -ForegroundColor Yellow
                        $ticketData[$prop.Name] = $prop.Value
                        $customFieldsFound++
                    }
                }
            }
            
            # Method 4: Try accessing known custom fields directly
            $knownCustomFields = Get-AppConfig -Path "Jira.KnownCustomFields"
            foreach ($fieldId in $knownCustomFields) {
                try {
                    $value = $null
                    if ($issue.PSObject.Properties[$fieldId] -and $issue.PSObject.Properties[$fieldId].Value) {
                        $value = $issue.PSObject.Properties[$fieldId].Value
                    } elseif ($issue.Fields -and $issue.Fields.PSObject.Properties[$fieldId] -and $issue.Fields.PSObject.Properties[$fieldId].Value) {
                        $value = $issue.Fields.PSObject.Properties[$fieldId].Value
                    }
                    
                    if ($value) {
                        Write-Host "DEBUG: Direct access CustomField - $fieldId = $value" -ForegroundColor Green
                        $ticketData[$fieldId] = $value
                        $customFieldsFound++
                    }
                } catch {
                    # Continue silently
                }
            }
            
            # Method 5: Get comments and add them for fallback parsing
            try {
                Write-Host "DEBUG: Attempting to fetch comments..." -ForegroundColor Cyan
                $comments = Get-JiraComment -Issue $ticketId -ErrorAction Stop
                if ($comments) {
                    $allComments = ($comments | ForEach-Object { $_.Body }) -join "`n"
                    $ticketData["comments"] = $allComments
                    Write-Host "DEBUG: Added $($comments.Count) comments for fallback parsing" -ForegroundColor Cyan
                }
            } catch {
                Write-Host "DEBUG: Could not fetch comments: $($_.Exception.Message)" -ForegroundColor Yellow
            }
            
            Write-Host "DEBUG: Total custom fields found: $customFieldsFound" -ForegroundColor Green
            Write-Host "DEBUG: Final ticketData keys: $($ticketData.Keys -join ', ')" -ForegroundColor Green
            
            $this.log.Info("Successfully fetched ticket $ticketId using JiraPS")
            return @{ Success = $true; Data = $ticketData }
            
        } catch {
            $this.log.Error("JiraPS ticket fetch failed for $ticketId", $_.Exception)
            Write-Host "DEBUG: Exception details: $($_.Exception.Message)" -ForegroundColor Red
            Write-Host "DEBUG: Exception type: $($_.Exception.GetType().FullName)" -ForegroundColor Red
            if ($_.Exception.InnerException) {
                Write-Host "DEBUG: Inner exception: $($_.Exception.InnerException.Message)" -ForegroundColor Red
            }
            $this.log.Info("Falling back to mock data")
            return $this.GetTicketUsingMockData($ticketId)
        }
    }
    
    [hashtable] GetTicketUsingMockData([string]$ticketId) {
        $this.log.Info("Using mock data for ticket $ticketId")
        Start-Sleep -Milliseconds 800
        
        # Mock data based on real JERAGM Jira ticket structure
        # Using actual field IDs from your Jira system
        $mockData = @{ 
            summary = "Onboarding Request - Test User ($ticketId)"
            description = "New Joiner Name: Test User`nJob Title: Test Analyst`nDepartment: IT`nModel Account: testmodel"
            # Core onboarding fields (these are the exact IDs from your Jira)
            customfield_10304 = "Test"           # First Name
            customfield_10305 = "User"           # Last Name  
            customfield_10238 = "Test Analyst"   # Job Title
            customfield_10120 = "IT"             # Department
            customfield_10343 = "testmodel"      # Model Account
            customfield_10115 = "Singapore"      # Office Location
            customfield_10342 = "JERAGM Employee" # Employee Type
            customfield_10344 = "$(Get-Date -Format 'ddd, dd MMM yyyy HH:mm:ss +0000')" # Effective Date
        }
        return [ErrorHelper]::Success($mockData, "MockTicketFetch")
    }

    [hashtable] PostComment([string]$ticketId, [string]$comment, [hashtable]$authInfo) {
        try {
            $this.log.Info("Posting comment to ticket $ticketId")
            
            if ($authInfo -and $authInfo.ApiToken) {
                return $this.PostCommentUsingRestAPI($ticketId, $comment, $authInfo)
            } elseif ($this.UseJiraPS) {
                return $this.PostCommentUsingJiraPS($ticketId, $comment)
            } else {
                return $this.PostCommentUsingMock($ticketId, $comment)
            }
        } catch {
            $this.log.Error("Failed to post comment to ticket $ticketId", $_.Exception)
            return @{ Success = $false; ErrorMessage = $_.Exception.Message }
        }
    }
    
    [hashtable] PostCommentUsingRestAPI([string]$ticketId, [string]$comment, [hashtable]$authInfo) {
        return [ErrorHelper]::SafeExecute({
            $this.log.Info("Using REST API to post comment to $ticketId")
            
            # Create comment body structure
            $commentBody = @{
                body = @{
                    type = "doc"
                    version = 1
                    content = @(
                        @{
                            type = "paragraph"
                            content = @(
                                @{
                                    type = "text"
                                    text = $comment
                                }
                            )
                        }
                    )
                }
            }
            
            $this.log.Debug("REST-CommentPost", "Comment preview: $($comment.Substring(0, [Math]::Min(50, $comment.Length)))...")
            
            # Use unified REST client for comment posting
            $result = $this.restClient.InvokeJiraAPI($authInfo, "/rest/api/3/issue/$ticketId/comment", "Post", $commentBody, "CommentPost")
            
            if (-not $result.Success) {
                throw "Comment post REST call failed: $($result.ErrorMessage)"
            }
            
            $response = $result.Data
            if ($response -and $response.id) {
                $this.log.Debug("REST-CommentPost", "Comment posted successfully with ID: $($response.id)")
                return @{ CommentId = $response.id }
            } else {
                throw "Invalid response from Jira comment API"
            }
        }, "REST-CommentPost", $this.log)
    }
    
    [hashtable] PostCommentUsingJiraPS([string]$ticketId, [string]$comment) {
        try {
            $this.log.Info("Using JiraPS to post comment to $ticketId")
            
            # Add comment using JiraPS
            Add-JiraComment -Issue $ticketId -Comment $comment
            
            $this.log.Info("Successfully posted comment to $ticketId using JiraPS")
            return @{ Success = $true }
            
        } catch {
            $this.log.Error("JiraPS comment posting failed for $ticketId", $_.Exception)
            $this.log.Info("Falling back to mock comment posting")
            return $this.PostCommentUsingMock($ticketId, $comment)
        }
    }
    
    [hashtable] PostCommentUsingMock([string]$ticketId, [string]$comment) {
        $this.log.Info("MOCK: Comment posted to ticket $ticketId")
        Start-Sleep -Milliseconds 300
        return @{ Success = $true }
    }
}

class ActiveDirectoryService {
    [bool]$Simulate
    $log

    ActiveDirectoryService([bool]$simulateMode, $loggingService) { 
        $this.Simulate = $simulateMode
        $this.log = $loggingService
    }

    [hashtable] DoesUserExist([string]$samAccountName) {
        return [ErrorHelper]::SafeExecute({
            $this.log.Info("Checking for existence of user $samAccountName")
            # MOCK IMPLEMENTATION - Replace with actual Get-ADUser call
            # $user = Get-ADUser -Identity $samAccountName -ErrorAction SilentlyContinue
            # return $user -ne $null
            return $false  # Mock: user doesn't exist
        }, "UserExistenceCheck", $this.log)
    }

    [hashtable] CreateUser([hashtable]$userDetails) {
        if ($this.Simulate) {
            $this.log.Info("SIMULATION: Creating user $($userDetails.SamAccountName)")
            Start-Sleep -Milliseconds 500
            return [ErrorHelper]::Success("SIMULATION: User created successfully.", "UserCreation-Simulation")
        }
        
        return [ErrorHelper]::SafeExecute({
            $this.log.Info("Creating AD User $($userDetails.SamAccountName)")
            # New-ADUser @userDetails
            # Mock success for now
            return "User $($userDetails.SamAccountName) created successfully."
        }, "UserCreation", $this.log)
    }
}

# SECTION 2: STATE MANAGEMENT CLASS
class AppState {
    # Direct properties - simpler approach
    [string]$StatusMessage = "Ready."
    [bool]$IsBusy = $false
    [string[]]$ValidationErrors = @()
    [int]$CurrentStepIndex = 0
    [hashtable]$StepValidationStatus = @{ 0 = $false; 1 = $false; 2 = $false; 3 = $false }
    [string]$JiraUrlInput = (Get-AppConfig -Path "Jira.DefaultUrl")
    [string]$JiraUsernameInput = ""
    [securestring]$ApiTokenInput = $null
    [string]$TicketIdInput = ""
    [hashtable]$CurrentOnboardingData = @{}
    [string]$SelectedOU = ""
    [bool]$IsJiraAuthenticated = $false
    [string]$AuthenticationStatus = "Not authenticated"
    
    # Property change event handlers
    hidden [System.Collections.Generic.List[scriptblock]]$PropertyChangedHandlers = [System.Collections.Generic.List[scriptblock]]::new()
    
    [void] AddPropertyChangedHandler([scriptblock]$handler) {
        $this.PropertyChangedHandlers.Add($handler)
    }
    
    [void] NotifyPropertyChanged([string]$propertyName) {
        Write-Host "DEBUG: [PROPERTY CHANGE] NotifyPropertyChanged called for: '$propertyName'" -ForegroundColor DarkMagenta
        foreach ($handler in $this.PropertyChangedHandlers) {
            try {
                Write-Host "DEBUG: [PROPERTY CHANGE] Executing handler for: '$propertyName'" -ForegroundColor DarkMagenta
                & $handler $propertyName
                Write-Host "DEBUG: [PROPERTY CHANGE] Handler completed for: '$propertyName'" -ForegroundColor DarkMagenta
            } catch {
                Write-Warning "Property change handler error for '$propertyName': $($_.Exception.Message)"
            }
        }
    }
    
    # Helper methods to trigger property change notifications
    [void] SetStatusMessage([string]$value) { 
        Write-Host "DEBUG: [STATE] Setting StatusMessage: '$value'" -ForegroundColor Magenta
        $this.StatusMessage = $value
        $this.NotifyPropertyChanged('StatusMessage')
    }
    
    [void] SetIsBusy([bool]$value) { 
        Write-Host "DEBUG: [STATE] Setting IsBusy: $value" -ForegroundColor Magenta
        $this.IsBusy = $value
        $this.NotifyPropertyChanged('IsBusy')
    }
    
    [void] SetCurrentStepIndex([int]$value) { 
        Write-Host "DEBUG: [STATE] Setting CurrentStepIndex: $value" -ForegroundColor Magenta
        $this.CurrentStepIndex = $value
        $this.NotifyPropertyChanged('CurrentStepIndex')
    }
    
    [void] SetCurrentOnboardingData([hashtable]$value) { 
        Write-Host "DEBUG: [STATE] Setting CurrentOnboardingData with $($value.Keys.Count) keys: $($value.Keys -join ', ')" -ForegroundColor Magenta
        $this.CurrentOnboardingData = $value
        $this.NotifyPropertyChanged('CurrentOnboardingData')
        
        # Trigger auto-save if enabled
        $autoSaveEnabled = [UserPreferencesManager]::GetPreference("AutoSave")
        if ($autoSaveEnabled) {
            [SessionManager]::AutoSave(@{
                CurrentOnboardingData = $this.CurrentOnboardingData
                CurrentStepIndex = $this.CurrentStepIndex
                JiraUrlInput = $this.JiraUrlInput
                JiraUsernameInput = $this.JiraUsernameInput
                TicketIdInput = $this.TicketIdInput
                IsJiraAuthenticated = $this.IsJiraAuthenticated
                Timestamp = Get-Date
            })
        }
    }
    
    [void] SetSelectedOU([string]$value) { 
        Write-Host "DEBUG: [STATE] Setting SelectedOU: '$value'" -ForegroundColor Magenta
        $this.SelectedOU = $value
        $this.NotifyPropertyChanged('SelectedOU')
    }
    
    # Additional setter methods for input fields
    [void] SetJiraUrlInput([string]$value) { 
        Write-Host "DEBUG: [STATE] Setting JiraUrlInput: '$value'" -ForegroundColor Magenta
        $this.JiraUrlInput = $value
        $this.NotifyPropertyChanged('JiraUrlInput')
    }
    
    [void] SetJiraUsernameInput([string]$value) { 
        Write-Host "DEBUG: [STATE] Setting JiraUsernameInput: '$value'" -ForegroundColor Magenta
        $this.JiraUsernameInput = $value
        $this.NotifyPropertyChanged('JiraUsernameInput')
    }
    
    [void] SetTicketIdInput([string]$value) { 
        Write-Host "DEBUG: [STATE] Setting TicketIdInput: '$value'" -ForegroundColor Magenta
        $this.TicketIdInput = $value
        $this.NotifyPropertyChanged('TicketIdInput')
    }
    
    [void] SetIsJiraAuthenticated([bool]$value) { 
        Write-Host "DEBUG: [STATE] Setting IsJiraAuthenticated: $value" -ForegroundColor Magenta
        $this.IsJiraAuthenticated = $value
        $this.NotifyPropertyChanged('IsJiraAuthenticated')
    }
    
    [void] SetAuthenticationStatus([string]$value) { 
        Write-Host "DEBUG: [STATE] Setting AuthenticationStatus: '$value'" -ForegroundColor Magenta
        $this.AuthenticationStatus = $value
        $this.NotifyPropertyChanged('AuthenticationStatus')
    }
}

# SECTION 3: VIEWMODEL CLASS
class WizardViewModel {
    $log
    $config
    $jira
    $ad
    $state
    $view

    WizardViewModel($loggingService, $configService, $jiraService, $adService, $appState) {
        $this.log = $loggingService
        $this.config = $configService
        $this.jira = $jiraService
        $this.ad = $adService
        $this.state = $appState
    }

    # This method is called by the View to link them
    RegisterView($view) {
        $this.view = $view
    }

    # --- Private Helper Methods ---
    hidden RunAsync($scriptBlock, $onSuccess, $onFailure, $argumentList = @()) {
        $this.log.Debug("RunAsync", "Called, IsBusy: $($this.state.IsBusy)")
        if ($this.state.IsBusy) { 
            $this.log.Debug("RunAsync", "Already busy, returning")
            return 
        }
        $this.state.SetIsBusy($true)
        $this.log.Debug("RunAsync", "Set IsBusy to true")

        try {
            $this.log.Debug("RunAsync", "About to call AsyncOperationHelper.RunAsync")
            # Use the AsyncOperationHelper for improved threading
            $capturedSuccess = $onSuccess
            $capturedFailure = $onFailure
            $capturedState = $this.state
            
            [AsyncOperationHelper]::RunAsync(
                $scriptBlock,
                {
                    param($result)
                    try {
                        $this.log.Debug("RunAsync", "Completion handler called")
                        $capturedState.SetIsBusy($false)
                        if ($capturedSuccess) {
                            $this.log.Debug("RunAsync", "About to call success handler")
                            & $capturedSuccess $result
                        }
                    } catch {
                        Write-Warning "Success handler failed: $($_.Exception.Message)"
                        $capturedState.SetIsBusy($false)
                    }
                }.GetNewClosure(),
                $this.view.window.Dispatcher,
                $argumentList
            )
            $this.log.Debug("RunAsync", "AsyncOperationHelper.RunAsync call completed")
        } catch {
            $this.log.Debug("RunAsync", "Exception in RunAsync: $($_.Exception.Message)")
            $this.state.SetIsBusy($false)
            if ($onFailure) {
                & $onFailure $_.Exception.Message
            }
        }
    }

    hidden [hashtable] ParseTicketData($jiraTicket) {
        $parsedData = @{}
        $mappings = $this.config.GetJiraFieldMappings()
        
        Write-Host "DEBUG: Parsing ticket data..." -ForegroundColor Cyan
        Write-Host "DEBUG: Available data keys: $($jiraTicket.Data.Keys -join ', ')" -ForegroundColor Cyan
        
        # First try to get data from custom fields
        foreach ($key in $mappings.Keys) {
            $fieldId = $mappings[$key].CustomFieldId
            Write-Host "DEBUG: Looking for $key in field $fieldId" -ForegroundColor Gray
            if ($jiraTicket.Data.ContainsKey($fieldId)) {
                $value = $jiraTicket.Data[$fieldId]
                Write-Host "DEBUG: Found $key = '$value'" -ForegroundColor Green
                $parsedData[$key] = $value
            } else {
                Write-Host "DEBUG: Field $fieldId not found for $key" -ForegroundColor Red
            }
        }
        
        # If we didn't get the required fields, try parsing from comments or description
        if ($parsedData.Count -lt 4) {
            Write-Host "DEBUG: Custom fields incomplete, trying comment parsing..." -ForegroundColor Yellow
            $parsedFromComments = $this.ParseFromComments($jiraTicket.Data)
            foreach ($key in $parsedFromComments.Keys) {
                if (-not $parsedData.ContainsKey($key)) {
                    $parsedData[$key] = $parsedFromComments[$key]
                    Write-Host "DEBUG: Added from comments: $key = '$($parsedFromComments[$key])'" -ForegroundColor Green
                }
            }
        }
        
        Write-Host "DEBUG: Final parsed data: $($parsedData.Keys -join ', ')" -ForegroundColor Cyan
        return $parsedData
    }
    
    hidden [hashtable] ParseFromComments($ticketData) {
        $parsedData = @{}
        
        try {
            # Look for structured data in description or comments
            $textToSearch = ""
            if ($ticketData.ContainsKey('description')) {
                $textToSearch += $ticketData.description + "`n"
            }
            if ($ticketData.ContainsKey('comments')) {
                $textToSearch += $ticketData.comments + "`n"
            }
            
            Write-Host "DEBUG: Searching in text content for structured data..." -ForegroundColor Yellow
            
            # Parse using regex patterns
            $patterns = @{
                FirstName = @("New Joiner Name:\s*([^\s]+)", "First Name:\s*([^\r\n]+)")
                LastName = @("New Joiner Name:\s*\S+\s+(\S+)", "Last Name:\s*([^\r\n]+)")
                JobTitle = @("Job Title:\s*([^\r\n]+)")
                Department = @("Department:\s*([^\r\n]+)")
                ModelAccount = @("Model Account:\s*([^\r\n]+)")
            }
            
            foreach ($field in $patterns.Keys) {
                foreach ($pattern in $patterns[$field]) {
                    if ($textToSearch -match $pattern) {
                        $value = $matches[1].Trim()
                        if (![string]::IsNullOrEmpty($value)) {
                            $parsedData[$field] = $value
                            Write-Host "DEBUG: Regex found $field = '$value'" -ForegroundColor Green
                            break
                        }
                    }
                }
            }
            
        } catch {
            Write-Host "DEBUG: Comment parsing failed: $($_.Exception.Message)" -ForegroundColor Red
        }
        
        return $parsedData
    }

    # --- Public Commands ---
    AuthenticateJira() {
        $this.log.Info("AuthenticateJira command executed.")
        $this.state.SetStatusMessage("Authenticating with Jira...")
        $this.state.SetIsBusy($true)
        
        try {
            # Get API token from the UI
            $apiToken = $this.view.controls.apiTokenBox.Password
            if ([string]::IsNullOrEmpty($apiToken)) {
                throw "API Token is required for authentication"
            }
            
            # Test authentication by making a simple API call (same as FIXED version)
            $jiraUrl = $this.state.JiraUrlInput
            $username = $this.state.JiraUsernameInput
            
            Write-Host "DEBUG: Testing Jira authentication..." -ForegroundColor Cyan
            Write-Host "DEBUG: URL: $jiraUrl" -ForegroundColor Gray
            Write-Host "DEBUG: Username: $username" -ForegroundColor Gray
            
            # Create basic auth header (exact same as FIXED version)
            $base64AuthInfo = [Convert]::ToBase64String([Text.Encoding]::ASCII.GetBytes("$username`:$apiToken"))
            $headers = @{
                "Authorization" = "Basic $base64AuthInfo"
                "Accept" = "application/json"
            }
            
            # Test authentication with /myself endpoint (exact same as FIXED version)
            $response = Invoke-RestMethod -Uri "$jiraUrl/rest/api/3/myself" -Headers $headers -Method Get -ErrorAction Stop
            
            if ($response -and $response.accountId) {
                $this.state.SetIsJiraAuthenticated($true)
                $this.state.SetAuthenticationStatus("✅ Authenticated as $($response.displayName)")
                $this.state.SetStatusMessage("Successfully authenticated with Jira!")
                $this.log.Info("Jira authentication successful for user: $($response.displayName)")
                Write-Host "DEBUG: Authentication successful!" -ForegroundColor Green
                Write-Host "DEBUG: User: $($response.displayName)" -ForegroundColor Green
                Write-Host "DEBUG: Email: $($response.emailAddress)" -ForegroundColor Green
                
                # Store credentials for future use
                $this.state.ApiTokenInput = ConvertTo-SecureString $apiToken -AsPlainText -Force
            } else {
                throw "Invalid response from Jira authentication"
            }
            
        } catch {
            $this.state.SetIsJiraAuthenticated($false)
            $this.state.SetAuthenticationStatus("❌ Authentication failed")
            $this.state.SetStatusMessage("Authentication failed: $($_.Exception.Message)")
            $this.log.Error("Jira authentication failed", $_.Exception)
            Write-Host "DEBUG: Authentication failed: $($_.Exception.Message)" -ForegroundColor Red
        } finally {
            $this.state.SetIsBusy($false)
            # Update UI authentication status
            $this.UpdateAuthenticationStatusDisplay()
        }
    }
    
    # Helper method to update authentication status in UI with JERA styling
    hidden UpdateAuthenticationStatusDisplay() {
        if ($this.view -and $this.view.controls.authStatusLabel) {
            $this.view.controls.authStatusLabel.Content = "Status: $($this.state.AuthenticationStatus)"
            if ($this.state.IsJiraAuthenticated) {
                # Use JERA success styling
                $this.view.controls.authStatusLabel.Foreground = (Get-AppConfig -Path "UI.Colors.Success")
                $this.view.controls.authStatusLabel.Background = (Get-AppConfig -Path "UI.Colors.StatusSuccess")
                # Enable fetch button when authenticated with JERA blue
                if ($this.view.controls.fetchButton) {
                    $this.view.controls.fetchButton.IsEnabled = $true
                    $this.view.controls.fetchButton.Background = (Get-AppConfig -Path "UI.Colors.JERABlue")  # JERA Blue
                    $this.view.controls.fetchButton.Foreground = "White"
                }
            } else {
                # Use JERA error styling
                $this.view.controls.authStatusLabel.Foreground = (Get-AppConfig -Path "UI.Colors.Error")
                $this.view.controls.authStatusLabel.Background = (Get-AppConfig -Path "UI.Colors.StatusError")
                # Disable fetch button when not authenticated
                if ($this.view.controls.fetchButton) {
                    $this.view.controls.fetchButton.IsEnabled = $false
                    $this.view.controls.fetchButton.Background = (Get-AppConfig -Path "UI.Colors.Disabled")
                    $this.view.controls.fetchButton.Foreground = (Get-AppConfig -Path "UI.Colors.TextMuted")
                }
            }
        }
    }
    
    FetchTicketDetails() {
        $this.log.Info("FetchTicketDetails command executed.")
        
        # Check if authenticated first
        if (-not $this.state.IsJiraAuthenticated) {
            $this.state.SetStatusMessage("Please authenticate with Jira first using the Login button.")
            $this.log.Warn("Attempted to fetch ticket without authentication")
            return
        }
        
        $this.state.SetStatusMessage("Fetching ticket $($this.state.TicketIdInput)...")
        $this.state.SetIsBusy($true)

        # Simplified approach - run synchronously for now to avoid threading issues
        try {
            $ticketId = $this.state.TicketIdInput
            Write-Host "DEBUG: FetchTicketDetails called with ticket ID: '$ticketId'" -ForegroundColor Magenta
            Write-Host "DEBUG: Ticket ID length: $($ticketId.Length)" -ForegroundColor Magenta
            Write-Host "DEBUG: Ticket ID is null or empty: $([string]::IsNullOrEmpty($ticketId))" -ForegroundColor Magenta
            
            $this.log.Debug("TicketFetch", "Starting ticket fetch for: $($this.state.TicketIdInput)")
            
            # Prepare authentication info
            $authInfo = $null
            if ($this.state.IsJiraAuthenticated -and $this.state.ApiTokenInput) {
                $authInfo = @{
                    Url = $this.state.JiraUrlInput
                    Username = $this.state.JiraUsernameInput
                    ApiToken = $this.state.ApiTokenInput
                }
            }
            
            $result = $this.jira.GetTicketDetails($ticketId, $authInfo)
            $this.log.Debug("TicketFetch", "Result - Success: $($result.Success)")
            
            if ($result.Success) {
                $this.state.SetStatusMessage("Ticket fetched successfully.")
                $parsedData = $this.ParseTicketData($result)
                $this.log.Debug("TicketFetch", "Parsed data keys: $($parsedData.Keys -join ', ')")
                $this.state.SetCurrentOnboardingData($parsedData)
                $this.state.StepValidationStatus[0] = $true
                $this.log.Debug("TicketFetch", "Step 0 validation set to TRUE")
            } else {
                $this.state.SetStatusMessage("Error: $($result.ErrorMessage)")
                $this.state.StepValidationStatus[0] = $false
                $this.log.Debug("TicketFetch", "Step 0 validation set to FALSE due to error")
            }
        } catch {
            $this.log.Debug("TicketFetch", "Exception in FetchTicketDetails: $($_.Exception.Message)")
            $this.state.SetStatusMessage("Error: $($_.Exception.Message)")
            $this.state.StepValidationStatus[0] = $false
        } finally {
            $this.state.SetIsBusy($false)
        }
    }

    ValidateUserDetails() {
        $this.log.Info("ValidateUserDetails command executed.")
        $this.state.SetStatusMessage("Validating user details...")
        
        $data = $this.state.CurrentOnboardingData
        $errors = @()
        
        # Ensure we have the required data
        if ($null -eq $data -or $data.Count -eq 0) {
            $this.state.SetStatusMessage("No user data found. Please fetch ticket details first.")
            return
        }
        
        # Validate first name
        if ($data.ContainsKey('FirstName')) {
            $fnValidation = $this.config.ValidateField('FirstName', $data.FirstName)
            if (-not $fnValidation.IsValid) { $errors += $fnValidation.ErrorMessage }
        } else {
            $errors += "First name is required"
        }
        
        # Validate last name
        if ($data.ContainsKey('LastName')) {
            $lnValidation = $this.config.ValidateField('LastName', $data.LastName)
            if (-not $lnValidation.IsValid) { $errors += $lnValidation.ErrorMessage }
        } else {
            $errors += "Last name is required"
        }
        
        # Check if user already exists (if we have both names)
        if ($data.ContainsKey('FirstName') -and $data.ContainsKey('LastName')) {
            $suggestedUsername = $this.GenerateUsername($data.FirstName, $data.LastName)
            $userExistsResult = $this.ad.DoesUserExist($suggestedUsername)
            
            if ($userExistsResult.Success -and $userExistsResult.Exists) {
                $errors += "User with username '$suggestedUsername' already exists"
            }
        }
        
        if ($errors.Count -eq 0) {
            $this.state.SetStatusMessage("Validation successful. Proceed to next step.")
            $this.state.StepValidationStatus[1] = $true
        } else {
            $this.state.SetStatusMessage("Validation failed: $($errors -join '; ')")
            $this.state.ValidationErrors = $errors
            $this.state.StepValidationStatus[1] = $false
        }
    }

    CreateUserAccount() {
        $this.log.Info("CreateUserAccount command executed.")
        $this.state.SetStatusMessage("Creating user account...")
        $this.state.SetIsBusy($true)
        
        try {
            $data = $this.state.CurrentOnboardingData
            
            # Ensure we have the required data
            if ($null -eq $data -or $data.Count -eq 0) {
                $this.state.SetStatusMessage("No user data found. Please complete previous steps first.")
                return
            }
            
            if (-not $data.ContainsKey('FirstName') -or -not $data.ContainsKey('LastName')) {
                $this.state.SetStatusMessage("First name and last name are required.")
                return
            }
            
            $username = $this.GenerateUsername($data.FirstName, $data.LastName)
            
            # Determine OU based on office location from Jira
            $targetOU = $this.state.SelectedOU
            if ([string]::IsNullOrEmpty($targetOU) -and $data.ContainsKey('OfficeLocation')) {
                $targetOU = $this.config.GetOUForLocation($data.OfficeLocation)
                $this.state.SetSelectedOU($targetOU)
                Write-Host "DEBUG: Auto-selected OU based on office location '$($data.OfficeLocation)': $targetOU" -ForegroundColor Green
            }
            
            if ([string]::IsNullOrEmpty($targetOU)) {
                $targetOU = Get-AppConfig -Path "ActiveDirectory.DefaultOU"
                $this.state.SetSelectedOU($targetOU)
                Write-Host "DEBUG: Using default OU: $targetOU" -ForegroundColor Yellow
            }
            
            $domain = Get-AppConfig -Path "ActiveDirectory.Domain"
            $company = Get-AppConfig -Path "ActiveDirectory.Company"
            $defaultPassword = Get-AppConfig -Path "ActiveDirectory.DefaultPassword"
            
            $userDetails = @{
                GivenName = $data.FirstName
                Surname = $data.LastName
                Name = "$($data.FirstName) $($data.LastName)"
                SamAccountName = $username
                UserPrincipalName = "$username@$domain"
                Title = if ($data.ContainsKey('JobTitle')) { $data.JobTitle } else { "" }
                Department = if ($data.ContainsKey('Department')) { $data.Department } else { "" }
                Company = $company
                Path = $targetOU
                AccountPassword = (ConvertTo-SecureString $defaultPassword -AsPlainText -Force)
                ChangePasswordAtLogon = $true
                Enabled = $true
            }

            $this.log.Debug("UserCreation", "Creating user with details - Username: $username, Full Name: $($userDetails.Name), Department: $($userDetails.Department), OU: $targetOU")
            
            $result = $this.ad.CreateUser($userDetails)
            
            if ($result.Success) {
                $this.state.SetStatusMessage("User account created successfully!")
                $this.state.StepValidationStatus[2] = $true
                $this.log.SecurityAudit("User Creation", "SUCCESS: $username")
                $this.log.Debug("UserCreation", "Step 2 validation set to TRUE")
            } else {
                $errorMsg = if ($result.ErrorMessage) { $result.ErrorMessage } else { "Unknown error occurred" }
                $this.state.SetStatusMessage("Error creating user: $errorMsg")
                $this.state.StepValidationStatus[2] = $false
                $this.log.SecurityAudit("User Creation", "FAILED: $username")
                $this.log.Debug("UserCreation", "Step 2 validation set to FALSE - Error: $errorMsg")
                
                # Log additional details if available
                if ($result.Operation) {
                    $this.log.Debug("UserCreation", "Failed operation: $($result.Operation)")
                }
            }
        } catch {
            $this.log.Debug("UserCreation", "Exception in CreateUserAccount: $($_.Exception.Message)")
            $this.state.SetStatusMessage("Error: $($_.Exception.Message)")
            $this.state.StepValidationStatus[2] = $false
        } finally {
            $this.state.SetIsBusy($false)
        }
    }

    PostJiraComment() {
        $this.log.Info("PostJiraComment command executed.")
        $this.state.SetStatusMessage("Posting comment to Jira ticket...")
        
        $data = $this.state.CurrentOnboardingData
        $username = $this.GenerateUsername($data.FirstName, $data.LastName)
        
        $comment = @"
User onboarding completed:
- Name: $($data.FirstName) $($data.LastName)
- Username: $username
- Department: $($data.Department)
- Job Title: $($data.JobTitle)
- OU: $($this.state.SelectedOU)

Account has been created and is ready for use.
"@

        $scriptBlock = { 
            param($ticketId, $comment, $authInfo, $logService) 
            try {
                Write-Host "DEBUG: Background script - Starting comment post" -ForegroundColor Cyan
                Write-Host "DEBUG: Background script - TicketId: $ticketId" -ForegroundColor Cyan
                Write-Host "DEBUG: Background script - AuthInfo null?: $($null -eq $authInfo)" -ForegroundColor Cyan
                
                if ($null -eq $authInfo) {
                    Write-Host "DEBUG: Background script - No auth info, using mock" -ForegroundColor Yellow
                    Start-Sleep -Milliseconds 300
                    return @{ Success = $true; Message = "Mock comment posted" }
                }
                
                # Execute REST API call directly in background  
                Write-Host "DEBUG: Background REST-CommentPost - Using REST API to post comment" -ForegroundColor Cyan
                
                # Extract credentials
                $jiraUrl = $authInfo.Url
                $username = $authInfo.Username
                $apiTokenSecure = $authInfo.ApiToken
                $apiToken = [Runtime.InteropServices.Marshal]::PtrToStringAuto([Runtime.InteropServices.Marshal]::SecureStringToBSTR($apiTokenSecure))
                
                # Create auth header (simplified from unified pattern)
                $base64AuthInfo = [Convert]::ToBase64String([Text.Encoding]::ASCII.GetBytes("$username`:$apiToken"))
                $headers = @{
                    "Authorization" = "Basic $base64AuthInfo"
                    "Accept" = "application/json"
                    "Content-Type" = "application/json"
                }
                
                # Create comment body (simplified from unified pattern)
                $commentBody = @{
                    body = @{
                        type = "doc"
                        version = 1
                        content = @(
                            @{
                                type = "paragraph"
                                content = @(
                                    @{
                                        type = "text"
                                        text = $comment
                                    }
                                )
                            }
                        )
                    }
                } | ConvertTo-Json -Depth 10
                
                Write-Host "DEBUG: Background REST-CommentPost - URL: $jiraUrl/rest/api/3/issue/$ticketId/comment" -ForegroundColor Gray
                Write-Host "DEBUG: Background REST-CommentPost - Comment preview: $($comment.Substring(0, [Math]::Min(50, $comment.Length)))..." -ForegroundColor Gray
                
                # Post the comment
                $response = Invoke-RestMethod -Uri "$jiraUrl/rest/api/3/issue/$ticketId/comment" -Headers $headers -Method Post -Body $commentBody -ErrorAction Stop
                
                if ($response -and $response.id) {
                    Write-Host "DEBUG: Background REST-CommentPost - Comment posted successfully with ID: $($response.id)" -ForegroundColor Green
                    return @{ Success = $true; CommentId = $response.id }
                } else {
                    throw "Invalid response from Jira comment API"
                }
                
            } catch {
                Write-Host "DEBUG: Background REST-CommentPost - Exception: $($_.Exception.Message)" -ForegroundColor Red
                Write-Host "DEBUG: Background REST-CommentPost - Exception Type: $($_.Exception.GetType().Name)" -ForegroundColor Red
                # Return mock result as fallback
                Write-Host "DEBUG: Background REST-CommentPost - Falling back to mock" -ForegroundColor Yellow
                Start-Sleep -Milliseconds 300
                return @{ Success = $true; Message = "Mock comment posted (REST API failed: $($_.Exception.Message))" }
            }
        }

        $onSuccess = {
            param($result)
            Write-Host "DEBUG: PostJiraComment onSuccess called" -ForegroundColor Green
            try {
                if ($result.Success) {
                    if ($result.CommentId) {
                        $this.state.SetStatusMessage("Comment posted to Jira successfully! (ID: $($result.CommentId))")
                        Write-Host "DEBUG: Comment posted with ID: $($result.CommentId)" -ForegroundColor Green
                    } else {
                        $this.state.SetStatusMessage("Comment posted to Jira successfully!")
                        Write-Host "DEBUG: Comment posted successfully (mock mode)" -ForegroundColor Green
                    }
                    $this.state.StepValidationStatus[3] = $true
                } else {
                    $this.state.SetStatusMessage("Error posting comment: $($result.ErrorMessage)")
                    Write-Host "DEBUG: Comment posting failed: $($result.ErrorMessage)" -ForegroundColor Red
                }
            } catch {
                Write-Host "ERROR: Exception in onSuccess callback: $($_.Exception.Message)" -ForegroundColor Red
                $this.state.SetStatusMessage("Error processing comment result: $($_.Exception.Message)")
            } finally {
                # CRITICAL: Always re-enable UI regardless of success/failure
                $this.state.SetIsBusy($false)
                Write-Host "DEBUG: IsBusy set to false, UI should be re-enabled" -ForegroundColor Green
            }
        }.GetNewClosure()

        $onFailure = { 
            param($reason) 
            $this.state.SetStatusMessage("Failed to post comment: $reason")
            $this.state.SetIsBusy($false)
        }.GetNewClosure()

        # Prepare authentication info for comment posting
        $authInfo = $null
        if ($this.state.IsJiraAuthenticated -and $this.state.ApiTokenInput) {
            $authInfo = @{
                Url = $this.state.JiraUrlInput
                Username = $this.state.JiraUsernameInput
                ApiToken = $this.state.ApiTokenInput
            }
            Write-Host "DEBUG: Using authenticated REST API for comment posting" -ForegroundColor Green
        } else {
            Write-Host "DEBUG: No authentication available, will use fallback method" -ForegroundColor Yellow
        }

        $argumentList = @($this.state.TicketIdInput, $comment, $authInfo, $this.log)
        $this.RunAsync($scriptBlock, $onSuccess, $onFailure, $argumentList)
    }

    hidden [string] GenerateUsername([string]$firstName, [string]$lastName) {
        # Generate username as first initial + last name, max 20 chars
        if ([string]::IsNullOrEmpty($firstName) -or [string]::IsNullOrEmpty($lastName)) {
            return "tempuser"
        }
        
        $cleanFirstName = $firstName -replace '[^a-zA-Z]', ''
        $cleanLastName = $lastName -replace '[^a-zA-Z]', ''
        
        if ($cleanFirstName.Length -eq 0 -or $cleanLastName.Length -eq 0) {
            return "tempuser"
        }
        
        $username = ($cleanFirstName.Substring(0,1) + $cleanLastName).ToLower()
        if ($username.Length -gt 20) {
            $username = $username.Substring(0, 20)
        }
        return $username
    }

    GoToNextStep() {
        Write-Host "DEBUG: GoToNextStep called. Current step: $($this.state.CurrentStepIndex)" -ForegroundColor Magenta
        Write-Host "DEBUG: Step 0 validation: $($this.state.StepValidationStatus[0])" -ForegroundColor Magenta
        Write-Host "DEBUG: Step 1 validation: $($this.state.StepValidationStatus[1])" -ForegroundColor Magenta
        Write-Host "DEBUG: Step 2 validation: $($this.state.StepValidationStatus[2])" -ForegroundColor Magenta
        Write-Host "DEBUG: Step 3 validation: $($this.state.StepValidationStatus[3])" -ForegroundColor Magenta
        
        $currentStatus = $this.state.StepValidationStatus
        if ($currentStatus[$this.state.CurrentStepIndex]) {
            Write-Host "DEBUG: Current step is valid, proceeding to next step" -ForegroundColor Green
            if ($this.state.CurrentStepIndex -lt 3) {
                $newStep = $this.state.CurrentStepIndex + 1
                Write-Host "DEBUG: Moving to step $newStep" -ForegroundColor Green
                $this.state.SetCurrentStepIndex($newStep)
                
                # Auto-validate Step 1 (Review) if we have required data
                if ($newStep -eq 1 -and $this.state.CurrentOnboardingData.Count -gt 0) {
                    $requiredFields = @('FirstName', 'LastName', 'Department', 'JobTitle')
                    $hasAllRequired = $true
                    foreach ($field in $requiredFields) {
                        if (-not $this.state.CurrentOnboardingData[$field]) {
                            $hasAllRequired = $false
                            break
                        }
                    }
                    if ($hasAllRequired) {
                        $this.state.StepValidationStatus[1] = $true
                        Write-Host "DEBUG: Auto-validated Step 1 - required data present" -ForegroundColor Green
                    }
                }
            } else {
                Write-Host "DEBUG: On final step, closing window" -ForegroundColor Green
                $this.view.window.Close()
            }
        } else {
            Write-Host "DEBUG: Current step is NOT valid, cannot proceed" -ForegroundColor Red
            $this.state.SetStatusMessage("Please complete the current step correctly before proceeding.")
        }
    }

    GoToPreviousStep() {
        if ($this.state.CurrentStepIndex -gt 0) {
            $this.state.SetCurrentStepIndex($this.state.CurrentStepIndex - 1)
        }
    }
}
# SECTION 4: VIEW CLASS

# Performance Monitor - Tracks operation timing and system performance
class PerformanceMonitor {
    static $OperationTimes = @{}
    static $CacheHitRatio = @{ Hits = 0; Misses = 0 }
    
    static [void] StartOperation([string]$operationName) {
        [PerformanceMonitor]::OperationTimes[$operationName] = @{
            StartTime = Get-Date
            EndTime = $null
            Duration = $null
        }
        Write-Host "DEBUG: [PERF] Started operation: $operationName" -ForegroundColor Magenta
    }
    
    static [void] EndOperation([string]$operationName) {
        if ([PerformanceMonitor]::OperationTimes.ContainsKey($operationName)) {
            $operation = [PerformanceMonitor]::OperationTimes[$operationName]
            $operation.EndTime = Get-Date
            $operation.Duration = $operation.EndTime - $operation.StartTime
            Write-Host "DEBUG: [PERF] Completed operation: $operationName in $($operation.Duration.TotalMilliseconds)ms" -ForegroundColor Magenta
        }
    }
    
    static [void] RecordCacheHit() {
        [PerformanceMonitor]::CacheHitRatio.Hits++
        Write-Host "DEBUG: [CACHE] Cache hit - Hit ratio: $([PerformanceMonitor]::GetCacheHitRatio())%" -ForegroundColor Green
    }
    
    static [void] RecordCacheMiss() {
        [PerformanceMonitor]::CacheHitRatio.Misses++
        Write-Host "DEBUG: [CACHE] Cache miss - Hit ratio: $([PerformanceMonitor]::GetCacheHitRatio())%" -ForegroundColor Yellow
    }
    
    static [double] GetCacheHitRatio() {
        $total = [PerformanceMonitor]::CacheHitRatio.Hits + [PerformanceMonitor]::CacheHitRatio.Misses
        if ($total -eq 0) { return 0 }
        return [Math]::Round(([PerformanceMonitor]::CacheHitRatio.Hits / $total) * 100, 2)
    }
    
    static [hashtable] GetPerformanceReport() {
        return @{
            OperationTimes = [PerformanceMonitor]::OperationTimes.Clone()
            CacheHitRatio = [PerformanceMonitor]::GetCacheHitRatio()
            TotalOperations = [PerformanceMonitor]::OperationTimes.Count
        }
    }
}

# Cache Manager - Handles data caching with TTL and automatic cleanup
class CacheManager {
    static $Cache = @{}
    static $DefaultTTL = 300  # 5 minutes in seconds
    
    static [void] Set([string]$key, $value, [int]$ttlSeconds = 0) {
        if ($ttlSeconds -eq 0) { $ttlSeconds = [CacheManager]::DefaultTTL }
        
        [CacheManager]::Cache[$key] = @{
            Value = $value
            Timestamp = Get-Date
            TTL = $ttlSeconds
        }
        Write-Host "DEBUG: [CACHE] Cached item: $key (TTL: $ttlSeconds s)" -ForegroundColor Cyan
    }
    
    static [object] Get([string]$key) {
        if (-not [CacheManager]::Cache.ContainsKey($key)) {
            [PerformanceMonitor]::RecordCacheMiss()
            return $null
        }
        
        $item = [CacheManager]::Cache[$key]
        $elapsed = ((Get-Date) - $item.Timestamp).TotalSeconds
        
        if ($elapsed -gt $item.TTL) {
            [CacheManager]::Remove($key)
            [PerformanceMonitor]::RecordCacheMiss()
            Write-Host "DEBUG: [CACHE] Item expired: $key" -ForegroundColor Yellow
            return $null
        }
        
        [PerformanceMonitor]::RecordCacheHit()
        return $item.Value
    }
    
    static [bool] Contains([string]$key) {
        $value = [CacheManager]::Get($key)
        return $null -ne $value
    }
    
    static [void] Remove([string]$key) {
        if ([CacheManager]::Cache.ContainsKey($key)) {
            [CacheManager]::Cache.Remove($key)
            Write-Host "DEBUG: [CACHE] Removed item: $key" -ForegroundColor Yellow
        }
    }
    
    static [void] Clear() {
        [CacheManager]::Cache.Clear()
        Write-Host "DEBUG: [CACHE] Cache cleared" -ForegroundColor Yellow
    }
    
    static [void] Cleanup() {
        $keysToRemove = @()
        foreach ($key in [CacheManager]::Cache.Keys) {
            $item = [CacheManager]::Cache[$key]
            $elapsed = ((Get-Date) - $item.Timestamp).TotalSeconds
            if ($elapsed -gt $item.TTL) {
                $keysToRemove += $key
            }
        }
        
        foreach ($key in $keysToRemove) {
            [CacheManager]::Remove($key)
        }
        
        if ($keysToRemove.Count -gt 0) {
            Write-Host "DEBUG: [CACHE] Cleanup removed $($keysToRemove.Count) expired items" -ForegroundColor Yellow
        }
    }
    
    static [hashtable] GetCacheStats() {
        [CacheManager]::Cleanup()  # Clean up expired items first
        return @{
            TotalItems = [CacheManager]::Cache.Count
            CacheKeys = [CacheManager]::Cache.Keys
            HitRatio = [PerformanceMonitor]::GetCacheHitRatio()
        }
    }
}

# Export Manager - Handles data export in multiple formats
class ExportManager {
    static [string] ExportToJson([hashtable]$data, [string]$fileName = "") {
        [PerformanceMonitor]::StartOperation("JsonExport")
        
        if ($fileName -eq "") {
            $fileName = "OnboardingData_$(Get-Date -Format 'yyyyMMdd_HHmmss').json"
        }
        
        try {
            $json = $data | ConvertTo-Json -Depth 10
            $filePath = Join-Path ([Environment]::GetFolderPath("Desktop")) $fileName
            $json | Out-File -FilePath $filePath -Encoding UTF8
            
            Write-Host "INFO: [EXPORT] Data exported to JSON: $filePath" -ForegroundColor Green
            [PerformanceMonitor]::EndOperation("JsonExport")
            return $filePath
        } catch {
            [PerformanceMonitor]::EndOperation("JsonExport")
            throw "Export to JSON failed: $($_.Exception.Message)"
        }
    }
    
    static [string] ExportToCsv([hashtable]$data, [string]$fileName = "") {
        [PerformanceMonitor]::StartOperation("CsvExport")
        
        if ($fileName -eq "") {
            $fileName = "OnboardingData_$(Get-Date -Format 'yyyyMMdd_HHmmss').csv"
        }
        
        try {
            # Flatten hashtable for CSV export
            $csvData = @()
            foreach ($key in $data.Keys) {
                $csvData += [PSCustomObject]@{
                    Field = $key
                    Value = if ($data[$key] -is [string]) { $data[$key] } else { $data[$key] | ConvertTo-Json -Compress }
                }
            }
            
            $filePath = Join-Path ([Environment]::GetFolderPath("Desktop")) $fileName
            $csvData | Export-Csv -Path $filePath -NoTypeInformation -Encoding UTF8
            
            Write-Host "INFO: [EXPORT] Data exported to CSV: $filePath" -ForegroundColor Green
            [PerformanceMonitor]::EndOperation("CsvExport")
            return $filePath
        } catch {
            [PerformanceMonitor]::EndOperation("CsvExport")
            throw "Export to CSV failed: $($_.Exception.Message)"
        }
    }
    
    static [string] ExportToXml([hashtable]$data, [string]$fileName = "") {
        [PerformanceMonitor]::StartOperation("XmlExport")
        
        if ($fileName -eq "") {
            $fileName = "OnboardingData_$(Get-Date -Format 'yyyyMMdd_HHmmss').xml"
        }
        
        try {
            $xml = New-Object System.Xml.XmlDocument
            $root = $xml.CreateElement("OnboardingData")
            $xml.AppendChild($root) | Out-Null
            
            foreach ($key in $data.Keys) {
                $element = $xml.CreateElement($key)
                $element.InnerText = if ($data[$key] -is [string]) { $data[$key] } else { $data[$key] | ConvertTo-Json -Compress }
                $root.AppendChild($element) | Out-Null
            }
            
            $filePath = Join-Path ([Environment]::GetFolderPath("Desktop")) $fileName
            $xml.Save($filePath)
            
            Write-Host "INFO: [EXPORT] Data exported to XML: $filePath" -ForegroundColor Green
            [PerformanceMonitor]::EndOperation("XmlExport")
            return $filePath
        } catch {
            [PerformanceMonitor]::EndOperation("XmlExport")
            throw "Export to XML failed: $($_.Exception.Message)"
        }
    }
    
    static [string] ExportPerformanceReport([string]$fileName = "") {
        if ($fileName -eq "") {
            $fileName = "PerformanceReport_$(Get-Date -Format 'yyyyMMdd_HHmmss').json"
        }
        
        $report = [PerformanceMonitor]::GetPerformanceReport()
        $cacheStats = [CacheManager]::GetCacheStats()
        $fullReport = @{
            Performance = $report
            Cache = $cacheStats
            Timestamp = Get-Date
        }
        
        return [ExportManager]::ExportToJson($fullReport, $fileName)
    }
}

# Quick Actions Manager - Handles common quick actions and shortcuts
class QuickActions {
    static [void] CopyToClipboard([string]$text) {
        try {
            $text | Set-Clipboard
            Write-Host "INFO: [QUICK] Copied to clipboard: $($text.Substring(0, [Math]::Min(50, $text.Length)))..." -ForegroundColor Green
        } catch {
            Write-Warning "Failed to copy to clipboard: $($_.Exception.Message)"
        }
    }
    
    static [void] OpenJiraTicket([string]$jiraUrl, [string]$ticketId) {
        try {
            $url = "$jiraUrl/browse/$ticketId"
            Start-Process $url
            Write-Host "INFO: [QUICK] Opened Jira ticket in browser: $url" -ForegroundColor Green
        } catch {
            Write-Warning "Failed to open Jira ticket: $($_.Exception.Message)"
        }
    }
    
    static [void] OpenDesktopFolder() {
        try {
            $desktop = [Environment]::GetFolderPath("Desktop")
            Start-Process "explorer.exe" -ArgumentList $desktop
            Write-Host "INFO: [QUICK] Opened Desktop folder" -ForegroundColor Green
        } catch {
            Write-Warning "Failed to open Desktop folder: $($_.Exception.Message)"
        }
    }
    
    static [hashtable] GetSystemInfo() {
        return @{
            ComputerName = $env:COMPUTERNAME
            UserName = $env:USERNAME
            Domain = $env:USERDOMAIN
            PowerShellVersion = "PowerShell"
            OSVersion = [System.Environment]::OSVersion.VersionString
            CurrentTime = Get-Date
            WorkingDirectory = Get-Location
        }
    }
}

# Advanced Error Management - Enhanced error handling with categorization and recovery
class AdvancedErrorManager {
    static $ErrorHistory = @()
    static $ErrorCategories = @{
        Network = @("timeout", "connection", "network", "ssl", "certificate")
        Authentication = @("unauthorized", "forbidden", "invalid credentials", "token")
        Validation = @("invalid", "required", "format", "length")
        System = @("memory", "disk", "access denied", "permissions")
        Application = @("null reference", "index", "cast", "format")
    }
    static $RecoveryStrategies = @{
        Network = { [AdvancedErrorManager]::NetworkRecovery($args[0]) }
        Authentication = { [AdvancedErrorManager]::AuthenticationRecovery($args[0]) }
        Validation = { [AdvancedErrorManager]::ValidationRecovery($args[0]) }
        System = { [AdvancedErrorManager]::SystemRecovery($args[0]) }
        Application = { [AdvancedErrorManager]::ApplicationRecovery($args[0]) }
    }
    
    static [hashtable] HandleError([string]$operation, [System.Exception]$exception, [object]$context = $null) {
        $errorInfo = @{
            Timestamp = Get-Date
            Operation = $operation
            Message = $exception.Message
            StackTrace = $exception.StackTrace
            Category = [AdvancedErrorManager]::CategorizeError($exception.Message)
            Context = $context
            RecoveryAttempted = $false
            RecoverySuccessful = $false
            ErrorId = [System.Guid]::NewGuid().ToString("N").Substring(0, 8)
        }
        
        # Add to error history
        [AdvancedErrorManager]::ErrorHistory += $errorInfo
        
        Write-Host "ERROR: [ADV] [$($errorInfo.ErrorId)] $operation failed: $($exception.Message)" -ForegroundColor Red
        Write-Host "DEBUG: [ADV] Error category: $($errorInfo.Category)" -ForegroundColor Yellow
        
        # Attempt automatic recovery
        $recoveryResult = [AdvancedErrorManager]::AttemptRecovery($errorInfo)
        if ($recoveryResult.Success) {
            Write-Host "INFO: [ADV] [$($errorInfo.ErrorId)] Recovery successful" -ForegroundColor Green
            $errorInfo.RecoveryAttempted = $true
            $errorInfo.RecoverySuccessful = $true
            return @{ Success = $true; Data = $recoveryResult.Data; ErrorInfo = $errorInfo }
        } else {
            Write-Host "WARN: [ADV] [$($errorInfo.ErrorId)] Recovery failed or not attempted" -ForegroundColor Yellow
            return @{ Success = $false; ErrorMessage = $exception.Message; ErrorInfo = $errorInfo }
        }
    }
    
    static [string] CategorizeError([string]$errorMessage) {
        $lowerMessage = $errorMessage.ToLower()
        foreach ($category in [AdvancedErrorManager]::ErrorCategories.Keys) {
            foreach ($keyword in [AdvancedErrorManager]::ErrorCategories[$category]) {
                if ($lowerMessage -match $keyword) {
                    return $category
                }
            }
        }
        return "Unknown"
    }
    
    static [hashtable] AttemptRecovery([hashtable]$errorInfo) {
        if ([AdvancedErrorManager]::RecoveryStrategies.ContainsKey($errorInfo.Category)) {
            try {
                $strategy = [AdvancedErrorManager]::RecoveryStrategies[$errorInfo.Category]
                $result = & $strategy $errorInfo
                return $result
            } catch {
                Write-Host "ERROR: [ADV] Recovery strategy failed: $($_.Exception.Message)" -ForegroundColor Red
                return @{ Success = $false }
            }
        }
        return @{ Success = $false }
    }
    
    static [hashtable] NetworkRecovery([hashtable]$errorInfo) {
        Write-Host "INFO: [ADV] Attempting network recovery..." -ForegroundColor Cyan
        # Clear cache to force fresh connections
        [CacheManager]::Clear()
        Start-Sleep -Seconds 2
        return @{ Success = $true; Data = "Network cache cleared" }
    }
    
    static [hashtable] AuthenticationRecovery([hashtable]$errorInfo) {
        Write-Host "INFO: [ADV] Attempting authentication recovery..." -ForegroundColor Cyan
        # Clear authentication cache
        $authKeys = [CacheManager]::Cache.Keys | Where-Object { $_ -like "auth_*" }
        foreach ($key in $authKeys) {
            [CacheManager]::Remove($key)
        }
        return @{ Success = $true; Data = "Authentication cache cleared" }
    }
    
    static [hashtable] ValidationRecovery([hashtable]$errorInfo) {
        Write-Host "INFO: [ADV] Attempting validation recovery..." -ForegroundColor Cyan
        # Provide validation guidance
        return @{ Success = $false; Data = "Please check input format and try again" }
    }
    
    static [hashtable] SystemRecovery([hashtable]$errorInfo) {
        Write-Host "INFO: [ADV] Attempting system recovery..." -ForegroundColor Cyan
        # Trigger garbage collection
        [System.GC]::Collect()
        return @{ Success = $true; Data = "System resources cleaned" }
    }
    
    static [hashtable] ApplicationRecovery([hashtable]$errorInfo) {
        Write-Host "INFO: [ADV] Attempting application recovery..." -ForegroundColor Cyan
        # Reset application state
        return @{ Success = $false; Data = "Application restart may be required" }
    }
    
    static [array] GetErrorHistory([int]$maxItems = 50) {
        $history = [AdvancedErrorManager]::ErrorHistory
        if ($history.Count -le $maxItems) {
            return $history
        } else {
            return $history[($history.Count - $maxItems)..($history.Count - 1)]
        }
    }
    
    static [hashtable] GetErrorStatistics() {
        $errors = [AdvancedErrorManager]::ErrorHistory
        $stats = @{
            TotalErrors = $errors.Count
            ErrorsByCategory = @{}
            RecoveryRate = 0
            RecentErrors = 0
        }
        
        # Group by category
        foreach ($error in $errors) {
            if (-not $stats.ErrorsByCategory.ContainsKey($error.Category)) {
                $stats.ErrorsByCategory[$error.Category] = 0
            }
            $stats.ErrorsByCategory[$error.Category]++
        }
        
        # Calculate recovery rate
        $recoveredErrors = ($errors | Where-Object { $_.RecoverySuccessful }).Count
        if ($errors.Count -gt 0) {
            $stats.RecoveryRate = [Math]::Round(($recoveredErrors / $errors.Count) * 100, 2)
        }
        
        # Recent errors (last 10 minutes)
        $recentTime = (Get-Date).AddMinutes(-10)
        $stats.RecentErrors = ($errors | Where-Object { $_.Timestamp -gt $recentTime }).Count
        
        return $stats
    }
}

# Advanced Logging and Diagnostics - Enhanced logging with categorization and filtering
class AdvancedLogger {
    static $LogLevel = "INFO"  # DEBUG, INFO, WARN, ERROR
    static $LogBuffer = @()
    static $MaxBufferSize = 1000
    static $LogToFile = $true
    static $LogFilePath = ""  # Will be set in Initialize()
    
    static [void] Initialize() {
        # Set log file path during initialization
        [AdvancedLogger]::LogFilePath = Join-Path ([Environment]::GetFolderPath("LocalApplicationData")) "JERA\OnboardingWizard\application.log"
        
        # Create log directory if it doesn't exist
        $logDir = Split-Path [AdvancedLogger]::LogFilePath -Parent
        if (-not (Test-Path $logDir)) {
            New-Item -Path $logDir -ItemType Directory -Force | Out-Null
        }
        
        [AdvancedLogger]::Info("SYSTEM", "Advanced logging initialized")
    }
    
    static [void] Debug([string]$category, [string]$message) {
        [AdvancedLogger]::Log("DEBUG", $category, $message)
    }
    
    static [void] Info([string]$category, [string]$message) {
        [AdvancedLogger]::Log("INFO", $category, $message)
    }
    
    static [void] Warn([string]$category, [string]$message) {
        [AdvancedLogger]::Log("WARN", $category, $message)
    }
    
    static [void] Error([string]$category, [string]$message) {
        [AdvancedLogger]::Log("ERROR", $category, $message)
    }
    
    static [void] Log([string]$level, [string]$category, [string]$message) {
        $logEntry = @{
            Timestamp = Get-Date
            Level = $level
            Category = $category
            Message = $message
            ThreadId = [System.Threading.Thread]::CurrentThread.ManagedThreadId
        }
        
        # Add to buffer
        [AdvancedLogger]::LogBuffer += $logEntry
        
        # Maintain buffer size
        if ([AdvancedLogger]::LogBuffer.Count -gt [AdvancedLogger]::MaxBufferSize) {
            [AdvancedLogger]::LogBuffer = [AdvancedLogger]::LogBuffer[-[AdvancedLogger]::MaxBufferSize..-1]
        }
        
        # Console output with color coding
        $color = switch ($level) {
            "DEBUG" { "Cyan" }
            "INFO" { "Green" }
            "WARN" { "Yellow" }
            "ERROR" { "Red" }
            default { "White" }
        }
        
        $formattedMessage = "[$($logEntry.Timestamp.ToString('yyyy-MM-dd HH:mm:ss'))] [$level] [$category] $message"
        
        # Check log level filtering
        $shouldLog = switch ([AdvancedLogger]::LogLevel) {
            "DEBUG" { $true }
            "INFO" { $level -in @("INFO", "WARN", "ERROR") }
            "WARN" { $level -in @("WARN", "ERROR") }
            "ERROR" { $level -eq "ERROR" }
            default { $true }
        }
        
        if ($shouldLog) {
            Write-Host $formattedMessage -ForegroundColor $color
            
            # Write to file if enabled
            if ([AdvancedLogger]::LogToFile) {
                try {
                    $formattedMessage | Out-File -FilePath [AdvancedLogger]::LogFilePath -Append -Encoding UTF8
                } catch {
                    # Silently fail to avoid recursive logging
                }
            }
        }
    }
    
    static [array] GetLogs([string]$level = $null, [string]$category = $null, [int]$maxItems = 100) {
        $logs = [AdvancedLogger]::LogBuffer
        
        if ($level) {
            $logs = $logs | Where-Object { $_.Level -eq $level }
        }
        
        if ($category) {
            $logs = $logs | Where-Object { $_.Category -eq $category }
        }
        
        if ($logs.Count -gt $maxItems) {
            $logs = $logs[-$maxItems..-1]
        }
        
        return $logs
    }
    
    static [hashtable] GetLogStatistics() {
        $logs = [AdvancedLogger]::LogBuffer
        $stats = @{
            TotalLogs = $logs.Count
            LogsByLevel = @{}
            LogsByCategory = @{}
            RecentActivity = 0
        }
        
        # Group by level
        $logs | Group-Object Level | ForEach-Object {
            $stats.LogsByLevel[$_.Name] = $_.Count
        }
        
        # Group by category
        $logs | Group-Object Category | ForEach-Object {
            $stats.LogsByCategory[$_.Name] = $_.Count
        }
        
        # Recent activity (last 5 minutes)
        $recentTime = (Get-Date).AddMinutes(-5)
        $stats.RecentActivity = ($logs | Where-Object { $_.Timestamp -gt $recentTime }).Count
        
        return $stats
    }
    
    static [void] SetLogLevel([string]$level) {
        [AdvancedLogger]::LogLevel = $level
        [AdvancedLogger]::Info("SYSTEM", "Log level changed to: $level")
    }
    
    static [void] ExportLogs([string]$filePath = "") {
        if ($filePath -eq "") {
            $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
            $filePath = Join-Path ([Environment]::GetFolderPath("Desktop")) "JERA_OnboardingLogs_$timestamp.json"
        }
        
        try {
            $exportData = @{
                ExportTimestamp = Get-Date
                LogLevel = [AdvancedLogger]::LogLevel
                Statistics = [AdvancedLogger]::GetLogStatistics()
                Logs = [AdvancedLogger]::LogBuffer
            }
            
            $json = $exportData | ConvertTo-Json -Depth 10
            $json | Out-File -FilePath $filePath -Encoding UTF8
            
            [AdvancedLogger]::Info("EXPORT", "Logs exported to: $filePath")
        } catch {
            [AdvancedLogger]::Error("EXPORT", "Failed to export logs: $($_.Exception.Message)")
        }
    }
}

# User Preferences Manager - Handles user settings and preferences persistence
class UserPreferencesManager {
    static $PreferencesPath = ""  # Will be set in Initialize()
    static $DefaultPreferences = @{
        Theme = "Professional"
        AutoSave = $true
        AutoSaveInterval = 30
        LogLevel = "INFO"
        CacheEnabled = $true
        CacheTTL = 300
        ExportFormat = "JSON"
        RememberWindowSize = $true
        ShowDebugInfo = $false
        EnablePerformanceMonitoring = $true
        DefaultJiraUrl = "https://jeragm.atlassian.net"
        ValidationStrictMode = $false
    }
    static $CurrentPreferences = @{}
    
    static [void] Initialize() {
        # Set preferences path during initialization
        [UserPreferencesManager]::PreferencesPath = Join-Path ([Environment]::GetFolderPath("LocalApplicationData")) "JERA\OnboardingWizard\preferences.json"
        
        # Create preferences directory if it doesn't exist
        $prefDir = Split-Path [UserPreferencesManager]::PreferencesPath -Parent
        if (-not (Test-Path $prefDir)) {
            New-Item -Path $prefDir -ItemType Directory -Force | Out-Null
        }
        
        [UserPreferencesManager]::LoadPreferences()
    }
    
    static [void] LoadPreferences() {
        try {
            if (Test-Path [UserPreferencesManager]::PreferencesPath) {
                $json = Get-Content -Path [UserPreferencesManager]::PreferencesPath -Raw
                $loadedPrefs = $json | ConvertFrom-Json
                
                # Merge with defaults to ensure all keys exist
                [UserPreferencesManager]::CurrentPreferences = [UserPreferencesManager]::DefaultPreferences.Clone()
                $loadedPrefs.PSObject.Properties | ForEach-Object {
                    [UserPreferencesManager]::CurrentPreferences[$_.Name] = $_.Value
                }
                
                Write-Host "INFO: [PREFS] User preferences loaded" -ForegroundColor Green
            } else {
                [UserPreferencesManager]::CurrentPreferences = [UserPreferencesManager]::DefaultPreferences.Clone()
                [UserPreferencesManager]::SavePreferences()
                Write-Host "INFO: [PREFS] Default preferences created" -ForegroundColor Green
            }
        } catch {
            Write-Host "ERROR: [PREFS] Failed to load preferences: $($_.Exception.Message)" -ForegroundColor Red
            [UserPreferencesManager]::CurrentPreferences = [UserPreferencesManager]::DefaultPreferences.Clone()
        }
    }
    
    static [void] SavePreferences() {
        try {
            $json = [UserPreferencesManager]::CurrentPreferences | ConvertTo-Json -Depth 5
            $json | Out-File -FilePath [UserPreferencesManager]::PreferencesPath -Encoding UTF8
            Write-Host "DEBUG: [PREFS] User preferences saved" -ForegroundColor Cyan
        } catch {
            Write-Host "ERROR: [PREFS] Failed to save preferences: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
    
    static [object] GetPreference([string]$key) {
        if ([UserPreferencesManager]::CurrentPreferences.ContainsKey($key)) {
            return [UserPreferencesManager]::CurrentPreferences[$key]
        } else {
            return $null
        }
    }
    
    static [void] SetPreference([string]$key, $value) {
        [UserPreferencesManager]::CurrentPreferences[$key] = $value
        [UserPreferencesManager]::SavePreferences()
        Write-Host "DEBUG: [PREFS] Preference updated: $key = $value" -ForegroundColor Cyan
    }
    
    static [hashtable] GetAllPreferences() {
        return [UserPreferencesManager]::CurrentPreferences.Clone()
    }
    
    static [void] ResetToDefaults() {
        [UserPreferencesManager]::CurrentPreferences = [UserPreferencesManager]::DefaultPreferences.Clone()
        [UserPreferencesManager]::SavePreferences()
        Write-Host "INFO: [PREFS] Preferences reset to defaults" -ForegroundColor Green
    }
    
    static [void] ExportPreferences([string]$filePath = "") {
        if ($filePath -eq "") {
            $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
            $filePath = Join-Path ([Environment]::GetFolderPath("Desktop")) "JERA_Preferences_$timestamp.json"
        }
        
        try {
            $exportData = @{
                ExportTimestamp = Get-Date
                Version = "3.0"
                Preferences = [UserPreferencesManager]::CurrentPreferences
            }
            
            $json = $exportData | ConvertTo-Json -Depth 5
            $json | Out-File -FilePath $filePath -Encoding UTF8
            
            Write-Host "INFO: [PREFS] Preferences exported to: $filePath" -ForegroundColor Green
        } catch {
            Write-Host "ERROR: [PREFS] Failed to export preferences: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
}

# Session Manager - Handles session persistence and auto-save functionality
class SessionManager {
    static $SessionPath = ""  # Will be set in Initialize()
    static $AutoSaveInterval = 30  # seconds
    static $LastAutoSave = $null
    static $SessionData = @{}
    
    static [void] Initialize() {
        # Set session path during initialization
        [SessionManager]::SessionPath = Join-Path ([Environment]::GetFolderPath("LocalApplicationData")) "JERA\OnboardingWizard"
        [SessionManager]::LastAutoSave = Get-Date
        
        # Create session directory if it doesn't exist
        if (-not (Test-Path [SessionManager]::SessionPath)) {
            New-Item -Path [SessionManager]::SessionPath -ItemType Directory -Force | Out-Null
            Write-Host "INFO: [SESSION] Created session directory: $([SessionManager]::SessionPath)" -ForegroundColor Green
        }
        
        # Load existing session if available
        [SessionManager]::LoadSession()
    }
        }
        
        $attempt = 1
        
        while ($attempt -le $maxRetries) {
            try {
                [PerformanceMonitor]::StartOperation("$operationName-Attempt-$attempt")
                Write-Host "DEBUG: [RESILIENCE] Executing $operationName (attempt $attempt/$maxRetries)" -ForegroundColor Cyan
                
                $result = & $operation
                
                # Success - reset failure count
                [ConnectionResilienceManager]::RecordSuccess()
                [PerformanceMonitor]::EndOperation("$operationName-Attempt-$attempt")
                
                Write-Host "INFO: [RESILIENCE] $operationName succeeded on attempt $attempt" -ForegroundColor Green
                return @{ Success = $true; Data = $result; AttemptsUsed = $attempt }
                
            } catch {
                [PerformanceMonitor]::EndOperation("$operationName-Attempt-$attempt")
                [ConnectionResilienceManager]::RecordFailure()
                
                $isLastAttempt = ($attempt -eq $maxRetries)
                $isRetryableError = [ConnectionResilienceManager]::IsRetryableError($_.Exception)
                
                Write-Host "WARN: [RESILIENCE] $operationName failed on attempt $attempt`: $($_.Exception.Message)" -ForegroundColor Yellow
                
                if ($isLastAttempt -or -not $isRetryableError) {
                    Write-Host "ERROR: [RESILIENCE] $operationName failed after $attempt attempts" -ForegroundColor Red
                    return @{ 
                        Success = $false
                        ErrorMessage = $_.Exception.Message
                        AttemptsUsed = $attempt
                        IsRetryable = $isRetryableError
                    }
                }
                
                # Wait before retry with exponential backoff
                $delaySeconds = [ConnectionResilienceManager]::RetryDelaySeconds * [Math]::Pow(2, $attempt - 1)
                Write-Host "DEBUG: [RESILIENCE] Waiting $delaySeconds seconds before retry..." -ForegroundColor Cyan
                Start-Sleep -Seconds $delaySeconds
                
                $attempt++
            }
        }
    }
    
    static [bool] IsRetryableError([System.Exception]$exception) {
        $retryablePatterns = @(
            "timeout", "network", "connection", "ssl", "certificate", 
            "503", "502", "500", "429", "temporarily unavailable"
        )
        
        $errorMessage = $exception.Message.ToLower()
        foreach ($pattern in $retryablePatterns) {
            if ($errorMessage -match $pattern) {
                return $true
            }
        }
        
        return $false
    }
    
    static [void] RecordSuccess() {
        [ConnectionResilienceManager]::FailureCount = 0
        [ConnectionResilienceManager]::CircuitBreakerOpen = $false
        Write-Host "DEBUG: [RESILIENCE] Success recorded, circuit breaker reset" -ForegroundColor Green
    }
    
    static [void] RecordFailure() {
        [ConnectionResilienceManager]::FailureCount++
        [ConnectionResilienceManager]::LastFailureTime = Get-Date
        
        if ([ConnectionResilienceManager]::FailureCount -ge [ConnectionResilienceManager]::CircuitBreakerThreshold) {
            [ConnectionResilienceManager]::CircuitBreakerOpen = $true
            Write-Host "WARN: [RESILIENCE] Circuit breaker opened after $([ConnectionResilienceManager]::FailureCount) failures" -ForegroundColor Red
        }
    }
    
    static [bool] IsCircuitBreakerOpen() {
        if (-not [ConnectionResilienceManager]::CircuitBreakerOpen) {
            return $false
        }
        
        # Check if reset time has passed
        if ([ConnectionResilienceManager]::LastFailureTime) {
            $elapsed = ((Get-Date) - [ConnectionResilienceManager]::LastFailureTime).TotalSeconds
            if ($elapsed -gt [ConnectionResilienceManager]::CircuitBreakerResetTime) {
                Write-Host "INFO: [RESILIENCE] Circuit breaker reset time reached, attempting to close" -ForegroundColor Yellow
                [ConnectionResilienceManager]::CircuitBreakerOpen = $false
                [ConnectionResilienceManager]::FailureCount = 0
                return $false
            }
        }
        
        return $true
    }
    
    static [int] GetCircuitBreakerResetTime() {
        if ([ConnectionResilienceManager]::LastFailureTime) {
            $elapsed = ((Get-Date) - [ConnectionResilienceManager]::LastFailureTime).TotalSeconds
            $remaining = [ConnectionResilienceManager]::CircuitBreakerResetTime - $elapsed
            return [Math]::Max(0, [Math]::Ceiling($remaining))
        }
        return 0
    }
    
    static [hashtable] GetResilienceStatistics() {
        return @{
            FailureCount = [ConnectionResilienceManager]::FailureCount
            CircuitBreakerOpen = [ConnectionResilienceManager]::CircuitBreakerOpen
            LastFailureTime = [ConnectionResilienceManager]::LastFailureTime
            ResetTimeRemaining = [ConnectionResilienceManager]::GetCircuitBreakerResetTime()
            RetryAttempts = [ConnectionResilienceManager]::RetryAttempts
            RetryDelay = [ConnectionResilienceManager]::RetryDelaySeconds
        }
    }
}

class UIFactory {
    static $AppConfig = (Get-AppConfig)
    static $Icons = $script:Icons
    
    # Create professional styled button with JERA branding
    static [object] CreateButton([string]$content, [string]$buttonType = "primary", [string]$margin = "0,10,0,0") {
        $button = New-Object System.Windows.Controls.Button
        $button.Content = $content
        $button.Margin = $margin
        $button.Height = (Get-AppConfig -Path "UI.Spacing.ButtonHeight")
        $button.MinWidth = 130
        $button.FontWeight = "SemiBold"
        $button.FontSize = (Get-AppConfig -Path "UI.Typography.BodyFontSize")
        $button.Cursor = "Hand"
        $button.BorderThickness = 0
        $button.Padding = "15,8"
        
        # Apply JERA Global Markets styling based on button type
        switch ($buttonType.ToLower()) {
            "primary" {
                $button.Background = (Get-AppConfig -Path "UI.Colors.Primary")        # JERA Navy
                $button.Foreground = "White"
            }
            "success" {
                $button.Background = (Get-AppConfig -Path "UI.Colors.Success")       # Green
                $button.Foreground = "White"
            }
            "warning" {
                $button.Background = (Get-AppConfig -Path "UI.Colors.JERAOrange")    # JERA Orange
                $button.Foreground = "White"
            }
            "error" {
                $button.Background = (Get-AppConfig -Path "UI.Colors.Error")         # Red
                $button.Foreground = "White"
            }
            "secondary" {
                $button.Background = (Get-AppConfig -Path "UI.Colors.Secondary")     # Gray
                $button.Foreground = "White"
            }
            "jera-blue" {
                $button.Background = (Get-AppConfig -Path "UI.Colors.JERABlue")      # JERA Blue
                $button.Foreground = "White"
            }
            "disabled" {
                $button.Background = (Get-AppConfig -Path "UI.Colors.Disabled")      # Light Gray
                $button.Foreground = (Get-AppConfig -Path "UI.Colors.TextMuted")     # Muted text
                $button.IsEnabled = $false
            }
        }
        
        return $button
    }
    
    # Create bordered panel with JERA styling
    static [object] CreateBorderedPanel([string]$title = "", [string]$margin = "0,0,0,15") {
        $border = New-Object System.Windows.Controls.Border
        $border.Background = (Get-AppConfig -Path "UI.Colors.ContentBackground")    # Pure white
        $border.Padding = (Get-AppConfig -Path "UI.Spacing.PanelPadding")
        $border.CornerRadius = 8  # Slightly more rounded for modern look
        $border.Margin = $margin
        $border.BorderBrush = (Get-AppConfig -Path "UI.Colors.BorderColor")
        $border.BorderThickness = 1
        
        # Simple styling without shadow for compatibility
        
        $panel = New-Object System.Windows.Controls.StackPanel
        
        if ($title -ne "") {
            $titleLabel = [UIFactory]::CreateSectionTitle($title, "")
            $panel.Children.Add($titleLabel)
        }
        
        $border.Child = $panel
        return @{ Border = $border; Panel = $panel }
    }
    
    # Create section title with JERA typography
    static [object] CreateSectionTitle([string]$content, [string]$iconKey = "") {
        $label = New-Object System.Windows.Controls.Label
        if ($iconKey -ne "" -and [UIFactory]::Icons.ContainsKey($iconKey)) {
            $label.Content = "$([UIFactory]::Icons[$iconKey]) $content"
        } else {
            $label.Content = $content
        }
        $label.FontSize = (Get-AppConfig -Path "UI.Typography.SectionFontSize")
        $label.FontWeight = 'Bold'
        $label.Margin = "0,0,0,15"
        $label.Foreground = (Get-AppConfig -Path "UI.Colors.TextPrimary")
        return $label
    }
    
    # Create standardized content label with JERA styling
    static [object] CreateLabel([string]$content, [string]$margin = "0", [string]$iconKey = "") {
        $label = New-Object System.Windows.Controls.Label
        if ($iconKey -ne "" -and [UIFactory]::Icons.ContainsKey($iconKey)) {
            $label.Content = "$([UIFactory]::Icons[$iconKey]) $content"
        } else {
            $label.Content = $content
        }
        if ($margin -ne "0") {
            $label.Margin = $margin
        }
        $label.FontWeight = "SemiBold"
        $label.FontSize = (Get-AppConfig -Path "UI.Typography.LabelFontSize")
        $label.Foreground = (Get-AppConfig -Path "UI.Colors.TextPrimary")
        return $label
    }
    
    # Create status label with JERA color coding
    static [object] CreateStatusLabel([string]$content, [string]$level = "info", [string]$margin = "0,8,0,0") {
        $label = New-Object System.Windows.Controls.Label
        $label.Content = $content
        $label.Margin = $margin
        $label.FontSize = (Get-AppConfig -Path "UI.Typography.StatusFontSize")
        $label.Padding = "8,4"
        
        switch ($level.ToLower()) {
            "success" { 
                $label.Foreground = (Get-AppConfig -Path "UI.Colors.Success")
                $label.Background = (Get-AppConfig -Path "UI.Colors.StatusSuccess")
                if ([UIFactory]::Icons.ContainsKey("Success")) {
                    $label.Content = "$([UIFactory]::Icons.Success) $content"
                }
            }
            "warning" { 
                $label.Foreground = (Get-AppConfig -Path "UI.Colors.Warning")
                $label.Background = (Get-AppConfig -Path "UI.Colors.StatusWarning")
                if ([UIFactory]::Icons.ContainsKey("Error")) {
                    $label.Content = "$([UIFactory]::Icons.Error) $content"
                }
            }
            "error" { 
                $label.Foreground = (Get-AppConfig -Path "UI.Colors.Error")
                $label.Background = (Get-AppConfig -Path "UI.Colors.StatusError")
                if ([UIFactory]::Icons.ContainsKey("Error")) {
                    $label.Content = "$([UIFactory]::Icons.Error) $content"
                }
            }
            default { 
                $label.Foreground = (Get-AppConfig -Path "UI.Colors.TextSecondary")
                $label.Background = "Transparent"
                if ([UIFactory]::Icons.ContainsKey("Info")) {
                    $label.Content = "$([UIFactory]::Icons.Info) $content"
                }
            }
        }
        
        return $label
    }
    
    # Legacy methods maintained for compatibility
    static [object] CreateTitleLabel([string]$content) {
        return [UIFactory]::CreateSectionTitle($content)
    }
    
    static [object] CreateActionButton([string]$content, [string]$margin = "0,10,0,0") {
        return [UIFactory]::CreateButton($content, "success", $margin)
    }
    
    static [object] CreateErrorLabel([string]$content, [string]$margin = "0,10,0,0") {
        return [UIFactory]::CreateStatusLabel($content, "error", $margin)
    }
    
    # Create standardized error panel with enhanced styling
    static [object] CreateErrorPanel([string]$margin = "20") {
        $panel = New-Object System.Windows.Controls.StackPanel
        $panel.Orientation = "Vertical"
        if ($margin -ne "0") {
            $panel.Margin = $margin
        }
        $panel.Background = "#FFE6E6"  # Light red background for errors
        $panel.Padding = "15"
        return $panel
    }
    
    # Create standardized text input with enhanced styling
    static [object] CreateTextBox([string]$initialText = "", [string]$margin = "0") {
        $textBox = New-Object System.Windows.Controls.TextBox
        if ($initialText -ne "") {
            $textBox.Text = $initialText
        }
        if ($margin -ne "0") {
            $textBox.Margin = $margin
        }
        $textBox.Padding = "5,3"
        $textBox.BorderBrush = "#CCCCCC"  # Fixed: Use direct color
        return $textBox
    }
    
    # Create standardized password input with enhanced styling
    static [object] CreatePasswordBox([string]$margin = "0") {
        $passwordBox = New-Object System.Windows.Controls.PasswordBox
        if ($margin -ne "0") {
            $passwordBox.Margin = $margin
        }
        $passwordBox.Padding = "5,3"
        $passwordBox.BorderBrush = "#CCCCCC"  # Fixed: Use direct color
        return $passwordBox
    }
    
    # Create standardized combo box with enhanced styling
    static [object] CreateComboBox([array]$items = @(), [string]$margin = "0") {
        $comboBox = New-Object System.Windows.Controls.ComboBox
        if ($items.Count -gt 0) {
            foreach ($item in $items) {
                $comboBox.Items.Add($item) | Out-Null
            }
        }
        if ($margin -ne "0") {
            $comboBox.Margin = $margin
        }
        $comboBox.Padding = "5,3"
        $comboBox.BorderBrush = "#CCCCCC"  # Fixed: Use direct color
        return $comboBox
    }
    
    # Create standardized stack panel
    static [object] CreateStackPanel([string]$orientation = "Vertical", [string]$margin = "15") {
        $panel = New-Object System.Windows.Controls.StackPanel
        $panel.Orientation = $orientation
        if ($margin -ne "0") {
            $panel.Margin = $margin
        }
        return $panel
    }
    
    # Create separator line with enhanced styling
    static [object] CreateSeparator([string]$margin = "0,15,0,15") {
        $separator = New-Object System.Windows.Controls.Separator
        $separator.Margin = $margin
        $separator.Background = "#CCCCCC"  # Fixed: Use direct color
        return $separator
    }
    
    # JERA Real-Time Validation Methods
    static [object] CreateValidatedTextBox([string]$initialText = "", [string]$margin = "0", [string]$validationType = "text") {
        $textBox = [UIFactory]::CreateTextBox($initialText, $margin)
        
        # Add validation styling based on type
        $textBox.Tag = @{
            ValidationType = $validationType
            IsValid = $true
            OriginalBorderBrush = $textBox.BorderBrush
        }
        
        return $textBox
    }
    
    static [void] ValidateTextBox([object]$textBox) {
        $tag = $textBox.Tag
        $text = $textBox.Text
        $isValid = $true
        
        switch ($tag.ValidationType) {
            "name" {
                $isValid = $text -match "^[a-zA-Z\s\-']{2,50}$" -and $text.Trim().Length -gt 1
            }
            "email" {
                $isValid = $text -match "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"
            }
            "required" {
                $isValid = $text.Trim().Length -gt 0
            }
            "jobtitle" {
                $isValid = $text.Trim().Length -gt 2 -and $text.Length -lt 100
            }
            default {
                $isValid = $text.Trim().Length -gt 0
            }
        }
        
        # Apply JERA-styled validation colors
        if ($isValid) {
            $textBox.BorderBrush = "#28A745"  # JERA Success Green
            $textBox.BorderThickness = "2"
            $textBox.Background = "#F8FFF8"   # Light green background
        } else {
            $textBox.BorderBrush = "#DC3545"  # JERA Error Red
            $textBox.BorderThickness = "2" 
            $textBox.Background = "#FFF8F8"   # Light red background
        }
        
        $tag.IsValid = $isValid
        $textBox.Tag = $tag
    }
    
    static [void] AddValidatedTextChangedHandler([object]$textBox, [scriptblock]$handler) {
        $textBox.add_TextChanged({
            try {
                # Perform validation first
                [UIFactory]::ValidateTextBox($textBox)
                
                Write-Host "DEBUG: [UI EVENT] Validated TextBox changed: '$($textBox.Text)' (Valid: $($textBox.Tag.IsValid))" -ForegroundColor Yellow
                $result = & $handler
                Write-Host "DEBUG: [UI EVENT] Validated TextBox change handler completed" -ForegroundColor Yellow
                return $result
            } catch {
                Write-Host "ERROR: [UI EVENT] Validated TextBox change handler failed: $($_.Exception.Message)" -ForegroundColor Red
            }
        }.GetNewClosure())
    }
    
    static [bool] ValidateAllInputs([array]$textBoxes) {
        $allValid = $true
        foreach ($textBox in $textBoxes) {
            if ($textBox.Tag -and $textBox.Tag.ValidationType) {
                [UIFactory]::ValidateTextBox($textBox)
                if (-not $textBox.Tag.IsValid) {
                    $allValid = $false
                }
            }
        }
        return $allValid
    }
    
    # Helper method to add click handler with proper closure and debug logging
    static [void] AddClickHandler([object]$button, [scriptblock]$handler) {
        $button.add_Click({
            try {
                Write-Host "DEBUG: [UI EVENT] Button clicked: '$($button.Content)'" -ForegroundColor Yellow
                $result = & $handler
                Write-Host "DEBUG: [UI EVENT] Button click handler completed for: '$($button.Content)'" -ForegroundColor Yellow
                return $result
            } catch {
                Write-Host "ERROR: [UI EVENT] Button click handler failed for '$($button.Content)': $($_.Exception.Message)" -ForegroundColor Red
                throw
            }
        }.GetNewClosure())
    }
    
    # Helper method to add text changed handler with proper closure and debug logging
    static [void] AddTextChangedHandler([object]$textBox, [scriptblock]$handler) {
        $textBox.add_TextChanged({
            try {
                Write-Host "DEBUG: [UI EVENT] TextBox changed: '$($textBox.Text)'" -ForegroundColor Yellow
                $result = & $handler
                Write-Host "DEBUG: [UI EVENT] TextBox change handler completed" -ForegroundColor Yellow
                return $result
            } catch {
                Write-Host "ERROR: [UI EVENT] TextBox change handler failed: $($_.Exception.Message)" -ForegroundColor Red
            }
        }.GetNewClosure())
    }
    
    # Helper method to add selection changed handler with debug logging
    static [void] AddSelectionChangedHandler([object]$comboBox, [scriptblock]$handler) {
        $comboBox.add_SelectionChanged({
            try {
                Write-Host "DEBUG: [UI EVENT] ComboBox selection changed to: '$($comboBox.SelectedItem)'" -ForegroundColor Yellow
                $result = & $handler
                Write-Host "DEBUG: [UI EVENT] ComboBox selection change handler completed" -ForegroundColor Yellow
                return $result
            } catch {
                Write-Host "ERROR: [UI EVENT] ComboBox selection change handler failed: $($_.Exception.Message)" -ForegroundColor Red
            }
        }.GetNewClosure())
    }
}

class WizardView {
    $viewModel
    $window
    $controls = @{}
    $validationTextBoxes = @()  # Store validation textboxes for batch validation
    
    # PHASE 4: Store critical UI references as class properties
    $contentArea
    $mainPanel  
    $navPanel
    $statusBar
    $statusText
    $prevButton
    $nextButton

    WizardView($wizardViewModel) {
        $this.viewModel = $wizardViewModel
        $this.controls = @{}  # Initialize controls dictionary
        $this.validationTextBoxes = @()  # Initialize validation textboxes
    }

    hidden [void] Bind($control, $property, [string]$path, $converter = $null) {
        $binding = New-Object System.Windows.Data.Binding $path
        $binding.Source = $this.viewModel.state
        $binding.Mode = 'TwoWay'
        $binding.UpdateSourceTrigger = 'PropertyChanged'
        if ($converter) { $binding.Converter = $converter }
        $control.SetBinding($property, $binding)
    }

    # PHASE 3: Add UI validation function
    hidden [bool] ValidateUIReferences($contentArea, $prevButton, $nextButton, $statusText, $mainPanel) {
        $isValid = $true
        
        Write-Host "DEBUG: Validating UI references..." -ForegroundColor Yellow
        
        if ($contentArea -eq $null) { 
            Write-Host "ERROR: contentArea is null" -ForegroundColor Red
            $isValid = $false 
        } else {
            Write-Host "DEBUG: contentArea is valid" -ForegroundColor Green
        }
        
        if ($prevButton -eq $null) { 
            Write-Host "ERROR: prevButton is null" -ForegroundColor Red
            $isValid = $false 
        } else {
            Write-Host "DEBUG: prevButton is valid" -ForegroundColor Green
        }
        
        if ($nextButton -eq $null) { 
            Write-Host "ERROR: nextButton is null" -ForegroundColor Red
            $isValid = $false 
        } else {
            Write-Host "DEBUG: nextButton is valid" -ForegroundColor Green
        }
        
        if ($statusText -eq $null) { 
            Write-Host "ERROR: statusText is null" -ForegroundColor Red
            $isValid = $false 
        } else {
            Write-Host "DEBUG: statusText is valid" -ForegroundColor Green
        }
        
        if ($mainPanel -eq $null) { 
            Write-Host "ERROR: mainPanel is null" -ForegroundColor Red
            $isValid = $false 
        } else {
            Write-Host "DEBUG: mainPanel is valid" -ForegroundColor Green
        }
        
        if ($this.window -eq $null) { 
            Write-Host "ERROR: window is null" -ForegroundColor Red
            $isValid = $false 
        } else {
            Write-Host "DEBUG: window is valid" -ForegroundColor Green
        }
        
        Write-Host "DEBUG: UI validation result: $isValid" -ForegroundColor $(if ($isValid) { "Green" } else { "Red" })
        return $isValid
    }

    # PHASE 6: Add fallback content creation for error scenarios (Updated with UIFactory)
    hidden [object] CreateErrorStep([string]$errorMessage) {
        Write-Host "DEBUG: Creating error step content using UIFactory" -ForegroundColor Red
        
        # Create error panel using UIFactory
        $panel = [UIFactory]::CreateErrorPanel("20")

        # Error Title using UIFactory
        $titleLabel = [UIFactory]::CreateTitleLabel("⚠️ Error Loading Step Content")
        $titleLabel.Foreground = "#AA0000"
        $panel.Children.Add($titleLabel)

        # Error Message using UIFactory
        $messageLabel = [UIFactory]::CreateErrorLabel($errorMessage)
        $panel.Children.Add($messageLabel)

        # Refresh Button using UIFactory
        $refreshButton = [UIFactory]::CreateActionButton("🔄 Refresh Content", "0,20,0,0")
        
        $capturedViewModel = $this.viewModel
        [UIFactory]::AddClickHandler($refreshButton, { 
            try {
                # Trigger a refresh by setting the current step again
                $currentStep = $capturedViewModel.state.CurrentStepIndex
                $capturedViewModel.state.SetCurrentStepIndex($currentStep)
                Write-Host "DEBUG: Manual refresh triggered for step $currentStep" -ForegroundColor Yellow
            } catch {
                Write-Host "ERROR: Manual refresh failed: $($_.Exception.Message)" -ForegroundColor Red
            }
        })
        $panel.Children.Add($refreshButton)

        return $panel
    }

    hidden [object] CreateWizardStep0() { # JERA Branded Jira Connection & Ticket Input Step
        $stepPanel = [UIFactory]::CreateStackPanel("Vertical", "20")

        # Jira Connection Section with JERA styling
        $connectionSection = [UIFactory]::CreateBorderedPanel("Jira Authentication", "0,0,0,20")
        $connectionPanel = $connectionSection.Panel
        
        # Add JERA-styled section title  
        $titleLabel = [UIFactory]::CreateSectionTitle("Connect to JERA Jira System", "Connection")
        $connectionPanel.Children.Add($titleLabel)

        # Jira URL with JERA styling
        $urlLabel = [UIFactory]::CreateLabel("Jira URL:", "0,5,0,0", "")
        $connectionPanel.Children.Add($urlLabel)

        $urlTextBox = [UIFactory]::CreateTextBox($this.viewModel.state.JiraUrlInput, "0,0,0,12")
        $capturedViewModelState = $this.viewModel.state
        $capturedUrlTextBox = $urlTextBox
        [UIFactory]::AddTextChangedHandler($urlTextBox, { 
            try {
                $capturedViewModelState.SetJiraUrlInput($capturedUrlTextBox.Text)
                Write-Host "DEBUG: ViewModel JiraUrlInput updated to: '$($capturedViewModelState.JiraUrlInput)'" -ForegroundColor Green
            } catch {
                Write-Warning "Error updating Jira URL: $($_.Exception.Message)"
            }
        }.GetNewClosure())
        $connectionPanel.Children.Add($urlTextBox)

        # Username with JERA styling
        $userLabel = [UIFactory]::CreateLabel("Username:", "0,5,0,0", "")
        $connectionPanel.Children.Add($userLabel)

        $userTextBox = [UIFactory]::CreateTextBox($this.viewModel.state.JiraUsernameInput, "0,0,0,12")
        $capturedUserTextBox = $userTextBox
        [UIFactory]::AddTextChangedHandler($userTextBox, { 
            try {
                $capturedViewModelState.SetJiraUsernameInput($capturedUserTextBox.Text)
                Write-Host "DEBUG: ViewModel JiraUsernameInput updated to: '$($capturedViewModelState.JiraUsernameInput)'" -ForegroundColor Green
            } catch {
                Write-Warning "Error updating username: $($_.Exception.Message)"
            }
        }.GetNewClosure())
        $connectionPanel.Children.Add($userTextBox)

        # API Token with JERA styling
        $tokenLabel = [UIFactory]::CreateLabel("API Token:", "0,5,0,0", "")
        $connectionPanel.Children.Add($tokenLabel)

        $tokenBox = [UIFactory]::CreatePasswordBox("0,0,0,15")
        $this.controls.apiTokenBox = $tokenBox
        $connectionPanel.Children.Add($tokenBox)
        
        # JERA-styled Login Button
        $loginButton = [UIFactory]::CreateButton("$($script:Icons.Login) Login to Jira", "primary", "0,5,0,0")
        $capturedVM = $this.viewModel
        [UIFactory]::AddClickHandler($loginButton, {
            $capturedVM.AuthenticateJira()
        }.GetNewClosure())
        $connectionPanel.Children.Add($loginButton)
        
        # JERA-styled Authentication Status
        $authStatusLabel = [UIFactory]::CreateStatusLabel("Status: Not authenticated", "info", "0,8,0,0")
        $this.controls.authStatusLabel = $authStatusLabel
        $connectionPanel.Children.Add($authStatusLabel)
        
        $stepPanel.Children.Add($connectionSection.Border)

        # Ticket Lookup Section with JERA styling
        $ticketSection = [UIFactory]::CreateBorderedPanel("Ticket Lookup", "0")
        $ticketPanel = $ticketSection.Panel
        
        $ticketTitleLabel = [UIFactory]::CreateSectionTitle("Fetch Onboarding Ticket", "Ticket")
        $ticketPanel.Children.Add($ticketTitleLabel)

        # Ticket ID with JERA styling
        $ticketLabel = [UIFactory]::CreateLabel("Jira Ticket ID:", "0,5,0,0", "")
        $ticketPanel.Children.Add($ticketLabel)

        $ticketTextBox = [UIFactory]::CreateTextBox($this.viewModel.state.TicketIdInput, "0,0,0,15")
        $capturedTicketTextBox = $ticketTextBox
        [UIFactory]::AddTextChangedHandler($ticketTextBox, { 
            try {
                $capturedViewModelState.SetTicketIdInput($capturedTicketTextBox.Text)
                Write-Host "DEBUG: ViewModel TicketIdInput updated to: '$($capturedViewModelState.TicketIdInput)'" -ForegroundColor Green
            } catch {
                Write-Warning "Error updating ticket ID: $($_.Exception.Message)"
            }
        }.GetNewClosure())
        $ticketPanel.Children.Add($ticketTextBox)

        # JERA-styled Fetch Button (disabled until authenticated)
        $fetchButton = [UIFactory]::CreateButton("$($script:Icons.Fetch) Fetch Ticket Details", "disabled", "0,5,0,0")
        $this.controls.fetchButton = $fetchButton
        [UIFactory]::AddClickHandler($fetchButton, {
            $capturedVM.FetchTicketDetails()
        }.GetNewClosure())
        $ticketPanel.Children.Add($fetchButton)

        $stepPanel.Children.Add($ticketSection.Border)
        return $stepPanel
    }

    hidden [object] CreateWizardStep1() { # User Details Review & Edit (JERA-styled with Real-time Validation)
        $stepPanel = [UIFactory]::CreateStackPanel("Vertical", "20")

        # JERA-styled section with bordered panel
        $userDetailsSection = [UIFactory]::CreateBorderedPanel("User Information", "0,0,0,20")
        $userPanel = $userDetailsSection.Panel
        
        # Add JERA-styled section title
        $titleLabel = [UIFactory]::CreateSectionTitle("Review and Edit User Details", "User")
        $userPanel.Children.Add($titleLabel)

        # Create form fields for user data using validated UIFactory controls
        $data = $this.viewModel.state.CurrentOnboardingData
        if ($null -eq $data) { $data = @{} }

        # First Name using Validated UIFactory
        $fnLabel = [UIFactory]::CreateLabel("First Name:", "0,10,0,0", "")
        $userPanel.Children.Add($fnLabel)
        
        $fnTextBox = [UIFactory]::CreateValidatedTextBox($(if ($data.ContainsKey('FirstName')) { $data.FirstName } else { "" }), "0,0,0,12", "name")
        $capturedViewModelState = $this.viewModel.state
        $capturedFnTextBox = $fnTextBox
        [UIFactory]::AddValidatedTextChangedHandler($fnTextBox, { 
            try {
                if (-not $capturedViewModelState.CurrentOnboardingData) { 
                    $capturedViewModelState.CurrentOnboardingData = @{} 
                }
                $capturedViewModelState.CurrentOnboardingData['FirstName'] = $capturedFnTextBox.Text
            } catch {
                Write-Warning "Error updating first name: $($_.Exception.Message)"
            }
        }.GetNewClosure())
        $userPanel.Children.Add($fnTextBox)

        # Last Name using Validated UIFactory
        $lnLabel = [UIFactory]::CreateLabel("Last Name:", "0,5,0,0", "")
        $userPanel.Children.Add($lnLabel)
        
        $lnTextBox = [UIFactory]::CreateValidatedTextBox($(if ($data.ContainsKey('LastName')) { $data.LastName } else { "" }), "0,0,0,12", "name")
        $capturedLnTextBox = $lnTextBox
        [UIFactory]::AddValidatedTextChangedHandler($lnTextBox, { 
            try {
                if (-not $capturedViewModelState.CurrentOnboardingData) { 
                    $capturedViewModelState.CurrentOnboardingData = @{} 
                }
                $capturedViewModelState.CurrentOnboardingData['LastName'] = $capturedLnTextBox.Text
            } catch {
                Write-Warning "Error updating last name: $($_.Exception.Message)"
            }
        }.GetNewClosure())
        $userPanel.Children.Add($lnTextBox)

        # Job Title using Validated UIFactory
        $jtLabel = [UIFactory]::CreateLabel("Job Title:", "0,5,0,0", "")
        $userPanel.Children.Add($jtLabel)
        
        $jtTextBox = [UIFactory]::CreateValidatedTextBox($(if ($data.ContainsKey('JobTitle')) { $data.JobTitle } else { "" }), "0,0,0,12", "jobtitle")
        $capturedJtTextBox = $jtTextBox
        [UIFactory]::AddValidatedTextChangedHandler($jtTextBox, { 
            try {
                if (-not $capturedViewModelState.CurrentOnboardingData) { 
                    $capturedViewModelState.CurrentOnboardingData = @{} 
                }
                $capturedViewModelState.CurrentOnboardingData['JobTitle'] = $capturedJtTextBox.Text
            } catch {
                Write-Warning "Error updating job title: $($_.Exception.Message)"
            }
        }.GetNewClosure())
        $userPanel.Children.Add($jtTextBox)

        # Department using Validated UIFactory
        $deptLabel = [UIFactory]::CreateLabel("Department:", "0,5,0,0", "")
        $userPanel.Children.Add($deptLabel)
        
        $deptTextBox = [UIFactory]::CreateValidatedTextBox($(if ($data.ContainsKey('Department')) { $data.Department } else { "" }), "0,0,0,12", "required")
        $capturedDeptTextBox = $deptTextBox
        [UIFactory]::AddValidatedTextChangedHandler($deptTextBox, { 
            try {
                if (-not $capturedViewModelState.CurrentOnboardingData) { 
                    $capturedViewModelState.CurrentOnboardingData = @{} 
                }
                $capturedViewModelState.CurrentOnboardingData['Department'] = $capturedDeptTextBox.Text
            } catch {
                Write-Warning "Error updating department: $($_.Exception.Message)"
            }
        }.GetNewClosure())
        $userPanel.Children.Add($deptTextBox)

        # Store textboxes for validation
        $this.validationTextBoxes = @($fnTextBox, $lnTextBox, $jtTextBox, $deptTextBox)

        # OU Selection using UIFactory
        $ouLabel = [UIFactory]::CreateLabel("Organizational Unit:", "0,5,0,0", "")
        $userPanel.Children.Add($ouLabel)

        $ouComboBox = [UIFactory]::CreateComboBox($this.viewModel.config.GetOUList(), "0,0,0,15")
        if ($ouComboBox.Items.Count -gt 0) { $ouComboBox.SelectedIndex = 0 }
        $capturedOuComboBox = $ouComboBox
        $ouComboBox.add_SelectionChanged({ 
            Write-Host "DEBUG: [UI EVENT] OU ComboBox selection changed to: '$($capturedOuComboBox.SelectedItem)'" -ForegroundColor Yellow
            if ($capturedOuComboBox.SelectedItem) {
                $capturedViewModelState.SetSelectedOU($capturedOuComboBox.SelectedItem.ToString())
                Write-Host "DEBUG: [UI EVENT] OU selection handler completed" -ForegroundColor Yellow
            }
        }.GetNewClosure())
        $userPanel.Children.Add($ouComboBox)

        # JERA-styled Validate button with real-time validation check
        $validateButton = [UIFactory]::CreateButton("$($script:Icons.Validate) Validate Details", "success", "0,15,0,0")
        $capturedViewModel = $this.viewModel
        $capturedTextBoxes = $this.validationTextBoxes
        [UIFactory]::AddClickHandler($validateButton, { 
            $allValid = [UIFactory]::ValidateAllInputs($capturedTextBoxes)
            if ($allValid) {
                Write-Host "INFO: All inputs are valid - proceeding with validation" -ForegroundColor Green
                $capturedViewModel.ValidateUserDetails()
            } else {
                Write-Host "WARNING: Please fix validation errors before proceeding" -ForegroundColor Red
                # Could add a popup or status message here
            }
        }.GetNewClosure())
        $userPanel.Children.Add($validateButton)

        $stepPanel.Children.Add($userDetailsSection.Border)
        return $stepPanel
    }

    hidden [object] CreateWizardStep2() { # User Creation Configuration (JERA-styled with UIFactory)
        $stepPanel = [UIFactory]::CreateStackPanel("Vertical", "20")

        # JERA-styled Account Configuration section
        $accountSection = [UIFactory]::CreateBorderedPanel("Account Configuration", "0,0,0,20")
        $accountPanel = $accountSection.Panel
        
        # Add JERA-styled section title
        $titleLabel = [UIFactory]::CreateSectionTitle("Configure User Account", "Create")
        $accountPanel.Children.Add($titleLabel)

        # Generated Username using UIFactory
        $userLabel = [UIFactory]::CreateLabel("Generated Username:", "0,10,0,0", "")
        $accountPanel.Children.Add($userLabel)

        $userTextBlock = [UIFactory]::CreateLabel("Will be generated based on first/last name", "0,0,0,15", "")
        $userTextBlock.FontStyle = 'Italic'
        $userTextBlock.Foreground = "#666666"
        $accountPanel.Children.Add($userTextBlock)

        # Password Options Section
        $passwordSection = [UIFactory]::CreateBorderedPanel("Password Settings", "0,10,0,20")
        $passwordPanel = $passwordSection.Panel

        $tempPassCheck = New-Object System.Windows.Controls.CheckBox
        $tempPassCheck.Content = "$($script:Icons.Settings) Generate temporary password"
        $tempPassCheck.IsChecked = $true
        $tempPassCheck.Margin = "0,5,0,5"
        $passwordPanel.Children.Add($tempPassCheck)

        $mustChangeCheck = New-Object System.Windows.Controls.CheckBox
        $mustChangeCheck.Content = "$($script:Icons.Settings) User must change password at next logon"
        $mustChangeCheck.IsChecked = $true
        $mustChangeCheck.Margin = "0,0,0,5"
        $passwordPanel.Children.Add($mustChangeCheck)

        # Groups Section
        $groupsSection = [UIFactory]::CreateBorderedPanel("Group Membership", "0,0,0,20")
        $groupsPanel = $groupsSection.Panel

        $groupTextBlock = [UIFactory]::CreateLabel("Domain Users (automatic)", "0,5,0,0", "")
        $groupTextBlock.FontStyle = 'Italic'
        $groupTextBlock.Foreground = "#666666"
        $groupsPanel.Children.Add($groupTextBlock)

        # JERA-styled Create User button
        $createButton = [UIFactory]::CreateButton("$($script:Icons.Create) Create User Account", "primary", "0,20,0,0")
        $capturedViewModel = $this.viewModel
        [UIFactory]::AddClickHandler($createButton, { 
            $capturedViewModel.CreateUserAccount() 
        }.GetNewClosure())
        $accountPanel.Children.Add($createButton)

        $stepPanel.Children.Add($accountSection.Border)
        $stepPanel.Children.Add($passwordSection.Border)
        $stepPanel.Children.Add($groupsSection.Border)
        return $stepPanel
    }

    hidden [object] CreateWizardStep3() { # Summary and Completion (JERA-styled with Export & Advanced Features)
        $stepPanel = [UIFactory]::CreateStackPanel("Vertical", "20")

        # JERA-styled Summary section
        $summarySection = [UIFactory]::CreateBorderedPanel("Operation Summary", "0,0,0,20")
        $summaryPanel = $summarySection.Panel
        
        # Add JERA-styled section title
        $titleLabel = [UIFactory]::CreateSectionTitle("Summary and Completion", "Success")
        $summaryPanel.Children.Add($titleLabel)

        # Results using UIFactory
        $resultsLabel = [UIFactory]::CreateLabel("Operation Results:", "0,10,0,0", "")
        $summaryPanel.Children.Add($resultsLabel)

        $resultsTextBox = [UIFactory]::CreateTextBox("User creation results will appear here...", "0,0,0,15")
        $resultsTextBox.IsReadOnly = $true
        $resultsTextBox.Height = 150
        $resultsTextBox.TextWrapping = 'Wrap'
        $resultsTextBox.VerticalScrollBarVisibility = 'Auto'
        $resultsTextBox.Background = "#F8F8F8"
        $this.controls.resultsTextBox = $resultsTextBox
        $summaryPanel.Children.Add($resultsTextBox)

        # JERA-styled Diagnostics section (Phase 3 feature)
        $diagnosticsSection = [UIFactory]::CreateBorderedPanel("System Diagnostics", "0,0,0,20")
        $diagnosticsPanel = $diagnosticsSection.Panel

        # Performance Statistics
        $perfLabel = [UIFactory]::CreateLabel("Performance & Cache Statistics:", "0,5,0,0", "")
        $diagnosticsPanel.Children.Add($perfLabel)

        $perfStatsTextBox = [UIFactory]::CreateTextBox("", "0,0,0,10")
        $perfStatsTextBox.IsReadOnly = $true
        $perfStatsTextBox.Height = 80
        $perfStatsTextBox.TextWrapping = 'Wrap'
        $perfStatsTextBox.Background = "#F0F8FF"
        
        # Populate diagnostics data
        try {
            $perfStats = [PerformanceMonitor]::GetPerformanceReport()
            $cacheStats = [CacheManager]::GetCacheStats()
            $errorStats = [AdvancedErrorManager]::GetErrorStatistics()
            $logStats = [AdvancedLogger]::GetLogStatistics()
            
            $diagnosticsText = @"
Performance: $($perfStats.TotalOperations) operations completed
Cache: $($cacheStats.TotalItems) items cached, $($cacheStats.HitRatio)% hit ratio
Errors: $($errorStats.TotalErrors) total, $($errorStats.RecoveryRate)% recovery rate
Logs: $($logStats.TotalLogs) entries, $($logStats.RecentActivity) recent
"@
            $perfStatsTextBox.Text = $diagnosticsText
        } catch {
            $perfStatsTextBox.Text = "Diagnostics data unavailable"
        }
        
        $diagnosticsPanel.Children.Add($perfStatsTextBox)

        # JERA-styled Export Actions section
        $exportSection = [UIFactory]::CreateBorderedPanel("Export Options", "0,0,0,20")
        $exportPanel = $exportSection.Panel

        # Create horizontal export button panel
        $exportButtonPanel = [UIFactory]::CreateStackPanel("Horizontal", "0,10,0,0")

        $exportJsonButton = [UIFactory]::CreateButton("$($script:Icons.Export) Export JSON", "secondary", "0,0,5,0")
        $capturedViewModel = $this.viewModel
        [UIFactory]::AddClickHandler($exportJsonButton, { 
            try {
                $data = $capturedViewModel.state.CurrentOnboardingData
                if ($data) {
                    $filePath = [ExportManager]::ExportToJson($data)
                    [QuickActions]::CopyToClipboard($filePath)
                    [AdvancedLogger]::Info("EXPORT", "JSON exported and path copied to clipboard")
                } else {
                    [AdvancedLogger]::Warn("EXPORT", "No data available to export")
                }
            } catch {
                [AdvancedLogger]::Error("EXPORT", "JSON export failed: $($_.Exception.Message)")
            }
        }.GetNewClosure())
        $exportButtonPanel.Children.Add($exportJsonButton)

        $exportCsvButton = [UIFactory]::CreateButton("$($script:Icons.Export) Export CSV", "secondary", "0,0,5,0")
        [UIFactory]::AddClickHandler($exportCsvButton, { 
            try {
                $data = $capturedViewModel.state.CurrentOnboardingData
                if ($data) {
                    $filePath = [ExportManager]::ExportToCsv($data)
                    [QuickActions]::CopyToClipboard($filePath)
                    [AdvancedLogger]::Info("EXPORT", "CSV exported and path copied to clipboard")
                } else {
                    [AdvancedLogger]::Warn("EXPORT", "No data available to export")
                }
            } catch {
                [AdvancedLogger]::Error("EXPORT", "CSV export failed: $($_.Exception.Message)")
            }
        }.GetNewClosure())
        $exportButtonPanel.Children.Add($exportCsvButton)

        $exportLogsButton = [UIFactory]::CreateButton("$($script:Icons.Settings) Export Logs", "secondary", "0")
        [UIFactory]::AddClickHandler($exportLogsButton, { 
            try {
                [AdvancedLogger]::ExportLogs()
                [AdvancedLogger]::Info("EXPORT", "Application logs exported")
            } catch {
                [AdvancedLogger]::Error("EXPORT", "Log export failed: $($_.Exception.Message)")
            }
        }.GetNewClosure())
        $exportButtonPanel.Children.Add($exportLogsButton)

        $exportPanel.Children.Add($exportButtonPanel)

        # JERA-styled Quick Actions section
        $quickActionsSection = [UIFactory]::CreateBorderedPanel("Quick Actions", "0,0,0,20")
        $quickActionsPanel = $quickActionsSection.Panel

        # Create horizontal quick action button panel
        $quickButtonPanel = [UIFactory]::CreateStackPanel("Horizontal", "0,10,0,0")

        $openJiraButton = [UIFactory]::CreateButton("$($script:Icons.Browser) Open Ticket", "secondary", "0,0,5,0")
        [UIFactory]::AddClickHandler($openJiraButton, { 
            try {
                $jiraUrl = $capturedViewModel.state.JiraUrlInput
                $ticketId = $capturedViewModel.state.TicketIdInput
                if ($jiraUrl -and $ticketId) {
                    [QuickActions]::OpenJiraTicket($jiraUrl, $ticketId)
                    [AdvancedLogger]::Info("QUICK", "Opened Jira ticket: $ticketId")
                } else {
                    [AdvancedLogger]::Warn("QUICK", "Jira URL or Ticket ID not available")
                }
            } catch {
                [AdvancedLogger]::Error("QUICK", "Failed to open Jira ticket: $($_.Exception.Message)")
            }
        }.GetNewClosure())
        $quickButtonPanel.Children.Add($openJiraButton)

        $openDesktopButton = [UIFactory]::CreateButton("$($script:Icons.Browser) Open Desktop", "secondary", "0,0,5,0")
        [UIFactory]::AddClickHandler($openDesktopButton, { 
            try {
                [QuickActions]::OpenDesktopFolder()
                [AdvancedLogger]::Info("QUICK", "Opened Desktop folder")
            } catch {
                [AdvancedLogger]::Error("QUICK", "Failed to open Desktop: $($_.Exception.Message)")
            }
        }.GetNewClosure())
        $quickButtonPanel.Children.Add($openDesktopButton)

        $preferencesButton = [UIFactory]::CreateButton("$($script:Icons.Settings) Preferences", "secondary", "0")
        [UIFactory]::AddClickHandler($preferencesButton, { 
            try {
                $prefsPath = [UserPreferencesManager]::PreferencesPath
                Start-Process "notepad.exe" -ArgumentList $prefsPath
                [AdvancedLogger]::Info("QUICK", "Opened preferences file")
            } catch {
                [AdvancedLogger]::Error("QUICK", "Failed to open preferences: $($_.Exception.Message)")
            }
        }.GetNewClosure())
        $quickButtonPanel.Children.Add($preferencesButton)

        $quickActionsPanel.Children.Add($quickButtonPanel)

        # JERA-styled Final Actions section
        $actionsSection = [UIFactory]::CreateBorderedPanel("Final Actions", "0")
        $actionsPanel = $actionsSection.Panel

        # Create horizontal button panel
        $buttonPanel = [UIFactory]::CreateStackPanel("Horizontal", "0,15,0,0")

        $commentButton = [UIFactory]::CreateButton("$($script:Icons.Comment) Post Comment to Jira", "secondary", "0,0,10,0")
        [UIFactory]::AddClickHandler($commentButton, { 
            [AdvancedLogger]::Info("JIRA", "Posting comment to Jira ticket")
            $capturedViewModel.PostJiraComment() 
        }.GetNewClosure())
        $buttonPanel.Children.Add($commentButton)

        $finishButton = [UIFactory]::CreateButton("$($script:Icons.Success) Finish", "success", "0")
        $capturedWindow = $this.window
        [UIFactory]::AddClickHandler($finishButton, { 
            try {
                # Phase 3 cleanup before closing
                [AdvancedLogger]::Info("APP", "Application closing - performing cleanup")
                [SessionManager]::BackupSession("application_close")
                [CacheManager]::Cleanup()
                [SessionManager]::CleanupOldSessions()
                
                $capturedWindow.Close()
            } catch {
                [AdvancedLogger]::Error("APP", "Error during application close: $($_.Exception.Message)")
                $capturedWindow.Close()
            }
        }.GetNewClosure())
        $buttonPanel.Children.Add($finishButton)

        $actionsPanel.Children.Add($buttonPanel)

        $stepPanel.Children.Add($summarySection.Border)
        $stepPanel.Children.Add($diagnosticsSection.Border)
        $stepPanel.Children.Add($exportSection.Border)
        $stepPanel.Children.Add($quickActionsSection.Border)
        $stepPanel.Children.Add($actionsSection.Border)
        return $stepPanel
    }
            } catch {
                Write-Host "ERROR: [EXPORT] JSON export failed: $($_.Exception.Message)" -ForegroundColor Red
            }
        }.GetNewClosure())
        $exportButtonPanel.Children.Add($exportJsonButton)

        $exportCsvButton = [UIFactory]::CreateButton("$($script:Icons.Export) Export CSV", "secondary", "0,0,5,0")
        [UIFactory]::AddClickHandler($exportCsvButton, { 
            try {
                $data = $capturedViewModel.state.CurrentOnboardingData
                if ($data) {
                    $filePath = [ExportManager]::ExportToCsv($data)
                    [QuickActions]::CopyToClipboard($filePath)
                    Write-Host "INFO: [EXPORT] CSV exported and path copied to clipboard" -ForegroundColor Green
                } else {
                    Write-Warning "No data available to export"
                }
            } catch {
                Write-Host "ERROR: [EXPORT] CSV export failed: $($_.Exception.Message)" -ForegroundColor Red
            }
        }.GetNewClosure())
        $exportButtonPanel.Children.Add($exportCsvButton)

        $exportPerfButton = [UIFactory]::CreateButton("$($script:Icons.Settings) Export Performance", "secondary", "0")
        [UIFactory]::AddClickHandler($exportPerfButton, { 
            try {
                $filePath = [ExportManager]::ExportPerformanceReport()
                [QuickActions]::CopyToClipboard($filePath)
                Write-Host "INFO: [EXPORT] Performance report exported and path copied to clipboard" -ForegroundColor Green
            } catch {
                Write-Host "ERROR: [EXPORT] Performance export failed: $($_.Exception.Message)" -ForegroundColor Red
            }
        }.GetNewClosure())
        $exportButtonPanel.Children.Add($exportPerfButton)

        $exportPanel.Children.Add($exportButtonPanel)

        # JERA-styled Quick Actions section
        $quickActionsSection = [UIFactory]::CreateBorderedPanel("Quick Actions", "0,0,0,20")
        $quickActionsPanel = $quickActionsSection.Panel

        # Create horizontal quick action button panel
        $quickButtonPanel = [UIFactory]::CreateStackPanel("Horizontal", "0,10,0,0")

        $openJiraButton = [UIFactory]::CreateButton("$($script:Icons.Browser) Open Ticket", "secondary", "0,0,5,0")
        [UIFactory]::AddClickHandler($openJiraButton, { 
            try {
                $jiraUrl = $capturedViewModel.state.JiraUrlInput
                $ticketId = $capturedViewModel.state.JiraTicketInput
                if ($jiraUrl -and $ticketId) {
                    [QuickActions]::OpenJiraTicket($jiraUrl, $ticketId)
                } else {
                    Write-Warning "Jira URL or Ticket ID not available"
                }
            } catch {
                Write-Host "ERROR: [QUICK] Failed to open Jira ticket: $($_.Exception.Message)" -ForegroundColor Red
            }
        }.GetNewClosure())
        $quickButtonPanel.Children.Add($openJiraButton)

        $openDesktopButton = [UIFactory]::CreateButton("$($script:Icons.Browser) Open Desktop", "secondary", "0,0,5,0")
        [UIFactory]::AddClickHandler($openDesktopButton, { 
            try {
                [QuickActions]::OpenDesktopFolder()
            } catch {
                Write-Host "ERROR: [QUICK] Failed to open Desktop: $($_.Exception.Message)" -ForegroundColor Red
            }
        }.GetNewClosure())
        $quickButtonPanel.Children.Add($openDesktopButton)

        $copyDataButton = [UIFactory]::CreateButton("$($script:Icons.Copy) Copy Data", "secondary", "0")
        [UIFactory]::AddClickHandler($copyDataButton, { 
            try {
                $data = $capturedViewModel.state.CurrentOnboardingData
                if ($data) {
                    $dataText = $data | ConvertTo-Json -Depth 5
                    [QuickActions]::CopyToClipboard($dataText)
                } else {
                    Write-Warning "No data available to copy"
                }
            } catch {
                Write-Host "ERROR: [QUICK] Failed to copy data: $($_.Exception.Message)" -ForegroundColor Red
            }
        }.GetNewClosure())
        $quickButtonPanel.Children.Add($copyDataButton)

        $quickActionsPanel.Children.Add($quickButtonPanel)

        # JERA-styled Final Actions section
        $actionsSection = [UIFactory]::CreateBorderedPanel("Final Actions", "0")
        $actionsPanel = $actionsSection.Panel

        # Create horizontal button panel
        $buttonPanel = [UIFactory]::CreateStackPanel("Horizontal", "0,15,0,0")

        $commentButton = [UIFactory]::CreateButton("$($script:Icons.Comment) Post Comment to Jira", "secondary", "0,0,10,0")
        [UIFactory]::AddClickHandler($commentButton, { 
            $capturedViewModel.PostJiraComment() 
        }.GetNewClosure())
        $buttonPanel.Children.Add($commentButton)

        $finishButton = [UIFactory]::CreateButton("$($script:Icons.Success) Finish", "success", "0")
        $capturedWindow = $this.window
        [UIFactory]::AddClickHandler($finishButton, { 
            # Auto-cleanup cache before closing
            [CacheManager]::Cleanup()
            $capturedWindow.Close() 
        }.GetNewClosure())
        $buttonPanel.Children.Add($finishButton)

        $actionsPanel.Children.Add($buttonPanel)

        $stepPanel.Children.Add($summarySection.Border)
        $stepPanel.Children.Add($exportSection.Border)
        $stepPanel.Children.Add($quickActionsSection.Border)
        $stepPanel.Children.Add($actionsSection.Border)
        return $stepPanel
    }

    Show() {
        try {
            # Ensure WPF assemblies are loaded
            if (-not ([System.Management.Automation.PSTypeName]'System.Windows.Window').Type) {
                throw "WPF assemblies not properly loaded"
            }
            
            $this.window = New-Object System.Windows.Window
            $this.window.Title = Get-AppConfig -Path "UI.WindowTitle"
            $windowSize = Get-AppConfig -Path "UI.WindowSize"
            $this.window.Width = $windowSize.Width
            $this.window.Height = $windowSize.Height
            $this.window.MinWidth = $windowSize.MinWidth
            $this.window.MinHeight = $windowSize.MinHeight
            $this.window.WindowStartupLocation = 'CenterScreen'
            $this.window.Background = (Get-AppConfig -Path "UI.Colors.Background")

            # Enhanced StackPanel Layout with JERA professional styling
            $this.mainPanel = New-Object System.Windows.Controls.StackPanel
            $this.window.Content = $this.mainPanel

            # JERA Professional Header
            $headerBorder = New-Object System.Windows.Controls.Border
            $headerBorder.Padding = "20,12"
            $headerBorder.Background = (Get-AppConfig -Path "UI.Colors.HeaderBackground")  # JERA Navy
            
            $headerStack = New-Object System.Windows.Controls.StackPanel
            $headerStack.Orientation = "Horizontal"
            
            # Status text with JERA branding
            $this.statusText = New-Object System.Windows.Controls.TextBlock
            $this.statusText.Text = "$($script:Icons.Wizard) " + $this.viewModel.state.StatusMessage
            $this.statusText.VerticalAlignment = "Center"
            $this.statusText.Foreground = "White"
            $this.statusText.FontWeight = "SemiBold"
            $this.statusText.FontSize = (Get-AppConfig -Path "UI.Typography.LabelFontSize")
            $headerStack.Children.Add($this.statusText)
            
            # Spacer
            $spacer = New-Object System.Windows.Controls.TextBlock
            $spacer.Width = 30
            $headerStack.Children.Add($spacer)
            
            # Debug toggle with JERA styling
            $debugCombo = New-Object System.Windows.Controls.ComboBox
            $debugCombo.Items.Add("Debug: ON") | Out-Null
            $debugCombo.Items.Add("Debug: OFF") | Out-Null
            $debugCombo.SelectedIndex = 0
            $debugCombo.Width = 120
            $debugCombo.Margin = "0,3,8,3"
            $debugCombo.FontSize = (Get-AppConfig -Path "UI.Typography.BodyFontSize")
            $headerStack.Children.Add($debugCombo)
            
            $headerBorder.Child = $headerStack
            $this.mainPanel.Children.Add($headerBorder)

            # Enhanced Content Area with JERA styling
            $contentBorder = New-Object System.Windows.Controls.Border
            $contentBorder.Background = (Get-AppConfig -Path "UI.Colors.ContentBackground")  # Pure white
            $contentBorder.BorderBrush = (Get-AppConfig -Path "UI.Colors.BorderColor")
            $contentBorder.BorderThickness = "1,0,1,0"
            $contentBorder.Padding = "0"
            
            $scrollViewer = New-Object System.Windows.Controls.ScrollViewer
            $scrollViewer.VerticalScrollBarVisibility = 'Auto'
            $scrollViewer.HorizontalScrollBarVisibility = 'Disabled'
            $scrollViewer.Height = (Get-AppConfig -Path "UI.Layout.ScrollViewerHeight")
            $scrollViewer.Background = (Get-AppConfig -Path "UI.Colors.Background")

            $this.contentArea = New-Object System.Windows.Controls.ContentControl
            $scrollViewer.Content = $this.contentArea
            $contentBorder.Child = $scrollViewer
            $this.mainPanel.Children.Add($contentBorder)

            # Enhanced Navigation Panel with JERA styling
            $navBorder = New-Object System.Windows.Controls.Border
            $navBorder.Background = (Get-AppConfig -Path "UI.Colors.Background")
            $navBorder.BorderBrush = (Get-AppConfig -Path "UI.Colors.BorderColor")
            $navBorder.BorderThickness = "0,1,0,0"
            $navBorder.Padding = "20,15"
            
            $this.navPanel = New-Object System.Windows.Controls.StackPanel
            $this.navPanel.Orientation = 'Horizontal'
            $this.navPanel.HorizontalAlignment = 'Right'

            $this.prevButton = New-Object System.Windows.Controls.Button
            $this.prevButton.Content = "$($script:Icons.Steps) Previous"
            $this.prevButton.Width = 140
            $this.prevButton.Height = (Get-AppConfig -Path "UI.Spacing.ButtonHeight")
            $this.prevButton.Margin = "0,0,15,0"
            $this.prevButton.Background = (Get-AppConfig -Path "UI.Colors.Secondary")       # JERA Gray
            $this.prevButton.Foreground = "White"
            $this.prevButton.FontWeight = "SemiBold"
            $this.prevButton.FontSize = (Get-AppConfig -Path "UI.Typography.BodyFontSize")
            $this.prevButton.BorderThickness = 0
            $this.prevButton.Padding = "15,8"
            
            $this.nextButton = New-Object System.Windows.Controls.Button
            $this.nextButton.Content = "$($script:Icons.Steps) Next"
            $this.nextButton.Width = 140
            $this.nextButton.Height = (Get-AppConfig -Path "UI.Spacing.ButtonHeight")
            $this.nextButton.Background = (Get-AppConfig -Path "UI.Colors.Primary")         # JERA Navy
            $this.nextButton.Foreground = "White"
            $this.nextButton.FontWeight = "SemiBold"
            $this.nextButton.FontSize = (Get-AppConfig -Path "UI.Typography.BodyFontSize")
            $this.nextButton.BorderThickness = 0
            $this.nextButton.Padding = "15,8"
            
            $this.navPanel.Children.Add($this.prevButton)
            $this.navPanel.Children.Add($this.nextButton)
            $navBorder.Child = $this.navPanel
            $this.mainPanel.Children.Add($navBorder)

            # JERA Professional Status Bar
            $this.statusBar = New-Object System.Windows.Controls.Border
            $this.statusBar.Height = (Get-AppConfig -Path "UI.Spacing.StatusBarHeight")
            $this.statusBar.Background = (Get-AppConfig -Path "UI.Colors.ContentBackground")  # Pure white
            $this.statusBar.BorderBrush = (Get-AppConfig -Path "UI.Colors.BorderColor")
            $this.statusBar.BorderThickness = "0,1,0,0"
            $this.statusBar.Padding = "20,8"
            
            $statusStack = New-Object System.Windows.Controls.StackPanel
            $statusStack.Orientation = "Horizontal"
            
            $progressInfo = New-Object System.Windows.Controls.TextBlock
            $progressInfo.Text = "$($script:Icons.Wizard) JERA Global Markets - Onboarding System v3.0"
            $progressInfo.FontSize = (Get-AppConfig -Path "UI.Typography.StatusFontSize")
            $progressInfo.VerticalAlignment = "Center"
            $progressInfo.Foreground = (Get-AppConfig -Path "UI.Colors.TextSecondary")       # JERA Gray
            $progressInfo.FontWeight = "Medium"
            $statusStack.Children.Add($progressInfo)
            
            $this.statusBar.Child = $statusStack
            $this.mainPanel.Children.Add($this.statusBar)

            # Wire up events with modern styling
            $capturedViewModel = $this.viewModel
            $this.prevButton.add_Click({ 
                Write-Host "DEBUG: [UI EVENT] Previous button clicked" -ForegroundColor Yellow
                $capturedViewModel.GoToPreviousStep() 
                Write-Host "DEBUG: [UI EVENT] Previous button handler completed" -ForegroundColor Yellow
            }.GetNewClosure())
            
            $this.nextButton.add_Click({ 
                Write-Host "DEBUG: [UI EVENT] Next button clicked (content: '$($this.nextButton.Content)')" -ForegroundColor Yellow
                $capturedViewModel.GoToNextStep() 
                Write-Host "DEBUG: [UI EVENT] Next button handler completed" -ForegroundColor Yellow
            }.GetNewClosure())
            
            # Wire up debug toggle event
            $debugCombo.add_SelectionChanged({
                Write-Host "DEBUG: [UI EVENT] Debug combo selection changed to index: $($debugCombo.SelectedIndex)" -ForegroundColor Yellow
                $isDebugOn = ($debugCombo.SelectedIndex -eq 0)
                $capturedViewModel.log.SetDebugMode($isDebugOn)
                Write-Host "DEBUG: [UI EVENT] Debug mode changed to: $(if ($isDebugOn) { 'ON' } else { 'OFF' })" -ForegroundColor $(if ($isDebugOn) { 'Green' } else { 'Red' })
            }.GetNewClosure())
            $capturedView = $this
            $capturedViewModel = $this.viewModel
            $capturedContentArea = $this.contentArea
            $capturedPrevButton = $this.prevButton  
            $capturedNextButton = $this.nextButton
            $capturedStatusText = $this.statusText
            $capturedMainPanel = $this.mainPanel
            $capturedWindow = $this.window
            
            # PHASE 3: Comprehensive UI validation
            $validationResult = $this.ValidateUIReferences($capturedContentArea, $capturedPrevButton, $capturedNextButton, $capturedStatusText, $capturedMainPanel)
            if (-not $validationResult) {
                Write-Host "CRITICAL ERROR: UI validation failed! Application may not work correctly." -ForegroundColor Red
                # Continue anyway but with warnings
            }

            # Manual binding for status message - FIXED to use captured variables
            $this.viewModel.state.AddPropertyChangedHandler({
                param($propertyName)
                if ($propertyName -eq 'StatusMessage') {
                    try {
                        # Capture variables for nested closure
                        $nestedCapturedWindow = $capturedWindow
                        $nestedCapturedStatusText = $capturedStatusText
                        $nestedCapturedViewModel = $capturedViewModel
                        
                        if ($nestedCapturedWindow.Dispatcher) {
                            $nestedCapturedWindow.Dispatcher.InvokeAsync({
                                try {
                                    $nestedCapturedStatusText.Text = $nestedCapturedViewModel.state.StatusMessage
                                    Write-Host "DEBUG: Status updated to: $($nestedCapturedViewModel.state.StatusMessage)" -ForegroundColor Cyan
                                } catch {
                                    Write-Host "ERROR: Status update failed: $($_.Exception.Message)" -ForegroundColor Red
                                }
                            }.GetNewClosure())
                        }
                    } catch {
                        Write-Host "ERROR: Status handler failed: $($_.Exception.Message)" -ForegroundColor Red
                    }
                }
            }.GetNewClosure())

            # Logic to switch wizard steps based on state.CurrentStepIndex
            $this.viewModel.state.AddPropertyChangedHandler({
                param($propertyName)
                if ($propertyName -eq 'CurrentStepIndex') {
                    try {
                        $stepIndex = $capturedViewModel.state.CurrentStepIndex
                        Write-Host "DEBUG: UI updating to step $stepIndex" -ForegroundColor Magenta
                        
                        # Capture variables for nested closure
                        $nestedCapturedWindow = $capturedWindow
                        $nestedCapturedContentArea = $capturedContentArea
                        $nestedCapturedPrevButton = $capturedPrevButton
                        $nestedCapturedNextButton = $capturedNextButton
                        $nestedCapturedView = $capturedView
                        $nestedStepIndex = $stepIndex
                        
                        if ($nestedCapturedWindow.Dispatcher) {
                            $nestedCapturedWindow.Dispatcher.InvokeAsync({
                                try {
                                    Write-Host "DEBUG: Dispatcher executing step update to $nestedStepIndex" -ForegroundColor Magenta
                                    Write-Host "DEBUG: nestedCapturedContentArea in closure null? $($nestedCapturedContentArea -eq $null)" -ForegroundColor Gray
                                    
                                    # PHASE 3: Enhanced validation and error handling
                                    if ($nestedCapturedContentArea -eq $null) {
                                        Write-Host "ERROR: nestedCapturedContentArea is null - cannot update UI!" -ForegroundColor Red
                                        return
                                    }
                                    
                                    if ($nestedCapturedView -eq $null) {
                                        Write-Host "ERROR: nestedCapturedView is null - cannot create content!" -ForegroundColor Red
                                        return
                                    }
                                    
                                    switch ($nestedStepIndex) {
                                        0 { 
                                            Write-Host "DEBUG: Creating step 0 content" -ForegroundColor Magenta
                                            $newContent = $null
                                            try {
                                                $newContent = $nestedCapturedView.CreateWizardStep0()
                                                if ($newContent -eq $null) {
                                                    Write-Host "ERROR: CreateWizardStep0 returned null content!" -ForegroundColor Red
                                                    $newContent = $nestedCapturedView.CreateErrorStep("Step 0 content creation returned null")
                                                } else {
                                                    Write-Host "DEBUG: Step 0 content created successfully, type: $($newContent.GetType().Name)" -ForegroundColor Green
                                                }
                                            } catch {
                                                Write-Host "ERROR: Failed to create step 0 content: $($_.Exception.Message)" -ForegroundColor Red
                                                $newContent = $nestedCapturedView.CreateErrorStep("Step 0 creation failed: $($_.Exception.Message)")
                                            }
                                            
                                            Write-Host "DEBUG: Assigning step 0 content to nestedCapturedContentArea" -ForegroundColor Magenta
                                            $nestedCapturedContentArea.Content = $newContent
                                            Write-Host "DEBUG: Step 0 content assignment completed" -ForegroundColor Green
                                        }
                                        1 { 
                                            Write-Host "DEBUG: Creating step 1 content" -ForegroundColor Magenta
                                            $newContent = $null
                                            try {
                                                $newContent = $nestedCapturedView.CreateWizardStep1()
                                                if ($newContent -eq $null) {
                                                    Write-Host "ERROR: CreateWizardStep1 returned null content!" -ForegroundColor Red
                                                    $newContent = $nestedCapturedView.CreateErrorStep("Step 1 content creation returned null")
                                                } else {
                                                    Write-Host "DEBUG: Step 1 content created successfully, type: $($newContent.GetType().Name)" -ForegroundColor Green
                                                }
                                            } catch {
                                                Write-Host "ERROR: Failed to create step 1 content: $($_.Exception.Message)" -ForegroundColor Red
                                                $newContent = $nestedCapturedView.CreateErrorStep("Step 1 creation failed: $($_.Exception.Message)")
                                            }
                                            
                                            Write-Host "DEBUG: Assigning step 1 content to nestedCapturedContentArea" -ForegroundColor Magenta
                                            $nestedCapturedContentArea.Content = $newContent
                                            Write-Host "DEBUG: Step 1 content assignment completed" -ForegroundColor Green
                                        }
                                        2 { 
                                            Write-Host "DEBUG: Creating step 2 content" -ForegroundColor Magenta
                                            $newContent = $null
                                            try {
                                                $newContent = $nestedCapturedView.CreateWizardStep2()
                                                if ($newContent -eq $null) {
                                                    Write-Host "ERROR: CreateWizardStep2 returned null content!" -ForegroundColor Red
                                                    $newContent = $nestedCapturedView.CreateErrorStep("Step 2 content creation returned null")
                                                } else {
                                                    Write-Host "DEBUG: Step 2 content created successfully, type: $($newContent.GetType().Name)" -ForegroundColor Green
                                                }
                                            } catch {
                                                Write-Host "ERROR: Failed to create step 2 content: $($_.Exception.Message)" -ForegroundColor Red
                                                $newContent = $nestedCapturedView.CreateErrorStep("Step 2 creation failed: $($_.Exception.Message)")
                                            }
                                            
                                            Write-Host "DEBUG: Assigning step 2 content to nestedCapturedContentArea" -ForegroundColor Magenta
                                            $nestedCapturedContentArea.Content = $newContent
                                            Write-Host "DEBUG: Step 2 content assignment completed" -ForegroundColor Green
                                        }
                                        3 { 
                                            Write-Host "DEBUG: Creating step 3 content" -ForegroundColor Magenta
                                            $newContent = $null
                                            try {
                                                $newContent = $nestedCapturedView.CreateWizardStep3()
                                                if ($newContent -eq $null) {
                                                    Write-Host "ERROR: CreateWizardStep3 returned null content!" -ForegroundColor Red
                                                    $newContent = $nestedCapturedView.CreateErrorStep("Step 3 content creation returned null")
                                                } else {
                                                    Write-Host "DEBUG: Step 3 content created successfully, type: $($newContent.GetType().Name)" -ForegroundColor Green
                                                }
                                            } catch {
                                                Write-Host "ERROR: Failed to create step 3 content: $($_.Exception.Message)" -ForegroundColor Red
                                                $newContent = $nestedCapturedView.CreateErrorStep("Step 3 creation failed: $($_.Exception.Message)")
                                            }
                                            
                                            Write-Host "DEBUG: Assigning step 3 content to nestedCapturedContentArea" -ForegroundColor Magenta
                                            $nestedCapturedContentArea.Content = $newContent
                                            Write-Host "DEBUG: Step 3 content assignment completed" -ForegroundColor Green
                                        }
                                        default {
                                            Write-Host "ERROR: Invalid step index: $nestedStepIndex" -ForegroundColor Red
                                            return
                                        }
                                    }
                                    
                                    # Update navigation buttons using nested captured references with enhanced validation
                                    Write-Host "DEBUG: Updating navigation buttons" -ForegroundColor Magenta
                                    try {
                                        if ($nestedCapturedPrevButton -eq $null) {
                                            Write-Host "ERROR: nestedCapturedPrevButton is null!" -ForegroundColor Red
                                        } else {
                                            $nestedCapturedPrevButton.IsEnabled = ($nestedStepIndex -gt 0)
                                            Write-Host "DEBUG: Previous button enabled: $($nestedStepIndex -gt 0)" -ForegroundColor Gray
                                        }
                                        
                                        if ($nestedCapturedNextButton -eq $null) {
                                            Write-Host "ERROR: nestedCapturedNextButton is null!" -ForegroundColor Red
                                        } else {
                                            $nestedCapturedNextButton.Content = if ($nestedStepIndex -eq 3) { "Finish" } else { "Next" }
                                            Write-Host "DEBUG: Next button content: $($nestedCapturedNextButton.Content)" -ForegroundColor Gray
                                        }
                                        
                                        Write-Host "DEBUG: Navigation buttons updated successfully" -ForegroundColor Green
                                    } catch {
                                        Write-Host "ERROR: Failed to update navigation buttons: $($_.Exception.Message)" -ForegroundColor Red
                                    }
                                    
                                    Write-Host "DEBUG: UI step update completed for step $nestedStepIndex" -ForegroundColor Green
                                } catch {
                                    Write-Host "ERROR: Content assignment failed: $($_.Exception.Message)" -ForegroundColor Red
                                    Write-Host "ERROR: Stack trace: $($_.ScriptStackTrace)" -ForegroundColor Red
                                }
                            }.GetNewClosure())
                        }
                    } catch {
                        Write-Host "ERROR: Step change handler failed: $($_.Exception.Message)" -ForegroundColor Red
                        Write-Host "ERROR: Stack trace: $($_.ScriptStackTrace)" -ForegroundColor Red
                    }
                }
            }.GetNewClosure())

            # Handle IsBusy state - FIXED to use captured variables
            $this.viewModel.state.AddPropertyChangedHandler({
                param($propertyName)
                if ($propertyName -eq 'IsBusy') {
                    try {
                        # Capture variables for nested closure
                        $nestedCapturedWindow = $capturedWindow
                        $nestedCapturedMainPanel = $capturedMainPanel
                        $nestedCapturedViewModel = $capturedViewModel
                        
                        if ($nestedCapturedWindow.Dispatcher) {
                            $nestedCapturedWindow.Dispatcher.InvokeAsync({
                                try {
                                    $nestedCapturedMainPanel.IsEnabled = -not $nestedCapturedViewModel.state.IsBusy
                                    Write-Host "DEBUG: IsBusy state updated: $($nestedCapturedViewModel.state.IsBusy)" -ForegroundColor Cyan
                                } catch {
                                    Write-Host "ERROR: IsBusy update failed: $($_.Exception.Message)" -ForegroundColor Red
                                }
                            }.GetNewClosure())
                        }
                    } catch {
                        Write-Host "ERROR: IsBusy handler failed: $($_.Exception.Message)" -ForegroundColor Red
                    }
                }
            }.GetNewClosure())

            # Initial content load - PHASE 4: Use class properties directly
            Write-Host "DEBUG: Loading initial content using class properties..." -ForegroundColor Yellow
            $this.contentArea.Content = $this.CreateWizardStep0()
            $this.prevButton.IsEnabled = $false
            Write-Host "DEBUG: Initial content loaded successfully" -ForegroundColor Green

            if (-not $this.window) {
                Write-Error "Window object is null - cannot show dialog"
                return
            }
            
            # Add window event logging
            $this.window.add_Loaded({
                Write-Host "DEBUG: [WINDOW EVENT] Window loaded" -ForegroundColor Blue
            })
            
            $this.window.add_Activated({
                Write-Host "DEBUG: [WINDOW EVENT] Window activated/focused" -ForegroundColor Blue
            })
            
            $this.window.add_Deactivated({
                Write-Host "DEBUG: [WINDOW EVENT] Window deactivated/lost focus" -ForegroundColor Blue
            })
            
            $this.window.add_Closing({
                Write-Host "DEBUG: [WINDOW EVENT] Window closing" -ForegroundColor Blue
            })
            
            $this.window.add_Closed({
                Write-Host "DEBUG: [WINDOW EVENT] Window closed" -ForegroundColor Blue
            })
            
            Write-Host "DEBUG: [WINDOW EVENT] About to show window dialog" -ForegroundColor Blue
            $this.window.ShowDialog() | Out-Null
            Write-Host "DEBUG: [WINDOW EVENT] Window dialog completed" -ForegroundColor Blue
        } catch {
            Write-Error "Failed to create or show window: $($_.Exception.Message)"
            throw
        }
    }
}

# SECTION 5: SCRIPT EXECUTION

try {
    # 1. Instantiate all services and state
    Write-Host "Initializing services..." -ForegroundColor Green
    
    # Initialize Phase 3 advanced systems
    Write-Host "Initializing advanced systems..." -ForegroundColor Cyan
    [AdvancedLogger]::Initialize()
    [UserPreferencesManager]::Initialize()
    [SessionManager]::Initialize()
    
    # Apply user preferences to system configuration
    $logLevel = [UserPreferencesManager]::GetPreference("LogLevel")
    if ($logLevel) {
        [AdvancedLogger]::SetLogLevel($logLevel)
    }
    
    $cacheEnabled = [UserPreferencesManager]::GetPreference("CacheEnabled")
    if (-not $cacheEnabled) {
        [CacheManager]::Clear()
        Write-Host "INFO: [PREFS] Caching disabled by user preference" -ForegroundColor Yellow
    }
    
    [AdvancedLogger]::Info("INIT", "Phase 3 advanced systems initialized")
    
    $logService = [LoggingService]::new()
    $configService = [ConfigurationService]::new()
    $jiraService = [JiraService]::new($logService)
    $adService = [ActiveDirectoryService]::new($true, $logService) # Start in simulation mode
    $appState = [AppState]::new()

    # 2. Instantiate the ViewModel, injecting all dependencies
    Write-Host "Creating ViewModel..." -ForegroundColor Green
    $viewModel = [WizardViewModel]::new($logService, $configService, $jiraService, $adService, $appState)

    # 3. Instantiate the View, injecting the ViewModel
    Write-Host "Creating View..." -ForegroundColor Green
    $view = [WizardView]::new($viewModel)

    # 4. Link View back to ViewModel (for dispatcher access)
    Write-Host "Linking View and ViewModel..." -ForegroundColor Green
    $viewModel.RegisterView($view)

    # 5. Start the application
    Write-Host "Starting application..." -ForegroundColor Green
    $logService.Info("Application starting...")
    $view.Show()
    $logService.Info("Application closed.")
    
} catch {
    Write-Error "Application failed to start: $($_.Exception.Message)"
    Write-Error "Stack trace: $($_.ScriptStackTrace)"
    if ($logService) {
        $logService.Error("Application startup failed", $_.Exception)
    }
    Read-Host "Press Enter to exit"
}
