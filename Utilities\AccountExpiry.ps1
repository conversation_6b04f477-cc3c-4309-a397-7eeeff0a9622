# Initialize logging
$timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
$logFile = "C:\temp\AccountExpiry_$timestamp.log"

# Logging function
function Write-Log {
    param(
        [string]$Level,
        [string]$Message
    )
    $logEntry = "$(Get-Date -Format 'yyyy-MM-dd HH:mm:ss') [$Level] $Message"
    Write-Output $logEntry
    Add-Content -Path $logFile -Value $logEntry -ErrorAction SilentlyContinue
}

Write-Log "INFO" "Account Expiry Script Started"

# Ensure temp directory exists
try {
    if (!(Test-Path "C:\temp")) {
        New-Item -ItemType Directory -Path "C:\temp" -Force | Out-Null
        Write-Log "INFO" "Created temp directory"
    }
} catch {
    Write-Log "ERROR" "Failed to create temp directory: $($_.Exception.Message)"
    exit 1
}

# Import ActiveDirectory module
try {
    Import-Module ActiveDirectory -ErrorAction Stop
    Write-Log "INFO" "ActiveDirectory module loaded successfully"
} catch {
    Write-Log "ERROR" "Failed to load ActiveDirectory module: $($_.Exception.Message)"
    exit 1
}

# Set the email parameters
$to = "<EMAIL>"
$from = "IT_Operations<<EMAIL>>"
$subject = "Expiring AD Accounts"
$expirationEndDate= (Get-Date).AddDays(45)
$expirationStartDate = (Get-Date).AddDays(1)
$AttachFile = "C:\temp\export-expiryandmanager.csv"
$body = "FYI on attached file, please check and notify relevant line managers / senior managers on account expiry dates."

Write-Log "INFO" "Configuration loaded - Checking accounts expiring between $expirationStartDate and $expirationEndDate"

# Query AD for expiring accounts
try {
    Write-Log "INFO" "Querying Active Directory for expiring accounts..."
    $users = Get-ADUser -filter {Enabled -eq $true -and PasswordNeverExpires -eq $false -and (AccountExpirationDate -ge $expirationStartDate) -and (AccountExpirationDate -le $expirationEndDate)} -Properties displayname, distinguishedName, city, company, department, EmailAddress, Manager, AccountExpirationDate -ErrorAction Stop
    
    if ($users) {
        Write-Log "INFO" "Found $($users.Count) expiring accounts"
    } else {
        Write-Log "INFO" "No expiring accounts found"
        exit 0
    }
} catch {
    Write-Log "ERROR" "AD query failed: $($_.Exception.Message)"
    exit 1
}

# Export to CSV
try {
    Write-Log "INFO" "Exporting data to CSV file: $AttachFile"
    $users | Select-Object displayname, distinguishedName, city, company, department, EmailAddress, Manager, AccountExpirationDate | Export-Csv -Path $AttachFile -NoTypeInformation -ErrorAction Stop
    
    # Verify file was created and has content
    if (Test-Path $AttachFile) {
        $fileSize = (Get-Item $AttachFile).Length
        Write-Log "INFO" "CSV export completed successfully - File size: $fileSize bytes"
    } else {
        throw "CSV file was not created"
    }
} catch {
    Write-Log "ERROR" "CSV export failed: $($_.Exception.Message)"
    exit 1
}

# Test SMTP connectivity before sending email
try {
    Write-Log "INFO" "Testing SMTP connectivity to smtp.jeragm.com"
    $tcpClient = New-Object System.Net.Sockets.TcpClient
    $tcpClient.Connect("smtp.jeragm.com", 25)
    $tcpClient.Close()
    Write-Log "INFO" "SMTP connectivity test successful"
} catch {
    Write-Log "WARNING" "SMTP connectivity test failed: $($_.Exception.Message)"
}

# Send the email
try {
    Write-Log "INFO" "Sending email notification to $to"
    Send-MailMessage -To $to -From $from -Subject $subject -Body $body -SmtpServer smtp.jeragm.com -Attachments $AttachFile -ErrorAction Stop
    Write-Log "INFO" "Email sent successfully"
} catch {
    Write-Log "ERROR" "Email sending failed: $($_.Exception.Message)"
    exit 1
}

Write-Log "INFO" "Account Expiry Script Completed Successfully"
