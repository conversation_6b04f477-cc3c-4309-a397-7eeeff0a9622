@{
    # JML Admin Account Management System Configuration
    # Version: 1.12
    # Security Level: Enhanced
    
    # =============================================================================
    # GENERAL SETTINGS
    # =============================================================================
    
    # Script behavior settings
    ScriptSettings = @{
        # Default domain for user accounts
        DefaultDomain = "jeragm.com"
        
        # Maximum retry attempts for operations
        MaxRetryAttempts = 3
        
        # Base delay for exponential backoff (seconds)
        BaseRetryDelay = 2
        
        # Maximum delay for exponential backoff (seconds)
        MaxRetryDelay = 30
        
        # Operation timeout in seconds
        OperationTimeout = 300
        
        # Enable progress indicators
        ShowProgress = $true
        
        # Enable detailed audit logging
        EnableAuditLogging = $true
    }
    
    # =============================================================================
    # LOGGING CONFIGURATION
    # =============================================================================
    
    Logging = @{
        # Log directory path (supports environment variables)
        LogDirectory = "C:\Temp\Scripts\Desktop Support\Logs"
        
        # Log file retention in days
        LogRetentionDays = 30
        
        # Maximum log file size in MB
        MaxLogFileSizeMB = 10
        
        # Enable log file rotation
        EnableLogRotation = $true
        
        # Log levels to write to file
        FileLogLevels = @('DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL')
        
        # Log levels to write to console
        ConsoleLogLevels = @('INFO', 'WARNING', 'ERROR', 'CRITICAL')
        
        # Enable sensitive data redaction
        EnableDataRedaction = $true
        
        # Data redaction settings
        DataRedaction = @{
            # Redact UPNs (user***@domain.com)
            RedactUPNs = $true
            
            # Redact email addresses
            RedactEmailAddresses = $true
            
            # Redact distinguished names (hash or complete redaction)
            RedactDistinguishedNames = $true
            
            # Redact server names/URLs
            RedactServerNames = $true
            
            # Hash algorithm for sensitive data (SHA256, SHA1, MD5)
            HashAlgorithm = "SHA256"
            
            # Salt for hashing (change this for your environment)
            HashSalt = "AdminAccountScript2024"
        }
    }
    
    # =============================================================================
    # ACTIVE DIRECTORY CONFIGURATION
    # =============================================================================
    
    ActiveDirectory = @{
        # OU mappings for admin accounts
        OUMappings = @{
            "Singapore" = "OU=Singapore,OU=Admins,OU=Accounts,OU=JeraGM,DC=jeragm,DC=com"
            "United Kingdom" = "OU=London,OU=Admins,OU=Accounts,OU=JeraGM,DC=jeragm,DC=com"
            "London" = "OU=London,OU=Admins,OU=Accounts,OU=JeraGM,DC=jeragm,DC=com"
        }
        
        # Default OU if mapping not found
        DefaultOU = "OU=London,OU=Admins,OU=Accounts,OU=JeraGM,DC=jeragm,DC=com"
        
        # AD query optimization settings
        QueryOptimization = @{
            # Properties to retrieve for user queries
            UserProperties = @(
                'GivenName', 'Surname', 'DisplayName', 'SamAccountName',
                'UserPrincipalName', 'EmailAddress', 'Enabled', 'DistinguishedName'
            )
            
            # Maximum results for AD queries
            MaxResults = 1000
            
            # Query timeout in seconds
            QueryTimeout = 30
            
            # Enable result caching
            EnableCaching = $true
            
            # Cache expiration in minutes
            CacheExpirationMinutes = 15
        }
        
        # Password policy settings
        PasswordPolicy = @{
            # Password length
            MinLength = 12
            
            # Number of special characters required
            MinSpecialChars = 2
            
            # Enable password complexity
            RequireComplexity = $true
            
            # Password expiration settings
            PasswordNeverExpires = $false
            
            # Require password change at next logon
            ChangePasswordAtLogon = $true
        }
    }
    
    # =============================================================================
    # EMAIL CONFIGURATION
    # =============================================================================
    
    Email = @{
        # SMTP server settings
        SmtpServer = "smtp.jeragm.com"
        
        # SMTP port (25, 587, 465)
        SmtpPort = 25
        
        # Use SSL/TLS
        UseSSL = $false
        
        # Default sender address
        DefaultFrom = "<EMAIL>"
        
        # Support team email
        SupportEmail = "<EMAIL>"
        
        # Email retry settings
        RetrySettings = @{
            MaxAttempts = 3
            BaseDelay = 2
            MaxDelay = 16
            EnableExponentialBackoff = $true
        }
        
        # Email timeout in seconds
        Timeout = 30
        
        # Enable email notifications
        EnableNotifications = $true
    }
    
    # =============================================================================
    # JIRA CONFIGURATION
    # =============================================================================
    
    Jira = @{
        # Jira server URL (placeholder - will be replaced with [JIRA-INSTANCE] in logs)
        ServerUrl = "https://jeragm.atlassian.net"
        
        # Expected work types for validation
        ExpectedWorkTypes = @{
            CreateAdmin = "Service Request with Approvals"
            DeleteAdmin = "Service Request with Approvals"
            ResetAdmin = "Service Request with Approvals"
        }
        
        # Expected request types for validation
        ExpectedRequestTypes = @{
            CreateAdmin = "JERAGM Onboarding Request"
            DeleteAdmin = "Remove Admin Account"
            ResetAdmin = "Reset Admin Account"
        }
        
        # Custom field IDs for data extraction
        CustomFields = @{
            FirstName = "customfield_10304"
            LastName = "customfield_10305"
            OfficeLocation = "customfield_10115"
            ITAdminAccount = "customfield_10453"
            Department = "customfield_10120"
            JobTitle = "customfield_10238"
            ModelAccount = "customfield_10343"
        }
        
        # API settings
        ApiSettings = @{
            # Request timeout in seconds
            Timeout = 60
            
            # Retry settings
            RetrySettings = @{
                MaxAttempts = 5
                BaseDelay = 1
                MaxDelay = 32
                EnableExponentialBackoff = $true
            }
            
            # Rate limiting
            RateLimit = @{
                RequestsPerMinute = 60
                EnableRateLimit = $true
            }
        }
        
        # Attachment settings
        AttachmentSettings = @{
            # Maximum file size in MB
            MaxFileSizeMB = 10
            
            # Allowed file types
            AllowedFileTypes = @('.log', '.txt', '.pdf', '.docx', '.xlsx')
            
            # Enable chunked upload for large files
            EnableChunkedUpload = $true
            
            # Chunk size in KB
            ChunkSizeKB = 1024
        }
        
        # Comment formatting settings
        CommentFormatting = @{
            # Use Atlassian Document Format (ADF) if supported
            PreferADF = $true
            
            # Fallback to wiki markup
            FallbackToWikiMarkup = $true
            
            # Enable rich formatting
            EnableRichFormatting = $true
        }
    }
    
    # =============================================================================
    # SECURITY CONFIGURATION
    # =============================================================================
    
    Security = @{
        # Credential storage settings
        CredentialStorage = @{
            # Primary method: SecretManagement, CredentialManager, EncryptedFile
            PrimaryMethod = "SecretManagement"

            # Fallback methods in order of preference
            FallbackMethods = @("CredentialManager", "EncryptedFile")

            # Secret vault name for SecretManagement
            SecretVaultName = "AdminAccountVault"

            # Vault password timeout in seconds (1 hour = 3600)
            VaultPasswordTimeout = 3600

            # Vault authentication method (Password, None)
            VaultAuthentication = "Password"
            
            # Credential names in vault
            CredentialNames = @{
                JiraUsername = "AdminScript-JiraUsername"
                JiraApiToken = "AdminScript-JiraApiToken"
                JiraServerUrl = "AdminScript-JiraServerUrl"
                SmtpCredentials = "AdminScript-SmtpCredentials"
            }
            
            # Encrypted file settings (fallback)
            EncryptedFileSettings = @{
                FilePath = ".\Modules\SecureCredentials.xml"
                UseUserScope = $true
                EnableCompression = $true
            }
        }
        
        # Input validation settings
        InputValidation = @{
            # Enable strict validation
            EnableStrictValidation = $true
            
            # Maximum input length
            MaxInputLength = 256
            
            # Allowed characters regex patterns
            AllowedPatterns = @{
                UPN = '^[\w][\w\.-]{1,62}[\w]@[\w][\w-]{1,62}\.[a-zA-Z]{2,}$'
                SamAccountName = '^[a-zA-Z0-9._-]{1,20}$'
                DisplayName = '^[a-zA-Z0-9\s._-]{1,64}$'
            }
            
            # Sanitization settings
            EnableSanitization = $true
            RemoveHtmlTags = $true
            RemoveScriptTags = $true
        }
        
        # Password policy settings for vault passwords
        PasswordPolicy = @{
            # Minimum password length
            MinLength = 12

            # Minimum number of special characters
            MinSpecialChars = 2

            # Require complexity (uppercase, lowercase, digit, special)
            RequireComplexity = $true

            # Enable password strength validation
            EnableStrengthValidation = $true

            # Warn on weak passwords
            WarnOnWeakPasswords = $true
        }

        # Audit trail settings
        AuditTrail = @{
            # Enable comprehensive audit logging
            EnableAuditTrail = $true

            # Log user identity for all operations
            LogUserIdentity = $true

            # Log function parameters (with redaction)
            LogFunctionParameters = $true

            # Log execution context
            LogExecutionContext = $true

            # Audit log retention in days
            AuditRetentionDays = 90

            # Log vault operations
            LogVaultOperations = $true

            # Log credential access attempts
            LogCredentialAccess = $true

            # Log authentication events
            LogAuthenticationEvents = $true
        }

        # Enterprise automation settings
        Automation = @{
            # Enable automation support
            EnableAutomationSupport = $true

            # Default password timeout for automation (in seconds)
            PasswordTimeout = 3600  # 1 hour

            # Interaction policy for automation
            Interaction = "None"

            # Scope for automation vault
            Scope = "CurrentUser"

            # Supported password sources for automation
            SupportedPasswordSources = @("Environment", "File", "Registry", "Pipeline")

            # Default environment variable name for vault password
            DefaultPasswordVariable = "JML_VAULT_PASSWORD"

            # Enable CI/CD pipeline integration
            EnablePipelineIntegration = $true

            # Automation logging settings
            LogAutomationEvents = $true

            # Fail fast in automation mode if credentials unavailable
            FailFastOnCredentialError = $true
        }
    }
    
    # =============================================================================
    # USER EXPERIENCE CONFIGURATION
    # =============================================================================
    
    UserExperience = @{
        # Console output settings
        ConsoleOutput = @{
            # Color scheme
            Colors = @{
                Success = "Green"
                Warning = "Yellow"
                Error = "Red"
                Information = "Cyan"
                Debug = "Gray"
                Progress = "Blue"
            }
            
            # Enable colored output
            EnableColors = $true
            
            # Message formatting
            EnableTimestamps = $true
            EnableLogLevels = $true
            EnableProgressBars = $true
        }
        
        # Progress indicator settings
        ProgressIndicators = @{
            # Enable progress bars
            EnableProgressBars = $true
            
            # Show estimated time remaining
            ShowTimeRemaining = $true
            
            # Show percentage completion
            ShowPercentage = $true
            
            # Update interval in milliseconds
            UpdateInterval = 500
        }
        
        # Input prompts
        InputPrompts = @{
            # Enable input validation feedback
            EnableValidationFeedback = $true
            
            # Show help text for inputs
            ShowHelpText = $true
            
            # Enable auto-completion where possible
            EnableAutoCompletion = $true
        }
    }
}
