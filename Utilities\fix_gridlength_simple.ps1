# Simple direct fix for G<PERSON><PERSON>ength issues
$scriptPath = "OnboardingFromJiraGUI.ps1"
$content = Get-Content $scriptPath -Raw

Write-Host "Applying simple GridLength fix..." -ForegroundColor Yellow

# Replace all GridLength constructor calls with direct enum usage
$content = $content -replace 'New-Object System\.Windows\.GridLength\(1, \$star\)', 'New-Object System.Windows.GridLength(1, "Star")'
$content = $content -replace 'New-Object System\.Windows\.GridLength\(([^,]+), \$star\)', 'New-Object System.Windows.GridLength($1, "Star")'

# Also handle any remaining GridUnitType issues by using string values
$content = $content -replace '\(\[System\.Windows\.GridUnitType\]::(\w+)\)', '"$1"'

$content | Set-Content $scriptPath -Encoding UTF8

Write-Host "Applied simple GridLength fix" -ForegroundColor Green