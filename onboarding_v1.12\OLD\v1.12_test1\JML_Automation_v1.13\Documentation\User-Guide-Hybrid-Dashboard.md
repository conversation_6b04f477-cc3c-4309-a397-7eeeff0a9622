# JML Hybrid Dashboard - User Guide

## Welcome to the New Interface

The JML Admin Account Management System now features a modern Hybrid Dashboard interface that provides real-time status information, persistent log viewing, and an improved user experience.

## Interface Overview

### Status Bar (Header)
The top of the screen displays real-time system information:

```
+------------------------------------------------------------------------------+
| JML v1.13 | STATUS: Healthy | AD: UAT | JIRA: Connected | CREDS: Ready | USER: akinje |
+------------------------------------------------------------------------------+
```

**Status Indicators:**
- **STATUS**: Overall system health (Healthy, Warning, Issues, Error)
- **AD**: Active Directory status (UAT, PROD, Available, Unavailable)
- **JIRA**: Jira integration status (Connected, Not Authenticated, Module Missing)
- **CREDS**: Credential system status (Ready, Locked, Setup Needed)
- **USER**: Current logged-in user

### Main Content Area
The center section displays menus, prompts, and operation results based on your current activity.

### Log Viewer (Bottom Panel)
The bottom section shows the last 5 system messages in real-time:

```
+-[ Real-time Log Viewer ]-----------------------------------------------------+
| 13:45:10 [INFO]  System Initialized. Session ID: 12345                      |
| 13:45:12 [DEBUG] AD Health Check: UAT Mode                                  |
| 13:45:12 [OK]    Jira Health Check: Connected                               |
| 13:45:15 [WARN]  User selected 'Create Admin Account'                       |
| 13:45:18 [INFO]  Prompting for mandatory Jira ticket...                     |
+------------------------------------------------------------------------------+
```

## Main Menu

The main menu provides access to all system operations:

```
Available Operations:

  [1] Create Admin Account
  [2] Delete Admin Account  
  [3] Reset Admin Password

  [4] System Information
  [5] Run Setup / Repair Credentials
  [6] Vault Management

  [7] Exit
```

### Menu Options Explained

1. **Create Admin Account**: Creates a new administrative account with proper security groups
2. **Delete Admin Account**: Safely removes an existing admin account
3. **Reset Admin Password**: Generates a new password for an existing admin account
4. **System Information**: Displays detailed system status and configuration
5. **Run Setup / Repair Credentials**: Configure or repair credential storage
6. **Vault Management**: Advanced SecretStore management (when available)
7. **Exit**: Safely close the application

## Responsive Design

The interface automatically adapts to your terminal size:

### Full Mode (Wide Terminals)
- Complete status information in header
- Bordered log viewer with full formatting
- All menu options visible

### Compact Mode (Narrow Terminals)
- Essential status information only
- Simplified log display
- Condensed menu layout

**Tip**: Resize your terminal window to see the interface adapt automatically!

## Security Features

### Auto-Exit Timer
When selecting credential storage methods, the system includes a security timeout:

```
=== CREDENTIAL STORAGE SELECTION ===

Please select your preferred credential storage method:

  [1] SecretManagement (Recommended)
  [2] Encrypted File
  [3] Windows Credential Manager

Auto-exit in: 4:30

Enter your choice (1-3) or 'X' to exit: _
```

The system will automatically exit after 5 minutes of inactivity to protect sensitive operations.

## Status Indicators Guide

### Overall System Status
- **Healthy**: All systems operational
- **Warning**: Minor issues detected
- **Issues**: Multiple problems requiring attention
- **Error**: Critical system problems

### Active Directory Status
- **PROD**: Connected to production environment
- **UAT**: Connected to test/development environment
- **Available**: Module loaded and ready
- **Unavailable**: Module not available or connection failed

### Jira Integration Status
- **Connected**: Successfully authenticated and ready
- **Not Authenticated**: Module available but not logged in
- **Module Missing**: JiraPS module not installed
- **Error**: Connection or authentication problems

### Credential System Status
- **Ready**: Credentials configured and accessible
- **Locked**: Vault is locked and requires authentication
- **Setup Needed**: Initial credential configuration required
- **Error**: Credential system problems

## Real-time Log Messages

The log viewer shows different types of messages:

- **[INFO]**: General information messages
- **[DEBUG]**: Detailed diagnostic information
- **[OK]**: Successful operations
- **[WARN]**: Warnings that don't stop operations
- **[ERROR]**: Error conditions requiring attention

## Common Workflows

### Creating an Admin Account

1. Select option **[1] Create Admin Account** from main menu
2. Enter Jira ticket key when prompted (optional but recommended)
3. Provide required account information
4. Review confirmation details
5. Confirm operation
6. Monitor progress in log viewer
7. Review results summary

### Setting Up Credentials (First Time)

1. Select option **[5] Run Setup / Repair Credentials**
2. Choose credential storage method:
   - **SecretManagement**: Most secure, requires master password
   - **Encrypted File**: Good security, user-specific encryption
   - **Windows Credential Manager**: Integrated with Windows
3. Follow setup prompts for your chosen method
4. Test credential access
5. Return to main menu when complete

### Troubleshooting Connection Issues

1. Check status bar for specific service issues
2. Select option **[4] System Information** for detailed diagnostics
3. Review log viewer for error messages
4. Use option **[5] Run Setup / Repair Credentials** if credential issues
5. Contact IT support if problems persist

## Keyboard Shortcuts

- **Number Keys (1-7)**: Select menu options
- **Enter**: Confirm selections or continue
- **X**: Exit from most screens
- **Ctrl+C**: Emergency exit (use sparingly)

## Tips for Best Experience

### Terminal Setup
- **Minimum Width**: 80 characters recommended
- **Preferred Width**: 100+ characters for full experience
- **Height**: 25+ lines for optimal log viewing

### Performance
- Status information updates every 5 seconds automatically
- Log messages appear immediately when operations occur
- Terminal resizing is detected automatically

### Security Best Practices
- Don't leave credential selection screen unattended
- Use SecretManagement for highest security
- Regularly update your vault password
- Monitor log messages for security events

## Troubleshooting

### Interface Not Displaying Correctly
1. Ensure terminal supports color output
2. Check terminal character encoding (UTF-8 recommended)
3. Verify terminal size meets minimum requirements
4. Try resizing terminal window

### Status Information Missing
1. Check if required modules are loaded
2. Verify network connectivity for Jira integration
3. Ensure proper permissions for Active Directory access
4. Review credential configuration

### Log Messages Not Updating
1. Verify UIManager service is initialized
2. Check for PowerShell execution policy restrictions
3. Ensure logging system is functioning
4. Restart application if needed

### Performance Issues
1. Status caching reduces frequent updates (normal behavior)
2. Large terminal sizes may impact performance
3. Close other PowerShell sessions if memory constrained
4. Consider using compact mode for better performance

## Getting Help

### Built-in Help
- Select option **[4] System Information** for system diagnostics
- Review log messages for operation details
- Check status bar for service health

### Documentation
- Phase 3 UI/UX Transformation Guide (technical details)
- JML System Administration Guide (general operations)
- PowerShell SecretStore Documentation (credential management)

### Support Contacts
- IT Helpdesk: For general system issues
- Security Team: For credential and access problems
- Development Team: For interface bugs or feature requests

## What's New in This Version

### Enhanced Features
- Real-time status monitoring
- Persistent log viewing
- Responsive design
- Auto-exit security
- Performance optimizations

### Improved User Experience
- Clear visual feedback
- Consistent interface layout
- Better error messaging
- Streamlined workflows

### Technical Improvements
- Service-oriented architecture
- Health check integration
- Caching for better performance
- Graceful fallback mechanisms

---

**Welcome to the future of JML administration!** The new Hybrid Dashboard provides a modern, efficient, and secure way to manage admin accounts while keeping you informed every step of the way.
