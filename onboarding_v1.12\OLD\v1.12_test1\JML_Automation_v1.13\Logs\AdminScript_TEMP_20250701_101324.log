2025-07-01 10:13:24.144 | akinje          | INFO     | JML Automation Script v1.13 starting...
2025-07-01 10:13:24.159 | akinje          | DEBUG    | Script path: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13
2025-07-01 10:13:24.178 | akinje          | DEBUG    | PowerShell version: 7.5.1
2025-07-01 10:13:24.197 | akinje          | DEBUG    | Execution policy: RemoteSigned
2025-07-01 10:13:24.213 | akinje          | DEBUG    | User: akinje
2025-07-01 10:13:24.232 | akinje          | DEBUG    | Computer: JGM-CG7THR3
2025-07-01 10:13:24.288 | akinje          | INFO     | === JML ENVIRONMENT VALIDATION ===
2025-07-01 10:13:24.308 | akinje          | INFO     | Verifying PowerShell Version...
2025-07-01 10:13:24.338 | akinje          | INFO     | === JML ENVIRONMENT VALIDATION ===
2025-07-01 10:13:24.358 | akinje          | INFO     | Verifying PowerShell Version...
2025-07-01 10:13:24.377 | akinje          | INFO     | PowerShell 7+ detected (version 7.5.1). Checking for updates...
2025-07-01 10:13:24.393 | akinje          | INFO     | PowerShell 7+ detected (version 7.5.1)
2025-07-01 10:13:24.413 | akinje          | INFO     | Checking for PowerShell updates online...
2025-07-01 10:13:24.429 | akinje          | DEBUG    | Checking for PowerShell updates online...
2025-07-01 10:13:24.660 | akinje          | INFO     | A newer version of PowerShell (7.5.2) is available.
2025-07-01 10:13:24.685 | akinje          | INFO     | Current version: 7.5.1
2025-07-01 10:13:24.718 | akinje          | INFO     | Download from: https://github.com/PowerShell/PowerShell/releases
2025-07-01 10:13:24.758 | akinje          | INFO     | === CHECKING REQUIRED MODULES ===
2025-07-01 10:13:24.800 | akinje          | INFO     | === CHECKING REQUIRED MODULES ===
2025-07-01 10:13:24.846 | akinje          | INFO     | Checking for required modules and available updates...
2025-07-01 10:13:26.421 | akinje          | INFO     | Checking module: Microsoft.PowerShell.SecretManagement...
2025-07-01 10:13:26.716 | akinje          | INFO     |   Found installed version 1.1.2
2025-07-01 10:13:27.510 | akinje          | INFO     |   Already up to date
2025-07-01 10:13:27.613 | akinje          | INFO     | Checking module: Microsoft.PowerShell.SecretStore...
2025-07-01 10:13:28.151 | akinje          | INFO     |   Found installed version 1.0.6
2025-07-01 10:13:28.874 | akinje          | INFO     |   Already up to date
2025-07-01 10:13:28.908 | akinje          | INFO     | Checking module: Microsoft.Graph...
2025-07-01 10:13:29.079 | akinje          | INFO     |   Found installed version 2.28.0
2025-07-01 10:13:30.209 | akinje          | INFO     |   Already up to date
2025-07-01 10:13:30.228 | akinje          | INFO     | Checking module: JiraPS...
2025-07-01 10:13:30.448 | akinje          | INFO     |   Found installed version 2.14.7
2025-07-01 10:13:31.239 | akinje          | INFO     |   Already up to date
2025-07-01 10:13:31.283 | akinje          | INFO     | Checking module: ImportExcel...
2025-07-01 10:13:31.502 | akinje          | INFO     |   Found installed version 7.8.10
2025-07-01 10:13:32.340 | akinje          | INFO     |   Already up to date
2025-07-01 10:13:32.362 | akinje          | INFO     | Checking module: PSWriteHTML...
2025-07-01 10:13:32.707 | akinje          | INFO     |   Found installed version 1.36.0
2025-07-01 10:13:33.952 | akinje          | INFO     |   Already up to date
2025-07-01 10:13:34.030 | akinje          | INFO     | All modules are up to date.
2025-07-01 10:13:34.079 | akinje          | INFO     | Verifying module availability...
2025-07-01 10:13:34.269 | akinje          | INFO     |   [VERIFIED] Microsoft.PowerShell.SecretManagement v1.1.2 is available.
2025-07-01 10:13:34.506 | akinje          | INFO     |   [VERIFIED] Microsoft.PowerShell.SecretStore v1.0.6 is available.
2025-07-01 10:13:34.693 | akinje          | INFO     |   [VERIFIED] Microsoft.Graph v2.28.0 is available.
2025-07-01 10:13:35.022 | akinje          | INFO     |   [VERIFIED] JiraPS v2.14.7 is available.
2025-07-01 10:13:35.450 | akinje          | INFO     |   [VERIFIED] ImportExcel v7.8.10 is available.
2025-07-01 10:13:36.072 | akinje          | INFO     |   [VERIFIED] PSWriteHTML v1.36.0 is available.
2025-07-01 10:13:36.194 | akinje          | INFO     | All required modules are successfully installed and available.
2025-07-01 10:13:36.340 | akinje          | INFO     | === JML ENVIRONMENT VALIDATION COMPLETE ===
2025-07-01 10:13:36.444 | akinje          | DEBUG    | [DEBUG] Starting JML script execution...
2025-07-01 10:13:36.689 | akinje          | DEBUG    | [DEBUG] Script directory: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13
2025-07-01 10:13:36.875 | akinje          | DEBUG    | [DEBUG] Checking ActiveDirectory module availability...
2025-07-01 10:13:37.328 | akinje          | INFO     | [INFO] ActiveDirectory module is not available - running in UAT mode
2025-07-01 10:13:37.478 | akinje          | INFO     | [INFO] This is normal for UAT/test environments
2025-07-01 10:13:37.857 | akinje          | DEBUG    | [DEBUG] Initializing JML paths...
2025-07-01 10:13:38.064 | akinje          | DEBUG    | [DEBUG] JML Paths initialized globally.
2025-07-01 10:13:38.182 | akinje          | DEBUG    | [DEBUG] Project Root: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13
2025-07-01 10:13:38.350 | akinje          | DEBUG    | [DEBUG] Modules Root: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Modules
2025-07-01 10:13:38.471 | akinje          | DEBUG    | [DEBUG] Config Dir: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Config
2025-07-01 10:13:38.558 | akinje          | DEBUG    | [DEBUG] Updated ConfigPath to use Config directory: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Config\AdminAccountConfig.psd1
2025-07-01 10:13:38.696 | akinje          | DEBUG    | [DEBUG] Starting enhanced module loading...
2025-07-01 10:13:38.941 | akinje          | INFO     | Loading JML modules from nested structure...
2025-07-01 10:13:39.205 | akinje          | INFO     | Modules Root: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Modules
2025-07-01 10:13:39.666 | akinje          | INFO     |   Loading: JML-Core/JML-Utilities [CRITICAL]
2025-07-01 10:13:40.016 | akinje          | INFO     |     Description: General utility functions
2025-07-01 10:13:40.147 | akinje          | INFO     |     Path: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Modules\JML-Core\JML-Utilities.psd1 (manifest)
2025-07-01 10:13:40.333 | akinje          | INFO     |     Attempting to import module using manifest...
2025-07-01 10:13:41.634 | akinje          | WARNING  | WARNING: JML-Security: JML-Configuration module functions not available. Some features may be limited.
2025-07-01 10:13:42.535 | akinje          | WARNING  | WARNING: JML-Logging: Missing dependencies: Get-ModuleConfiguration (JML-Configuration), Protect-SensitiveData (JML-Security). Some features may be limited.
2025-07-01 10:13:42.762 | akinje          | INFO     |     [SUCCESS] Module loaded: JML-Core/JML-Utilities
2025-07-01 10:13:43.038 | akinje          | INFO     |   Loading: JML-Core/JML-Configuration [CRITICAL]
2025-07-01 10:13:43.178 | akinje          | INFO     |     Description: Core configuration management
2025-07-01 10:13:43.284 | akinje          | INFO     |     Path: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Modules\JML-Core\JML-Configuration.psd1 (manifest)
2025-07-01 10:13:43.422 | akinje          | INFO     |     Attempting to import module using manifest...
2025-07-01 10:13:43.964 | akinje          | INFO     |     [SUCCESS] Module loaded: JML-Core/JML-Configuration
2025-07-01 10:13:44.582 | akinje          | INFO     |     [VERIFIED] Function Initialize-SmartConfiguration is available
2025-07-01 10:13:45.135 | akinje          | INFO     |     [VERIFIED] Function Get-ModuleConfiguration is available
2025-07-01 10:13:45.296 | akinje          | INFO     |   Loading: JML-Core/JML-Security [CRITICAL]
2025-07-01 10:13:45.399 | akinje          | INFO     |     Description: Security and credential management
2025-07-01 10:13:45.648 | akinje          | INFO     |     Path: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Modules\JML-Core\JML-Security.psd1 (manifest)
2025-07-01 10:13:45.901 | akinje          | INFO     |     Attempting to import module using manifest...
2025-07-01 10:13:46.259 | akinje          | INFO     |     [SUCCESS] Module loaded: JML-Core/JML-Security
2025-07-01 10:13:46.502 | akinje          | INFO     |     [VERIFIED] Function Get-SecureCredential is available
2025-07-01 10:13:46.751 | akinje          | INFO     |     [VERIFIED] Function Set-SecureCredential is available
2025-07-01 10:13:46.845 | akinje          | INFO     |     [VERIFIED] Function Get-StringHash is available
2025-07-01 10:13:47.178 | akinje          | INFO     |   Loading: JML-Core/JML-Logging [CRITICAL]
2025-07-01 10:13:47.314 | akinje          | INFO     |     Description: Secure logging functionality
2025-07-01 10:13:47.550 | akinje          | INFO     |     Path: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Modules\JML-Core\JML-Logging.psd1 (manifest)
2025-07-01 10:13:47.654 | akinje          | INFO     |     Attempting to import module using manifest...
2025-07-01 10:13:48.077 | akinje          | INFO     |     [SUCCESS] Module loaded: JML-Core/JML-Logging
2025-07-01 10:13:48.162 | akinje          | INFO     |     [VERIFIED] Function Write-SecureLog is available
2025-07-01 10:13:48.332 | akinje          | INFO     |     [VERIFIED] Function Initialize-SecureLogging is available
2025-07-01 10:13:48.419 | akinje          | INFO     |   Loading: JML-Features/JML-ActiveDirectory [OPTIONAL]
2025-07-01 10:13:48.551 | akinje          | INFO     |     Description: Active Directory operations
2025-07-01 10:13:48.618 | akinje          | INFO     |     Path: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Modules\JML-Features\JML-ActiveDirectory.psd1 (manifest)
2025-07-01 10:13:48.682 | akinje          | INFO     |     Attempting to import module using manifest...
2025-07-01 10:13:49.244 | akinje          | INFO     |     [SUCCESS] Module loaded: JML-Features/JML-ActiveDirectory
2025-07-01 10:13:49.323 | akinje          | INFO     |   Loading: JML-Features/JML-Email [OPTIONAL]
2025-07-01 10:13:49.389 | akinje          | INFO     |     Description: Email notification functionality
2025-07-01 10:13:49.455 | akinje          | INFO     |     Path: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Modules\JML-Features\JML-Email.psd1 (manifest)
2025-07-01 10:13:49.515 | akinje          | INFO     |     Attempting to import module using manifest...
2025-07-01 10:13:49.699 | akinje          | INFO     |     [SUCCESS] Module loaded: JML-Features/JML-Email
2025-07-01 10:13:49.755 | akinje          | INFO     |   Loading: JML-Features/JML-Jira [OPTIONAL]
2025-07-01 10:13:49.819 | akinje          | INFO     |     Description: Jira integration
2025-07-01 10:13:49.874 | akinje          | INFO     |     Path: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Modules\JML-Features\JML-Jira.psd1 (manifest)
2025-07-01 10:13:49.940 | akinje          | INFO     |     Attempting to import module using manifest...
2025-07-01 10:13:50.266 | akinje          | INFO     |     [SUCCESS] Module loaded: JML-Features/JML-Jira
2025-07-01 10:13:50.324 | akinje          | INFO     |   Loading: JML-Infrastructure/JML-Setup [OPTIONAL]
2025-07-01 10:13:50.421 | akinje          | INFO     |     Description: Setup and environment validation
2025-07-01 10:13:50.489 | akinje          | INFO     |     Path: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Modules\JML-Infrastructure\JML-Setup.psd1 (manifest)
2025-07-01 10:13:50.547 | akinje          | INFO     |     Attempting to import module using manifest...
2025-07-01 10:13:50.779 | akinje          | INFO     |     [SUCCESS] Module loaded: JML-Infrastructure/JML-Setup
2025-07-01 10:13:50.842 | akinje          | INFO     |   Loading: JML-Infrastructure/JML-Monitoring [OPTIONAL]
2025-07-01 10:13:50.897 | akinje          | INFO     |     Description: System monitoring
2025-07-01 10:13:50.997 | akinje          | INFO     |     Path: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Modules\JML-Infrastructure\JML-Monitoring.psd1 (manifest)
2025-07-01 10:13:51.051 | akinje          | INFO     |     Attempting to import module using manifest...
2025-07-01 10:13:51.268 | akinje          | INFO     |     [SUCCESS] Module loaded: JML-Infrastructure/JML-Monitoring
2025-07-01 10:13:51.329 | akinje          | INFO     |   Loading: JML-Infrastructure/JML-UIManager [OPTIONAL]
2025-07-01 10:13:51.426 | akinje          | INFO     |     Description: Modern hybrid dashboard UI with real-time status
2025-07-01 10:13:51.520 | akinje          | INFO     |     Path: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Modules\JML-Infrastructure\JML-UIManager.psd1 (manifest)
2025-07-01 10:13:51.578 | akinje          | INFO     |     Attempting to import module using manifest...
2025-07-01 10:13:51.763 | akinje          | INFO     |     [SUCCESS] Module loaded: JML-Infrastructure/JML-UIManager
2025-07-01 10:13:51.812 | akinje          | INFO     | Module Loading Summary:
2025-07-01 10:13:51.858 | akinje          | INFO     |   Successfully loaded: 10/10
2025-07-01 10:13:51.921 | akinje          | DEBUG    | [DEBUG] Enhanced module loading completed successfully
2025-07-01 10:13:51.990 | akinje          | DEBUG    | [DEBUG] Performing post-loading verification...
2025-07-01 10:13:52.036 | akinje          | DEBUG    | [DEBUG] ✓ Initialize-SmartConfiguration is available
2025-07-01 10:13:52.083 | akinje          | DEBUG    | [DEBUG] ✓ Get-ModuleConfiguration is available
2025-07-01 10:14:28.018 | akinje          | INFO     | Configuration loaded from: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Config\AdminAccountConfig.psd1
2025-07-01 10:14:28.231 | akinje          | INFO     | [SUCCESS] Configuration initialized successfully
2025-07-01 10:14:28.426 | akinje          | DEBUG    | [DEBUG] Initializing credential storage selection...
2025-07-01 10:14:29.563 | akinje          | INFO     | ================================================================================
2025-07-01 10:14:29.845 | akinje          | INFO     |                     JML Credential Storage Selection                      
2025-07-01 10:14:30.012 | akinje          | INFO     | ================================================================================
2025-07-01 10:14:30.153 | akinje          | INFO     | Please select your preferred credential storage method:
2025-07-01 10:14:30.244 | akinje          | INFO     |   1. Encrypted File (Recommended for Portability)
2025-07-01 10:14:30.319 | akinje          | INFO     |      - Stores credentials in an encrypted XML file
2025-07-01 10:14:30.368 | akinje          | INFO     |      - Portable across different machines
2025-07-01 10:14:30.408 | akinje          | INFO     |      - Uses Windows DPAPI for encryption
2025-07-01 10:14:30.462 | akinje          | INFO     |   2. PowerShell Secret Management / SecretStore
2025-07-01 10:14:30.506 | akinje          | INFO     |      - Uses Microsoft.PowerShell.SecretStore module
2025-07-01 10:14:30.544 | akinje          | INFO     |      - Requires vault password for access
2025-07-01 10:14:30.576 | akinje          | INFO     |      - More secure but requires setup
2025-07-01 10:14:30.625 | akinje          | INFO     |   3. Windows Credential Manager
2025-07-01 10:14:30.662 | akinje          | INFO     |      - Uses Windows built-in credential storage
2025-07-01 10:14:30.700 | akinje          | INFO     |      - Integrated with Windows security
2025-07-01 10:14:30.813 | akinje          | INFO     |      - Machine-specific storage
2025-07-01 10:14:30.893 | akinje          | INFO     | Enter your choice (1-3) [Default: 1 in 10 seconds]: 
2025-07-01 10:14:41.020 | akinje          | INFO     | 1 (default - Encrypted File)
2025-07-01 10:14:41.076 | akinje          | INFO     | Selected: Encrypted File storage
2025-07-01 10:14:41.107 | akinje          | INFO     | Credentials will be stored in an encrypted XML file for portability.
2025-07-01 10:14:41.141 | akinje          | INFO     | Credential storage method set for this session: EncryptedFile
2025-07-01 10:14:41.171 | akinje          | INFO     | Press any key to continue...
2025-07-01 10:14:56.860 | akinje          | DEBUG    | [DEBUG] Credential storage method selected: EncryptedFile
2025-07-01 10:14:56.887 | akinje          | DEBUG    | [DEBUG] Using credential method for status check: EncryptedFile
2025-07-01 10:14:56.925 | akinje          | DEBUG    | [DEBUG] Checking credential setup status for method: EncryptedFile
2025-07-01 10:14:56.956 | akinje          | DEBUG    | [DEBUG] Checking EncryptedFile credential storage
2025-07-01 10:14:56.990 | akinje          | DEBUG    | [DEBUG] Checking for encrypted file at: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Config\SecureCredentials.xml
2025-07-01 10:14:57.051 | akinje          | DEBUG    | [DEBUG] EncryptedFile credentials appear to be configured and accessible
2025-07-01 10:14:57.094 | akinje          | INFO     | === JML SERVICE-ORIENTED ARCHITECTURE INITIALIZATION ===
2025-07-01 10:14:57.123 | akinje          | INFO     | Creating JML context...
2025-07-01 10:14:57.223 | akinje          | INFO     | Registering core services...
2025-07-01 10:14:57.272 | akinje          | DEBUG    | Transitioned from early logging to full logging system
