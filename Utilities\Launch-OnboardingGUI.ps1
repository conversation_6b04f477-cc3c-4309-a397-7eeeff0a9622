# WPF Launcher Script - Loads assemblies first, then runs the main script
Write-Host "Loading WPF assemblies..." -ForegroundColor Yellow

try {
    Add-Type -AssemblyName PresentationCore
    Add-Type -AssemblyName PresentationFramework
    Add-Type -AssemblyName WindowsBase
    Add-Type -AssemblyName System.Windows.Forms
    Write-Host "WPF assemblies loaded successfully" -ForegroundColor Green
} catch {
    Write-Error "Failed to load WPF assemblies: $($_.Exception.Message)"
    exit 1
}

Write-Host "Starting OnboardingFromJiraGUI..." -ForegroundColor Yellow

# Now dot-source the main script
. ".\OnboardingFromJiraGUI.ps1"