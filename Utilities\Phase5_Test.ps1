# Phase 5: REST API Consolidation - Validation Test
# Tests for unified JiraRestClient and consolidated REST operations

Write-Host "`n=== PHASE 5: REST API CONSOLIDATION VALIDATION ===" -ForegroundColor Cyan
Write-Host "Testing unified JiraRestClient and consolidated REST operations" -ForegroundColor White

$scriptPath = "/mnt/c/Users/<USER>/OneDrive - JERA Global Markets/Azure Devops/it-endusersupport-automation/scripts/Utilities/OnboardingFromJiraGUI_v2.ps1"

# Test 1: Verify JiraRestClient class exists
Write-Host "`nTest 1: JiraRestClient Class Existence" -ForegroundColor Yellow
$jiraRestClientLines = Select-String -Path $scriptPath -Pattern "class JiraRestClient"
if ($jiraRestClientLines.Count -ge 1) {
    Write-Host "✅ JiraRestClient class found" -ForegroundColor Green
} else {
    Write-Host "❌ JiraRestClient class not found" -ForegroundColor Red
}

# Test 2: Verify unified InvokeJiraAPI method
Write-Host "`nTest 2: Unified InvokeJiraAPI Method" -ForegroundColor Yellow
$invokeJiraAPILines = Select-String -Path $scriptPath -Pattern "InvokeJiraAPI.*operation"
if ($invokeJiraAPILines.Count -ge 1) {
    Write-Host "✅ Unified InvokeJiraAPI method found" -ForegroundColor Green
    Write-Host "   Method signature: $($invokeJiraAPILines[0].Line.Trim())" -ForegroundColor Gray
} else {
    Write-Host "❌ Unified InvokeJiraAPI method not found" -ForegroundColor Red
}

# Test 3: Verify JiraService uses restClient
Write-Host "`nTest 3: JiraService Uses REST Client" -ForegroundColor Yellow
$restClientUsageLines = Select-String -Path $scriptPath -Pattern '\$this\.restClient\.InvokeJiraAPI'
if ($restClientUsageLines.Count -ge 2) {
    Write-Host "✅ JiraService uses restClient ($($restClientUsageLines.Count) occurrences)" -ForegroundColor Green
    foreach ($usage in $restClientUsageLines) {
        Write-Host "   Line $($usage.LineNumber): $($usage.Line.Trim())" -ForegroundColor Gray
    }
} else {
    Write-Host "❌ JiraService not using restClient properly" -ForegroundColor Red
}

# Test 4: Verify operation-specific debug contexts
Write-Host "`nTest 4: Operation-Specific Debug Contexts" -ForegroundColor Yellow
$debugContexts = @("REST-Authentication", "REST-TicketFetch", "REST-CommentPost")
$foundContexts = @()

# Check for REST-$operation pattern which generates the contexts dynamically
$restOperationPattern = Select-String -Path $scriptPath -Pattern 'REST-\$operation'
if ($restOperationPattern.Count -ge 1) {
    Write-Host "✅ Dynamic REST operation context pattern found" -ForegroundColor Green
    $foundContexts += "REST-Dynamic"
}

foreach ($context in $debugContexts) {
    $contextLines = Select-String -Path $scriptPath -Pattern $context
    if ($contextLines.Count -ge 1) {
        $foundContexts += $context
        Write-Host "✅ $context context found ($($contextLines.Count) occurrences)" -ForegroundColor Green
    } else {
        # Check if using dynamic pattern instead
        Write-Host "ℹ️  $context - using dynamic REST-\$operation pattern" -ForegroundColor Cyan
    }
}

# Test 5: Verify reduced code duplication (no direct Invoke-RestMethod in JiraService methods)
Write-Host "`nTest 5: Code Duplication Reduction" -ForegroundColor Yellow
$directRestMethodLines = Select-String -Path $scriptPath -Pattern "Invoke-RestMethod" | Where-Object { 
    $_.Line -notmatch "Background|script" -and $_.LineNumber -ge 531 -and $_.LineNumber -le 950
}
if ($directRestMethodLines.Count -le 1) {
    Write-Host "✅ Reduced direct Invoke-RestMethod calls in JiraService" -ForegroundColor Green
} else {
    Write-Host "❌ Still has direct Invoke-RestMethod calls in JiraService:" -ForegroundColor Red
    foreach ($line in $directRestMethodLines) {
        Write-Host "   Line $($line.LineNumber): $($line.Line.Trim())" -ForegroundColor Gray
    }
}

# Test 6: Verify background script uses consistent debug context
Write-Host "`nTest 6: Background Script Debug Context" -ForegroundColor Yellow
$backgroundDebugLines = Select-String -Path $scriptPath -Pattern "Background REST-CommentPost"
if ($backgroundDebugLines.Count -ge 2) {
    Write-Host "✅ Background script uses consistent REST-CommentPost debug context" -ForegroundColor Green
} else {
    Write-Host "❌ Background script debug context not consistent" -ForegroundColor Red
}

# Test 7: Verify CreateAuthHeaders removal from JiraService
Write-Host "`nTest 7: Duplicate CreateAuthHeaders Removal" -ForegroundColor Yellow
$createAuthHeadersInJiraService = Select-String -Path $scriptPath -Pattern "CreateAuthHeaders" | Where-Object { 
    $_.LineNumber -ge 531 -and $_.LineNumber -le 950
}
if ($createAuthHeadersInJiraService.Count -le 1) {
    Write-Host "✅ Duplicate CreateAuthHeaders removed from JiraService" -ForegroundColor Green
} else {
    Write-Host "❌ Duplicate CreateAuthHeaders still exists in JiraService" -ForegroundColor Red
}

# Summary
Write-Host "`n=== PHASE 5 VALIDATION SUMMARY ===" -ForegroundColor Cyan
$totalTests = 7
$passedTests = 0

if ($jiraRestClientLines.Count -ge 1) { $passedTests++ }
if ($invokeJiraAPILines.Count -ge 1) { $passedTests++ }
if ($restClientUsageLines.Count -ge 2) { $passedTests++ }
if ($foundContexts.Count -ge 3) { $passedTests++ }
if ($directRestMethodLines.Count -le 1) { $passedTests++ }
if ($backgroundDebugLines.Count -ge 2) { $passedTests++ }
if ($createAuthHeadersInJiraService.Count -le 1) { $passedTests++ }

Write-Host "Tests Passed: $passedTests/$totalTests" -ForegroundColor White
if ($passedTests -eq $totalTests) {
    Write-Host "🎉 PHASE 5: REST API CONSOLIDATION - COMPLETED SUCCESSFULLY!" -ForegroundColor Green
    Write-Host "✅ Unified JiraRestClient with operation-specific debug context" -ForegroundColor Green
    Write-Host "✅ Consolidated authentication, ticket fetch, and comment posting" -ForegroundColor Green
    Write-Host "✅ Reduced code duplication and improved maintainability" -ForegroundColor Green
} else {
    Write-Host "⚠️  PHASE 5: Some tests failed - review implementation" -ForegroundColor Yellow
}

Write-Host "`n=== EXPECTED IMPROVEMENTS ===" -ForegroundColor Cyan
Write-Host "• Single unified REST client handling all Jira API calls" -ForegroundColor White
Write-Host "• Operation-specific debug context (REST-Authentication, REST-TicketFetch, REST-CommentPost)" -ForegroundColor White
Write-Host "• Eliminated duplicate auth header creation and error handling" -ForegroundColor White
Write-Host "• Consistent debug logging across all REST operations" -ForegroundColor White
Write-Host "• Reduced code by ~100+ lines through consolidation" -ForegroundColor White