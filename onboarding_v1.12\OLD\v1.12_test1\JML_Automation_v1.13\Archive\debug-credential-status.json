﻿{
    "SystemInfo":  {
                       "CurrentLocation":  "C:\\Users\\<USER>\\OneDrive - JERA Global Markets\\Azure Devops\\it-endusersupport-automation\\scripts\\onboarding_v1.12\\OLD\\v1.12_test1",
                       "ProcessId":  54484,
                       "UserName":  "akinje",
                       "SessionId":  "c48fa5d7-7fa4-4508-9a39-540e30e65043",
                       "OSVersion":  "Microsoft Windows NT 10.0.19045.0",
                       "PowerShellVersion":  "5.1.19041.5848",
                       "ExecutionPolicy":  "Unrestricted",
                       "PowerShellEdition":  "Desktop",
                       "MachineName":  "JGM-CG7THR3",
                       "UserDomain":  "JERAGM"
                   },
    "Timestamp":  "\/Date(1750767087937)\/",
    "CredentialStatus":  {
                             "CurrentUser":  "akinje",
                             "VaultExists":  true,
                             "SetupComplete":  false,
                             "ComputerName":  "JGM-CG7THR3",
                             "VaultAccessible":  true,
                             "LastSetupDate":  null,
                             "CredentialsStored":  [

                                                   ],
                             "RecoveryActions":  [
                                                     "Use credential repair function to add missing credentials",
                                                     "Run setup to configure all required credentials"
                                                 ],
                             "PerformanceMetrics":  {
                                                        "Duration":  2.9167726,
                                                        "EndTime":  "\/Date(1750767090921)\/",
                                                        "StartTime":  "\/Date(1750767088004)\/",
                                                        "TimeoutOccurred":  false
                                                    },
                             "MissingCredentials":  [
                                                        "AdminScript-SmtpCredentials",
                                                        "AdminScript-JiraApiToken",
                                                        "AdminScript-JiraUsername",
                                                        "AdminScript-JiraServerUrl"
                                                    ],
                             "Warnings":  [
                                              "Found 4 missing credential(s)"
                                          ],
                             "DetailedErrors":  [

                                                ],
                             "ErrorMessage":  null
                         },
    "TroubleshootingGuide":  {
                                 "CommonIssues":  [
                                                      "Missing 4 credential(s): AdminScript-SmtpCredentials, AdminScript-JiraApiToken, AdminScript-JiraUsername, AdminScript-JiraServerUrl"
                                                  ],
                                 "RecommendedActions":  [
                                                            "Use credential repair function"
                                                        ],
                                 "KnownSolutions":  [
                                                        "Run setup and choose credential repair option"
                                                    ]
                             },
    "ConnectivityTests":  {

                          },
    "ModuleInfo":  {
                       "JMLModules":  {
                                          "JML-Configuration":  {
                                                                    "ExportedFunctions":  "N/A",
                                                                    "Loaded":  false,
                                                                    "Version":  "Not loaded"
                                                                },
                                          "JML-Logging":  {
                                                              "ExportedFunctions":  "N/A",
                                                              "Loaded":  false,
                                                              "Version":  "Not loaded"
                                                          },
                                          "JML-Setup":  {
                                                            "ExportedFunctions":  "Debug-CredentialStatus, Get-CredentialStatus, Get-JMLVersion, Get-SecureCredentialInput, Initialize-CredentialStorage, Initialize-SecretVault, Install-SecretStoreModules, Invoke-VaultPasswordPrompt, Remove-SecretStoreVault, Start-CredentialRepair, Start-JMLSetup, Test-CredentialSetupNeeded, Test-Environment, Test-JiraConnection, Test-ModuleAvailability, Test-SecretStoreModules, Write-SetupMessage",
                                                            "Loaded":  true,
                                                            "Version":  "0.0"
                                                        },
                                          "JML-Security":  {
                                                               "ExportedFunctions":  "N/A",
                                                               "Loaded":  false,
                                                               "Version":  "Not loaded"
                                                           }
                                      },
                       "SecretStore":  {
                                           "ImportedSuccessfully":  true,
                                           "Version":  "1.0.6",
                                           "Installed":  true,
                                           "Location":  "C:\\Users\\<USER>\\OneDrive - JERA Global Markets\\Documents\\WindowsPowerShell\\Modules\\Microsoft.PowerShell.SecretStore\\1.0.6"
                                       },
                       "SecretManagement":  {
                                                "ImportedSuccessfully":  true,
                                                "Version":  "1.1.2",
                                                "Installed":  true,
                                                "Location":  "C:\\Users\\<USER>\\OneDrive - JERA Global Markets\\Documents\\WindowsPowerShell\\Modules\\Microsoft.PowerShell.SecretManagement\\1.1.2"
                                            }
                   }
}
