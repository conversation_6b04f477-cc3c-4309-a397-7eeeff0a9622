﻿# Admin Account Creation Script Configuration
# Generated on: 2025-06-24 13:30:03
# Version: 2.0 - Enhanced Security Edition
#
# This file contains configuration settings for the Admin Account Creation Script.
# Customize the values below to match your environment.
#
# IMPORTANT: This file contains only data, no executable code.
# It is safe to edit and version control.

@{
    ActiveDirectory = @{
        DefaultOU = 'OU=London,OU=Admins,OU=Accounts,OU=JeraGM,DC=jeragm,DC=com'
        OUMappings = @{
            London = 'OU=London,OU=Admins,OU=Accounts,OU=JeraGM,DC=jeragm,DC=com'
            Singapore = 'OU=Singapore,OU=Admins,OU=Accounts,OU=JeraGM,DC=jeragm,DC=com'
            United Kingdom = 'OU=London,OU=Admins,OU=Accounts,OU=JeraGM,DC=jeragm,DC=com'
        }
        PasswordPolicy = @{
            ChangePasswordAtLogon = $True
            MinLength = 12
            MinSpecialChars = 2
            PasswordNeverExpires = $False
            RequireComplexity = $True
        }
        QueryOptimization = @{
            CacheExpirationMinutes = 15
            EnableCaching = $True
            MaxResults = 1000
            QueryTimeout = 30
            UserProperties = @('GivenName', 'Surname', 'DisplayName', 'SamAccountName', 'UserPrincipalName', 'EmailAddress', 'Enabled', 'DistinguishedName')
        }
    }
    ConfigMetadata = @{
        CompatibilityLevel = '1.0.0'
        CreatedDate = '2025-06-24 13:30:03'
        LastModified = '2025-06-24 13:30:03'
        ModifiedBy = 'JERAGM\akinje'
        SchemaVersion = '1.12.0'
    }
    ConfigVersion = '1.12.0'
    Email = @{
        DefaultFrom = '<EMAIL>'
        EnableNotifications = $True
        RetrySettings = @{
            BaseDelay = 2
            EnableExponentialBackoff = $True
            MaxAttempts = 3
            MaxDelay = 16
        }
        SmtpPort = 25
        SmtpServer = 'smtp.jeragm.com'
        SupportEmail = '<EMAIL>'
        Timeout = 30
        UseSSL = $False
    }
    FeatureFlags = @{
        DefaultState = 'Enabled'
        EnableFeatureFlags = $True
        EnvironmentOverrides = @{
            Development = @{
                DebugMode = $True
                ExperimentalFeatures = $True
            }
            Production = @{
                DebugMode = $False
                ExperimentalFeatures = $False
            }
        }
        Flags = @{
            CredentialRepairFunction = $True
            DebugMode = $False
            EnhancedStatusChecking = $True
            ExperimentalFeatures = $False
        }
    }
    Jira = @{
        ApiSettings = @{
            RateLimit = @{
                EnableRateLimit = $True
                RequestsPerMinute = 60
            }
            RetrySettings = @{
                BaseDelay = 1
                EnableExponentialBackoff = $True
                MaxAttempts = 5
                MaxDelay = 32
            }
            Timeout = 60
        }
        AttachmentSettings = @{
            AllowedFileTypes = @('.log', '.txt', '.pdf', '.docx', '.xlsx')
            ChunkSizeKB = 1024
            EnableChunkedUpload = $True
            MaxFileSizeMB = 10
        }
        CommentFormatting = @{
            EnableRichFormatting = $True
            FallbackToWikiMarkup = $True
            PreferADF = $True
        }
        CustomFields = @{
            Department = 'customfield_10120'
            FirstName = 'customfield_10304'
            ITAdminAccount = 'customfield_10453'
            JobTitle = 'customfield_10238'
            LastName = 'customfield_10305'
            ModelAccount = 'customfield_10343'
            OfficeLocation = 'customfield_10115'
        }
        ExpectedRequestTypes = @{
            CreateAdmin = 'JERAGM Onboarding Request'
            DeleteAdmin = 'Remove Admin Account'
            ResetAdmin = 'Reset Admin Account'
        }
        ExpectedWorkTypes = @{
            CreateAdmin = 'Service Request with Approvals'
            DeleteAdmin = 'Service Request with Approvals'
            ResetAdmin = 'Service Request with Approvals'
        }
        ServerUrl = 'https://jeragm.atlassian.net'
    }
    LastMigration = @{
        Date = '2025-06-24 13:30:03'
        FromVersion = '1.0.0'
        MigrationsApplied = @('1.0.0 -> 1.1.0: Added security enhancements', '1.1.0 -> 1.2.0: Added enterprise automation support', '1.2.0 -> 1.12.0: Added versioning and migration system')
        ToVersion = '1.12.0'
    }
    Logging = @{
        ConsoleLogLevels = @('INFO', 'WARNING', 'ERROR', 'CRITICAL')
        DataRedaction = @{
            HashAlgorithm = 'SHA256'
            HashSalt = 'AdminAccountScript2024'
            RedactDistinguishedNames = $True
            RedactEmailAddresses = $True
            RedactServerNames = $True
            RedactUPNs = $True
        }
        EnableDataRedaction = $True
        EnableLogRotation = $True
        FileLogLevels = @('DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL')
        LogDirectory = 'C:\Temp\Scripts\Desktop Support\Logs'
        LogRetentionDays = 30
        MaxLogFileSizeMB = 10
    }
    Monitoring = @{
        Alerting = @{
            AlertThresholds = @{
                ErrorRate = 0.05
                MemoryUsage = 0.8
                ResponseTime = 30
            }
            EnableAlerts = $False
            NotificationMethods = @('Email', 'Log')
        }
        EnableHealthChecks = $True
        HealthCheckInterval = 300
        PerformanceMetrics = @{
            EnableMetrics = $True
            MetricsRetentionDays = 30
            TrackErrorRates = $True
            TrackMemoryUsage = $True
            TrackOperationTimes = $True
        }
    }
    ScriptSettings = @{
        BaseRetryDelay = 2
        DefaultDomain = 'jeragm.com'
        EnableAuditLogging = $True
        MaxRetryAttempts = 3
        MaxRetryDelay = 30
        OperationTimeout = 300
        ShowProgress = $True
    }
    Security = @{
        AuditTrail = @{
            AuditRetentionDays = 90
            EnableAuditTrail = $True
            LogAuthenticationEvents = $True
            LogCredentialAccess = $True
            LogExecutionContext = $True
            LogFunctionParameters = $True
            LogUserIdentity = $True
            LogVaultOperations = $True
        }
        Automation = @{
            DefaultPasswordVariable = 'JML_VAULT_PASSWORD'
            EnableAutomationSupport = $True
            EnablePipelineIntegration = $True
            FailFastOnCredentialError = $True
            Interaction = 'None'
            LogAutomationEvents = $True
            PasswordTimeout = 3600
            Scope = 'CurrentUser'
            SupportedPasswordSources = @('Environment', 'File', 'Registry', 'Pipeline')
        }
        CredentialStorage = @{
            CredentialMetadata = @{
                JiraApiToken = @{
                    Description = 'API token generated from Jira user profile'
                    Purpose = 'Jira API token for secure authentication'
                    Required = $True
                    Sensitive = $True
                    ValidationPattern = '^[A-Za-z0-9+/=]{20,}$'
                }
                JiraServerUrl = @{
                    Description = 'Full URL to your Jira instance'
                    Purpose = 'Jira server URL for API connections'
                    Required = $True
                    Sensitive = $False
                    ValidationPattern = '^https?://[\w\.-]+\.atlassian\.net/?$'
                }
                JiraUsername = @{
                    Description = 'Email address used for Jira authentication'
                    Purpose = 'Jira API authentication'
                    Required = $True
                    Sensitive = $False
                    ValidationPattern = '^[\w][\w\.-]{1,62}[\w]@[\w][\w-]{1,62}\.[a-zA-Z]{2,}$'
                }
                SmtpCredentials = @{
                    Description = 'Credentials for sending email notifications'
                    Purpose = 'SMTP authentication for email notifications'
                    Required = $False
                    Sensitive = $True
                    ValidationPattern = ''
                }
            }
            CredentialNames = @{
                JiraApiToken = 'AdminScript-JiraApiToken'
                JiraServerUrl = 'AdminScript-JiraServerUrl'
                JiraUsername = 'AdminScript-JiraUsername'
                SmtpCredentials = 'AdminScript-SmtpCredentials'
            }
            EncryptedFileSettings = @{
                EnableCompression = $True
                FilePath = '.\Modules\SecureCredentials.xml'
                UseUserScope = $True
            }
            FallbackMethods = @('CredentialManager', 'EncryptedFile')
            PrimaryMethod = 'SecretManagement'
            SecretVaultName = 'AdminAccountVault'
            VaultAuthentication = 'Password'
            VaultPasswordTimeout = 3600
        }
        InputValidation = @{
            AllowedPatterns = @{
                DisplayName = '^[a-zA-Z0-9\s._-]{1,64}$'
                SamAccountName = '^[a-zA-Z0-9._-]{1,20}$'
                UPN = '^[\w][\w\.-]{1,62}[\w]@[\w][\w-]{1,62}\.[a-zA-Z]{2,}$'
            }
            EnableSanitization = $True
            EnableStrictValidation = $True
            MaxInputLength = 256
            RemoveHtmlTags = $True
            RemoveScriptTags = $True
        }
        PasswordPolicy = @{
            EnableStrengthValidation = $True
            MinLength = 12
            MinSpecialChars = 2
            RequireComplexity = $True
            WarnOnWeakPasswords = $True
        }
    }
    UserExperience = @{
        ConsoleOutput = @{
            Colors = @{
                Debug = 'Gray'
                Error = 'Red'
                Information = 'Cyan'
                Progress = 'Blue'
                Success = 'Green'
                Warning = 'Yellow'
            }
            EnableColors = $True
            EnableLogLevels = $True
            EnableProgressBars = $True
            EnableTimestamps = $True
        }
        InputPrompts = @{
            EnableAutoCompletion = $True
            EnableValidationFeedback = $True
            ShowHelpText = $True
        }
        ProgressIndicators = @{
            EnableProgressBars = $True
            ShowPercentage = $True
            ShowTimeRemaining = $True
            UpdateInterval = 500
        }
    }

}
