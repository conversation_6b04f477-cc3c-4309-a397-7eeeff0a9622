﻿[2025-07-03 01:46:00.174] [DEBUG] [Testing] [16] Test registered: ConfigurationValidation in suite Unit
[2025-07-03 01:46:00.416] [DEBUG] [Testing] [16] Test registered: ConfigurationValidation in Unit
[2025-07-03 01:46:00.429] [DEBUG] [Testing] [16] Test registered: LoggingSystem in suite Unit
[2025-07-03 01:46:00.440] [DEBUG] [Testing] [16] Test registered: LoggingSystem in Unit
[2025-07-03 01:46:00.449] [DEBUG] [Testing] [16] Test registered: VersionManager in suite Unit
[2025-07-03 01:46:00.459] [DEBUG] [Testing] [16] Test registered: VersionManager in Unit
[2025-07-03 01:46:00.489] [DEBUG] [Testing] [16] Test registered: JiraConnection in suite Integration
[2025-07-03 01:46:00.499] [DEBUG] [Testing] [16] Test registered: JiraConnection in Integration
[2025-07-03 01:46:00.514] [DEBUG] [Testing] [16] Test registered: ActiveDirectoryConnection in suite Integration
[2025-07-03 01:46:00.528] [DEBUG] [Testing] [16] Test registered: ActiveDirectoryConnection in Integration
[2025-07-03 01:46:00.550] [DEBUG] [Testing] [16] Test registered: StartupTime in suite Performance
[2025-07-03 01:46:00.561] [DEBUG] [Testing] [16] Test registered: StartupTime in Performance
[2025-07-03 01:46:00.612] [DEBUG] [Testing] [16] Test registered: MemoryUsage in suite Performance
[2025-07-03 01:46:00.633] [DEBUG] [Testing] [16] Test registered: MemoryUsage in Performance
[2025-07-03 01:46:00.646] [DEBUG] [Testing] [16] Test registered: CredentialHandling in suite Security
[2025-07-03 01:46:00.658] [DEBUG] [Testing] [16] Test registered: CredentialHandling in Security
[2025-07-03 01:46:00.669] [DEBUG] [Testing] [16] Test registered: InputValidation in suite Security
[2025-07-03 01:46:00.689] [DEBUG] [Testing] [16] Test registered: InputValidation in Security
[2025-07-03 01:46:00.712] [DEBUG] [Testing] [16] Test registered: WindowCreation in suite UI
[2025-07-03 01:46:00.728] [DEBUG] [Testing] [16] Test registered: WindowCreation in UI
[2025-07-03 01:46:00.740] [INFO] [Testing] [16] Core tests initialized
[2025-07-03 01:46:00.777] [INFO] [Documentation] [16] Initializing documentation system
[2025-07-03 01:46:00.802] [INFO] [Documentation] [16] Starting documentation generation for: api
[2025-07-03 01:46:00.884] [INFO] [Documentation] [16] Documentation saved: Documentation\API_Reference.html
[2025-07-03 01:46:00.917] [INFO] [Documentation] [16] Documentation saved: Documentation\API_Reference.md
[2025-07-03 01:46:00.949] [INFO] [Documentation] [16] Documentation generation completed for: api
[2025-07-03 01:46:00.975] [INFO] [Documentation] [16] API documentation generated: 2 files
[2025-07-03 01:46:00.994] [INFO] [Documentation] [16] Starting documentation generation for: help
[2025-07-03 01:46:01.014] [INFO] [Documentation] [16] Documentation generation completed for: help
[2025-07-03 01:46:01.028] [INFO] [Documentation] [16] Help system documentation generated
[2025-07-03 01:46:01.049] [INFO] [Documentation] [16] Documentation system initialized
[2025-07-03 01:46:01.239] [INFO] [Application] [16] Initializing enhanced UI components...
[2025-07-03 01:46:01.444] [DEBUG] [Application] [16] Theme applied to controls
[2025-07-03 01:46:01.585] [DEBUG] [Application] [16] Modern theme applied successfully
[2025-07-03 01:46:01.634] [DEBUG] [Application] [16] Field validation initialized
[2025-07-03 01:46:01.690] [DEBUG] [Application] [16] Responsive layout initialized
[2025-07-03 01:46:01.878] [DEBUG] [Application] [16] Workflow step 'Connection' status: Pending
[2025-07-03 01:46:01.973] [INFO] [Application] [16] Enhanced UI initialization completed
[2025-07-03 01:46:02.052] [INFO] [Application] [16] Welcome! Please connect to Jira to begin.
[2025-07-03 01:46:02.089] [INFO] [Application] [16] OnboardingFromJiraGUI started successfully
[2025-07-03 01:46:02.148] [INFO] [Application] [16] [OK] Monitoring system initialized
[2025-07-03 01:46:02.443] [INFO] [Audit] [16] Audit: akinje performed PermissionInit on UserPermissions - Success | Context: SessionId=Unknown; Action=PermissionInit; Timestamp=07/03/2025 01:46:02; Result=Success; User=akinje; Detected=Standard user privileges; Target=UserPermissions; PrivilegeLevel=Standard
[2025-07-03 01:46:02.503] [INFO] [Application] [16] [OK] User permissions initialized for JERAGM\akinje
[2025-07-03 01:46:02.549] [DEBUG] [Application] [16]  Cache cleanup scheduled every 30 minutes
[2025-07-03 01:46:02.587] [INFO] [Application] [16] Applying Modern UI Integration...
[2025-07-03 01:46:02.778] [DEBUG] [Application] [16] Auto-completion setup for LastNameBox
[2025-07-03 01:46:02.810] [DEBUG] [Application] [16] Auto-completion setup for JobTitleBox
[2025-07-03 01:46:02.902] [DEBUG] [Application] [16] Auto-completion setup for FirstNameBox
[2025-07-03 01:46:02.945] [DEBUG] [Application] [16] Auto-completion setup for DepartmentBox
[2025-07-03 01:46:02.978] [DEBUG] [Application] [16] Auto-completion setup for ModelAccountBox
[2025-07-03 01:46:03.014] [DEBUG] [Application] [16] Auto-completion integration completed
[2025-07-03 01:46:03.110] [INFO] [Application] [16] Modern UI Integration completed successfully
[2025-07-03 01:48:18.764] [INFO] [Application] [16] Step 1: Connect to Jira
[2025-07-03 01:48:18.796] [DEBUG] [Application] [16] Workflow step 'Connection' status: InProgress
[2025-07-03 01:48:18.840] [INFO] [Application] [16] Connecting to https://jeragm.atlassian.net...
[2025-07-03 01:48:20.694] [DEBUG] [Application] [16] Workflow step 'Connection' status: Completed
[2025-07-03 01:48:20.720] [INFO] [Application] [16] Successfully connected to Jira
[2025-07-03 01:49:48.769] [INFO] [Application] [16] Step 2: Fetch Ticket Data
[2025-07-03 01:49:48.812] [DEBUG] [Application] [16] Workflow step 'FetchData' status: InProgress
[2025-07-03 01:49:48.833] [INFO] [Application] [16] Fetching data for ticket TESTIT-49342...
[2025-07-03 01:49:52.513] [DEBUG] [Application] [16] Cached ticket: TESTIT-49342
[2025-07-03 01:49:52.567] [DEBUG] [Application] [16] Issue object properties: customfield_10553, customfield_10058, customfield_10305, customfield_10149, customfield_10723, customfield_10928, customfield_10194, customfield_10363, customfield_10750, customfield_10147, customfield_11280, customfield_10729, customfield_10153, customfield_10369, customfield_10145, customfield_10783, customfield_10727, duedate, customfield_10151, customfield_10367, customfield_10370, customfield_10486, customfield_10373, worklog, customfield_10157, customfield_10371, customfield_10008, customfield_10155, customfield_10519, customfield_10202, customfield_10306, customfield_10380, customfield_10062, ID, customfield_10375, customfield_10072, customfield_10069, customfield_10685, customfield_11251, customfield_10200, customfield_10187, customfield_11113, customfield_11245, components, customfield_11253, Transition, customfield_11250, customfield_11247, resolutiondate, customfield_10101, LastViewed, customfield_10385, customfield_11249, customfield_11279, customfield_10344, customfield_10209, customfield_10189, customfield_11212, customfield_10651, Project, customfield_10211, customfield_10105, customfield_11214, customfield_10012, customfield_10213, customfield_10395, customfield_10684, customfield_10116, customfield_10172, timeoriginalestimate, customfield_10203, customfield_10174, customfield_10025, customfield_10336, customfield_10065, customfield_10330, customfield_10223, customfield_10027, customfield_10686, fixVersions, customfield_10126, customfield_10182, customfield_10356, progress, customfield_10128, customfield_10042, customfield_10180, customfield_10562, customfield_10196, customfield_10858, customfield_10629, customfield_10261, customfield_10004, customfield_10049, customfield_10627, aggregatetimeestimate, customfield_10663, customfield_10185, customfield_10237, customfield_10557, customfield_10081, timespent, security, customfield_10057, aggregateprogress, aggregatetimespent, customfield_10314, environment, customfield_10722, customfield_10195, customfield_10239, customfield_10368, customfield_10229, customfield_10037, issuelinks, customfield_10348, customfield_10056, customfield_10131, customfield_10199, customfield_10054, Status, customfield_10618, customfield_10751, customfield_10161, Comment, Key, customfield_11254, customfield_10063, customfield_10060, customfield_10066, customfield_10226, customfield_10068, labels, customfield_10520, customfield_10489, statuscategorychangedate, customfield_10160, customfield_10222, customfield_10354, customfield_11252, customfield_10159, customfield_10393, customfield_11248, RestUrl, resolution, customfield_10384, customfield_10216, customfield_10379, customfield_10208, customfield_10386, customfield_10341, customfield_10132, customfield_10559, customfield_10171, customfield_10013, customfield_10100, customfield_10212, customfield_10204, customfield_10725, customfield_10117, customfield_10173, customfield_10011, customfield_10075, customfield_10398, customfield_10115, customfield_10347, customfield_10337, customfield_10321, customfield_10177, customfield_10215, customfield_10224, customfield_10352, Assignee, customfield_10130, customfield_10717, customfield_10129, customfield_10181, customfield_10000, customfield_10453, customfield_11181, customfield_10178, customfield_10029, customfield_10276, watches, customfield_10560, customfield_10133, customfield_10120, customfield_10102, customfield_10634, customfield_10249, customfield_10320, customfield_10048, customfield_11278, customfield_10346, customfield_10383, customfield_10184, customfield_11281, customfield_10726, customfield_10030, priority, customfield_10235, customfield_10720, customfield_10134, customfield_10047, customfield_10190, customfield_10721, customfield_10045, customfield_10448, customfield_10238, customfield_10882, customfield_10250, customfield_10198, customfield_10118, customfield_10619, customfield_10197, customfield_10251, customfield_10318, customfield_10055, Summary, Description, customfield_10447, customfield_10304, customfield_10255, customfield_10059, customfield_10053, customfield_10148, customfield_10382, customfield_10193, customfield_10488, attachment, customfield_10728, customfield_10225, customfield_10158, workratio, customfield_10372, customfield_10206, customfield_10156, customfield_10664, customfield_10201, customfield_10154, customfield_10521, statusCategory, Created, customfield_10064, issuetype, customfield_10254, customfield_10374, customfield_10326, customfield_10342, customfield_10317, customfield_10554, customfield_10227, customfield_10210, customfield_10214, customfield_10391, customfield_11246, customfield_10585, customfield_10070, customfield_11182, customfield_10630, customfield_10135, aggregatetimeoriginalestimate, customfield_10631, customfield_10010, customfield_10061, customfield_10009, customfield_11215, timeestimate, customfield_10662, customfield_11213, customfield_10556, customfield_10028, customfield_10635, customfield_10396, customfield_10275, customfield_10719, timetracking, customfield_10175, customfield_10024, Creator, customfield_10381, customfield_10023, Updated, customfield_11179, customfield_10127, customfield_10183, customfield_11122, customfield_10561, customfield_10043, customfield_10322, customfield_10046, customfield_10192, customfield_10041, Reporter, customfield_10044, customfield_10628, customfield_10335, customfield_10040, subtasks, customfield_10191, customfield_10626, customfield_10343, versions, customfield_10186, HttpUrl, customfield_10319, customfield_10303, customfield_10001, customfield_10555, customfield_10252, customfield_10050
[2025-07-03 01:49:52.804] [INFO] [Application] [16] Starting data extraction from ticket TESTIT-49342
[2025-07-03 01:49:52.864] [DEBUG] [Application] [16] Extracted data - Name: 'Test1 Test2', Job: 'Intern', Dept: 'IT', Model: 'test', Location: ''
[2025-07-03 01:49:53.421] [DEBUG] [Application] [16] Generating SAM account name for Test1 Test2
[2025-07-03 01:49:53.449] [DEBUG] [Application] [16] Checking if SAM account name 'test2t' exists
[2025-07-03 01:49:53.486] [INFO] [Application] [16] Generated SAM account name: test2t (could not verify uniqueness)
[2025-07-03 01:49:53.826] [DEBUG] [Application] [16] Generated from Jira ticket - SAM: 31 32 33 test2t, UPN: <EMAIL> (based on Test1.Test2), Email: <EMAIL>
[2025-07-03 01:49:53.897] [INFO] [Application] [16] Data extraction completed. Please verify the populated fields.
[2025-07-03 01:49:53.915] [DEBUG] [Application] [16] Workflow step 'FetchData' status: Completed
[2025-07-03 01:49:53.934] [INFO] [Application] [16] Step 3: Validate Information
[2025-07-03 01:49:53.952] [DEBUG] [Application] [16] Workflow step 'Validation' status: InProgress
[2025-07-03 01:49:53.983] [INFO] [Application] [16] Successfully fetched and populated data from ticket TESTIT-49342
[2025-07-03 02:00:32.526] [DEBUG] [Testing] [16] Test registered: ConfigurationValidation in suite Unit
[2025-07-03 02:00:32.667] [DEBUG] [Testing] [16] Test registered: ConfigurationValidation in Unit
[2025-07-03 02:00:32.681] [DEBUG] [Testing] [16] Test registered: LoggingSystem in suite Unit
[2025-07-03 02:00:32.694] [DEBUG] [Testing] [16] Test registered: LoggingSystem in Unit
[2025-07-03 02:00:32.709] [DEBUG] [Testing] [16] Test registered: VersionManager in suite Unit
[2025-07-03 02:00:32.720] [DEBUG] [Testing] [16] Test registered: VersionManager in Unit
[2025-07-03 02:00:32.731] [DEBUG] [Testing] [16] Test registered: JiraConnection in suite Integration
[2025-07-03 02:00:32.744] [DEBUG] [Testing] [16] Test registered: JiraConnection in Integration
[2025-07-03 02:00:32.757] [DEBUG] [Testing] [16] Test registered: ActiveDirectoryConnection in suite Integration
[2025-07-03 02:00:32.769] [DEBUG] [Testing] [16] Test registered: ActiveDirectoryConnection in Integration
[2025-07-03 02:00:32.830] [DEBUG] [Testing] [16] Test registered: StartupTime in suite Performance
[2025-07-03 02:00:32.841] [DEBUG] [Testing] [16] Test registered: StartupTime in Performance
[2025-07-03 02:00:32.853] [DEBUG] [Testing] [16] Test registered: MemoryUsage in suite Performance
[2025-07-03 02:00:32.865] [DEBUG] [Testing] [16] Test registered: MemoryUsage in Performance
[2025-07-03 02:00:32.876] [DEBUG] [Testing] [16] Test registered: CredentialHandling in suite Security
[2025-07-03 02:00:32.888] [DEBUG] [Testing] [16] Test registered: CredentialHandling in Security
[2025-07-03 02:00:32.898] [DEBUG] [Testing] [16] Test registered: InputValidation in suite Security
[2025-07-03 02:00:32.915] [DEBUG] [Testing] [16] Test registered: InputValidation in Security
[2025-07-03 02:00:32.929] [DEBUG] [Testing] [16] Test registered: WindowCreation in suite UI
[2025-07-03 02:00:32.944] [DEBUG] [Testing] [16] Test registered: WindowCreation in UI
[2025-07-03 02:00:32.956] [INFO] [Testing] [16] Core tests initialized
[2025-07-03 02:00:32.994] [INFO] [Documentation] [16] Initializing documentation system
[2025-07-03 02:00:33.022] [INFO] [Documentation] [16] Starting documentation generation for: api
[2025-07-03 02:00:33.100] [INFO] [Documentation] [16] Documentation saved: Documentation\API_Reference.html
[2025-07-03 02:00:33.139] [INFO] [Documentation] [16] Documentation saved: Documentation\API_Reference.md
[2025-07-03 02:00:33.172] [INFO] [Documentation] [16] Documentation generation completed for: api
[2025-07-03 02:00:33.199] [INFO] [Documentation] [16] API documentation generated: 2 files
[2025-07-03 02:00:33.219] [INFO] [Documentation] [16] Starting documentation generation for: help
[2025-07-03 02:00:33.243] [INFO] [Documentation] [16] Documentation generation completed for: help
[2025-07-03 02:00:33.265] [INFO] [Documentation] [16] Help system documentation generated
[2025-07-03 02:00:33.283] [INFO] [Documentation] [16] Documentation system initialized
[2025-07-03 02:00:33.494] [INFO] [Application] [16] Initializing enhanced UI components...
[2025-07-03 02:00:33.561] [DEBUG] [Application] [16] Theme applied to controls
[2025-07-03 02:00:33.578] [DEBUG] [Application] [16] Modern theme applied successfully
[2025-07-03 02:00:33.645] [DEBUG] [Application] [16] Field validation initialized
[2025-07-03 02:00:33.702] [DEBUG] [Application] [16] Responsive layout initialized
[2025-07-03 02:00:33.793] [DEBUG] [Application] [16] Workflow step 'Connection' status: Pending
[2025-07-03 02:00:33.842] [INFO] [Application] [16] Enhanced UI initialization completed
[2025-07-03 02:00:33.888] [INFO] [Application] [16] Welcome! Please connect to Jira to begin.
[2025-07-03 02:00:34.033] [INFO] [Application] [16] OnboardingFromJiraGUI started successfully
[2025-07-03 02:00:34.073] [INFO] [Application] [16] [OK] Monitoring system initialized
[2025-07-03 02:00:34.150] [INFO] [Audit] [16] Audit: akinje performed PermissionInit on UserPermissions - Success | Context: SessionId=Unknown; Action=PermissionInit; Timestamp=07/03/2025 02:00:34; Result=Success; User=akinje; Detected=Standard user privileges; Target=UserPermissions; PrivilegeLevel=Standard
[2025-07-03 02:00:34.255] [INFO] [Application] [16] [OK] User permissions initialized for JERAGM\akinje
[2025-07-03 02:00:34.297] [DEBUG] [Application] [16]  Cache cleanup scheduled every 30 minutes
[2025-07-03 02:00:34.323] [INFO] [Application] [16] Applying Modern UI Integration...
[2025-07-03 02:00:34.426] [DEBUG] [Application] [16] Auto-completion setup for LastNameBox
[2025-07-03 02:00:34.452] [DEBUG] [Application] [16] Auto-completion setup for JobTitleBox
[2025-07-03 02:00:34.489] [DEBUG] [Application] [16] Auto-completion setup for FirstNameBox
[2025-07-03 02:00:34.526] [DEBUG] [Application] [16] Auto-completion setup for DepartmentBox
[2025-07-03 02:00:34.549] [DEBUG] [Application] [16] Auto-completion setup for ModelAccountBox
[2025-07-03 02:00:34.571] [DEBUG] [Application] [16] Auto-completion integration completed
[2025-07-03 02:00:34.617] [INFO] [Application] [16] Modern UI Integration completed successfully
[2025-07-03 02:01:33.420] [INFO] [Application] [16] Step 1: Connect to Jira
[2025-07-03 02:01:33.436] [DEBUG] [Application] [16] Workflow step 'Connection' status: InProgress
[2025-07-03 02:01:33.472] [INFO] [Application] [16] Connecting to https://jeragm.atlassian.net...
[2025-07-03 02:01:35.342] [DEBUG] [Application] [16] Workflow step 'Connection' status: Completed
[2025-07-03 02:01:35.357] [INFO] [Application] [16] Successfully connected to Jira
[2025-07-03 02:01:48.047] [INFO] [Application] [16] Step 2: Fetch Ticket Data
[2025-07-03 02:01:48.064] [DEBUG] [Application] [16] Workflow step 'FetchData' status: InProgress
[2025-07-03 02:01:48.087] [INFO] [Application] [16] Fetching data for ticket TESTIT-49342...
[2025-07-03 02:01:51.012] [DEBUG] [Application] [16] Cached ticket: TESTIT-49342
[2025-07-03 02:01:51.046] [DEBUG] [Application] [16] Issue object properties: customfield_10553, customfield_10058, customfield_10305, customfield_10149, customfield_10723, customfield_10928, customfield_10194, customfield_10363, customfield_10750, customfield_10147, customfield_11280, customfield_10729, customfield_10153, customfield_10369, customfield_10145, customfield_10783, customfield_10727, duedate, customfield_10151, customfield_10367, customfield_10370, customfield_10486, customfield_10373, worklog, customfield_10157, customfield_10371, customfield_10008, customfield_10155, customfield_10519, customfield_10202, customfield_10306, customfield_10380, customfield_10062, ID, customfield_10375, customfield_10072, customfield_10069, customfield_10685, customfield_11251, customfield_10200, customfield_10187, customfield_11113, customfield_11245, components, customfield_11253, Transition, customfield_11250, customfield_11247, resolutiondate, customfield_10101, LastViewed, customfield_10385, customfield_11249, customfield_11279, customfield_10344, customfield_10209, customfield_10189, customfield_11212, customfield_10651, Project, customfield_10211, customfield_10105, customfield_11214, customfield_10012, customfield_10213, customfield_10395, customfield_10684, customfield_10116, customfield_10172, timeoriginalestimate, customfield_10203, customfield_10174, customfield_10025, customfield_10336, customfield_10065, customfield_10330, customfield_10223, customfield_10027, customfield_10686, fixVersions, customfield_10126, customfield_10182, customfield_10356, progress, customfield_10128, customfield_10042, customfield_10180, customfield_10562, customfield_10196, customfield_10858, customfield_10629, customfield_10261, customfield_10004, customfield_10049, customfield_10627, aggregatetimeestimate, customfield_10663, customfield_10185, customfield_10237, customfield_10557, customfield_10081, timespent, security, customfield_10057, aggregateprogress, aggregatetimespent, customfield_10314, environment, customfield_10722, customfield_10195, customfield_10239, customfield_10368, customfield_10229, customfield_10037, issuelinks, customfield_10348, customfield_10056, customfield_10131, customfield_10199, customfield_10054, Status, customfield_10618, customfield_10751, customfield_10161, Comment, Key, customfield_11254, customfield_10063, customfield_10060, customfield_10066, customfield_10226, customfield_10068, labels, customfield_10520, customfield_10489, statuscategorychangedate, customfield_10160, customfield_10222, customfield_10354, customfield_11252, customfield_10159, customfield_10393, customfield_11248, RestUrl, resolution, customfield_10384, customfield_10216, customfield_10379, customfield_10208, customfield_10386, customfield_10341, customfield_10132, customfield_10559, customfield_10171, customfield_10013, customfield_10100, customfield_10212, customfield_10204, customfield_10725, customfield_10117, customfield_10173, customfield_10011, customfield_10075, customfield_10398, customfield_10115, customfield_10347, customfield_10337, customfield_10321, customfield_10177, customfield_10215, customfield_10224, customfield_10352, Assignee, customfield_10130, customfield_10717, customfield_10129, customfield_10181, customfield_10000, customfield_10453, customfield_11181, customfield_10178, customfield_10029, customfield_10276, watches, customfield_10560, customfield_10133, customfield_10120, customfield_10102, customfield_10634, customfield_10249, customfield_10320, customfield_10048, customfield_11278, customfield_10346, customfield_10383, customfield_10184, customfield_11281, customfield_10726, customfield_10030, priority, customfield_10235, customfield_10720, customfield_10134, customfield_10047, customfield_10190, customfield_10721, customfield_10045, customfield_10448, customfield_10238, customfield_10882, customfield_10250, customfield_10198, customfield_10118, customfield_10619, customfield_10197, customfield_10251, customfield_10318, customfield_10055, Summary, Description, customfield_10447, customfield_10304, customfield_10255, customfield_10059, customfield_10053, customfield_10148, customfield_10382, customfield_10193, customfield_10488, attachment, customfield_10728, customfield_10225, customfield_10158, workratio, customfield_10372, customfield_10206, customfield_10156, customfield_10664, customfield_10201, customfield_10154, customfield_10521, statusCategory, Created, customfield_10064, issuetype, customfield_10254, customfield_10374, customfield_10326, customfield_10342, customfield_10317, customfield_10554, customfield_10227, customfield_10210, customfield_10214, customfield_10391, customfield_11246, customfield_10585, customfield_10070, customfield_11182, customfield_10630, customfield_10135, aggregatetimeoriginalestimate, customfield_10631, customfield_10010, customfield_10061, customfield_10009, customfield_11215, timeestimate, customfield_10662, customfield_11213, customfield_10556, customfield_10028, customfield_10635, customfield_10396, customfield_10275, customfield_10719, timetracking, customfield_10175, customfield_10024, Creator, customfield_10381, customfield_10023, Updated, customfield_11179, customfield_10127, customfield_10183, customfield_11122, customfield_10561, customfield_10043, customfield_10322, customfield_10046, customfield_10192, customfield_10041, Reporter, customfield_10044, customfield_10628, customfield_10335, customfield_10040, subtasks, customfield_10191, customfield_10626, customfield_10343, versions, customfield_10186, HttpUrl, customfield_10319, customfield_10303, customfield_10001, customfield_10555, customfield_10252, customfield_10050
[2025-07-03 02:01:51.079] [DEBUG] [Application] [16] Found Comment property with 18 comments
[2025-07-03 02:01:51.110] [INFO] [Application] [16] Starting data extraction from ticket TESTIT-49342
[2025-07-03 02:01:51.156] [DEBUG] [Application] [16] Extracted data - Name: 'Test1 Test2', Job: 'Intern', Dept: 'IT', Model: 'test', Location: ''
[2025-07-03 02:01:51.731] [DEBUG] [Application] [16] Generating SAM account name for Test1 Test2
[2025-07-03 02:01:51.752] [DEBUG] [Application] [16] Checking if SAM account name 'test2t' exists
[2025-07-03 02:01:51.777] [INFO] [Application] [16] Generated SAM account name: test2t (simulation mode - could not verify uniqueness)
[2025-07-03 02:01:52.083] [DEBUG] [Application] [16] Generated from Jira ticket - SAM: 31 32 33 test2t, UPN: <EMAIL> (based on Test1.Test2), Email: <EMAIL>
[2025-07-03 02:01:52.158] [INFO] [Application] [16] Data extraction completed. Please verify the populated fields.
[2025-07-03 02:01:52.180] [DEBUG] [Application] [16] Workflow step 'FetchData' status: Completed
[2025-07-03 02:01:52.196] [INFO] [Application] [16] Step 3: Validate Information
[2025-07-03 02:01:52.209] [DEBUG] [Application] [16] Workflow step 'Validation' status: InProgress
[2025-07-03 02:01:52.228] [INFO] [Application] [16] Successfully fetched and populated data from ticket TESTIT-49342
[2025-07-03 07:43:40.802] [DEBUG] [Testing] [16] Test registered: ConfigurationValidation in suite Unit
[2025-07-03 07:43:40.914] [DEBUG] [Testing] [16] Test registered: ConfigurationValidation in Unit
[2025-07-03 07:43:40.921] [DEBUG] [Testing] [16] Test registered: LoggingSystem in suite Unit
[2025-07-03 07:43:40.921] [DEBUG] [Testing] [16] Test registered: LoggingSystem in Unit
[2025-07-03 07:43:40.937] [DEBUG] [Testing] [16] Test registered: VersionManager in suite Unit
[2025-07-03 07:43:40.937] [DEBUG] [Testing] [16] Test registered: VersionManager in Unit
[2025-07-03 07:43:40.937] [DEBUG] [Testing] [16] Test registered: JiraConnection in suite Integration
[2025-07-03 07:43:40.951] [DEBUG] [Testing] [16] Test registered: JiraConnection in Integration
[2025-07-03 07:43:40.955] [DEBUG] [Testing] [16] Test registered: ActiveDirectoryConnection in suite Integration
[2025-07-03 07:43:40.955] [DEBUG] [Testing] [16] Test registered: ActiveDirectoryConnection in Integration
[2025-07-03 07:43:40.972] [DEBUG] [Testing] [16] Test registered: StartupTime in suite Performance
[2025-07-03 07:43:40.985] [DEBUG] [Testing] [16] Test registered: StartupTime in Performance
[2025-07-03 07:43:40.992] [DEBUG] [Testing] [16] Test registered: MemoryUsage in suite Performance
[2025-07-03 07:43:41.051] [DEBUG] [Testing] [16] Test registered: MemoryUsage in Performance
[2025-07-03 07:43:41.051] [DEBUG] [Testing] [16] Test registered: CredentialHandling in suite Security
[2025-07-03 07:43:41.051] [DEBUG] [Testing] [16] Test registered: CredentialHandling in Security
[2025-07-03 07:43:41.067] [DEBUG] [Testing] [16] Test registered: InputValidation in suite Security
[2025-07-03 07:43:41.067] [DEBUG] [Testing] [16] Test registered: InputValidation in Security
[2025-07-03 07:43:41.091] [DEBUG] [Testing] [16] Test registered: WindowCreation in suite UI
[2025-07-03 07:43:41.100] [DEBUG] [Testing] [16] Test registered: WindowCreation in UI
[2025-07-03 07:43:41.111] [INFO] [Testing] [16] Core tests initialized
[2025-07-03 07:43:41.139] [INFO] [Documentation] [16] Initializing documentation system
[2025-07-03 07:43:41.155] [INFO] [Documentation] [16] Starting documentation generation for: api
[2025-07-03 07:43:41.217] [INFO] [Documentation] [16] Documentation saved: Documentation\API_Reference.html
[2025-07-03 07:43:41.233] [INFO] [Documentation] [16] Documentation saved: Documentation\API_Reference.md
[2025-07-03 07:43:41.247] [INFO] [Documentation] [16] Documentation generation completed for: api
[2025-07-03 07:43:41.258] [INFO] [Documentation] [16] API documentation generated: 2 files
[2025-07-03 07:43:41.270] [INFO] [Documentation] [16] Starting documentation generation for: help
[2025-07-03 07:43:41.282] [INFO] [Documentation] [16] Documentation generation completed for: help
[2025-07-03 07:43:41.288] [INFO] [Documentation] [16] Help system documentation generated
[2025-07-03 07:43:41.293] [INFO] [Documentation] [16] Documentation system initialized
[2025-07-03 07:43:41.391] [INFO] [Application] [16] Initializing enhanced UI components...
[2025-07-03 07:43:41.468] [DEBUG] [Application] [16] Theme applied to controls
[2025-07-03 07:43:41.497] [DEBUG] [Application] [16] Modern theme applied successfully
[2025-07-03 07:43:41.516] [DEBUG] [Application] [16] Field validation initialized
[2025-07-03 07:43:41.553] [DEBUG] [Application] [16] Responsive layout initialized
[2025-07-03 07:43:41.699] [DEBUG] [Application] [16] Workflow step 'Connection' status: Pending
[2025-07-03 07:43:41.769] [INFO] [Application] [16] Enhanced UI initialization completed
[2025-07-03 07:43:41.811] [INFO] [Application] [16] Welcome! Please connect to Jira to begin.
[2025-07-03 07:43:41.833] [INFO] [Application] [16] OnboardingFromJiraGUI started successfully
[2025-07-03 07:43:41.868] [INFO] [Application] [16] [OK] Monitoring system initialized
[2025-07-03 07:43:42.007] [INFO] [Audit] [16] Audit: akinje performed PermissionInit on UserPermissions - Success | Context: SessionId=Unknown; Action=PermissionInit; Timestamp=07/03/2025 07:43:41; Result=Success; User=akinje; Detected=Standard user privileges; Target=UserPermissions; PrivilegeLevel=Standard
[2025-07-03 07:43:42.048] [INFO] [Application] [16] [OK] User permissions initialized for JERAGM\akinje
[2025-07-03 07:43:42.064] [DEBUG] [Application] [16]  Cache cleanup scheduled every 30 minutes
[2025-07-03 07:43:42.079] [INFO] [Application] [16] Applying Modern UI Integration...
[2025-07-03 07:43:42.160] [DEBUG] [Application] [16] Auto-completion setup for LastNameBox
[2025-07-03 07:43:42.171] [DEBUG] [Application] [16] Auto-completion setup for JobTitleBox
[2025-07-03 07:43:42.180] [DEBUG] [Application] [16] Auto-completion setup for FirstNameBox
[2025-07-03 07:43:42.198] [DEBUG] [Application] [16] Auto-completion setup for DepartmentBox
[2025-07-03 07:43:42.216] [DEBUG] [Application] [16] Auto-completion setup for ModelAccountBox
[2025-07-03 07:43:42.230] [DEBUG] [Application] [16] Auto-completion integration completed
[2025-07-03 07:43:42.291] [INFO] [Application] [16] Modern UI Integration completed successfully
[2025-07-03 07:44:52.247] [INFO] [Application] [16] Step 1: Connect to Jira
[2025-07-03 07:44:52.268] [DEBUG] [Application] [16] Workflow step 'Connection' status: InProgress
[2025-07-03 07:44:52.313] [INFO] [Application] [16] Connecting to https://jeragm.atlassian.net...
[2025-07-03 07:44:54.115] [DEBUG] [Application] [16] Workflow step 'Connection' status: Completed
[2025-07-03 07:44:54.145] [INFO] [Application] [16] Successfully connected to Jira
[2025-07-03 07:45:09.866] [INFO] [Application] [16] Step 2: Fetch Ticket Data
[2025-07-03 07:45:09.887] [DEBUG] [Application] [16] Workflow step 'FetchData' status: InProgress
[2025-07-03 07:45:09.899] [INFO] [Application] [16] Fetching data for ticket TESTIT-49342...
[2025-07-03 07:45:11.358] [DEBUG] [Application] [16] Cached ticket: TESTIT-49342
[2025-07-03 07:45:11.377] [DEBUG] [Application] [16] Issue object properties: customfield_10553, customfield_10058, customfield_10305, customfield_10149, customfield_10723, customfield_10928, customfield_10194, customfield_10363, customfield_10750, customfield_10147, customfield_11280, customfield_10729, customfield_10153, customfield_10369, customfield_10145, customfield_10783, customfield_10727, duedate, customfield_10151, customfield_10367, customfield_10370, customfield_10486, customfield_10373, worklog, customfield_10157, customfield_10371, customfield_10008, customfield_10155, customfield_10519, customfield_10202, customfield_10306, customfield_10380, customfield_10062, ID, customfield_10375, customfield_10072, customfield_10069, customfield_10685, customfield_11251, customfield_10200, customfield_10187, customfield_11113, customfield_11245, components, customfield_11253, Transition, customfield_11250, customfield_11247, resolutiondate, customfield_10101, LastViewed, customfield_10385, customfield_11249, customfield_11279, customfield_10344, customfield_10209, customfield_10189, customfield_11212, customfield_10651, Project, customfield_10211, customfield_10105, customfield_11214, customfield_10012, customfield_10213, customfield_10395, customfield_10684, customfield_10116, customfield_10172, timeoriginalestimate, customfield_10203, customfield_10174, customfield_10025, customfield_10336, customfield_10065, customfield_10330, customfield_10223, customfield_10027, customfield_10686, fixVersions, customfield_10126, customfield_10182, customfield_10356, progress, customfield_10128, customfield_10042, customfield_10180, customfield_10562, customfield_10196, customfield_10858, customfield_10629, customfield_10261, customfield_10004, customfield_10049, customfield_10627, aggregatetimeestimate, customfield_10663, customfield_10185, customfield_10237, customfield_10557, customfield_10081, timespent, security, customfield_10057, aggregateprogress, aggregatetimespent, customfield_10314, environment, customfield_10722, customfield_10195, customfield_10239, customfield_10368, customfield_10229, customfield_10037, issuelinks, customfield_10348, customfield_10056, customfield_10131, customfield_10199, customfield_10054, Status, customfield_10618, customfield_10751, customfield_10161, Comment, Key, customfield_11254, customfield_10063, customfield_10060, customfield_10066, customfield_10226, customfield_10068, labels, customfield_10520, customfield_10489, statuscategorychangedate, customfield_10160, customfield_10222, customfield_10354, customfield_11252, customfield_10159, customfield_10393, customfield_11248, RestUrl, resolution, customfield_10384, customfield_10216, customfield_10379, customfield_10208, customfield_10386, customfield_10341, customfield_10132, customfield_10559, customfield_10171, customfield_10013, customfield_10100, customfield_10212, customfield_10204, customfield_10725, customfield_10117, customfield_10173, customfield_10011, customfield_10075, customfield_10398, customfield_10115, customfield_10347, customfield_10337, customfield_10321, customfield_10177, customfield_10215, customfield_10224, customfield_10352, Assignee, customfield_10130, customfield_10717, customfield_10129, customfield_10181, customfield_10000, customfield_10453, customfield_11181, customfield_10178, customfield_10029, customfield_10276, watches, customfield_10560, customfield_10133, customfield_10120, customfield_10102, customfield_10634, customfield_10249, customfield_10320, customfield_10048, customfield_11278, customfield_10346, customfield_10383, customfield_10184, customfield_11281, customfield_10726, customfield_10030, priority, customfield_10235, customfield_10720, customfield_10134, customfield_10047, customfield_10190, customfield_10721, customfield_10045, customfield_10448, customfield_10238, customfield_10882, customfield_10250, customfield_10198, customfield_10118, customfield_10619, customfield_10197, customfield_10251, customfield_10318, customfield_10055, Summary, Description, customfield_10447, customfield_10304, customfield_10255, customfield_10059, customfield_10053, customfield_10148, customfield_10382, customfield_10193, customfield_10488, attachment, customfield_10728, customfield_10225, customfield_10158, workratio, customfield_10372, customfield_10206, customfield_10156, customfield_10664, customfield_10201, customfield_10154, customfield_10521, statusCategory, Created, customfield_10064, issuetype, customfield_10254, customfield_10374, customfield_10326, customfield_10342, customfield_10317, customfield_10554, customfield_10227, customfield_10210, customfield_10214, customfield_10391, customfield_11246, customfield_10585, customfield_10070, customfield_11182, customfield_10630, customfield_10135, aggregatetimeoriginalestimate, customfield_10631, customfield_10010, customfield_10061, customfield_11215, timeestimate, customfield_10662, customfield_11213, customfield_10556, customfield_10028, customfield_10635, customfield_10396, customfield_10275, customfield_10719, timetracking, customfield_10175, customfield_10024, Creator, customfield_10381, customfield_10023, Updated, customfield_11179, customfield_10127, customfield_10183, customfield_11122, customfield_10561, customfield_10043, customfield_10322, customfield_10046, customfield_10192, customfield_10041, Reporter, customfield_10044, customfield_10628, customfield_10335, customfield_10040, subtasks, customfield_10191, customfield_10626, customfield_10343, versions, customfield_10186, HttpUrl, customfield_10319, customfield_10303, customfield_10001, customfield_10555, customfield_10252, customfield_10050
[2025-07-03 07:45:11.389] [DEBUG] [Application] [16] Found Comment property with 18 comments
[2025-07-03 07:45:11.399] [INFO] [Application] [16] Starting data extraction from ticket TESTIT-49342
[2025-07-03 07:45:11.417] [DEBUG] [Application] [16] Extracted data - Name: 'Test1 Test2', Job: 'Intern', Dept: 'IT', Model: 'test', Location: ''
[2025-07-03 07:45:11.826] [DEBUG] [Application] [16] Generating SAM account name for Test1 Test2
[2025-07-03 07:45:11.842] [DEBUG] [Application] [16] Checking if SAM account name 'test2t' exists
[2025-07-03 07:45:11.855] [INFO] [Application] [16] Generated SAM account name: test2t (simulation mode - could not verify uniqueness)
[2025-07-03 07:45:12.081] [DEBUG] [Application] [16] DEBUG SAM Generation - generatedSam variable type: Object[]
[2025-07-03 07:45:12.081] [DEBUG] [Application] [16] DEBUG SAM Generation - generatedSam length: 4
[2025-07-03 07:45:12.098] [DEBUG] [Application] [16] DEBUG SAM Generation - generatedSam value: '31 32 33 test2t'
[2025-07-03 07:45:12.105] [DEBUG] [Application] [16] DEBUG SAM Generation - generatedSam bytes: 51 49 32 51 50 32 51 51 32 116 101 115 116 50 116
[2025-07-03 07:45:12.122] [DEBUG] [Application] [16] Generated from Jira ticket - SAM: 31 32 33 test2t, UPN: <EMAIL> (based on Test1.Test2), Email: <EMAIL>
[2025-07-03 07:45:12.168] [INFO] [Application] [16] Data extraction completed. Please verify the populated fields.
[2025-07-03 07:45:12.181] [DEBUG] [Application] [16] Workflow step 'FetchData' status: Completed
[2025-07-03 07:45:12.188] [INFO] [Application] [16] Step 3: Validate Information
[2025-07-03 07:45:12.188] [DEBUG] [Application] [16] Workflow step 'Validation' status: InProgress
[2025-07-03 07:45:12.204] [INFO] [Application] [16] Successfully fetched and populated data from ticket TESTIT-49342
[2025-07-03 07:57:18.640] [DEBUG] [Testing] [16] Test registered: ConfigurationValidation in suite Unit
[2025-07-03 07:57:18.906] [DEBUG] [Testing] [16] Test registered: ConfigurationValidation in Unit
[2025-07-03 07:57:18.926] [DEBUG] [Testing] [16] Test registered: LoggingSystem in suite Unit
[2025-07-03 07:57:18.942] [DEBUG] [Testing] [16] Test registered: LoggingSystem in Unit
[2025-07-03 07:57:19.071] [DEBUG] [Testing] [16] Test registered: VersionManager in suite Unit
[2025-07-03 07:57:19.088] [DEBUG] [Testing] [16] Test registered: VersionManager in Unit
[2025-07-03 07:57:19.106] [DEBUG] [Testing] [16] Test registered: JiraConnection in suite Integration
[2025-07-03 07:57:19.119] [DEBUG] [Testing] [16] Test registered: JiraConnection in Integration
[2025-07-03 07:57:19.137] [DEBUG] [Testing] [16] Test registered: ActiveDirectoryConnection in suite Integration
[2025-07-03 07:57:19.155] [DEBUG] [Testing] [16] Test registered: ActiveDirectoryConnection in Integration
[2025-07-03 07:57:19.174] [DEBUG] [Testing] [16] Test registered: StartupTime in suite Performance
[2025-07-03 07:57:19.191] [DEBUG] [Testing] [16] Test registered: StartupTime in Performance
[2025-07-03 07:57:19.206] [DEBUG] [Testing] [16] Test registered: MemoryUsage in suite Performance
[2025-07-03 07:57:19.217] [DEBUG] [Testing] [16] Test registered: MemoryUsage in Performance
[2025-07-03 07:57:19.233] [DEBUG] [Testing] [16] Test registered: CredentialHandling in suite Security
[2025-07-03 07:57:19.252] [DEBUG] [Testing] [16] Test registered: CredentialHandling in Security
[2025-07-03 07:57:19.269] [DEBUG] [Testing] [16] Test registered: InputValidation in suite Security
[2025-07-03 07:57:19.293] [DEBUG] [Testing] [16] Test registered: InputValidation in Security
[2025-07-03 07:57:19.338] [DEBUG] [Testing] [16] Test registered: WindowCreation in suite UI
[2025-07-03 07:57:19.355] [DEBUG] [Testing] [16] Test registered: WindowCreation in UI
[2025-07-03 07:57:19.389] [INFO] [Testing] [16] Core tests initialized
[2025-07-03 07:57:19.471] [INFO] [Documentation] [16] Initializing documentation system
[2025-07-03 07:57:19.530] [INFO] [Documentation] [16] Starting documentation generation for: api
[2025-07-03 07:57:19.688] [INFO] [Documentation] [16] Documentation saved: Documentation\API_Reference.html
[2025-07-03 07:57:19.721] [INFO] [Documentation] [16] Documentation saved: Documentation\API_Reference.md
[2025-07-03 07:57:19.755] [INFO] [Documentation] [16] Documentation generation completed for: api
[2025-07-03 07:57:19.786] [INFO] [Documentation] [16] API documentation generated: 2 files
[2025-07-03 07:57:19.808] [INFO] [Documentation] [16] Starting documentation generation for: help
[2025-07-03 07:57:19.832] [INFO] [Documentation] [16] Documentation generation completed for: help
[2025-07-03 07:57:19.855] [INFO] [Documentation] [16] Help system documentation generated
[2025-07-03 07:57:19.871] [INFO] [Documentation] [16] Documentation system initialized
[2025-07-03 07:57:20.041] [INFO] [Application] [16] Initializing enhanced UI components...
[2025-07-03 07:57:20.204] [DEBUG] [Application] [16] Theme applied to controls
[2025-07-03 07:57:20.344] [DEBUG] [Application] [16] Modern theme applied successfully
[2025-07-03 07:57:20.488] [DEBUG] [Application] [16] Field validation initialized
[2025-07-03 07:57:20.671] [DEBUG] [Application] [16] Responsive layout initialized
[2025-07-03 07:57:20.976] [DEBUG] [Application] [16] Workflow step 'Connection' status: Pending
[2025-07-03 07:57:21.166] [INFO] [Application] [16] Enhanced UI initialization completed
[2025-07-03 07:57:21.224] [INFO] [Application] [16] Welcome! Please connect to Jira to begin.
[2025-07-03 07:57:21.255] [INFO] [Application] [16] OnboardingFromJiraGUI started successfully
[2025-07-03 07:57:21.321] [INFO] [Application] [16] [OK] Monitoring system initialized
[2025-07-03 07:57:21.488] [INFO] [Audit] [16] Audit: akinje performed PermissionInit on UserPermissions - Success | Context: SessionId=Unknown; Action=PermissionInit; Timestamp=07/03/2025 07:57:21; Result=Success; User=akinje; Detected=Standard user privileges; Target=UserPermissions; PrivilegeLevel=Standard
[2025-07-03 07:57:21.654] [INFO] [Application] [16] [OK] User permissions initialized for JERAGM\akinje
[2025-07-03 07:57:21.688] [DEBUG] [Application] [16]  Cache cleanup scheduled every 30 minutes
[2025-07-03 07:57:21.726] [INFO] [Application] [16] Applying Modern UI Integration...
[2025-07-03 07:57:21.929] [DEBUG] [Application] [16] Auto-completion setup for LastNameBox
[2025-07-03 07:57:21.938] [DEBUG] [Application] [16] Auto-completion setup for JobTitleBox
[2025-07-03 07:57:21.955] [DEBUG] [Application] [16] Auto-completion setup for FirstNameBox
[2025-07-03 07:57:21.982] [DEBUG] [Application] [16] Auto-completion setup for DepartmentBox
[2025-07-03 07:57:22.012] [DEBUG] [Application] [16] Auto-completion setup for ModelAccountBox
[2025-07-03 07:57:22.039] [DEBUG] [Application] [16] Auto-completion integration completed
[2025-07-03 07:57:22.154] [INFO] [Application] [16] Modern UI Integration completed successfully
[2025-07-03 07:58:32.391] [INFO] [Application] [16] Step 1: Connect to Jira
[2025-07-03 07:58:32.407] [DEBUG] [Application] [16] Workflow step 'Connection' status: InProgress
[2025-07-03 07:58:32.440] [INFO] [Application] [16] Connecting to https://jeragm.atlassian.net...
[2025-07-03 07:58:33.811] [DEBUG] [Application] [16] Workflow step 'Connection' status: Completed
[2025-07-03 07:58:33.827] [INFO] [Application] [16] Successfully connected to Jira
[2025-07-03 07:58:43.121] [INFO] [Application] [16] Step 2: Fetch Ticket Data
[2025-07-03 07:58:43.132] [DEBUG] [Application] [16] Workflow step 'FetchData' status: InProgress
[2025-07-03 07:58:43.144] [INFO] [Application] [16] Fetching data for ticket TESTIT-49342...
[2025-07-03 07:58:45.052] [DEBUG] [Application] [16] Cached ticket: TESTIT-49342
[2025-07-03 07:58:45.063] [DEBUG] [Application] [16] Issue object properties: customfield_10553, customfield_10058, customfield_10305, customfield_10149, customfield_10723, customfield_10928, customfield_10194, customfield_10363, customfield_10750, customfield_10147, customfield_11280, customfield_10729, customfield_10153, customfield_10369, customfield_10145, customfield_10783, customfield_10727, duedate, customfield_10151, customfield_10367, customfield_10370, customfield_10486, customfield_10373, worklog, customfield_10157, customfield_10371, customfield_10008, customfield_10155, customfield_10519, customfield_10202, customfield_10306, customfield_10380, customfield_10062, ID, customfield_10375, customfield_10072, customfield_10069, customfield_10685, customfield_11251, customfield_10200, customfield_10187, customfield_11113, customfield_11245, components, customfield_11253, Transition, customfield_11250, customfield_11247, resolutiondate, customfield_10101, LastViewed, customfield_10385, customfield_11249, customfield_11279, customfield_10344, customfield_10209, customfield_10189, customfield_11212, customfield_10651, Project, customfield_10211, customfield_10105, customfield_11214, customfield_10012, customfield_10213, customfield_10395, customfield_10684, customfield_10116, customfield_10172, timeoriginalestimate, customfield_10203, customfield_10174, customfield_10025, customfield_10336, customfield_10065, customfield_10330, customfield_10223, customfield_10027, customfield_10686, fixVersions, customfield_10126, customfield_10182, customfield_10356, progress, customfield_10128, customfield_10042, customfield_10180, customfield_10562, customfield_10196, customfield_10858, customfield_10629, customfield_10261, customfield_10004, customfield_10049, customfield_10627, aggregatetimeestimate, customfield_10663, customfield_10185, customfield_10237, customfield_10557, customfield_10081, timespent, security, customfield_10057, aggregateprogress, aggregatetimespent, customfield_10314, environment, customfield_10722, customfield_10195, customfield_10239, customfield_10368, customfield_10229, customfield_10037, issuelinks, customfield_10348, customfield_10056, customfield_10131, customfield_10199, customfield_10054, Status, customfield_10618, customfield_10751, customfield_10161, Comment, Key, customfield_11254, customfield_10063, customfield_10060, customfield_10066, customfield_10226, customfield_10068, labels, customfield_10520, customfield_10489, statuscategorychangedate, customfield_10160, customfield_10222, customfield_10354, customfield_11252, customfield_10159, customfield_10393, customfield_11248, RestUrl, resolution, customfield_10384, customfield_10216, customfield_10379, customfield_10208, customfield_10386, customfield_10341, customfield_10132, customfield_10559, customfield_10171, customfield_10013, customfield_10100, customfield_10212, customfield_10204, customfield_10725, customfield_10117, customfield_10173, customfield_10011, customfield_10075, customfield_10398, customfield_10115, customfield_10347, customfield_10337, customfield_10321, customfield_10177, customfield_10215, customfield_10224, customfield_10352, Assignee, customfield_10130, customfield_10717, customfield_10129, customfield_10181, customfield_10000, customfield_10453, customfield_11181, customfield_10178, customfield_10029, customfield_10276, watches, customfield_10560, customfield_10133, customfield_10120, customfield_10102, customfield_10634, customfield_10249, customfield_10320, customfield_10048, customfield_11278, customfield_10346, customfield_10383, customfield_10184, customfield_11281, customfield_10726, customfield_10030, priority, customfield_10235, customfield_10720, customfield_10134, customfield_10047, customfield_10190, customfield_10721, customfield_10045, customfield_10448, customfield_10238, customfield_10882, customfield_10250, customfield_10198, customfield_10118, customfield_10619, customfield_10197, customfield_10251, customfield_10318, customfield_10055, Summary, Description, customfield_10447, customfield_10304, customfield_10255, customfield_10059, customfield_10053, customfield_10148, customfield_10382, customfield_10193, customfield_10488, attachment, customfield_10728, customfield_10225, customfield_10158, workratio, customfield_10372, customfield_10206, customfield_10156, customfield_10664, customfield_10201, customfield_10154, customfield_10521, statusCategory, Created, customfield_10064, issuetype, customfield_10254, customfield_10374, customfield_10326, customfield_10342, customfield_10317, customfield_10554, customfield_10227, customfield_10210, customfield_10214, customfield_10391, customfield_11246, customfield_10585, customfield_10070, customfield_11182, customfield_10630, customfield_10135, aggregatetimeoriginalestimate, customfield_10631, customfield_10010, customfield_10061, customfield_11215, timeestimate, customfield_10662, customfield_11213, customfield_10556, customfield_10028, customfield_10635, customfield_10396, customfield_10275, customfield_10719, timetracking, customfield_10175, customfield_10024, Creator, customfield_10381, customfield_10023, Updated, customfield_11179, customfield_10127, customfield_10183, customfield_11122, customfield_10561, customfield_10043, customfield_10322, customfield_10046, customfield_10192, customfield_10041, Reporter, customfield_10044, customfield_10628, customfield_10335, customfield_10040, subtasks, customfield_10191, customfield_10626, customfield_10343, versions, customfield_10186, HttpUrl, customfield_10319, customfield_10303, customfield_10001, customfield_10555, customfield_10252, customfield_10050
[2025-07-03 07:58:45.073] [DEBUG] [Application] [16] Found Comment property with 18 comments
[2025-07-03 07:58:45.088] [INFO] [Application] [16] Starting data extraction from ticket TESTIT-49342
[2025-07-03 07:58:45.106] [DEBUG] [Application] [16] Extracted data - Name: 'Test1 Test2', Job: 'Intern', Dept: 'IT', Model: 'test', Location: ''
[2025-07-03 07:58:45.530] [DEBUG] [Application] [16] Generating SAM account name for Test1 Test2
[2025-07-03 07:58:45.545] [DEBUG] [Application] [16] Checking if SAM account name 'test2t' exists
[2025-07-03 07:58:45.556] [INFO] [Application] [16] Generated SAM account name: test2t (simulation mode - could not verify uniqueness)
[2025-07-03 07:58:45.814] [DEBUG] [Application] [16] Generated from Jira ticket - SAM: 31 32 33 test2t, UPN: <EMAIL> (based on Test1.Test2), Email: <EMAIL>
[2025-07-03 07:58:45.838] [INFO] [Application] [16] Data extraction completed. Please verify the populated fields.
[2025-07-03 07:58:45.854] [DEBUG] [Application] [16] Workflow step 'FetchData' status: Completed
[2025-07-03 07:58:45.854] [INFO] [Application] [16] Step 3: Validate Information
[2025-07-03 07:58:45.871] [DEBUG] [Application] [16] Workflow step 'Validation' status: InProgress
[2025-07-03 07:58:45.888] [INFO] [Application] [16] Successfully fetched and populated data from ticket TESTIT-49342
[2025-07-03 08:02:03.398] [DEBUG] [Testing] [16] Test registered: ConfigurationValidation in suite Unit
[2025-07-03 08:02:03.484] [DEBUG] [Testing] [16] Test registered: ConfigurationValidation in Unit
[2025-07-03 08:02:03.491] [DEBUG] [Testing] [16] Test registered: LoggingSystem in suite Unit
[2025-07-03 08:02:03.496] [DEBUG] [Testing] [16] Test registered: LoggingSystem in Unit
[2025-07-03 08:02:03.501] [DEBUG] [Testing] [16] Test registered: VersionManager in suite Unit
[2025-07-03 08:02:03.506] [DEBUG] [Testing] [16] Test registered: VersionManager in Unit
[2025-07-03 08:02:03.509] [DEBUG] [Testing] [16] Test registered: JiraConnection in suite Integration
[2025-07-03 08:02:03.512] [DEBUG] [Testing] [16] Test registered: JiraConnection in Integration
[2025-07-03 08:02:03.521] [DEBUG] [Testing] [16] Test registered: ActiveDirectoryConnection in suite Integration
[2025-07-03 08:02:03.521] [DEBUG] [Testing] [16] Test registered: ActiveDirectoryConnection in Integration
[2025-07-03 08:02:03.536] [DEBUG] [Testing] [16] Test registered: StartupTime in suite Performance
[2025-07-03 08:02:03.604] [DEBUG] [Testing] [16] Test registered: StartupTime in Performance
[2025-07-03 08:02:03.604] [DEBUG] [Testing] [16] Test registered: MemoryUsage in suite Performance
[2025-07-03 08:02:03.620] [DEBUG] [Testing] [16] Test registered: MemoryUsage in Performance
[2025-07-03 08:02:03.620] [DEBUG] [Testing] [16] Test registered: CredentialHandling in suite Security
[2025-07-03 08:02:03.620] [DEBUG] [Testing] [16] Test registered: CredentialHandling in Security
[2025-07-03 08:02:03.636] [DEBUG] [Testing] [16] Test registered: InputValidation in suite Security
[2025-07-03 08:02:03.646] [DEBUG] [Testing] [16] Test registered: InputValidation in Security
[2025-07-03 08:02:03.653] [DEBUG] [Testing] [16] Test registered: WindowCreation in suite UI
[2025-07-03 08:02:03.658] [DEBUG] [Testing] [16] Test registered: WindowCreation in UI
[2025-07-03 08:02:03.667] [INFO] [Testing] [16] Core tests initialized
[2025-07-03 08:02:03.688] [INFO] [Documentation] [16] Initializing documentation system
[2025-07-03 08:02:03.705] [INFO] [Documentation] [16] Starting documentation generation for: api
[2025-07-03 08:02:03.755] [INFO] [Documentation] [16] Documentation saved: Documentation\API_Reference.html
[2025-07-03 08:02:03.755] [INFO] [Documentation] [16] Documentation saved: Documentation\API_Reference.md
[2025-07-03 08:02:03.770] [INFO] [Documentation] [16] Documentation generation completed for: api
[2025-07-03 08:02:03.788] [INFO] [Documentation] [16] API documentation generated: 2 files
[2025-07-03 08:02:03.788] [INFO] [Documentation] [16] Starting documentation generation for: help
[2025-07-03 08:02:03.805] [INFO] [Documentation] [16] Documentation generation completed for: help
[2025-07-03 08:02:03.805] [INFO] [Documentation] [16] Help system documentation generated
[2025-07-03 08:02:03.821] [INFO] [Documentation] [16] Documentation system initialized
[2025-07-03 08:02:03.887] [INFO] [Application] [16] Initializing enhanced UI components...
[2025-07-03 08:02:03.954] [DEBUG] [Application] [16] Theme applied to controls
[2025-07-03 08:02:03.954] [DEBUG] [Application] [16] Modern theme applied successfully
[2025-07-03 08:02:03.988] [DEBUG] [Application] [16] Field validation initialized
[2025-07-03 08:02:04.028] [DEBUG] [Application] [16] Responsive layout initialized
[2025-07-03 08:02:04.104] [DEBUG] [Application] [16] Workflow step 'Connection' status: Pending
[2025-07-03 08:02:04.138] [INFO] [Application] [16] Enhanced UI initialization completed
[2025-07-03 08:02:04.172] [INFO] [Application] [16] Welcome! Please connect to Jira to begin.
[2025-07-03 08:02:04.191] [INFO] [Application] [16] OnboardingFromJiraGUI started successfully
[2025-07-03 08:02:04.255] [INFO] [Application] [16] [OK] Monitoring system initialized
[2025-07-03 08:02:04.305] [INFO] [Audit] [16] Audit: akinje performed PermissionInit on UserPermissions - Success | Context: SessionId=Unknown; Action=PermissionInit; Timestamp=07/03/2025 08:02:04; Result=Success; User=akinje; Detected=Standard user privileges; Target=UserPermissions; PrivilegeLevel=Standard
[2025-07-03 08:02:04.321] [INFO] [Application] [16] [OK] User permissions initialized for JERAGM\akinje
[2025-07-03 08:02:04.350] [DEBUG] [Application] [16]  Cache cleanup scheduled every 30 minutes
[2025-07-03 08:02:04.359] [INFO] [Application] [16] Applying Modern UI Integration...
[2025-07-03 08:02:04.422] [DEBUG] [Application] [16] Auto-completion setup for LastNameBox
[2025-07-03 08:02:04.437] [DEBUG] [Application] [16] Auto-completion setup for JobTitleBox
[2025-07-03 08:02:04.437] [DEBUG] [Application] [16] Auto-completion setup for FirstNameBox
[2025-07-03 08:02:04.455] [DEBUG] [Application] [16] Auto-completion setup for DepartmentBox
[2025-07-03 08:02:04.471] [DEBUG] [Application] [16] Auto-completion setup for ModelAccountBox
[2025-07-03 08:02:04.488] [DEBUG] [Application] [16] Auto-completion integration completed
[2025-07-03 08:02:04.545] [INFO] [Application] [16] Modern UI Integration completed successfully
[2025-07-03 08:05:16.086] [INFO] [Application] [16] Step 1: Connect to Jira
[2025-07-03 08:05:16.104] [DEBUG] [Application] [16] Workflow step 'Connection' status: InProgress
[2025-07-03 08:05:16.164] [INFO] [Application] [16] Connecting to https://jeragm.atlassian.net...
[2025-07-03 08:05:19.411] [DEBUG] [Application] [16] Workflow step 'Connection' status: Completed
[2025-07-03 08:05:19.439] [INFO] [Application] [16] Successfully connected to Jira
[2025-07-03 08:05:25.286] [INFO] [Application] [16] Step 2: Fetch Ticket Data
[2025-07-03 08:05:25.320] [DEBUG] [Application] [16] Workflow step 'FetchData' status: InProgress
[2025-07-03 08:05:25.354] [INFO] [Application] [16] Fetching data for ticket TESTIT-49342...
[2025-07-03 08:05:27.422] [DEBUG] [Application] [16] Cached ticket: TESTIT-49342
[2025-07-03 08:05:27.439] [DEBUG] [Application] [16] Issue object properties: customfield_10553, customfield_10058, customfield_10305, customfield_10149, customfield_10723, customfield_10928, customfield_10194, customfield_10363, customfield_10750, customfield_10147, customfield_11280, customfield_10729, customfield_10153, customfield_10369, customfield_10145, customfield_10783, customfield_10727, duedate, customfield_10151, customfield_10367, customfield_10370, customfield_10486, customfield_10373, worklog, customfield_10157, customfield_10371, customfield_10008, customfield_10155, customfield_10519, customfield_10202, customfield_10306, customfield_10380, customfield_10062, ID, customfield_10375, customfield_10072, customfield_10069, customfield_10685, customfield_11251, customfield_10200, customfield_10187, customfield_11113, customfield_11245, components, customfield_11253, Transition, customfield_11250, customfield_11247, resolutiondate, customfield_10101, LastViewed, customfield_10385, customfield_11249, customfield_11279, customfield_10344, customfield_10209, customfield_10189, customfield_11212, customfield_10651, Project, customfield_10211, customfield_10105, customfield_11214, customfield_10012, customfield_10213, customfield_10395, customfield_10684, customfield_10116, customfield_10172, timeoriginalestimate, customfield_10203, customfield_10174, customfield_10025, customfield_10336, customfield_10065, customfield_10330, customfield_10223, customfield_10027, customfield_10686, fixVersions, customfield_10126, customfield_10182, customfield_10356, progress, customfield_10128, customfield_10042, customfield_10180, customfield_10562, customfield_10196, customfield_10858, customfield_10629, customfield_10261, customfield_10004, customfield_10049, customfield_10627, aggregatetimeestimate, customfield_10663, customfield_10185, customfield_10237, customfield_10557, customfield_10081, timespent, security, customfield_10057, aggregateprogress, aggregatetimespent, customfield_10314, environment, customfield_10722, customfield_10195, customfield_10239, customfield_10368, customfield_10229, customfield_10037, issuelinks, customfield_10348, customfield_10056, customfield_10131, customfield_10199, customfield_10054, Status, customfield_10618, customfield_10751, customfield_10161, Comment, Key, customfield_11254, customfield_10063, customfield_10060, customfield_10066, customfield_10226, customfield_10068, labels, customfield_10520, customfield_10489, statuscategorychangedate, customfield_10160, customfield_10222, customfield_10354, customfield_11252, customfield_10159, customfield_10393, customfield_11248, RestUrl, resolution, customfield_10384, customfield_10216, customfield_10379, customfield_10208, customfield_10386, customfield_10341, customfield_10132, customfield_10559, customfield_10171, customfield_10013, customfield_10100, customfield_10212, customfield_10204, customfield_10725, customfield_10117, customfield_10173, customfield_10011, customfield_10075, customfield_10398, customfield_10115, customfield_10347, customfield_10337, customfield_10321, customfield_10177, customfield_10215, customfield_10224, customfield_10352, Assignee, customfield_10130, customfield_10717, customfield_10129, customfield_10181, customfield_10000, customfield_10453, customfield_11181, customfield_10178, customfield_10029, customfield_10276, watches, customfield_10560, customfield_10133, customfield_10120, customfield_10102, customfield_10634, customfield_10249, customfield_10320, customfield_10048, customfield_11278, customfield_10346, customfield_10383, customfield_10184, customfield_11281, customfield_10726, customfield_10030, priority, customfield_10235, customfield_10720, customfield_10134, customfield_10047, customfield_10190, customfield_10721, customfield_10045, customfield_10448, customfield_10238, customfield_10882, customfield_10250, customfield_10198, customfield_10118, customfield_10619, customfield_10197, customfield_10251, customfield_10318, customfield_10055, Summary, Description, customfield_10447, customfield_10304, customfield_10255, customfield_10059, customfield_10053, customfield_10148, customfield_10382, customfield_10193, customfield_10488, attachment, customfield_10728, customfield_10225, customfield_10158, workratio, customfield_10372, customfield_10206, customfield_10156, customfield_10664, customfield_10201, customfield_10154, customfield_10521, statusCategory, Created, customfield_10064, issuetype, customfield_10254, customfield_10374, customfield_10326, customfield_10342, customfield_10317, customfield_10554, customfield_10227, customfield_10210, customfield_10214, customfield_10391, customfield_11246, customfield_10585, customfield_10070, customfield_11182, customfield_10630, customfield_10135, aggregatetimeoriginalestimate, customfield_10631, customfield_10010, customfield_10061, customfield_11215, timeestimate, customfield_10662, customfield_11213, customfield_10556, customfield_10028, customfield_10635, customfield_10396, customfield_10275, customfield_10719, timetracking, customfield_10175, customfield_10024, Creator, customfield_10381, customfield_10023, Updated, customfield_11179, customfield_10127, customfield_10183, customfield_11122, customfield_10561, customfield_10043, customfield_10322, customfield_10046, customfield_10192, customfield_10041, Reporter, customfield_10044, customfield_10628, customfield_10335, customfield_10040, subtasks, customfield_10191, customfield_10626, customfield_10343, versions, customfield_10186, HttpUrl, customfield_10319, customfield_10303, customfield_10001, customfield_10555, customfield_10252, customfield_10050
[2025-07-03 08:05:27.456] [DEBUG] [Application] [16] Found Comment property with 18 comments
[2025-07-03 08:05:27.471] [INFO] [Application] [16] Starting data extraction from ticket TESTIT-49342
[2025-07-03 08:05:27.486] [DEBUG] [Application] [16] Extracted data - Name: 'Test1 Test2', Job: 'Intern', Dept: 'IT', Model: 'test', Location: ''
[2025-07-03 08:05:27.882] [DEBUG] [Application] [16] Generating SAM account name for Test1 Test2
[2025-07-03 08:05:27.894] [DEBUG] [Application] [16] Checking if SAM account name 'test2t' exists
[2025-07-03 08:05:27.904] [INFO] [Application] [16] Generated SAM account name: test2t (simulation mode - could not verify uniqueness)
[2025-07-03 08:05:27.904] [DEBUG] [Application] [16] SAM generation debug - Type: String, Value: '31 32 33 test2t'
[2025-07-03 08:05:28.170] [DEBUG] [Application] [16] Generated account details from Jira ticket successfully
[2025-07-03 08:05:28.220] [INFO] [Application] [16] Data extraction completed. Please verify the populated fields.
[2025-07-03 08:05:28.230] [DEBUG] [Application] [16] Workflow step 'FetchData' status: Completed
[2025-07-03 08:05:28.240] [INFO] [Application] [16] Step 3: Validate Information
[2025-07-03 08:05:28.249] [DEBUG] [Application] [16] Workflow step 'Validation' status: InProgress
[2025-07-03 08:05:28.253] [INFO] [Application] [16] Successfully fetched and populated data from ticket TESTIT-49342
[2025-07-03 08:12:14.674] [DEBUG] [Testing] [16] Test registered: ConfigurationValidation in suite Unit
[2025-07-03 08:12:14.933] [DEBUG] [Testing] [16] Test registered: ConfigurationValidation in Unit
[2025-07-03 08:12:14.950] [DEBUG] [Testing] [16] Test registered: LoggingSystem in suite Unit
[2025-07-03 08:12:14.961] [DEBUG] [Testing] [16] Test registered: LoggingSystem in Unit
[2025-07-03 08:12:14.972] [DEBUG] [Testing] [16] Test registered: VersionManager in suite Unit
[2025-07-03 08:12:14.984] [DEBUG] [Testing] [16] Test registered: VersionManager in Unit
[2025-07-03 08:12:14.996] [DEBUG] [Testing] [16] Test registered: JiraConnection in suite Integration
[2025-07-03 08:12:15.007] [DEBUG] [Testing] [16] Test registered: JiraConnection in Integration
[2025-07-03 08:12:15.015] [DEBUG] [Testing] [16] Test registered: ActiveDirectoryConnection in suite Integration
[2025-07-03 08:12:15.024] [DEBUG] [Testing] [16] Test registered: ActiveDirectoryConnection in Integration
[2025-07-03 08:12:15.024] [DEBUG] [Testing] [16] Test registered: StartupTime in suite Performance
[2025-07-03 08:12:15.048] [DEBUG] [Testing] [16] Test registered: StartupTime in Performance
[2025-07-03 08:12:15.058] [DEBUG] [Testing] [16] Test registered: MemoryUsage in suite Performance
[2025-07-03 08:12:15.067] [DEBUG] [Testing] [16] Test registered: MemoryUsage in Performance
[2025-07-03 08:12:15.074] [DEBUG] [Testing] [16] Test registered: CredentialHandling in suite Security
[2025-07-03 08:12:15.084] [DEBUG] [Testing] [16] Test registered: CredentialHandling in Security
[2025-07-03 08:12:15.097] [DEBUG] [Testing] [16] Test registered: InputValidation in suite Security
[2025-07-03 08:12:15.107] [DEBUG] [Testing] [16] Test registered: InputValidation in Security
[2025-07-03 08:12:15.119] [DEBUG] [Testing] [16] Test registered: WindowCreation in suite UI
[2025-07-03 08:12:15.130] [DEBUG] [Testing] [16] Test registered: WindowCreation in UI
[2025-07-03 08:12:15.158] [INFO] [Testing] [16] Core tests initialized
[2025-07-03 08:12:15.201] [INFO] [Documentation] [16] Initializing documentation system
[2025-07-03 08:12:15.224] [INFO] [Documentation] [16] Starting documentation generation for: api
[2025-07-03 08:12:15.345] [INFO] [Documentation] [16] Documentation saved: Documentation\API_Reference.html
[2025-07-03 08:12:15.369] [INFO] [Documentation] [16] Documentation saved: Documentation\API_Reference.md
[2025-07-03 08:12:15.389] [INFO] [Documentation] [16] Documentation generation completed for: api
[2025-07-03 08:12:15.410] [INFO] [Documentation] [16] API documentation generated: 2 files
[2025-07-03 08:12:15.424] [INFO] [Documentation] [16] Starting documentation generation for: help
[2025-07-03 08:12:15.449] [INFO] [Documentation] [16] Documentation generation completed for: help
[2025-07-03 08:12:15.461] [INFO] [Documentation] [16] Help system documentation generated
[2025-07-03 08:12:15.470] [INFO] [Documentation] [16] Documentation system initialized
[2025-07-03 08:12:15.641] [INFO] [Application] [16] Initializing enhanced UI components...
[2025-07-03 08:12:15.741] [DEBUG] [Application] [16] Theme applied to controls
[2025-07-03 08:12:15.906] [DEBUG] [Application] [16] Modern theme applied successfully
[2025-07-03 08:12:15.976] [DEBUG] [Application] [16] Field validation initialized
[2025-07-03 08:12:16.029] [DEBUG] [Application] [16] Responsive layout initialized
[2025-07-03 08:12:16.289] [DEBUG] [Application] [16] Workflow step 'Connection' status: Pending
[2025-07-03 08:12:16.398] [INFO] [Application] [16] Enhanced UI initialization completed
[2025-07-03 08:12:16.456] [INFO] [Application] [16] Welcome! Please connect to Jira to begin.
[2025-07-03 08:12:16.482] [INFO] [Application] [16] OnboardingFromJiraGUI started successfully
[2025-07-03 08:12:16.517] [INFO] [Application] [16] [OK] Monitoring system initialized
[2025-07-03 08:12:16.577] [INFO] [Audit] [16] Audit: akinje performed PermissionInit on UserPermissions - Success | Context: SessionId=Unknown; Action=PermissionInit; Timestamp=07/03/2025 08:12:16; Result=Success; User=akinje; Detected=Standard user privileges; Target=UserPermissions; PrivilegeLevel=Standard
[2025-07-03 08:12:16.628] [INFO] [Application] [16] [OK] User permissions initialized for JERAGM\akinje
[2025-07-03 08:12:16.698] [DEBUG] [Application] [16]  Cache cleanup scheduled every 30 minutes
[2025-07-03 08:12:16.719] [INFO] [Application] [16] Applying Modern UI Integration...
[2025-07-03 08:12:16.842] [DEBUG] [Application] [16] Auto-completion setup for LastNameBox
[2025-07-03 08:12:16.871] [DEBUG] [Application] [16] Auto-completion setup for JobTitleBox
[2025-07-03 08:12:16.895] [DEBUG] [Application] [16] Auto-completion setup for FirstNameBox
[2025-07-03 08:12:16.919] [DEBUG] [Application] [16] Auto-completion setup for DepartmentBox
[2025-07-03 08:12:16.943] [DEBUG] [Application] [16] Auto-completion setup for ModelAccountBox
[2025-07-03 08:12:16.966] [DEBUG] [Application] [16] Auto-completion integration completed
[2025-07-03 08:12:17.041] [INFO] [Application] [16] Modern UI Integration completed successfully
[2025-07-03 08:12:53.086] [INFO] [Application] [16] Step 1: Connect to Jira
[2025-07-03 08:12:53.107] [DEBUG] [Application] [16] Workflow step 'Connection' status: InProgress
[2025-07-03 08:12:53.135] [INFO] [Application] [16] Connecting to https://jeragm.atlassian.net...
[2025-07-03 08:12:54.837] [DEBUG] [Application] [16] Workflow step 'Connection' status: Completed
[2025-07-03 08:12:54.853] [INFO] [Application] [16] Successfully connected to Jira
[2025-07-03 08:13:00.594] [INFO] [Application] [16] Step 2: Fetch Ticket Data
[2025-07-03 08:13:00.602] [DEBUG] [Application] [16] Workflow step 'FetchData' status: InProgress
[2025-07-03 08:13:00.612] [INFO] [Application] [16] Fetching data for ticket TESTIT-49342...
[2025-07-03 08:13:02.688] [DEBUG] [Application] [16] Cached ticket: TESTIT-49342
[2025-07-03 08:13:02.711] [DEBUG] [Application] [16] Issue object properties: customfield_10553, customfield_10058, customfield_10305, customfield_10149, customfield_10723, customfield_10928, customfield_10194, customfield_10363, customfield_10750, customfield_10147, customfield_11280, customfield_10729, customfield_10153, customfield_10369, customfield_10145, customfield_10783, customfield_10727, duedate, customfield_10151, customfield_10367, customfield_10370, customfield_10486, customfield_10373, worklog, customfield_10157, customfield_10371, customfield_10008, customfield_10155, customfield_10519, customfield_10202, customfield_10306, customfield_10380, customfield_10062, ID, customfield_10375, customfield_10072, customfield_10069, customfield_10685, customfield_11251, customfield_10200, customfield_10187, customfield_11113, customfield_11245, components, customfield_11253, Transition, customfield_11250, customfield_11247, resolutiondate, customfield_10101, LastViewed, customfield_10385, customfield_11249, customfield_11279, customfield_10344, customfield_10209, customfield_10189, customfield_11212, customfield_10651, Project, customfield_10211, customfield_10105, customfield_11214, customfield_10012, customfield_10213, customfield_10395, customfield_10684, customfield_10116, customfield_10172, timeoriginalestimate, customfield_10203, customfield_10174, customfield_10025, customfield_10336, customfield_10065, customfield_10330, customfield_10223, customfield_10027, customfield_10686, fixVersions, customfield_10126, customfield_10182, customfield_10356, progress, customfield_10128, customfield_10042, customfield_10180, customfield_10562, customfield_10196, customfield_10858, customfield_10629, customfield_10261, customfield_10004, customfield_10049, customfield_10627, aggregatetimeestimate, customfield_10663, customfield_10185, customfield_10237, customfield_10557, customfield_10081, timespent, security, customfield_10057, aggregateprogress, aggregatetimespent, customfield_10314, environment, customfield_10722, customfield_10195, customfield_10239, customfield_10368, customfield_10229, customfield_10037, issuelinks, customfield_10348, customfield_10056, customfield_10131, customfield_10199, customfield_10054, Status, customfield_10618, customfield_10751, customfield_10161, Comment, Key, customfield_11254, customfield_10063, customfield_10060, customfield_10066, customfield_10226, customfield_10068, labels, customfield_10520, customfield_10489, statuscategorychangedate, customfield_10160, customfield_10222, customfield_10354, customfield_11252, customfield_10159, customfield_10393, customfield_11248, RestUrl, resolution, customfield_10384, customfield_10216, customfield_10379, customfield_10208, customfield_10386, customfield_10341, customfield_10132, customfield_10559, customfield_10171, customfield_10013, customfield_10100, customfield_10212, customfield_10204, customfield_10725, customfield_10117, customfield_10173, customfield_10011, customfield_10075, customfield_10398, customfield_10115, customfield_10347, customfield_10337, customfield_10321, customfield_10177, customfield_10215, customfield_10224, customfield_10352, Assignee, customfield_10130, customfield_10717, customfield_10129, customfield_10181, customfield_10000, customfield_10453, customfield_11181, customfield_10178, customfield_10029, customfield_10276, watches, customfield_10560, customfield_10133, customfield_10120, customfield_10102, customfield_10634, customfield_10249, customfield_10320, customfield_10048, customfield_11278, customfield_10346, customfield_10383, customfield_10184, customfield_11281, customfield_10726, customfield_10030, priority, customfield_10235, customfield_10720, customfield_10134, customfield_10047, customfield_10190, customfield_10721, customfield_10045, customfield_10448, customfield_10238, customfield_10882, customfield_10250, customfield_10198, customfield_10118, customfield_10619, customfield_10197, customfield_10251, customfield_10318, customfield_10055, Summary, Description, customfield_10447, customfield_10304, customfield_10255, customfield_10059, customfield_10053, customfield_10148, customfield_10382, customfield_10193, customfield_10488, attachment, customfield_10728, customfield_10225, customfield_10158, workratio, customfield_10372, customfield_10206, customfield_10156, customfield_10664, customfield_10201, customfield_10154, customfield_10521, statusCategory, Created, customfield_10064, issuetype, customfield_10254, customfield_10374, customfield_10326, customfield_10342, customfield_10317, customfield_10554, customfield_10227, customfield_10210, customfield_10214, customfield_10391, customfield_11246, customfield_10585, customfield_10070, customfield_11182, customfield_10630, customfield_10135, aggregatetimeoriginalestimate, customfield_10631, customfield_10010, customfield_10061, customfield_11215, timeestimate, customfield_10662, customfield_11213, customfield_10556, customfield_10028, customfield_10635, customfield_10396, customfield_10275, customfield_10719, timetracking, customfield_10175, customfield_10024, Creator, customfield_10381, customfield_10023, Updated, customfield_11179, customfield_10127, customfield_10183, customfield_11122, customfield_10561, customfield_10043, customfield_10322, customfield_10046, customfield_10192, customfield_10041, Reporter, customfield_10044, customfield_10628, customfield_10335, customfield_10040, subtasks, customfield_10191, customfield_10626, customfield_10343, versions, customfield_10186, HttpUrl, customfield_10319, customfield_10303, customfield_10001, customfield_10555, customfield_10252, customfield_10050
[2025-07-03 08:13:02.735] [DEBUG] [Application] [16] Found Comment property with 18 comments
[2025-07-03 08:13:02.752] [INFO] [Application] [16] Starting data extraction from ticket TESTIT-49342
[2025-07-03 08:13:02.802] [DEBUG] [Application] [16] Extracted data - Name: 'Test1 Test2', Job: 'Intern', Dept: 'IT', Model: 'test', Location: ''
[2025-07-03 08:13:03.207] [DEBUG] [Application] [16] Generating SAM account name for Test1 Test2
[2025-07-03 08:13:03.207] [DEBUG] [Application] [16] Checking if SAM account name 'test2t' exists
[2025-07-03 08:13:03.224] [INFO] [Application] [16] Generated SAM account name: test2t (simulation mode - could not verify uniqueness)
[2025-07-03 08:13:03.235] [DEBUG] [Application] [16] SAM generation debug - Type: String, Value: '31 32 33 test2t'
[2025-07-03 08:13:03.477] [DEBUG] [Application] [16] Generated account details from Jira ticket successfully
[2025-07-03 08:13:03.492] [INFO] [Application] [16] Data extraction completed. Please verify the populated fields.
[2025-07-03 08:13:03.503] [DEBUG] [Application] [16] Workflow step 'FetchData' status: Completed
[2025-07-03 08:13:03.512] [INFO] [Application] [16] Step 3: Validate Information
[2025-07-03 08:13:03.522] [DEBUG] [Application] [16] Workflow step 'Validation' status: InProgress
[2025-07-03 08:13:03.535] [INFO] [Application] [16] Successfully fetched and populated data from ticket TESTIT-49342
[2025-07-03 08:17:35.039] [DEBUG] [Testing] [18] Test registered: ConfigurationValidation in suite Unit
[2025-07-03 08:17:35.181] [DEBUG] [Testing] [18] Test registered: ConfigurationValidation in Unit
[2025-07-03 08:17:35.189] [DEBUG] [Testing] [18] Test registered: LoggingSystem in suite Unit
[2025-07-03 08:17:35.197] [DEBUG] [Testing] [18] Test registered: LoggingSystem in Unit
[2025-07-03 08:17:35.206] [DEBUG] [Testing] [18] Test registered: VersionManager in suite Unit
[2025-07-03 08:17:35.217] [DEBUG] [Testing] [18] Test registered: VersionManager in Unit
[2025-07-03 08:17:35.221] [DEBUG] [Testing] [18] Test registered: JiraConnection in suite Integration
[2025-07-03 08:17:35.238] [DEBUG] [Testing] [18] Test registered: JiraConnection in Integration
[2025-07-03 08:17:35.238] [DEBUG] [Testing] [18] Test registered: ActiveDirectoryConnection in suite Integration
[2025-07-03 08:17:35.257] [DEBUG] [Testing] [18] Test registered: ActiveDirectoryConnection in Integration
[2025-07-03 08:17:35.267] [DEBUG] [Testing] [18] Test registered: StartupTime in suite Performance
[2025-07-03 08:17:35.389] [DEBUG] [Testing] [18] Test registered: StartupTime in Performance
[2025-07-03 08:17:35.389] [DEBUG] [Testing] [18] Test registered: MemoryUsage in suite Performance
[2025-07-03 08:17:35.405] [DEBUG] [Testing] [18] Test registered: MemoryUsage in Performance
[2025-07-03 08:17:35.422] [DEBUG] [Testing] [18] Test registered: CredentialHandling in suite Security
[2025-07-03 08:17:35.439] [DEBUG] [Testing] [18] Test registered: CredentialHandling in Security
[2025-07-03 08:17:35.456] [DEBUG] [Testing] [18] Test registered: InputValidation in suite Security
[2025-07-03 08:17:35.472] [DEBUG] [Testing] [18] Test registered: InputValidation in Security
[2025-07-03 08:17:35.487] [DEBUG] [Testing] [18] Test registered: WindowCreation in suite UI
[2025-07-03 08:17:35.504] [DEBUG] [Testing] [18] Test registered: WindowCreation in UI
[2025-07-03 08:17:35.504] [INFO] [Testing] [18] Core tests initialized
[2025-07-03 08:17:35.550] [INFO] [Documentation] [18] Initializing documentation system
[2025-07-03 08:17:35.570] [INFO] [Documentation] [18] Starting documentation generation for: api
[2025-07-03 08:17:35.654] [INFO] [Documentation] [18] Documentation saved: Documentation\API_Reference.html
[2025-07-03 08:17:35.682] [INFO] [Documentation] [18] Documentation saved: Documentation\API_Reference.md
[2025-07-03 08:17:35.725] [INFO] [Documentation] [18] Documentation generation completed for: api
[2025-07-03 08:17:35.753] [INFO] [Documentation] [18] API documentation generated: 2 files
[2025-07-03 08:17:35.770] [INFO] [Documentation] [18] Starting documentation generation for: help
[2025-07-03 08:17:35.804] [INFO] [Documentation] [18] Documentation generation completed for: help
[2025-07-03 08:17:35.822] [INFO] [Documentation] [18] Help system documentation generated
[2025-07-03 08:17:35.838] [INFO] [Documentation] [18] Documentation system initialized
[2025-07-03 08:17:35.967] [INFO] [Application] [18] Initializing enhanced UI components...
[2025-07-03 08:17:36.063] [DEBUG] [Application] [18] Theme applied to controls
[2025-07-03 08:17:36.087] [DEBUG] [Application] [18] Modern theme applied successfully
[2025-07-03 08:17:36.121] [DEBUG] [Application] [18] Field validation initialized
[2025-07-03 08:17:36.203] [DEBUG] [Application] [18] Responsive layout initialized
[2025-07-03 08:17:36.357] [DEBUG] [Application] [18] Workflow step 'Connection' status: Pending
[2025-07-03 08:17:36.437] [INFO] [Application] [18] Enhanced UI initialization completed
[2025-07-03 08:17:36.488] [INFO] [Application] [18] Welcome! Please connect to Jira to begin.
[2025-07-03 08:17:36.521] [INFO] [Application] [18] OnboardingFromJiraGUI started successfully
[2025-07-03 08:17:36.762] [INFO] [Application] [18] [OK] Monitoring system initialized
[2025-07-03 08:17:36.857] [INFO] [Audit] [18] Audit: akinje performed PermissionInit on UserPermissions - Success | Context: SessionId=Unknown; Action=PermissionInit; Timestamp=07/03/2025 08:17:36; Result=Success; User=akinje; Detected=Standard user privileges; Target=UserPermissions; PrivilegeLevel=Standard
[2025-07-03 08:17:36.909] [INFO] [Application] [18] [OK] User permissions initialized for JERAGM\akinje
[2025-07-03 08:17:36.961] [DEBUG] [Application] [18]  Cache cleanup scheduled every 30 minutes
[2025-07-03 08:17:37.008] [INFO] [Application] [18] Applying Modern UI Integration...
[2025-07-03 08:17:37.182] [DEBUG] [Application] [18] Auto-completion setup for LastNameBox
[2025-07-03 08:17:37.196] [DEBUG] [Application] [18] Auto-completion setup for JobTitleBox
[2025-07-03 08:17:37.217] [DEBUG] [Application] [18] Auto-completion setup for FirstNameBox
[2025-07-03 08:17:37.250] [DEBUG] [Application] [18] Auto-completion setup for DepartmentBox
[2025-07-03 08:17:37.290] [DEBUG] [Application] [18] Auto-completion setup for ModelAccountBox
[2025-07-03 08:17:37.332] [DEBUG] [Application] [18] Auto-completion integration completed
[2025-07-03 08:17:37.441] [INFO] [Application] [18] Modern UI Integration completed successfully
[2025-07-03 08:17:46.604] [INFO] [Application] [18] Step 1: Connect to Jira
[2025-07-03 08:17:46.624] [DEBUG] [Application] [18] Workflow step 'Connection' status: InProgress
[2025-07-03 08:17:46.642] [INFO] [Application] [18] Connecting to https://jeragm.atlassian.net...
[2025-07-03 08:17:48.592] [DEBUG] [Application] [18] Workflow step 'Connection' status: Completed
[2025-07-03 08:17:48.624] [INFO] [Application] [18] Successfully connected to Jira
[2025-07-03 08:17:53.271] [INFO] [Application] [18] Step 2: Fetch Ticket Data
[2025-07-03 08:17:53.288] [DEBUG] [Application] [18] Workflow step 'FetchData' status: InProgress
[2025-07-03 08:17:53.290] [INFO] [Application] [18] Fetching data for ticket TESTIT-49342...
[2025-07-03 08:17:54.732] [DEBUG] [Application] [18] Cached ticket: TESTIT-49342
[2025-07-03 08:17:54.742] [DEBUG] [Application] [18] Issue object properties: customfield_10553, customfield_10058, customfield_10305, customfield_10149, customfield_10723, customfield_10928, customfield_10194, customfield_10363, customfield_10750, customfield_10147, customfield_11280, customfield_10729, customfield_10153, customfield_10369, customfield_10145, customfield_10783, customfield_10727, duedate, customfield_10151, customfield_10367, customfield_10370, customfield_10486, customfield_10373, worklog, customfield_10157, customfield_10371, customfield_10008, customfield_10155, customfield_10519, customfield_10202, customfield_10306, customfield_10380, customfield_10062, ID, customfield_10375, customfield_10072, customfield_10069, customfield_10685, customfield_11251, customfield_10200, customfield_10187, customfield_11113, customfield_11245, components, customfield_11253, Transition, customfield_11250, customfield_11247, resolutiondate, customfield_10101, LastViewed, customfield_10385, customfield_11249, customfield_11279, customfield_10344, customfield_10209, customfield_10189, customfield_11212, customfield_10651, Project, customfield_10211, customfield_10105, customfield_11214, customfield_10012, customfield_10213, customfield_10395, customfield_10684, customfield_10116, customfield_10172, timeoriginalestimate, customfield_10203, customfield_10174, customfield_10025, customfield_10336, customfield_10065, customfield_10330, customfield_10223, customfield_10027, customfield_10686, fixVersions, customfield_10126, customfield_10182, customfield_10356, progress, customfield_10128, customfield_10042, customfield_10180, customfield_10562, customfield_10196, customfield_10858, customfield_10629, customfield_10261, customfield_10004, customfield_10049, customfield_10627, aggregatetimeestimate, customfield_10663, customfield_10185, customfield_10237, customfield_10557, customfield_10081, timespent, security, customfield_10057, aggregateprogress, aggregatetimespent, customfield_10314, environment, customfield_10722, customfield_10195, customfield_10239, customfield_10368, customfield_10229, customfield_10037, issuelinks, customfield_10348, customfield_10056, customfield_10131, customfield_10199, customfield_10054, Status, customfield_10618, customfield_10751, customfield_10161, Comment, Key, customfield_11254, customfield_10063, customfield_10060, customfield_10066, customfield_10226, customfield_10068, labels, customfield_10520, customfield_10489, statuscategorychangedate, customfield_10160, customfield_10222, customfield_10354, customfield_11252, customfield_10159, customfield_10393, customfield_11248, RestUrl, resolution, customfield_10384, customfield_10216, customfield_10379, customfield_10208, customfield_10386, customfield_10341, customfield_10132, customfield_10559, customfield_10171, customfield_10013, customfield_10100, customfield_10212, customfield_10204, customfield_10725, customfield_10117, customfield_10173, customfield_10011, customfield_10075, customfield_10398, customfield_10115, customfield_10347, customfield_10337, customfield_10321, customfield_10177, customfield_10215, customfield_10224, customfield_10352, Assignee, customfield_10130, customfield_10717, customfield_10129, customfield_10181, customfield_10000, customfield_10453, customfield_11181, customfield_10178, customfield_10029, customfield_10276, watches, customfield_10560, customfield_10133, customfield_10120, customfield_10102, customfield_10634, customfield_10249, customfield_10320, customfield_10048, customfield_11278, customfield_10346, customfield_10383, customfield_10184, customfield_11281, customfield_10726, customfield_10030, priority, customfield_10235, customfield_10720, customfield_10134, customfield_10047, customfield_10190, customfield_10721, customfield_10045, customfield_10448, customfield_10238, customfield_10882, customfield_10250, customfield_10198, customfield_10118, customfield_10619, customfield_10197, customfield_10251, customfield_10318, customfield_10055, Summary, Description, customfield_10447, customfield_10304, customfield_10255, customfield_10059, customfield_10053, customfield_10148, customfield_10382, customfield_10193, customfield_10488, attachment, customfield_10728, customfield_10225, customfield_10158, workratio, customfield_10372, customfield_10206, customfield_10156, customfield_10664, customfield_10201, customfield_10154, customfield_10521, statusCategory, Created, customfield_10064, issuetype, customfield_10254, customfield_10374, customfield_10326, customfield_10342, customfield_10317, customfield_10554, customfield_10227, customfield_10210, customfield_10214, customfield_10391, customfield_11246, customfield_10585, customfield_10070, customfield_11182, customfield_10630, customfield_10135, aggregatetimeoriginalestimate, customfield_10631, customfield_10010, customfield_10061, customfield_11215, timeestimate, customfield_10662, customfield_11213, customfield_10556, customfield_10028, customfield_10635, customfield_10396, customfield_10275, customfield_10719, timetracking, customfield_10175, customfield_10024, Creator, customfield_10381, customfield_10023, Updated, customfield_11179, customfield_10127, customfield_10183, customfield_11122, customfield_10561, customfield_10043, customfield_10322, customfield_10046, customfield_10192, customfield_10041, Reporter, customfield_10044, customfield_10628, customfield_10335, customfield_10040, subtasks, customfield_10191, customfield_10626, customfield_10343, versions, customfield_10186, HttpUrl, customfield_10319, customfield_10303, customfield_10001, customfield_10555, customfield_10252, customfield_10050
[2025-07-03 08:17:54.754] [DEBUG] [Application] [18] Found Comment property with 18 comments
[2025-07-03 08:17:54.773] [INFO] [Application] [18] Starting data extraction from ticket TESTIT-49342
[2025-07-03 08:17:54.801] [DEBUG] [Application] [18] Extracted data - Name: 'Test1 Test2', Job: 'Intern', Dept: 'IT', Model: 'test', Location: ''
[2025-07-03 08:17:55.143] [DEBUG] [Application] [18] Generating SAM account name for Test1 Test2
[2025-07-03 08:17:55.151] [DEBUG] [Application] [18] Checking if SAM account name 'test2t' exists
[2025-07-03 08:17:55.167] [INFO] [Application] [18] Generated SAM account name: test2t (simulation mode - could not verify uniqueness)
[2025-07-03 08:17:55.189] [DEBUG] [Application] [18] SAM generation debug - Type: String, Value: '31 32 33 test2t'
[2025-07-03 08:17:55.355] [DEBUG] [Application] [18] Generated account details from Jira ticket successfully
[2025-07-03 08:17:55.355] [INFO] [Application] [18] Data extraction completed. Please verify the populated fields.
[2025-07-03 08:17:55.373] [DEBUG] [Application] [18] Workflow step 'FetchData' status: Completed
[2025-07-03 08:17:55.388] [INFO] [Application] [18] Step 3: Validate Information
[2025-07-03 08:17:55.394] [DEBUG] [Application] [18] Workflow step 'Validation' status: InProgress
[2025-07-03 08:17:55.406] [INFO] [Application] [18] Successfully fetched and populated data from ticket TESTIT-49342
[2025-07-03 08:29:13.939] [DEBUG] [Testing] [17] Test registered: ConfigurationValidation in suite Unit
[2025-07-03 08:29:14.143] [DEBUG] [Testing] [17] Test registered: ConfigurationValidation in Unit
[2025-07-03 08:29:14.196] [DEBUG] [Testing] [17] Test registered: LoggingSystem in suite Unit
[2025-07-03 08:29:14.212] [DEBUG] [Testing] [17] Test registered: LoggingSystem in Unit
[2025-07-03 08:29:14.234] [DEBUG] [Testing] [17] Test registered: VersionManager in suite Unit
[2025-07-03 08:29:14.234] [DEBUG] [Testing] [17] Test registered: VersionManager in Unit
[2025-07-03 08:29:14.250] [DEBUG] [Testing] [17] Test registered: JiraConnection in suite Integration
[2025-07-03 08:29:14.266] [DEBUG] [Testing] [17] Test registered: JiraConnection in Integration
[2025-07-03 08:29:14.281] [DEBUG] [Testing] [17] Test registered: ActiveDirectoryConnection in suite Integration
[2025-07-03 08:29:14.281] [DEBUG] [Testing] [17] Test registered: ActiveDirectoryConnection in Integration
[2025-07-03 08:29:14.310] [DEBUG] [Testing] [17] Test registered: StartupTime in suite Performance
[2025-07-03 08:29:14.323] [DEBUG] [Testing] [17] Test registered: StartupTime in Performance
[2025-07-03 08:29:14.335] [DEBUG] [Testing] [17] Test registered: MemoryUsage in suite Performance
[2025-07-03 08:29:14.347] [DEBUG] [Testing] [17] Test registered: MemoryUsage in Performance
[2025-07-03 08:29:14.361] [DEBUG] [Testing] [17] Test registered: CredentialHandling in suite Security
[2025-07-03 08:29:14.373] [DEBUG] [Testing] [17] Test registered: CredentialHandling in Security
[2025-07-03 08:29:14.384] [DEBUG] [Testing] [17] Test registered: InputValidation in suite Security
[2025-07-03 08:29:14.396] [DEBUG] [Testing] [17] Test registered: InputValidation in Security
[2025-07-03 08:29:14.403] [DEBUG] [Testing] [17] Test registered: WindowCreation in suite UI
[2025-07-03 08:29:14.424] [DEBUG] [Testing] [17] Test registered: WindowCreation in UI
[2025-07-03 08:29:14.437] [INFO] [Testing] [17] Core tests initialized
[2025-07-03 08:29:14.475] [INFO] [Documentation] [17] Initializing documentation system
[2025-07-03 08:29:14.505] [INFO] [Documentation] [17] Starting documentation generation for: api
[2025-07-03 08:29:14.585] [INFO] [Documentation] [17] Documentation saved: Documentation\API_Reference.html
[2025-07-03 08:29:14.608] [INFO] [Documentation] [17] Documentation saved: Documentation\API_Reference.md
[2025-07-03 08:29:14.637] [INFO] [Documentation] [17] Documentation generation completed for: api
[2025-07-03 08:29:14.655] [INFO] [Documentation] [17] API documentation generated: 2 files
[2025-07-03 08:29:14.688] [INFO] [Documentation] [17] Starting documentation generation for: help
[2025-07-03 08:29:14.706] [INFO] [Documentation] [17] Documentation generation completed for: help
[2025-07-03 08:29:14.717] [INFO] [Documentation] [17] Help system documentation generated
[2025-07-03 08:29:14.726] [INFO] [Documentation] [17] Documentation system initialized
[2025-07-03 08:29:14.872] [INFO] [Application] [17] Initializing enhanced UI components...
[2025-07-03 08:29:15.000] [DEBUG] [Application] [17] Theme applied to controls
[2025-07-03 08:29:15.015] [DEBUG] [Application] [17] Modern theme applied successfully
[2025-07-03 08:29:15.062] [DEBUG] [Application] [17] Field validation initialized
[2025-07-03 08:29:15.096] [DEBUG] [Application] [17] Responsive layout initialized
[2025-07-03 08:29:15.246] [DEBUG] [Application] [17] Workflow step 'Connection' status: Pending
[2025-07-03 08:29:15.298] [INFO] [Application] [17] Enhanced UI initialization completed
[2025-07-03 08:29:15.337] [INFO] [Application] [17] Welcome! Please connect to Jira to begin.
[2025-07-03 08:29:15.358] [INFO] [Application] [17] OnboardingFromJiraGUI started successfully
[2025-07-03 08:29:15.405] [INFO] [Application] [17] [OK] Monitoring system initialized
[2025-07-03 08:29:15.434] [INFO] [Audit] [17] Audit: akinje performed PermissionInit on UserPermissions - Success | Context: SessionId=Unknown; Action=PermissionInit; Timestamp=07/03/2025 08:29:15; Result=Success; User=akinje; Detected=Standard user privileges; Target=UserPermissions; PrivilegeLevel=Standard
[2025-07-03 08:29:15.469] [INFO] [Application] [17] [OK] User permissions initialized for JERAGM\akinje
[2025-07-03 08:29:15.484] [DEBUG] [Application] [17]  Cache cleanup scheduled every 30 minutes
[2025-07-03 08:29:15.484] [INFO] [Application] [17] Applying Modern UI Integration...
[2025-07-03 08:29:15.547] [DEBUG] [Application] [17] Auto-completion setup for LastNameBox
[2025-07-03 08:29:15.558] [DEBUG] [Application] [17] Auto-completion setup for JobTitleBox
[2025-07-03 08:29:15.568] [DEBUG] [Application] [17] Auto-completion setup for FirstNameBox
[2025-07-03 08:29:15.577] [DEBUG] [Application] [17] Auto-completion setup for DepartmentBox
[2025-07-03 08:29:15.587] [DEBUG] [Application] [17] Auto-completion setup for ModelAccountBox
[2025-07-03 08:29:15.595] [DEBUG] [Application] [17] Auto-completion integration completed
[2025-07-03 08:29:15.625] [INFO] [Application] [17] Modern UI Integration completed successfully
[2025-07-03 08:32:00.757] [INFO] [Application] [17] Step 1: Connect to Jira
[2025-07-03 08:32:00.758] [DEBUG] [Application] [17] Workflow step 'Connection' status: InProgress
[2025-07-03 08:32:00.786] [INFO] [Application] [17] Connecting to https://jeragm.atlassian.net...
[2025-07-03 08:32:02.276] [DEBUG] [Application] [17] Workflow step 'Connection' status: Completed
[2025-07-03 08:32:02.276] [INFO] [Application] [17] Successfully connected to Jira
[2025-07-03 08:32:10.411] [INFO] [Application] [17] Step 2: Fetch Ticket Data
[2025-07-03 08:32:10.429] [DEBUG] [Application] [17] Workflow step 'FetchData' status: InProgress
[2025-07-03 08:32:10.445] [INFO] [Application] [17] Fetching data for ticket TESTIT-49342...
[2025-07-03 08:32:11.879] [DEBUG] [Application] [17] Cached ticket: TESTIT-49342
[2025-07-03 08:32:11.898] [DEBUG] [Application] [17] Issue object properties: customfield_10553, customfield_10058, customfield_10305, customfield_10149, customfield_10723, customfield_10928, customfield_10194, customfield_10363, customfield_10750, customfield_10147, customfield_11280, customfield_10729, customfield_10153, customfield_10369, customfield_10145, customfield_10783, customfield_10727, duedate, customfield_10151, customfield_10367, customfield_10370, customfield_10486, customfield_10373, worklog, customfield_10157, customfield_10371, customfield_10008, customfield_10155, customfield_10519, customfield_10202, customfield_10306, customfield_10380, customfield_10062, ID, customfield_10375, customfield_10072, customfield_10069, customfield_10685, customfield_11251, customfield_10200, customfield_10187, customfield_11113, customfield_11245, components, customfield_11253, Transition, customfield_11250, customfield_11247, resolutiondate, customfield_10101, LastViewed, customfield_10385, customfield_11249, customfield_11279, customfield_10344, customfield_10209, customfield_10189, customfield_11212, customfield_10651, Project, customfield_10211, customfield_10105, customfield_11214, customfield_10012, customfield_10213, customfield_10395, customfield_10684, customfield_10116, customfield_10172, timeoriginalestimate, customfield_10203, customfield_10174, customfield_10025, customfield_10336, customfield_10065, customfield_10330, customfield_10223, customfield_10027, customfield_10686, fixVersions, customfield_10126, customfield_10182, customfield_10356, progress, customfield_10128, customfield_10042, customfield_10180, customfield_10562, customfield_10196, customfield_10858, customfield_10629, customfield_10261, customfield_10004, customfield_10049, customfield_10627, aggregatetimeestimate, customfield_10663, customfield_10185, customfield_10237, customfield_10557, customfield_10081, timespent, security, customfield_10057, aggregateprogress, aggregatetimespent, customfield_10314, environment, customfield_10722, customfield_10195, customfield_10239, customfield_10368, customfield_10229, customfield_10037, issuelinks, customfield_10348, customfield_10056, customfield_10131, customfield_10199, customfield_10054, Status, customfield_10618, customfield_10751, customfield_10161, Comment, Key, customfield_11254, customfield_10063, customfield_10060, customfield_10066, customfield_10226, customfield_10068, labels, customfield_10520, customfield_10489, statuscategorychangedate, customfield_10160, customfield_10222, customfield_10354, customfield_11252, customfield_10159, customfield_10393, customfield_11248, RestUrl, resolution, customfield_10384, customfield_10216, customfield_10379, customfield_10208, customfield_10386, customfield_10341, customfield_10132, customfield_10559, customfield_10171, customfield_10013, customfield_10100, customfield_10212, customfield_10204, customfield_10725, customfield_10117, customfield_10173, customfield_10011, customfield_10075, customfield_10398, customfield_10115, customfield_10347, customfield_10337, customfield_10321, customfield_10177, customfield_10215, customfield_10224, customfield_10352, Assignee, customfield_10130, customfield_10717, customfield_10129, customfield_10181, customfield_10000, customfield_10453, customfield_11181, customfield_10178, customfield_10029, customfield_10276, watches, customfield_10560, customfield_10133, customfield_10120, customfield_10102, customfield_10634, customfield_10249, customfield_10320, customfield_10048, customfield_11278, customfield_10346, customfield_10383, customfield_10184, customfield_11281, customfield_10726, customfield_10030, priority, customfield_10235, customfield_10720, customfield_10134, customfield_10047, customfield_10190, customfield_10721, customfield_10045, customfield_10448, customfield_10238, customfield_10882, customfield_10250, customfield_10198, customfield_10118, customfield_10619, customfield_10197, customfield_10251, customfield_10318, customfield_10055, Summary, Description, customfield_10447, customfield_10304, customfield_10255, customfield_10059, customfield_10053, customfield_10148, customfield_10382, customfield_10193, customfield_10488, attachment, customfield_10728, customfield_10225, customfield_10158, workratio, customfield_10372, customfield_10206, customfield_10156, customfield_10664, customfield_10201, customfield_10154, customfield_10521, statusCategory, Created, customfield_10064, issuetype, customfield_10254, customfield_10374, customfield_10326, customfield_10342, customfield_10317, customfield_10554, customfield_10227, customfield_10210, customfield_10214, customfield_10391, customfield_11246, customfield_10585, customfield_10070, customfield_11182, customfield_10630, customfield_10135, aggregatetimeoriginalestimate, customfield_10631, customfield_10010, customfield_10061, customfield_11215, timeestimate, customfield_10662, customfield_11213, customfield_10556, customfield_10028, customfield_10635, customfield_10396, customfield_10275, customfield_10719, timetracking, customfield_10175, customfield_10024, Creator, customfield_10381, customfield_10023, Updated, customfield_11179, customfield_10127, customfield_10183, customfield_11122, customfield_10561, customfield_10043, customfield_10322, customfield_10046, customfield_10192, customfield_10041, Reporter, customfield_10044, customfield_10628, customfield_10335, customfield_10040, subtasks, customfield_10191, customfield_10626, customfield_10343, versions, customfield_10186, HttpUrl, customfield_10319, customfield_10303, customfield_10001, customfield_10555, customfield_10252, customfield_10050
[2025-07-03 08:32:11.911] [DEBUG] [Application] [17] Found Comment property with 18 comments
[2025-07-03 08:32:11.919] [INFO] [Application] [17] Starting data extraction from ticket TESTIT-49342
[2025-07-03 08:32:11.950] [DEBUG] [Application] [17] Extracted data - Name: 'Test1 Test2', Job: 'Intern', Dept: 'IT', Model: 'test', Location: ''
[2025-07-03 08:32:12.307] [DEBUG] [Application] [17] Generating SAM account name for Test1 Test2
[2025-07-03 08:32:12.323] [DEBUG] [Application] [17] Checking if SAM account name 'test2t' exists
[2025-07-03 08:32:12.323] [INFO] [Application] [17] Generated SAM account name: test2t (simulation mode - could not verify uniqueness)
[2025-07-03 08:32:12.346] [DEBUG] [Application] [17] SAM account generated successfully
[2025-07-03 08:32:12.620] [DEBUG] [Application] [17] Generated account details from Jira ticket successfully
[2025-07-03 08:32:12.657] [INFO] [Application] [17] Data extraction completed. Please verify the populated fields.
[2025-07-03 08:32:12.657] [DEBUG] [Application] [17] Workflow step 'FetchData' status: Completed
[2025-07-03 08:32:12.657] [INFO] [Application] [17] Step 3: Validate Information
[2025-07-03 08:32:12.673] [DEBUG] [Application] [17] Workflow step 'Validation' status: InProgress
[2025-07-03 08:32:12.673] [INFO] [Application] [17] Successfully fetched and populated data from ticket TESTIT-49342
[2025-07-03 08:39:13.771] [DEBUG] [Testing] [17] Test registered: ConfigurationValidation in suite Unit
[2025-07-03 08:39:13.930] [DEBUG] [Testing] [17] Test registered: ConfigurationValidation in Unit
[2025-07-03 08:39:13.946] [DEBUG] [Testing] [17] Test registered: LoggingSystem in suite Unit
[2025-07-03 08:39:13.959] [DEBUG] [Testing] [17] Test registered: LoggingSystem in Unit
[2025-07-03 08:39:13.979] [DEBUG] [Testing] [17] Test registered: VersionManager in suite Unit
[2025-07-03 08:39:13.993] [DEBUG] [Testing] [17] Test registered: VersionManager in Unit
[2025-07-03 08:39:14.020] [DEBUG] [Testing] [17] Test registered: JiraConnection in suite Integration
[2025-07-03 08:39:14.034] [DEBUG] [Testing] [17] Test registered: JiraConnection in Integration
[2025-07-03 08:39:14.046] [DEBUG] [Testing] [17] Test registered: ActiveDirectoryConnection in suite Integration
[2025-07-03 08:39:14.063] [DEBUG] [Testing] [17] Test registered: ActiveDirectoryConnection in Integration
[2025-07-03 08:39:14.077] [DEBUG] [Testing] [17] Test registered: StartupTime in suite Performance
[2025-07-03 08:39:14.093] [DEBUG] [Testing] [17] Test registered: StartupTime in Performance
[2025-07-03 08:39:14.126] [DEBUG] [Testing] [17] Test registered: MemoryUsage in suite Performance
[2025-07-03 08:39:14.131] [DEBUG] [Testing] [17] Test registered: MemoryUsage in Performance
[2025-07-03 08:39:14.144] [DEBUG] [Testing] [17] Test registered: CredentialHandling in suite Security
[2025-07-03 08:39:14.161] [DEBUG] [Testing] [17] Test registered: CredentialHandling in Security
[2025-07-03 08:39:14.176] [DEBUG] [Testing] [17] Test registered: InputValidation in suite Security
[2025-07-03 08:39:14.194] [DEBUG] [Testing] [17] Test registered: InputValidation in Security
[2025-07-03 08:39:14.211] [DEBUG] [Testing] [17] Test registered: WindowCreation in suite UI
[2025-07-03 08:39:14.228] [DEBUG] [Testing] [17] Test registered: WindowCreation in UI
[2025-07-03 08:39:14.243] [INFO] [Testing] [17] Core tests initialized
[2025-07-03 08:39:14.276] [INFO] [Documentation] [17] Initializing documentation system
[2025-07-03 08:39:14.327] [INFO] [Documentation] [17] Starting documentation generation for: api
[2025-07-03 08:39:14.431] [INFO] [Documentation] [17] Documentation saved: Documentation\API_Reference.html
[2025-07-03 08:39:14.493] [INFO] [Documentation] [17] Documentation saved: Documentation\API_Reference.md
[2025-07-03 08:39:14.510] [INFO] [Documentation] [17] Documentation generation completed for: api
[2025-07-03 08:39:14.550] [INFO] [Documentation] [17] API documentation generated: 2 files
[2025-07-03 08:39:14.568] [INFO] [Documentation] [17] Starting documentation generation for: help
[2025-07-03 08:39:14.595] [INFO] [Documentation] [17] Documentation generation completed for: help
[2025-07-03 08:39:14.610] [INFO] [Documentation] [17] Help system documentation generated
[2025-07-03 08:39:14.610] [INFO] [Documentation] [17] Documentation system initialized
[2025-07-03 08:39:14.844] [INFO] [Application] [17] Initializing enhanced UI components...
[2025-07-03 08:39:14.993] [DEBUG] [Application] [17] Theme applied to controls
[2025-07-03 08:39:15.195] [DEBUG] [Application] [17] Modern theme applied successfully
[2025-07-03 08:39:15.285] [DEBUG] [Application] [17] Field validation initialized
[2025-07-03 08:39:15.401] [DEBUG] [Application] [17] Responsive layout initialized
[2025-07-03 08:39:15.710] [DEBUG] [Application] [17] Workflow step 'Connection' status: Pending
[2025-07-03 08:39:15.854] [INFO] [Application] [17] Enhanced UI initialization completed
[2025-07-03 08:39:15.917] [INFO] [Application] [17] Welcome! Please connect to Jira to begin.
[2025-07-03 08:39:15.969] [INFO] [Application] [17] OnboardingFromJiraGUI started successfully
[2025-07-03 08:39:16.046] [INFO] [Application] [17] [OK] Monitoring system initialized
[2025-07-03 08:39:16.174] [INFO] [Audit] [17] Audit: akinje performed PermissionInit on UserPermissions - Success | Context: SessionId=Unknown; Action=PermissionInit; Timestamp=07/03/2025 08:39:16; Result=Success; User=akinje; Detected=Standard user privileges; Target=UserPermissions; PrivilegeLevel=Standard
[2025-07-03 08:39:16.317] [INFO] [Application] [17] [OK] User permissions initialized for JERAGM\akinje
[2025-07-03 08:39:16.349] [DEBUG] [Application] [17]  Cache cleanup scheduled every 30 minutes
[2025-07-03 08:39:16.395] [INFO] [Application] [17] Applying Modern UI Integration...
[2025-07-03 08:39:16.527] [DEBUG] [Application] [17] Auto-completion setup for LastNameBox
[2025-07-03 08:39:16.542] [DEBUG] [Application] [17] Auto-completion setup for JobTitleBox
[2025-07-03 08:39:16.570] [DEBUG] [Application] [17] Auto-completion setup for FirstNameBox
[2025-07-03 08:39:16.610] [DEBUG] [Application] [17] Auto-completion setup for DepartmentBox
[2025-07-03 08:39:16.641] [DEBUG] [Application] [17] Auto-completion setup for ModelAccountBox
[2025-07-03 08:39:16.679] [DEBUG] [Application] [17] Auto-completion integration completed
[2025-07-03 08:39:16.827] [INFO] [Application] [17] Modern UI Integration completed successfully
[2025-07-03 08:39:45.703] [INFO] [Application] [17] Step 1: Connect to Jira
[2025-07-03 08:39:45.725] [DEBUG] [Application] [17] Workflow step 'Connection' status: InProgress
[2025-07-03 08:39:45.845] [INFO] [Application] [17] Connecting to https://jeragm.atlassian.net...
[2025-07-03 08:39:49.244] [DEBUG] [Application] [17] Workflow step 'Connection' status: Completed
[2025-07-03 08:39:49.263] [INFO] [Application] [17] Successfully connected to Jira
[2025-07-03 08:39:55.712] [INFO] [Application] [17] Step 2: Fetch Ticket Data
[2025-07-03 08:39:55.737] [DEBUG] [Application] [17] Workflow step 'FetchData' status: InProgress
[2025-07-03 08:39:55.753] [INFO] [Application] [17] Fetching data for ticket TESTIT-49342...
[2025-07-03 08:39:58.604] [DEBUG] [Application] [17] Cached ticket: TESTIT-49342
[2025-07-03 08:39:58.659] [DEBUG] [Application] [17] Issue object properties: customfield_10553, customfield_10058, customfield_10305, customfield_10149, customfield_10723, customfield_10928, customfield_10194, customfield_10363, customfield_10750, customfield_10147, customfield_11280, customfield_10729, customfield_10153, customfield_10369, customfield_10145, customfield_10783, customfield_10727, duedate, customfield_10151, customfield_10367, customfield_10370, customfield_10486, customfield_10373, worklog, customfield_10157, customfield_10371, customfield_10008, customfield_10155, customfield_10519, customfield_10202, customfield_10306, customfield_10380, customfield_10062, ID, customfield_10375, customfield_10072, customfield_10069, customfield_10685, customfield_11251, customfield_10200, customfield_10187, customfield_11113, customfield_11245, components, customfield_11253, Transition, customfield_11250, customfield_11247, resolutiondate, customfield_10101, LastViewed, customfield_10385, customfield_11249, customfield_11279, customfield_10344, customfield_10209, customfield_10189, customfield_11212, customfield_10651, Project, customfield_10211, customfield_10105, customfield_11214, customfield_10012, customfield_10213, customfield_10395, customfield_10684, customfield_10116, customfield_10172, timeoriginalestimate, customfield_10203, customfield_10174, customfield_10025, customfield_10336, customfield_10065, customfield_10330, customfield_10223, customfield_10027, customfield_10686, fixVersions, customfield_10126, customfield_10182, customfield_10356, progress, customfield_10128, customfield_10042, customfield_10180, customfield_10562, customfield_10196, customfield_10858, customfield_10629, customfield_10261, customfield_10004, customfield_10049, customfield_10627, aggregatetimeestimate, customfield_10663, customfield_10185, customfield_10237, customfield_10557, customfield_10081, timespent, security, customfield_10057, aggregateprogress, aggregatetimespent, customfield_10314, environment, customfield_10722, customfield_10195, customfield_10239, customfield_10368, customfield_10229, customfield_10037, issuelinks, customfield_10348, customfield_10056, customfield_10131, customfield_10199, customfield_10054, Status, customfield_10618, customfield_10751, customfield_10161, Comment, Key, customfield_11254, customfield_10063, customfield_10060, customfield_10066, customfield_10226, customfield_10068, labels, customfield_10520, customfield_10489, statuscategorychangedate, customfield_10160, customfield_10222, customfield_10354, customfield_11252, customfield_10159, customfield_10393, customfield_11248, RestUrl, resolution, customfield_10384, customfield_10216, customfield_10379, customfield_10208, customfield_10386, customfield_10341, customfield_10132, customfield_10559, customfield_10171, customfield_10013, customfield_10100, customfield_10212, customfield_10204, customfield_10725, customfield_10117, customfield_10173, customfield_10011, customfield_10075, customfield_10398, customfield_10115, customfield_10347, customfield_10337, customfield_10321, customfield_10177, customfield_10215, customfield_10224, customfield_10352, Assignee, customfield_10130, customfield_10717, customfield_10129, customfield_10181, customfield_10000, customfield_10453, customfield_11181, customfield_10178, customfield_10029, customfield_10276, watches, customfield_10560, customfield_10133, customfield_10120, customfield_10102, customfield_10634, customfield_10249, customfield_10320, customfield_10048, customfield_11278, customfield_10346, customfield_10383, customfield_10184, customfield_11281, customfield_10726, customfield_10030, priority, customfield_10235, customfield_10720, customfield_10134, customfield_10047, customfield_10190, customfield_10721, customfield_10045, customfield_10448, customfield_10238, customfield_10882, customfield_10250, customfield_10198, customfield_10118, customfield_10619, customfield_10197, customfield_10251, customfield_10318, customfield_10055, Summary, Description, customfield_10447, customfield_10304, customfield_10255, customfield_10059, customfield_10053, customfield_10148, customfield_10382, customfield_10193, customfield_10488, attachment, customfield_10728, customfield_10225, customfield_10158, workratio, customfield_10372, customfield_10206, customfield_10156, customfield_10664, customfield_10201, customfield_10154, customfield_10521, statusCategory, Created, customfield_10064, issuetype, customfield_10254, customfield_10374, customfield_10326, customfield_10342, customfield_10317, customfield_10554, customfield_10227, customfield_10210, customfield_10214, customfield_10391, customfield_11246, customfield_10585, customfield_10070, customfield_11182, customfield_10630, customfield_10135, aggregatetimeoriginalestimate, customfield_10631, customfield_10010, customfield_10061, customfield_11215, timeestimate, customfield_10662, customfield_11213, customfield_10556, customfield_10028, customfield_10635, customfield_10396, customfield_10275, customfield_10719, timetracking, customfield_10175, customfield_10024, Creator, customfield_10381, customfield_10023, Updated, customfield_11179, customfield_10127, customfield_10183, customfield_11122, customfield_10561, customfield_10043, customfield_10322, customfield_10046, customfield_10192, customfield_10041, Reporter, customfield_10044, customfield_10628, customfield_10335, customfield_10040, subtasks, customfield_10191, customfield_10626, customfield_10343, versions, customfield_10186, HttpUrl, customfield_10319, customfield_10303, customfield_10001, customfield_10555, customfield_10252, customfield_10050
[2025-07-03 08:39:58.747] [DEBUG] [Application] [17] Found Comment property with 18 comments
[2025-07-03 08:39:58.781] [INFO] [Application] [17] Starting data extraction from ticket TESTIT-49342
[2025-07-03 08:39:58.857] [DEBUG] [Application] [17] Extracted data - Name: 'Test1 Test2', Job: 'Intern', Dept: 'IT', Model: 'test', Location: ''
[2025-07-03 08:39:59.377] [DEBUG] [Application] [17] Generating SAM account name for Test1 Test2
[2025-07-03 08:39:59.395] [DEBUG] [Application] [17] Checking if SAM account name 'test2t' exists
[2025-07-03 08:39:59.447] [INFO] [Application] [17] Generated SAM account name: test2t (simulation mode - could not verify uniqueness)
[2025-07-03 08:39:59.496] [DEBUG] [Application] [17] SAM account generated successfully
[2025-07-03 08:39:59.762] [DEBUG] [Application] [17] Generated account details from Jira ticket successfully
[2025-07-03 08:39:59.842] [INFO] [Application] [17] Data extraction completed. Please verify the populated fields.
[2025-07-03 08:39:59.876] [DEBUG] [Application] [17] Workflow step 'FetchData' status: Completed
[2025-07-03 08:39:59.896] [INFO] [Application] [17] Step 3: Validate Information
[2025-07-03 08:39:59.945] [DEBUG] [Application] [17] Workflow step 'Validation' status: InProgress
[2025-07-03 08:39:59.983] [INFO] [Application] [17] Successfully fetched and populated data from ticket TESTIT-49342
[2025-07-03 09:27:28.897] [DEBUG] [Testing] [16] Test registered: ConfigurationValidation in suite Unit
[2025-07-03 09:27:29.021] [DEBUG] [Testing] [16] Test registered: ConfigurationValidation in Unit
[2025-07-03 09:27:29.035] [DEBUG] [Testing] [16] Test registered: LoggingSystem in suite Unit
[2025-07-03 09:27:29.042] [DEBUG] [Testing] [16] Test registered: LoggingSystem in Unit
[2025-07-03 09:27:29.048] [DEBUG] [Testing] [16] Test registered: VersionManager in suite Unit
[2025-07-03 09:27:29.063] [DEBUG] [Testing] [16] Test registered: VersionManager in Unit
[2025-07-03 09:27:29.073] [DEBUG] [Testing] [16] Test registered: JiraConnection in suite Integration
[2025-07-03 09:27:29.080] [DEBUG] [Testing] [16] Test registered: JiraConnection in Integration
[2025-07-03 09:27:29.094] [DEBUG] [Testing] [16] Test registered: ActiveDirectoryConnection in suite Integration
[2025-07-03 09:27:29.103] [DEBUG] [Testing] [16] Test registered: ActiveDirectoryConnection in Integration
[2025-07-03 09:27:29.116] [DEBUG] [Testing] [16] Test registered: StartupTime in suite Performance
[2025-07-03 09:27:29.130] [DEBUG] [Testing] [16] Test registered: StartupTime in Performance
[2025-07-03 09:27:29.139] [DEBUG] [Testing] [16] Test registered: MemoryUsage in suite Performance
[2025-07-03 09:27:29.146] [DEBUG] [Testing] [16] Test registered: MemoryUsage in Performance
[2025-07-03 09:27:29.236] [DEBUG] [Testing] [16] Test registered: CredentialHandling in suite Security
[2025-07-03 09:27:29.242] [DEBUG] [Testing] [16] Test registered: CredentialHandling in Security
[2025-07-03 09:27:29.251] [DEBUG] [Testing] [16] Test registered: InputValidation in suite Security
[2025-07-03 09:27:29.266] [DEBUG] [Testing] [16] Test registered: InputValidation in Security
[2025-07-03 09:27:29.278] [DEBUG] [Testing] [16] Test registered: WindowCreation in suite UI
[2025-07-03 09:27:29.289] [DEBUG] [Testing] [16] Test registered: WindowCreation in UI
[2025-07-03 09:27:29.299] [INFO] [Testing] [16] Core tests initialized
[2025-07-03 09:27:29.335] [INFO] [Documentation] [16] Initializing documentation system
[2025-07-03 09:27:29.361] [INFO] [Documentation] [16] Starting documentation generation for: api
[2025-07-03 09:27:29.435] [INFO] [Documentation] [16] Documentation saved: Documentation\API_Reference.html
[2025-07-03 09:27:29.460] [INFO] [Documentation] [16] Documentation saved: Documentation\API_Reference.md
[2025-07-03 09:27:29.479] [INFO] [Documentation] [16] Documentation generation completed for: api
[2025-07-03 09:27:29.492] [INFO] [Documentation] [16] API documentation generated: 2 files
[2025-07-03 09:27:29.510] [INFO] [Documentation] [16] Starting documentation generation for: help
[2025-07-03 09:27:29.527] [INFO] [Documentation] [16] Documentation generation completed for: help
[2025-07-03 09:27:29.539] [INFO] [Documentation] [16] Help system documentation generated
[2025-07-03 09:27:29.550] [INFO] [Documentation] [16] Documentation system initialized
[2025-07-03 09:27:29.695] [INFO] [Application] [16] Initializing enhanced UI components...
[2025-07-03 09:27:29.783] [DEBUG] [Application] [16] Theme applied to controls
[2025-07-03 09:27:29.827] [DEBUG] [Application] [16] Modern theme applied successfully
[2025-07-03 09:27:29.870] [DEBUG] [Application] [16] Field validation initialized
[2025-07-03 09:27:29.912] [DEBUG] [Application] [16] Responsive layout initialized
[2025-07-03 09:27:30.114] [DEBUG] [Application] [16] Workflow step 'Connection' status: Pending
[2025-07-03 09:27:30.189] [INFO] [Application] [16] Enhanced UI initialization completed
[2025-07-03 09:27:30.241] [INFO] [Application] [16] Welcome! Please connect to Jira to begin.
[2025-07-03 09:27:30.264] [INFO] [Application] [16] OnboardingFromJiraGUI started successfully
[2025-07-03 09:27:30.302] [INFO] [Application] [16] [OK] Monitoring system initialized
[2025-07-03 09:27:30.359] [INFO] [Audit] [16] Audit: akinje performed PermissionInit on UserPermissions - Success | Context: SessionId=Unknown; Action=PermissionInit; Timestamp=07/03/2025 09:27:30; Result=Success; User=akinje; Detected=Standard user privileges; Target=UserPermissions; PrivilegeLevel=Standard
[2025-07-03 09:27:30.527] [INFO] [Application] [16] [OK] User permissions initialized for JERAGM\akinje
[2025-07-03 09:27:30.571] [DEBUG] [Application] [16]  Cache cleanup scheduled every 30 minutes
[2025-07-03 09:27:30.590] [INFO] [Application] [16] Applying Modern UI Integration...
[2025-07-03 09:27:30.736] [DEBUG] [Application] [16] Auto-completion setup for LastNameBox
[2025-07-03 09:27:30.754] [DEBUG] [Application] [16] Auto-completion setup for JobTitleBox
[2025-07-03 09:27:30.785] [DEBUG] [Application] [16] Auto-completion setup for FirstNameBox
[2025-07-03 09:27:30.807] [DEBUG] [Application] [16] Auto-completion setup for DepartmentBox
[2025-07-03 09:27:30.833] [DEBUG] [Application] [16] Auto-completion setup for ModelAccountBox
[2025-07-03 09:27:30.856] [DEBUG] [Application] [16] Auto-completion integration completed
[2025-07-03 09:27:30.915] [INFO] [Application] [16] Modern UI Integration completed successfully
[2025-07-03 09:27:48.851] [INFO] [Application] [16] Step 1: Connect to Jira
[2025-07-03 09:27:48.872] [DEBUG] [Application] [16] Workflow step 'Connection' status: InProgress
[2025-07-03 09:27:48.934] [INFO] [Application] [16] Connecting to https://jeragm.atlassian.net...
[2025-07-03 09:27:51.179] [DEBUG] [Application] [16] Workflow step 'Connection' status: Completed
[2025-07-03 09:27:51.200] [INFO] [Application] [16] Successfully connected to Jira
[2025-07-03 09:28:01.556] [INFO] [Application] [16] Step 2: Fetch Ticket Data
[2025-07-03 09:28:01.587] [DEBUG] [Application] [16] Workflow step 'FetchData' status: InProgress
[2025-07-03 09:28:01.614] [INFO] [Application] [16] Fetching data for ticket TESTIT-49342...
[2025-07-03 09:28:04.782] [DEBUG] [Application] [16] Cached ticket: TESTIT-49342
[2025-07-03 09:28:04.853] [DEBUG] [Application] [16] Issue object properties: customfield_10553, customfield_10058, customfield_10305, customfield_10149, customfield_10723, customfield_10928, customfield_10194, customfield_10363, customfield_10750, customfield_10147, customfield_11280, customfield_10729, customfield_10153, customfield_10369, customfield_10145, customfield_10783, customfield_10727, duedate, customfield_10151, customfield_10367, customfield_10370, customfield_10486, customfield_10373, worklog, customfield_10157, customfield_10371, customfield_10008, customfield_10155, customfield_10519, customfield_10202, customfield_10306, customfield_10380, customfield_10062, ID, customfield_10375, customfield_10072, customfield_10069, customfield_10685, customfield_11251, customfield_10200, customfield_10187, customfield_11113, customfield_11245, components, customfield_11253, Transition, customfield_11250, customfield_11247, resolutiondate, customfield_10101, LastViewed, customfield_10385, customfield_11249, customfield_11279, customfield_10344, customfield_10209, customfield_10189, customfield_11212, customfield_10651, Project, customfield_10211, customfield_10105, customfield_11214, customfield_10012, customfield_10213, customfield_10395, customfield_10684, customfield_10116, customfield_10172, timeoriginalestimate, customfield_10203, customfield_10174, customfield_10025, customfield_10336, customfield_10065, customfield_10330, customfield_10223, customfield_10027, customfield_10686, fixVersions, customfield_10126, customfield_10182, customfield_10356, progress, customfield_10128, customfield_10042, customfield_10180, customfield_10562, customfield_10196, customfield_10858, customfield_10629, customfield_10261, customfield_10004, customfield_10049, customfield_10627, aggregatetimeestimate, customfield_10663, customfield_10185, customfield_10237, customfield_10557, customfield_10081, timespent, security, customfield_10057, aggregateprogress, aggregatetimespent, customfield_10314, environment, customfield_10722, customfield_10195, customfield_10239, customfield_10368, customfield_10229, customfield_10037, issuelinks, customfield_10348, customfield_10056, customfield_10131, customfield_10199, customfield_10054, Status, customfield_10618, customfield_10751, customfield_10161, Comment, Key, customfield_11254, customfield_10063, customfield_10060, customfield_10066, customfield_10226, customfield_10068, labels, customfield_10520, customfield_10489, statuscategorychangedate, customfield_10160, customfield_10222, customfield_10354, customfield_11252, customfield_10159, customfield_10393, customfield_11248, RestUrl, resolution, customfield_10384, customfield_10216, customfield_10379, customfield_10208, customfield_10386, customfield_10341, customfield_10132, customfield_10559, customfield_10171, customfield_10013, customfield_10100, customfield_10212, customfield_10204, customfield_10725, customfield_10117, customfield_10173, customfield_10011, customfield_10075, customfield_10398, customfield_10115, customfield_10347, customfield_10337, customfield_10321, customfield_10177, customfield_10215, customfield_10224, customfield_10352, Assignee, customfield_10130, customfield_10717, customfield_10129, customfield_10181, customfield_10000, customfield_10453, customfield_11181, customfield_10178, customfield_10029, customfield_10276, watches, customfield_10560, customfield_10133, customfield_10120, customfield_10102, customfield_10634, customfield_10249, customfield_10320, customfield_10048, customfield_11278, customfield_10346, customfield_10383, customfield_10184, customfield_11281, customfield_10726, customfield_10030, priority, customfield_10235, customfield_10720, customfield_10134, customfield_10047, customfield_10190, customfield_10721, customfield_10045, customfield_10448, customfield_10238, customfield_10882, customfield_10250, customfield_10198, customfield_10118, customfield_10619, customfield_10197, customfield_10251, customfield_10318, customfield_10055, Summary, Description, customfield_10447, customfield_10304, customfield_10255, customfield_10059, customfield_10053, customfield_10148, customfield_10382, customfield_10193, customfield_10488, attachment, customfield_10728, customfield_10225, customfield_10158, workratio, customfield_10372, customfield_10206, customfield_10156, customfield_10664, customfield_10201, customfield_10154, customfield_10521, statusCategory, Created, customfield_10064, issuetype, customfield_10254, customfield_10374, customfield_10326, customfield_10342, customfield_10317, customfield_10554, customfield_10227, customfield_10210, customfield_10214, customfield_10391, customfield_11246, customfield_10585, customfield_10070, customfield_11182, customfield_10630, customfield_10135, aggregatetimeoriginalestimate, customfield_10631, customfield_10010, customfield_10061, customfield_11215, timeestimate, customfield_10662, customfield_11213, customfield_10556, customfield_10028, customfield_10635, customfield_10396, customfield_10275, customfield_10719, timetracking, customfield_10175, customfield_10024, Creator, customfield_10381, customfield_10023, Updated, customfield_11179, customfield_10127, customfield_10183, customfield_11122, customfield_10561, customfield_10043, customfield_10322, customfield_10046, customfield_10192, customfield_10041, Reporter, customfield_10044, customfield_10628, customfield_10335, customfield_10040, subtasks, customfield_10191, customfield_10626, customfield_10343, versions, customfield_10186, HttpUrl, customfield_10319, customfield_10303, customfield_10001, customfield_10555, customfield_10252, customfield_10050
[2025-07-03 09:28:04.898] [DEBUG] [Application] [16] Found Comment property with 18 comments
[2025-07-03 09:28:04.919] [INFO] [Application] [16] Starting data extraction from ticket TESTIT-49342
[2025-07-03 09:28:04.981] [DEBUG] [Application] [16] Extracted data - Name: 'Test1 Test2', Job: 'Intern', Dept: 'IT', Model: 'test', Location: ''
[2025-07-03 09:28:05.669] [DEBUG] [Application] [16] Generating SAM account name for Test1 Test2
[2025-07-03 09:28:05.686] [DEBUG] [Application] [16] Checking if SAM account name 'test2t' exists
[2025-07-03 09:28:05.712] [INFO] [Application] [16] Generated SAM account name: test2t (simulation mode - could not verify uniqueness)
[2025-07-03 09:28:05.738] [DEBUG] [Application] [16] SAM account generated successfully
[2025-07-03 09:28:06.105] [DEBUG] [Application] [16] Generated account details from Jira ticket successfully
[2025-07-03 09:28:06.187] [INFO] [Application] [16] Data extraction completed. Please verify the populated fields.
[2025-07-03 09:28:06.216] [DEBUG] [Application] [16] Workflow step 'FetchData' status: Completed
[2025-07-03 09:28:06.237] [INFO] [Application] [16] Step 3: Validate Information
[2025-07-03 09:28:06.264] [DEBUG] [Application] [16] Workflow step 'Validation' status: InProgress
[2025-07-03 09:28:06.286] [INFO] [Application] [16] Successfully fetched and populated data from ticket TESTIT-49342
[2025-07-03 09:30:44.640] [DEBUG] [Testing] [16] Test registered: ConfigurationValidation in suite Unit
[2025-07-03 09:30:44.821] [DEBUG] [Testing] [16] Test registered: ConfigurationValidation in Unit
[2025-07-03 09:30:44.859] [DEBUG] [Testing] [16] Test registered: LoggingSystem in suite Unit
[2025-07-03 09:30:44.877] [DEBUG] [Testing] [16] Test registered: LoggingSystem in Unit
[2025-07-03 09:30:44.892] [DEBUG] [Testing] [16] Test registered: VersionManager in suite Unit
[2025-07-03 09:30:44.910] [DEBUG] [Testing] [16] Test registered: VersionManager in Unit
[2025-07-03 09:30:44.921] [DEBUG] [Testing] [16] Test registered: JiraConnection in suite Integration
[2025-07-03 09:30:44.938] [DEBUG] [Testing] [16] Test registered: JiraConnection in Integration
[2025-07-03 09:30:44.949] [DEBUG] [Testing] [16] Test registered: ActiveDirectoryConnection in suite Integration
[2025-07-03 09:30:44.969] [DEBUG] [Testing] [16] Test registered: ActiveDirectoryConnection in Integration
[2025-07-03 09:30:44.984] [DEBUG] [Testing] [16] Test registered: StartupTime in suite Performance
[2025-07-03 09:30:45.003] [DEBUG] [Testing] [16] Test registered: StartupTime in Performance
[2025-07-03 09:30:45.070] [DEBUG] [Testing] [16] Test registered: MemoryUsage in suite Performance
[2025-07-03 09:30:45.082] [DEBUG] [Testing] [16] Test registered: MemoryUsage in Performance
[2025-07-03 09:30:45.093] [DEBUG] [Testing] [16] Test registered: CredentialHandling in suite Security
[2025-07-03 09:30:45.104] [DEBUG] [Testing] [16] Test registered: CredentialHandling in Security
[2025-07-03 09:30:45.123] [DEBUG] [Testing] [16] Test registered: InputValidation in suite Security
[2025-07-03 09:30:45.142] [DEBUG] [Testing] [16] Test registered: InputValidation in Security
[2025-07-03 09:30:45.165] [DEBUG] [Testing] [16] Test registered: WindowCreation in suite UI
[2025-07-03 09:30:45.184] [DEBUG] [Testing] [16] Test registered: WindowCreation in UI
[2025-07-03 09:30:45.211] [INFO] [Testing] [16] Core tests initialized
[2025-07-03 09:30:45.282] [INFO] [Documentation] [16] Initializing documentation system
[2025-07-03 09:30:45.324] [INFO] [Documentation] [16] Starting documentation generation for: api
[2025-07-03 09:30:45.444] [INFO] [Documentation] [16] Documentation saved: Documentation\API_Reference.html
[2025-07-03 09:30:45.471] [INFO] [Documentation] [16] Documentation saved: Documentation\API_Reference.md
[2025-07-03 09:30:45.492] [INFO] [Documentation] [16] Documentation generation completed for: api
[2025-07-03 09:30:45.519] [INFO] [Documentation] [16] API documentation generated: 2 files
[2025-07-03 09:30:45.548] [INFO] [Documentation] [16] Starting documentation generation for: help
[2025-07-03 09:30:45.567] [INFO] [Documentation] [16] Documentation generation completed for: help
[2025-07-03 09:30:45.576] [INFO] [Documentation] [16] Help system documentation generated
[2025-07-03 09:30:45.590] [INFO] [Documentation] [16] Documentation system initialized
[2025-07-03 09:30:45.714] [INFO] [Application] [16] Initializing enhanced UI components...
[2025-07-03 09:30:45.797] [DEBUG] [Application] [16] Theme applied to controls
[2025-07-03 09:30:45.813] [DEBUG] [Application] [16] Modern theme applied successfully
[2025-07-03 09:30:45.873] [DEBUG] [Application] [16] Field validation initialized
[2025-07-03 09:30:45.919] [DEBUG] [Application] [16] Responsive layout initialized
[2025-07-03 09:30:46.015] [DEBUG] [Application] [16] Workflow step 'Connection' status: Pending
[2025-07-03 09:30:46.062] [INFO] [Application] [16] Enhanced UI initialization completed
[2025-07-03 09:30:46.098] [INFO] [Application] [16] Welcome! Please connect to Jira to begin.
[2025-07-03 09:30:46.119] [INFO] [Application] [16] OnboardingFromJiraGUI started successfully
[2025-07-03 09:30:46.174] [INFO] [Application] [16] [OK] Monitoring system initialized
[2025-07-03 09:30:46.400] [INFO] [Audit] [16] Audit: akinje performed PermissionInit on UserPermissions - Success | Context: SessionId=Unknown; Action=PermissionInit; Timestamp=07/03/2025 09:30:46; Result=Success; User=akinje; Detected=Standard user privileges; Target=UserPermissions; PrivilegeLevel=Standard
[2025-07-03 09:30:46.434] [INFO] [Application] [16] [OK] User permissions initialized for JERAGM\akinje
[2025-07-03 09:30:46.457] [DEBUG] [Application] [16]  Cache cleanup scheduled every 30 minutes
[2025-07-03 09:30:46.475] [INFO] [Application] [16] Applying Modern UI Integration...
[2025-07-03 09:30:46.610] [DEBUG] [Application] [16] Auto-completion setup for LastNameBox
[2025-07-03 09:30:46.630] [DEBUG] [Application] [16] Auto-completion setup for JobTitleBox
[2025-07-03 09:30:46.648] [DEBUG] [Application] [16] Auto-completion setup for FirstNameBox
[2025-07-03 09:30:46.670] [DEBUG] [Application] [16] Auto-completion setup for DepartmentBox
[2025-07-03 09:30:46.688] [DEBUG] [Application] [16] Auto-completion setup for ModelAccountBox
[2025-07-03 09:30:46.704] [DEBUG] [Application] [16] Auto-completion integration completed
[2025-07-03 09:30:46.772] [INFO] [Application] [16] Modern UI Integration completed successfully
[2025-07-03 09:31:02.702] [INFO] [Application] [16] Step 1: Connect to Jira
[2025-07-03 09:31:02.715] [DEBUG] [Application] [16] Workflow step 'Connection' status: InProgress
[2025-07-03 09:31:02.749] [INFO] [Application] [16] Connecting to https://jeragm.atlassian.net...
[2025-07-03 09:31:03.952] [DEBUG] [Application] [16] Workflow step 'Connection' status: Completed
[2025-07-03 09:31:03.972] [INFO] [Application] [16] Successfully connected to Jira
[2025-07-03 09:31:08.434] [INFO] [Application] [16] Step 2: Fetch Ticket Data
[2025-07-03 09:31:08.445] [DEBUG] [Application] [16] Workflow step 'FetchData' status: InProgress
[2025-07-03 09:31:08.460] [INFO] [Application] [16] Fetching data for ticket TESTIT-49342...
[2025-07-03 09:31:10.286] [DEBUG] [Application] [16] Cached ticket: TESTIT-49342
[2025-07-03 09:31:10.307] [DEBUG] [Application] [16] Issue object properties: customfield_10553, customfield_10058, customfield_10305, customfield_10149, customfield_10723, customfield_10928, customfield_10194, customfield_10363, customfield_10750, customfield_10147, customfield_11280, customfield_10729, customfield_10153, customfield_10369, customfield_10145, customfield_10783, customfield_10727, duedate, customfield_10151, customfield_10367, customfield_10370, customfield_10486, customfield_10373, worklog, customfield_10157, customfield_10371, customfield_10008, customfield_10155, customfield_10519, customfield_10202, customfield_10306, customfield_10380, customfield_10062, ID, customfield_10375, customfield_10072, customfield_10069, customfield_10685, customfield_11251, customfield_10200, customfield_10187, customfield_11113, customfield_11245, components, customfield_11253, Transition, customfield_11250, customfield_11247, resolutiondate, customfield_10101, LastViewed, customfield_10385, customfield_11249, customfield_11279, customfield_10344, customfield_10209, customfield_10189, customfield_11212, customfield_10651, Project, customfield_10211, customfield_10105, customfield_11214, customfield_10012, customfield_10213, customfield_10395, customfield_10684, customfield_10116, customfield_10172, timeoriginalestimate, customfield_10203, customfield_10174, customfield_10025, customfield_10336, customfield_10065, customfield_10330, customfield_10223, customfield_10027, customfield_10686, fixVersions, customfield_10126, customfield_10182, customfield_10356, progress, customfield_10128, customfield_10042, customfield_10180, customfield_10562, customfield_10196, customfield_10858, customfield_10629, customfield_10261, customfield_10004, customfield_10049, customfield_10627, aggregatetimeestimate, customfield_10663, customfield_10185, customfield_10237, customfield_10557, customfield_10081, timespent, security, customfield_10057, aggregateprogress, aggregatetimespent, customfield_10314, environment, customfield_10722, customfield_10195, customfield_10239, customfield_10368, customfield_10229, customfield_10037, issuelinks, customfield_10348, customfield_10056, customfield_10131, customfield_10199, customfield_10054, Status, customfield_10618, customfield_10751, customfield_10161, Comment, Key, customfield_11254, customfield_10063, customfield_10060, customfield_10066, customfield_10226, customfield_10068, labels, customfield_10520, customfield_10489, statuscategorychangedate, customfield_10160, customfield_10222, customfield_10354, customfield_11252, customfield_10159, customfield_10393, customfield_11248, RestUrl, resolution, customfield_10384, customfield_10216, customfield_10379, customfield_10208, customfield_10386, customfield_10341, customfield_10132, customfield_10559, customfield_10171, customfield_10013, customfield_10100, customfield_10212, customfield_10204, customfield_10725, customfield_10117, customfield_10173, customfield_10011, customfield_10075, customfield_10398, customfield_10115, customfield_10347, customfield_10337, customfield_10321, customfield_10177, customfield_10215, customfield_10224, customfield_10352, Assignee, customfield_10130, customfield_10717, customfield_10129, customfield_10181, customfield_10000, customfield_10453, customfield_11181, customfield_10178, customfield_10029, customfield_10276, watches, customfield_10560, customfield_10133, customfield_10120, customfield_10102, customfield_10634, customfield_10249, customfield_10320, customfield_10048, customfield_11278, customfield_10346, customfield_10383, customfield_10184, customfield_11281, customfield_10726, customfield_10030, priority, customfield_10235, customfield_10720, customfield_10134, customfield_10047, customfield_10190, customfield_10721, customfield_10045, customfield_10448, customfield_10238, customfield_10882, customfield_10250, customfield_10198, customfield_10118, customfield_10619, customfield_10197, customfield_10251, customfield_10318, customfield_10055, Summary, Description, customfield_10447, customfield_10304, customfield_10255, customfield_10059, customfield_10053, customfield_10148, customfield_10382, customfield_10193, customfield_10488, attachment, customfield_10728, customfield_10225, customfield_10158, workratio, customfield_10372, customfield_10206, customfield_10156, customfield_10664, customfield_10201, customfield_10154, customfield_10521, statusCategory, Created, customfield_10064, issuetype, customfield_10254, customfield_10374, customfield_10326, customfield_10342, customfield_10317, customfield_10554, customfield_10227, customfield_10210, customfield_10214, customfield_10391, customfield_11246, customfield_10585, customfield_10070, customfield_11182, customfield_10630, customfield_10135, aggregatetimeoriginalestimate, customfield_10631, customfield_10010, customfield_10061, customfield_11215, timeestimate, customfield_10662, customfield_11213, customfield_10556, customfield_10028, customfield_10635, customfield_10396, customfield_10275, customfield_10719, timetracking, customfield_10175, customfield_10024, Creator, customfield_10381, customfield_10023, Updated, customfield_11179, customfield_10127, customfield_10183, customfield_11122, customfield_10561, customfield_10043, customfield_10322, customfield_10046, customfield_10192, customfield_10041, Reporter, customfield_10044, customfield_10628, customfield_10335, customfield_10040, subtasks, customfield_10191, customfield_10626, customfield_10343, versions, customfield_10186, HttpUrl, customfield_10319, customfield_10303, customfield_10001, customfield_10555, customfield_10252, customfield_10050
[2025-07-03 09:31:10.329] [DEBUG] [Application] [16] Found Comment property with 18 comments
[2025-07-03 09:31:10.343] [INFO] [Application] [16] Starting data extraction from ticket TESTIT-49342
[2025-07-03 09:31:10.380] [DEBUG] [Application] [16] Extracted data - Name: 'Test1 Test2', Job: 'Intern', Dept: 'IT', Model: 'test', Location: ''
[2025-07-03 09:31:10.808] [DEBUG] [Application] [16] Generating SAM account name for Test1 Test2
[2025-07-03 09:31:10.819] [DEBUG] [Application] [16] Checking if SAM account name 'test2t' exists
[2025-07-03 09:31:10.836] [INFO] [Application] [16] Generated SAM account name: test2t (simulation mode - could not verify uniqueness)
[2025-07-03 09:31:10.850] [DEBUG] [Application] [16] SAM account generated successfully
[2025-07-03 09:31:11.045] [DEBUG] [Application] [16] Generated account details from Jira ticket successfully
[2025-07-03 09:31:11.077] [INFO] [Application] [16] Data extraction completed. Please verify the populated fields.
[2025-07-03 09:31:11.098] [DEBUG] [Application] [16] Workflow step 'FetchData' status: Completed
[2025-07-03 09:31:11.123] [INFO] [Application] [16] Step 3: Validate Information
[2025-07-03 09:31:11.136] [DEBUG] [Application] [16] Workflow step 'Validation' status: InProgress
[2025-07-03 09:31:11.160] [INFO] [Application] [16] Successfully fetched and populated data from ticket TESTIT-49342
[2025-07-03 09:37:29.811] [DEBUG] [Testing] [16] Test registered: ConfigurationValidation in suite Unit
[2025-07-03 09:37:29.913] [DEBUG] [Testing] [16] Test registered: ConfigurationValidation in Unit
[2025-07-03 09:37:29.923] [DEBUG] [Testing] [16] Test registered: LoggingSystem in suite Unit
[2025-07-03 09:37:29.932] [DEBUG] [Testing] [16] Test registered: LoggingSystem in Unit
[2025-07-03 09:37:29.944] [DEBUG] [Testing] [16] Test registered: VersionManager in suite Unit
[2025-07-03 09:37:29.960] [DEBUG] [Testing] [16] Test registered: VersionManager in Unit
[2025-07-03 09:37:29.981] [DEBUG] [Testing] [16] Test registered: JiraConnection in suite Integration
[2025-07-03 09:37:29.995] [DEBUG] [Testing] [16] Test registered: JiraConnection in Integration
[2025-07-03 09:37:30.008] [DEBUG] [Testing] [16] Test registered: ActiveDirectoryConnection in suite Integration
[2025-07-03 09:37:30.021] [DEBUG] [Testing] [16] Test registered: ActiveDirectoryConnection in Integration
[2025-07-03 09:37:30.036] [DEBUG] [Testing] [16] Test registered: StartupTime in suite Performance
[2025-07-03 09:37:30.055] [DEBUG] [Testing] [16] Test registered: StartupTime in Performance
[2025-07-03 09:37:30.068] [DEBUG] [Testing] [16] Test registered: MemoryUsage in suite Performance
[2025-07-03 09:37:30.087] [DEBUG] [Testing] [16] Test registered: MemoryUsage in Performance
[2025-07-03 09:37:30.198] [DEBUG] [Testing] [16] Test registered: CredentialHandling in suite Security
[2025-07-03 09:37:30.209] [DEBUG] [Testing] [16] Test registered: CredentialHandling in Security
[2025-07-03 09:37:30.219] [DEBUG] [Testing] [16] Test registered: InputValidation in suite Security
[2025-07-03 09:37:30.229] [DEBUG] [Testing] [16] Test registered: InputValidation in Security
[2025-07-03 09:37:30.247] [DEBUG] [Testing] [16] Test registered: WindowCreation in suite UI
[2025-07-03 09:37:30.264] [DEBUG] [Testing] [16] Test registered: WindowCreation in UI
[2025-07-03 09:37:30.281] [INFO] [Testing] [16] Core tests initialized
[2025-07-03 09:37:30.330] [INFO] [Documentation] [16] Initializing documentation system
[2025-07-03 09:37:30.367] [INFO] [Documentation] [16] Starting documentation generation for: api
[2025-07-03 09:37:30.462] [INFO] [Documentation] [16] Documentation saved: Documentation\API_Reference.html
[2025-07-03 09:37:30.488] [INFO] [Documentation] [16] Documentation saved: Documentation\API_Reference.md
[2025-07-03 09:37:30.512] [INFO] [Documentation] [16] Documentation generation completed for: api
[2025-07-03 09:37:30.539] [INFO] [Documentation] [16] API documentation generated: 2 files
[2025-07-03 09:37:30.561] [INFO] [Documentation] [16] Starting documentation generation for: help
[2025-07-03 09:37:30.578] [INFO] [Documentation] [16] Documentation generation completed for: help
[2025-07-03 09:37:30.592] [INFO] [Documentation] [16] Help system documentation generated
[2025-07-03 09:37:30.611] [INFO] [Documentation] [16] Documentation system initialized
[2025-07-03 09:37:30.785] [INFO] [Application] [16] Initializing enhanced UI components...
[2025-07-03 09:37:30.907] [DEBUG] [Application] [16] Theme applied to controls
[2025-07-03 09:37:30.983] [DEBUG] [Application] [16] Modern theme applied successfully
[2025-07-03 09:37:31.027] [DEBUG] [Application] [16] Field validation initialized
[2025-07-03 09:37:31.099] [DEBUG] [Application] [16] Responsive layout initialized
[2025-07-03 09:37:31.448] [DEBUG] [Application] [16] Workflow step 'Connection' status: Pending
[2025-07-03 09:37:31.558] [INFO] [Application] [16] Enhanced UI initialization completed
[2025-07-03 09:37:31.631] [INFO] [Application] [16] Welcome! Please connect to Jira to begin.
[2025-07-03 09:37:31.669] [INFO] [Application] [16] OnboardingFromJiraGUI started successfully
[2025-07-03 09:37:31.709] [INFO] [Application] [16] [OK] Monitoring system initialized
[2025-07-03 09:37:31.786] [INFO] [Audit] [16] Audit: akinje performed PermissionInit on UserPermissions - Success | Context: SessionId=Unknown; Action=PermissionInit; Timestamp=07/03/2025 09:37:31; Result=Success; User=akinje; Detected=Standard user privileges; Target=UserPermissions; PrivilegeLevel=Standard
[2025-07-03 09:37:32.012] [INFO] [Application] [16] [OK] User permissions initialized for JERAGM\akinje
[2025-07-03 09:37:32.054] [DEBUG] [Application] [16]  Cache cleanup scheduled every 30 minutes
[2025-07-03 09:37:32.077] [INFO] [Application] [16] Applying Modern UI Integration...
[2025-07-03 09:37:32.227] [DEBUG] [Application] [16] Auto-completion setup for LastNameBox
[2025-07-03 09:37:32.257] [DEBUG] [Application] [16] Auto-completion setup for JobTitleBox
[2025-07-03 09:37:32.281] [DEBUG] [Application] [16] Auto-completion setup for FirstNameBox
[2025-07-03 09:37:32.325] [DEBUG] [Application] [16] Auto-completion setup for DepartmentBox
[2025-07-03 09:37:32.357] [DEBUG] [Application] [16] Auto-completion setup for ModelAccountBox
[2025-07-03 09:37:32.382] [DEBUG] [Application] [16] Auto-completion integration completed
[2025-07-03 09:37:32.478] [INFO] [Application] [16] Modern UI Integration completed successfully
[2025-07-03 09:37:53.889] [INFO] [Application] [16] Step 1: Connect to Jira
[2025-07-03 09:37:53.899] [DEBUG] [Application] [16] Workflow step 'Connection' status: InProgress
[2025-07-03 09:37:53.940] [INFO] [Application] [16] Connecting to https://jeragm.atlassian.net...
[2025-07-03 09:37:56.527] [DEBUG] [Application] [16] Workflow step 'Connection' status: Completed
[2025-07-03 09:37:56.546] [INFO] [Application] [16] Successfully connected to Jira
[2025-07-03 09:37:59.254] [INFO] [Application] [16] Step 2: Fetch Ticket Data
[2025-07-03 09:37:59.292] [DEBUG] [Application] [16] Workflow step 'FetchData' status: InProgress
[2025-07-03 09:37:59.310] [INFO] [Application] [16] Fetching data for ticket TESTIT-49342...
[2025-07-03 09:38:01.918] [DEBUG] [Application] [16] Cached ticket: TESTIT-49342
[2025-07-03 09:38:01.942] [DEBUG] [Application] [16] Issue object properties: customfield_10553, customfield_10058, customfield_10305, customfield_10149, customfield_10723, customfield_10928, customfield_10194, customfield_10363, customfield_10750, customfield_10147, customfield_11280, customfield_10729, customfield_10153, customfield_10369, customfield_10145, customfield_10783, customfield_10727, duedate, customfield_10151, customfield_10367, customfield_10370, customfield_10486, customfield_10373, worklog, customfield_10157, customfield_10371, customfield_10008, customfield_10155, customfield_10519, customfield_10202, customfield_10306, customfield_10380, customfield_10062, ID, customfield_10375, customfield_10072, customfield_10069, customfield_10685, customfield_11251, customfield_10200, customfield_10187, customfield_11113, customfield_11245, components, customfield_11253, Transition, customfield_11250, customfield_11247, resolutiondate, customfield_10101, LastViewed, customfield_10385, customfield_11249, customfield_11279, customfield_10344, customfield_10209, customfield_10189, customfield_11212, customfield_10651, Project, customfield_10211, customfield_10105, customfield_11214, customfield_10012, customfield_10213, customfield_10395, customfield_10684, customfield_10116, customfield_10172, timeoriginalestimate, customfield_10203, customfield_10174, customfield_10025, customfield_10336, customfield_10065, customfield_10330, customfield_10223, customfield_10027, customfield_10686, fixVersions, customfield_10126, customfield_10182, customfield_10356, progress, customfield_10128, customfield_10042, customfield_10180, customfield_10562, customfield_10196, customfield_10858, customfield_10629, customfield_10261, customfield_10004, customfield_10049, customfield_10627, aggregatetimeestimate, customfield_10663, customfield_10185, customfield_10237, customfield_10557, customfield_10081, timespent, security, customfield_10057, aggregateprogress, aggregatetimespent, customfield_10314, environment, customfield_10722, customfield_10195, customfield_10239, customfield_10368, customfield_10229, customfield_10037, issuelinks, customfield_10348, customfield_10056, customfield_10131, customfield_10199, customfield_10054, Status, customfield_10618, customfield_10751, customfield_10161, Comment, Key, customfield_11254, customfield_10063, customfield_10060, customfield_10066, customfield_10226, customfield_10068, labels, customfield_10520, customfield_10489, statuscategorychangedate, customfield_10160, customfield_10222, customfield_10354, customfield_11252, customfield_10159, customfield_10393, customfield_11248, RestUrl, resolution, customfield_10384, customfield_10216, customfield_10379, customfield_10208, customfield_10386, customfield_10341, customfield_10132, customfield_10559, customfield_10171, customfield_10013, customfield_10100, customfield_10212, customfield_10204, customfield_10725, customfield_10117, customfield_10173, customfield_10011, customfield_10075, customfield_10398, customfield_10115, customfield_10347, customfield_10337, customfield_10321, customfield_10177, customfield_10215, customfield_10224, customfield_10352, Assignee, customfield_10130, customfield_10717, customfield_10129, customfield_10181, customfield_10000, customfield_10453, customfield_11181, customfield_10178, customfield_10029, customfield_10276, watches, customfield_10560, customfield_10133, customfield_10120, customfield_10102, customfield_10634, customfield_10249, customfield_10320, customfield_10048, customfield_11278, customfield_10346, customfield_10383, customfield_10184, customfield_11281, customfield_10726, customfield_10030, priority, customfield_10235, customfield_10720, customfield_10134, customfield_10047, customfield_10190, customfield_10721, customfield_10045, customfield_10448, customfield_10238, customfield_10882, customfield_10250, customfield_10198, customfield_10118, customfield_10619, customfield_10197, customfield_10251, customfield_10318, customfield_10055, Summary, Description, customfield_10447, customfield_10304, customfield_10255, customfield_10059, customfield_10053, customfield_10148, customfield_10382, customfield_10193, customfield_10488, attachment, customfield_10728, customfield_10225, customfield_10158, workratio, customfield_10372, customfield_10206, customfield_10156, customfield_10664, customfield_10201, customfield_10154, customfield_10521, statusCategory, Created, customfield_10064, issuetype, customfield_10254, customfield_10374, customfield_10326, customfield_10342, customfield_10317, customfield_10554, customfield_10227, customfield_10210, customfield_10214, customfield_10391, customfield_11246, customfield_10585, customfield_10070, customfield_11182, customfield_10630, customfield_10135, aggregatetimeoriginalestimate, customfield_10631, customfield_10010, customfield_10061, customfield_11215, timeestimate, customfield_10662, customfield_11213, customfield_10556, customfield_10028, customfield_10635, customfield_10396, customfield_10275, customfield_10719, timetracking, customfield_10175, customfield_10024, Creator, customfield_10381, customfield_10023, Updated, customfield_11179, customfield_10127, customfield_10183, customfield_11122, customfield_10561, customfield_10043, customfield_10322, customfield_10046, customfield_10192, customfield_10041, Reporter, customfield_10044, customfield_10628, customfield_10335, customfield_10040, subtasks, customfield_10191, customfield_10626, customfield_10343, versions, customfield_10186, HttpUrl, customfield_10319, customfield_10303, customfield_10001, customfield_10555, customfield_10252, customfield_10050
[2025-07-03 09:38:01.959] [DEBUG] [Application] [16] Found Comment property with 18 comments
[2025-07-03 09:38:01.984] [INFO] [Application] [16] Starting data extraction from ticket TESTIT-49342
[2025-07-03 09:38:02.017] [DEBUG] [Application] [16] Extracted data - Name: 'Test1 Test2', Job: 'Intern', Dept: 'IT', Model: 'test', Location: ''
[2025-07-03 09:38:02.317] [ERROR] [Application] [16] Failed to fetch ticket data: Exception setting "Text": "Method invocation failed because [System.Windows.Threading.DispatcherTimer] does not contain a method named 'dispose'." | Stack: Write-StructuredLog:7453 -> Write-AppLog:7672 -> Write-Log:5710 -> Update-Status:5733 -> :10534 -> OnboardingFromJiraGUI.ps1:11072 -> <ScriptBlock>:1
[2025-07-03 09:38:02.486] [DEBUG] [Application] [16] Workflow step 'FetchData' status: Error
[2025-07-03 09:38:02.522] [ERROR] [Application] [16] Failed to fetch ticket data: Exception setting "Text": "Method invocation failed because [System.Windows.Threading.DispatcherTimer] does not contain a method named 'dispose'." | Stack: Write-StructuredLog:7453 -> Write-AppLog:7672 -> Write-Log:5710 -> Write-EnhancedLog:5944 -> :10537 -> OnboardingFromJiraGUI.ps1:11072 -> <ScriptBlock>:1
[2025-07-03 09:42:08.180] [DEBUG] [Testing] [16] Test registered: ConfigurationValidation in suite Unit
[2025-07-03 09:42:08.422] [DEBUG] [Testing] [16] Test registered: ConfigurationValidation in Unit
[2025-07-03 09:42:08.438] [DEBUG] [Testing] [16] Test registered: LoggingSystem in suite Unit
[2025-07-03 09:42:08.452] [DEBUG] [Testing] [16] Test registered: LoggingSystem in Unit
[2025-07-03 09:42:08.473] [DEBUG] [Testing] [16] Test registered: VersionManager in suite Unit
[2025-07-03 09:42:08.492] [DEBUG] [Testing] [16] Test registered: VersionManager in Unit
[2025-07-03 09:42:08.508] [DEBUG] [Testing] [16] Test registered: JiraConnection in suite Integration
[2025-07-03 09:42:08.522] [DEBUG] [Testing] [16] Test registered: JiraConnection in Integration
[2025-07-03 09:42:08.542] [DEBUG] [Testing] [16] Test registered: ActiveDirectoryConnection in suite Integration
[2025-07-03 09:42:08.564] [DEBUG] [Testing] [16] Test registered: ActiveDirectoryConnection in Integration
[2025-07-03 09:42:08.585] [DEBUG] [Testing] [16] Test registered: StartupTime in suite Performance
[2025-07-03 09:42:08.611] [DEBUG] [Testing] [16] Test registered: StartupTime in Performance
[2025-07-03 09:42:08.634] [DEBUG] [Testing] [16] Test registered: MemoryUsage in suite Performance
[2025-07-03 09:42:08.659] [DEBUG] [Testing] [16] Test registered: MemoryUsage in Performance
[2025-07-03 09:42:08.690] [DEBUG] [Testing] [16] Test registered: CredentialHandling in suite Security
[2025-07-03 09:42:08.720] [DEBUG] [Testing] [16] Test registered: CredentialHandling in Security
[2025-07-03 09:42:08.784] [DEBUG] [Testing] [16] Test registered: InputValidation in suite Security
[2025-07-03 09:42:08.810] [DEBUG] [Testing] [16] Test registered: InputValidation in Security
[2025-07-03 09:42:08.826] [DEBUG] [Testing] [16] Test registered: WindowCreation in suite UI
[2025-07-03 09:42:08.846] [DEBUG] [Testing] [16] Test registered: WindowCreation in UI
[2025-07-03 09:42:08.870] [INFO] [Testing] [16] Core tests initialized
[2025-07-03 09:42:08.946] [INFO] [Documentation] [16] Initializing documentation system
[2025-07-03 09:42:09.008] [INFO] [Documentation] [16] Starting documentation generation for: api
[2025-07-03 09:42:09.150] [INFO] [Documentation] [16] Documentation saved: Documentation\API_Reference.html
[2025-07-03 09:42:09.192] [INFO] [Documentation] [16] Documentation saved: Documentation\API_Reference.md
[2025-07-03 09:42:09.231] [INFO] [Documentation] [16] Documentation generation completed for: api
[2025-07-03 09:42:09.271] [INFO] [Documentation] [16] API documentation generated: 2 files
[2025-07-03 09:42:09.287] [INFO] [Documentation] [16] Starting documentation generation for: help
[2025-07-03 09:42:09.323] [INFO] [Documentation] [16] Documentation generation completed for: help
[2025-07-03 09:42:09.348] [INFO] [Documentation] [16] Help system documentation generated
[2025-07-03 09:42:09.362] [INFO] [Documentation] [16] Documentation system initialized
[2025-07-03 09:42:09.541] [INFO] [Application] [16] Initializing enhanced UI components...
[2025-07-03 09:42:09.653] [DEBUG] [Application] [16] Theme applied to controls
[2025-07-03 09:42:09.891] [DEBUG] [Application] [16] Modern theme applied successfully
[2025-07-03 09:42:09.941] [DEBUG] [Application] [16] Field validation initialized
[2025-07-03 09:42:10.004] [DEBUG] [Application] [16] Responsive layout initialized
[2025-07-03 09:42:10.199] [DEBUG] [Application] [16] Workflow step 'Connection' status: Pending
[2025-07-03 09:42:10.320] [INFO] [Application] [16] Enhanced UI initialization completed
[2025-07-03 09:42:10.402] [INFO] [Application] [16] Welcome! Please connect to Jira to begin.
[2025-07-03 09:42:10.452] [INFO] [Application] [16] OnboardingFromJiraGUI started successfully
[2025-07-03 09:42:10.506] [INFO] [Application] [16] [OK] Monitoring system initialized
[2025-07-03 09:42:10.595] [INFO] [Audit] [16] Audit: akinje performed PermissionInit on UserPermissions - Success | Context: SessionId=Unknown; Action=PermissionInit; Timestamp=07/03/2025 09:42:10; Result=Success; User=akinje; Detected=Standard user privileges; Target=UserPermissions; PrivilegeLevel=Standard
[2025-07-03 09:42:10.652] [INFO] [Application] [16] [OK] User permissions initialized for JERAGM\akinje
[2025-07-03 09:42:10.739] [DEBUG] [Application] [16]  Cache cleanup scheduled every 30 minutes
[2025-07-03 09:42:10.780] [INFO] [Application] [16] Applying Modern UI Integration...
[2025-07-03 09:42:10.964] [DEBUG] [Application] [16] Auto-completion setup for LastNameBox
[2025-07-03 09:42:10.991] [DEBUG] [Application] [16] Auto-completion setup for JobTitleBox
[2025-07-03 09:42:11.026] [DEBUG] [Application] [16] Auto-completion setup for FirstNameBox
[2025-07-03 09:42:11.053] [DEBUG] [Application] [16] Auto-completion setup for DepartmentBox
[2025-07-03 09:42:11.087] [DEBUG] [Application] [16] Auto-completion setup for ModelAccountBox
[2025-07-03 09:42:11.112] [DEBUG] [Application] [16] Auto-completion integration completed
[2025-07-03 09:42:11.205] [INFO] [Application] [16] Modern UI Integration completed successfully
[2025-07-03 09:42:31.645] [INFO] [Application] [16] Step 1: Connect to Jira
[2025-07-03 09:42:31.684] [DEBUG] [Application] [16] Workflow step 'Connection' status: InProgress
[2025-07-03 09:42:31.823] [INFO] [Application] [16] Connecting to https://jeragm.atlassian.net...
[2025-07-03 09:42:35.010] [DEBUG] [Application] [16] Workflow step 'Connection' status: Completed
[2025-07-03 09:42:35.078] [INFO] [Application] [16] Successfully connected to Jira
[2025-07-03 09:42:37.312] [INFO] [Application] [16] Step 2: Fetch Ticket Data
[2025-07-03 09:42:37.376] [DEBUG] [Application] [16] Workflow step 'FetchData' status: InProgress
[2025-07-03 09:42:37.423] [INFO] [Application] [16] Fetching data for ticket TESTIT-49342...
[2025-07-03 09:42:42.493] [DEBUG] [Application] [16] Cached ticket: TESTIT-49342
[2025-07-03 09:42:42.552] [DEBUG] [Application] [16] Issue object properties: customfield_10553, customfield_10058, customfield_10305, customfield_10149, customfield_10723, customfield_10928, customfield_10194, customfield_10363, customfield_10750, customfield_10147, customfield_11280, customfield_10729, customfield_10153, customfield_10369, customfield_10145, customfield_10783, customfield_10727, duedate, customfield_10151, customfield_10367, customfield_10370, customfield_10486, customfield_10373, worklog, customfield_10157, customfield_10371, customfield_10008, customfield_10155, customfield_10519, customfield_10202, customfield_10306, customfield_10380, customfield_10062, ID, customfield_10375, customfield_10072, customfield_10069, customfield_10685, customfield_11251, customfield_10200, customfield_10187, customfield_11113, customfield_11245, components, customfield_11253, Transition, customfield_11250, customfield_11247, resolutiondate, customfield_10101, LastViewed, customfield_10385, customfield_11249, customfield_11279, customfield_10344, customfield_10209, customfield_10189, customfield_11212, customfield_10651, Project, customfield_10211, customfield_10105, customfield_11214, customfield_10012, customfield_10213, customfield_10395, customfield_10684, customfield_10116, customfield_10172, timeoriginalestimate, customfield_10203, customfield_10174, customfield_10025, customfield_10336, customfield_10065, customfield_10330, customfield_10223, customfield_10027, customfield_10686, fixVersions, customfield_10126, customfield_10182, customfield_10356, progress, customfield_10128, customfield_10042, customfield_10180, customfield_10562, customfield_10196, customfield_10858, customfield_10629, customfield_10261, customfield_10004, customfield_10049, customfield_10627, aggregatetimeestimate, customfield_10663, customfield_10185, customfield_10237, customfield_10557, customfield_10081, timespent, security, customfield_10057, aggregateprogress, aggregatetimespent, customfield_10314, environment, customfield_10722, customfield_10195, customfield_10239, customfield_10368, customfield_10229, customfield_10037, issuelinks, customfield_10348, customfield_10056, customfield_10131, customfield_10199, customfield_10054, Status, customfield_10618, customfield_10751, customfield_10161, Comment, Key, customfield_11254, customfield_10063, customfield_10060, customfield_10066, customfield_10226, customfield_10068, labels, customfield_10520, customfield_10489, statuscategorychangedate, customfield_10160, customfield_10222, customfield_10354, customfield_11252, customfield_10159, customfield_10393, customfield_11248, RestUrl, resolution, customfield_10384, customfield_10216, customfield_10379, customfield_10208, customfield_10386, customfield_10341, customfield_10132, customfield_10559, customfield_10171, customfield_10013, customfield_10100, customfield_10212, customfield_10204, customfield_10725, customfield_10117, customfield_10173, customfield_10011, customfield_10075, customfield_10398, customfield_10115, customfield_10347, customfield_10337, customfield_10321, customfield_10177, customfield_10215, customfield_10224, customfield_10352, Assignee, customfield_10130, customfield_10717, customfield_10129, customfield_10181, customfield_10000, customfield_10453, customfield_11181, customfield_10178, customfield_10029, customfield_10276, watches, customfield_10560, customfield_10133, customfield_10120, customfield_10102, customfield_10634, customfield_10249, customfield_10320, customfield_10048, customfield_11278, customfield_10346, customfield_10383, customfield_10184, customfield_11281, customfield_10726, customfield_10030, priority, customfield_10235, customfield_10720, customfield_10134, customfield_10047, customfield_10190, customfield_10721, customfield_10045, customfield_10448, customfield_10238, customfield_10882, customfield_10250, customfield_10198, customfield_10118, customfield_10619, customfield_10197, customfield_10251, customfield_10318, customfield_10055, Summary, Description, customfield_10447, customfield_10304, customfield_10255, customfield_10059, customfield_10053, customfield_10148, customfield_10382, customfield_10193, customfield_10488, attachment, customfield_10728, customfield_10225, customfield_10158, workratio, customfield_10372, customfield_10206, customfield_10156, customfield_10664, customfield_10201, customfield_10154, customfield_10521, statusCategory, Created, customfield_10064, issuetype, customfield_10254, customfield_10374, customfield_10326, customfield_10342, customfield_10317, customfield_10554, customfield_10227, customfield_10210, customfield_10214, customfield_10391, customfield_11246, customfield_10585, customfield_10070, customfield_11182, customfield_10630, customfield_10135, aggregatetimeoriginalestimate, customfield_10631, customfield_10010, customfield_10061, customfield_11215, timeestimate, customfield_10662, customfield_11213, customfield_10556, customfield_10028, customfield_10635, customfield_10396, customfield_10275, customfield_10719, timetracking, customfield_10175, customfield_10024, Creator, customfield_10381, customfield_10023, Updated, customfield_11179, customfield_10127, customfield_10183, customfield_11122, customfield_10561, customfield_10043, customfield_10322, customfield_10046, customfield_10192, customfield_10041, Reporter, customfield_10044, customfield_10628, customfield_10335, customfield_10040, subtasks, customfield_10191, customfield_10626, customfield_10343, versions, customfield_10186, HttpUrl, customfield_10319, customfield_10303, customfield_10001, customfield_10555, customfield_10252, customfield_10050
[2025-07-03 09:42:42.589] [DEBUG] [Application] [16] Found Comment property with 18 comments
[2025-07-03 09:42:42.617] [INFO] [Application] [16] Starting data extraction from ticket TESTIT-49342
[2025-07-03 09:42:42.679] [DEBUG] [Application] [16] Extracted data - Name: 'Test1 Test2', Job: 'Intern', Dept: 'IT', Model: 'test', Location: ''
[2025-07-03 09:42:43.206] [DEBUG] [Application] [16] Generating SAM account name for Test1 Test2
[2025-07-03 09:42:43.224] [DEBUG] [Application] [16] Checking if SAM account name 'test2t' exists
[2025-07-03 09:42:43.249] [INFO] [Application] [16] Generated SAM account name: test2t (simulation mode - could not verify uniqueness)
[2025-07-03 09:42:43.288] [DEBUG] [Application] [16] SAM account generated successfully
[2025-07-03 09:42:43.514] [DEBUG] [Application] [16] Generated account details from Jira ticket successfully
[2025-07-03 09:42:43.561] [INFO] [Application] [16] Data extraction completed. Please verify the populated fields.
[2025-07-03 09:42:43.601] [DEBUG] [Application] [16] Workflow step 'FetchData' status: Completed
[2025-07-03 09:42:43.629] [INFO] [Application] [16] Step 3: Validate Information
[2025-07-03 09:42:43.653] [DEBUG] [Application] [16] Workflow step 'Validation' status: InProgress
[2025-07-03 09:42:43.675] [INFO] [Application] [16] Successfully fetched and populated data from ticket TESTIT-49342
[2025-07-03 09:49:50.548] [DEBUG] [Testing] [18] Test registered: ConfigurationValidation in suite Unit
[2025-07-03 09:49:50.895] [DEBUG] [Testing] [18] Test registered: ConfigurationValidation in Unit
[2025-07-03 09:49:50.928] [DEBUG] [Testing] [18] Test registered: LoggingSystem in suite Unit
[2025-07-03 09:49:50.956] [DEBUG] [Testing] [18] Test registered: LoggingSystem in Unit
[2025-07-03 09:49:50.988] [DEBUG] [Testing] [18] Test registered: VersionManager in suite Unit
[2025-07-03 09:49:51.006] [DEBUG] [Testing] [18] Test registered: VersionManager in Unit
[2025-07-03 09:49:51.034] [DEBUG] [Testing] [18] Test registered: JiraConnection in suite Integration
[2025-07-03 09:49:51.062] [DEBUG] [Testing] [18] Test registered: JiraConnection in Integration
[2025-07-03 09:49:51.092] [DEBUG] [Testing] [18] Test registered: ActiveDirectoryConnection in suite Integration
[2025-07-03 09:49:51.138] [DEBUG] [Testing] [18] Test registered: ActiveDirectoryConnection in Integration
[2025-07-03 09:49:51.163] [DEBUG] [Testing] [18] Test registered: StartupTime in suite Performance
[2025-07-03 09:49:51.239] [DEBUG] [Testing] [18] Test registered: StartupTime in Performance
[2025-07-03 09:49:51.265] [DEBUG] [Testing] [18] Test registered: MemoryUsage in suite Performance
[2025-07-03 09:49:51.297] [DEBUG] [Testing] [18] Test registered: MemoryUsage in Performance
[2025-07-03 09:49:51.328] [DEBUG] [Testing] [18] Test registered: CredentialHandling in suite Security
[2025-07-03 09:49:51.353] [DEBUG] [Testing] [18] Test registered: CredentialHandling in Security
[2025-07-03 09:49:51.383] [DEBUG] [Testing] [18] Test registered: InputValidation in suite Security
[2025-07-03 09:49:51.411] [DEBUG] [Testing] [18] Test registered: InputValidation in Security
[2025-07-03 09:49:51.438] [DEBUG] [Testing] [18] Test registered: WindowCreation in suite UI
[2025-07-03 09:49:51.469] [DEBUG] [Testing] [18] Test registered: WindowCreation in UI
[2025-07-03 09:49:51.499] [INFO] [Testing] [18] Core tests initialized
[2025-07-03 09:49:51.595] [INFO] [Documentation] [18] Initializing documentation system
[2025-07-03 09:49:51.663] [INFO] [Documentation] [18] Starting documentation generation for: api
[2025-07-03 09:49:51.840] [INFO] [Documentation] [18] Documentation saved: Documentation\API_Reference.html
[2025-07-03 09:49:51.911] [INFO] [Documentation] [18] Documentation saved: Documentation\API_Reference.md
[2025-07-03 09:49:51.968] [INFO] [Documentation] [18] Documentation generation completed for: api
[2025-07-03 09:49:52.006] [INFO] [Documentation] [18] API documentation generated: 2 files
[2025-07-03 09:49:52.047] [INFO] [Documentation] [18] Starting documentation generation for: help
[2025-07-03 09:49:52.097] [INFO] [Documentation] [18] Documentation generation completed for: help
[2025-07-03 09:49:52.128] [INFO] [Documentation] [18] Help system documentation generated
[2025-07-03 09:49:52.157] [INFO] [Documentation] [18] Documentation system initialized
[2025-07-03 09:49:52.530] [INFO] [Application] [18] Initializing enhanced UI components...
[2025-07-03 09:49:53.005] [DEBUG] [Application] [18] Theme applied to controls
[2025-07-03 09:49:53.039] [DEBUG] [Application] [18] Modern theme applied successfully
[2025-07-03 09:49:53.123] [DEBUG] [Application] [18] Field validation initialized
[2025-07-03 09:49:53.230] [DEBUG] [Application] [18] Responsive layout initialized
[2025-07-03 09:49:53.645] [DEBUG] [Application] [18] Workflow step 'Connection' status: Pending
[2025-07-03 09:49:53.838] [INFO] [Application] [18] Enhanced UI initialization completed
[2025-07-03 09:49:53.939] [INFO] [Application] [18] Welcome! Please connect to Jira to begin.
[2025-07-03 09:49:54.035] [INFO] [Application] [18] OnboardingFromJiraGUI started successfully
[2025-07-03 09:49:54.108] [INFO] [Application] [18] [OK] Monitoring system initialized
[2025-07-03 09:49:54.237] [INFO] [Audit] [18] Audit: akinje performed PermissionInit on UserPermissions - Success | Context: SessionId=Unknown; Action=PermissionInit; Timestamp=07/03/2025 09:49:54; Result=Success; User=akinje; Detected=Standard user privileges; Target=UserPermissions; PrivilegeLevel=Standard
[2025-07-03 09:49:54.388] [INFO] [Application] [18] [OK] User permissions initialized for JERAGM\akinje
[2025-07-03 09:49:54.438] [DEBUG] [Application] [18]  Cache cleanup scheduled every 30 minutes
[2025-07-03 09:49:54.482] [INFO] [Application] [18] Applying Modern UI Integration...
[2025-07-03 09:49:54.719] [DEBUG] [Application] [18] Auto-completion setup for LastNameBox
[2025-07-03 09:49:54.755] [DEBUG] [Application] [18] Auto-completion setup for JobTitleBox
[2025-07-03 09:49:54.797] [DEBUG] [Application] [18] Auto-completion setup for FirstNameBox
[2025-07-03 09:49:54.833] [DEBUG] [Application] [18] Auto-completion setup for DepartmentBox
[2025-07-03 09:49:54.882] [DEBUG] [Application] [18] Auto-completion setup for ModelAccountBox
[2025-07-03 09:49:54.930] [DEBUG] [Application] [18] Auto-completion integration completed
[2025-07-03 09:49:55.093] [INFO] [Application] [18] Modern UI Integration completed successfully
[2025-07-03 09:50:39.407] [INFO] [Application] [18] Step 1: Connect to Jira
[2025-07-03 09:50:39.438] [DEBUG] [Application] [18] Workflow step 'Connection' status: InProgress
[2025-07-03 09:50:39.508] [INFO] [Application] [18] Connecting to https://jeragm.atlassian.net...
[2025-07-03 09:50:42.055] [DEBUG] [Application] [18] Workflow step 'Connection' status: Completed
[2025-07-03 09:50:42.095] [INFO] [Application] [18] Successfully connected to Jira
[2025-07-03 09:50:47.374] [INFO] [Application] [18] Step 2: Fetch Ticket Data
[2025-07-03 09:50:47.401] [DEBUG] [Application] [18] Workflow step 'FetchData' status: InProgress
[2025-07-03 09:50:47.415] [INFO] [Application] [18] Fetching data for ticket TESTIT-49342...
[2025-07-03 09:50:49.163] [DEBUG] [Application] [18] Cached ticket: TESTIT-49342
[2025-07-03 09:50:49.190] [DEBUG] [Application] [18] Issue object properties: customfield_10553, customfield_10058, customfield_10305, customfield_10149, customfield_10723, customfield_10928, customfield_10194, customfield_10363, customfield_10750, customfield_10147, customfield_11280, customfield_10729, customfield_10153, customfield_10369, customfield_10145, customfield_10783, customfield_10727, duedate, customfield_10151, customfield_10367, customfield_10370, customfield_10486, customfield_10373, worklog, customfield_10157, customfield_10371, customfield_10008, customfield_10155, customfield_10519, customfield_10202, customfield_10306, customfield_10380, customfield_10062, ID, customfield_10375, customfield_10072, customfield_10069, customfield_10685, customfield_11251, customfield_10200, customfield_10187, customfield_11113, customfield_11245, components, customfield_11253, Transition, customfield_11250, customfield_11247, resolutiondate, customfield_10101, LastViewed, customfield_10385, customfield_11249, customfield_11279, customfield_10344, customfield_10209, customfield_10189, customfield_11212, customfield_10651, Project, customfield_10211, customfield_10105, customfield_11214, customfield_10012, customfield_10213, customfield_10395, customfield_10684, customfield_10116, customfield_10172, timeoriginalestimate, customfield_10203, customfield_10174, customfield_10025, customfield_10336, customfield_10065, customfield_10330, customfield_10223, customfield_10027, customfield_10686, fixVersions, customfield_10126, customfield_10182, customfield_10356, progress, customfield_10128, customfield_10042, customfield_10180, customfield_10562, customfield_10196, customfield_10858, customfield_10629, customfield_10261, customfield_10004, customfield_10049, customfield_10627, aggregatetimeestimate, customfield_10663, customfield_10185, customfield_10237, customfield_10557, customfield_10081, timespent, security, customfield_10057, aggregateprogress, aggregatetimespent, customfield_10314, environment, customfield_10722, customfield_10195, customfield_10239, customfield_10368, customfield_10229, customfield_10037, issuelinks, customfield_10348, customfield_10056, customfield_10131, customfield_10199, customfield_10054, Status, customfield_10618, customfield_10751, customfield_10161, Comment, Key, customfield_11254, customfield_10063, customfield_10060, customfield_10066, customfield_10226, customfield_10068, labels, customfield_10520, customfield_10489, statuscategorychangedate, customfield_10160, customfield_10222, customfield_10354, customfield_11252, customfield_10159, customfield_10393, customfield_11248, RestUrl, resolution, customfield_10384, customfield_10216, customfield_10379, customfield_10208, customfield_10386, customfield_10341, customfield_10132, customfield_10559, customfield_10171, customfield_10013, customfield_10100, customfield_10212, customfield_10204, customfield_10725, customfield_10117, customfield_10173, customfield_10011, customfield_10075, customfield_10398, customfield_10115, customfield_10347, customfield_10337, customfield_10321, customfield_10177, customfield_10215, customfield_10224, customfield_10352, Assignee, customfield_10130, customfield_10717, customfield_10129, customfield_10181, customfield_10000, customfield_10453, customfield_11181, customfield_10178, customfield_10029, customfield_10276, watches, customfield_10560, customfield_10133, customfield_10120, customfield_10102, customfield_10634, customfield_10249, customfield_10320, customfield_10048, customfield_11278, customfield_10346, customfield_10383, customfield_10184, customfield_11281, customfield_10726, customfield_10030, priority, customfield_10235, customfield_10720, customfield_10134, customfield_10047, customfield_10190, customfield_10721, customfield_10045, customfield_10448, customfield_10238, customfield_10882, customfield_10250, customfield_10198, customfield_10118, customfield_10619, customfield_10197, customfield_10251, customfield_10318, customfield_10055, Summary, Description, customfield_10447, customfield_10304, customfield_10255, customfield_10059, customfield_10053, customfield_10148, customfield_10382, customfield_10193, customfield_10488, attachment, customfield_10728, customfield_10225, customfield_10158, workratio, customfield_10372, customfield_10206, customfield_10156, customfield_10664, customfield_10201, customfield_10154, customfield_10521, statusCategory, Created, customfield_10064, issuetype, customfield_10254, customfield_10374, customfield_10326, customfield_10342, customfield_10317, customfield_10554, customfield_10227, customfield_10210, customfield_10214, customfield_10391, customfield_11246, customfield_10585, customfield_10070, customfield_11182, customfield_10630, customfield_10135, aggregatetimeoriginalestimate, customfield_10631, customfield_10010, customfield_10061, customfield_11215, timeestimate, customfield_10662, customfield_11213, customfield_10556, customfield_10028, customfield_10635, customfield_10396, customfield_10275, customfield_10719, timetracking, customfield_10175, customfield_10024, Creator, customfield_10381, customfield_10023, Updated, customfield_11179, customfield_10127, customfield_10183, customfield_11122, customfield_10561, customfield_10043, customfield_10322, customfield_10046, customfield_10192, customfield_10041, Reporter, customfield_10044, customfield_10628, customfield_10335, customfield_10040, subtasks, customfield_10191, customfield_10626, customfield_10343, versions, customfield_10186, HttpUrl, customfield_10319, customfield_10303, customfield_10001, customfield_10555, customfield_10252, customfield_10050
[2025-07-03 09:50:49.204] [DEBUG] [Application] [18] Found Comment property with 18 comments
[2025-07-03 09:50:49.223] [INFO] [Application] [18] Starting data extraction from ticket TESTIT-49342
[2025-07-03 09:50:49.253] [DEBUG] [Application] [18] Extracted data - Name: 'Test1 Test2', Job: 'Intern', Dept: 'IT', Model: 'test', Location: ''
[2025-07-03 09:50:49.785] [DEBUG] [Application] [18] Generating SAM account name for Test1 Test2
[2025-07-03 09:50:49.795] [DEBUG] [Application] [18] Checking if SAM account name 'test2t' exists
[2025-07-03 09:50:49.803] [INFO] [Application] [18] Generated SAM account name: test2t (simulation mode - could not verify uniqueness)
[2025-07-03 09:50:49.814] [DEBUG] [Application] [18] DIAGNOSTIC: generatedSam = '31 32 33 test2t'
[2025-07-03 09:50:49.824] [DEBUG] [Application] [18] DIAGNOSTIC: samForGui = '31 32 33 test2t'
[2025-07-03 09:50:49.843] [DEBUG] [Application] [18] DIAGNOSTIC: samForGui length = 15
[2025-07-03 09:50:49.990] [DEBUG] [Application] [18] DIAGNOSTIC: Original value = '31 32 33 test2t'
[2025-07-03 09:50:50.000] [DEBUG] [Application] [18] DIAGNOSTIC: SamBox.Text after assignment = '31 32 33 test2t'
[2025-07-03 09:50:50.016] [DEBUG] [Application] [18] DIAGNOSTIC: Values match = True
[2025-07-03 09:50:50.179] [DEBUG] [Application] [18] Generated account details from Jira ticket successfully
[2025-07-03 09:50:50.244] [INFO] [Application] [18] Data extraction completed. Please verify the populated fields.
[2025-07-03 09:50:50.276] [DEBUG] [Application] [18] Workflow step 'FetchData' status: Completed
[2025-07-03 09:50:50.298] [INFO] [Application] [18] Step 3: Validate Information
[2025-07-03 09:50:50.326] [DEBUG] [Application] [18] Workflow step 'Validation' status: InProgress
[2025-07-03 09:50:50.357] [INFO] [Application] [18] Successfully fetched and populated data from ticket TESTIT-49342
[2025-07-03 09:54:23.292] [DEBUG] [Testing] [18] Test registered: ConfigurationValidation in suite Unit
[2025-07-03 09:54:23.466] [DEBUG] [Testing] [18] Test registered: ConfigurationValidation in Unit
[2025-07-03 09:54:23.496] [DEBUG] [Testing] [18] Test registered: LoggingSystem in suite Unit
[2025-07-03 09:54:23.522] [DEBUG] [Testing] [18] Test registered: LoggingSystem in Unit
[2025-07-03 09:54:23.551] [DEBUG] [Testing] [18] Test registered: VersionManager in suite Unit
[2025-07-03 09:54:23.581] [DEBUG] [Testing] [18] Test registered: VersionManager in Unit
[2025-07-03 09:54:23.612] [DEBUG] [Testing] [18] Test registered: JiraConnection in suite Integration
[2025-07-03 09:54:23.637] [DEBUG] [Testing] [18] Test registered: JiraConnection in Integration
[2025-07-03 09:54:23.665] [DEBUG] [Testing] [18] Test registered: ActiveDirectoryConnection in suite Integration
[2025-07-03 09:54:23.694] [DEBUG] [Testing] [18] Test registered: ActiveDirectoryConnection in Integration
[2025-07-03 09:54:23.728] [DEBUG] [Testing] [18] Test registered: StartupTime in suite Performance
[2025-07-03 09:54:23.752] [DEBUG] [Testing] [18] Test registered: StartupTime in Performance
[2025-07-03 09:54:23.781] [DEBUG] [Testing] [18] Test registered: MemoryUsage in suite Performance
[2025-07-03 09:54:23.809] [DEBUG] [Testing] [18] Test registered: MemoryUsage in Performance
[2025-07-03 09:54:23.853] [DEBUG] [Testing] [18] Test registered: CredentialHandling in suite Security
[2025-07-03 09:54:23.883] [DEBUG] [Testing] [18] Test registered: CredentialHandling in Security
[2025-07-03 09:54:23.916] [DEBUG] [Testing] [18] Test registered: InputValidation in suite Security
[2025-07-03 09:54:23.945] [DEBUG] [Testing] [18] Test registered: InputValidation in Security
[2025-07-03 09:54:23.977] [DEBUG] [Testing] [18] Test registered: WindowCreation in suite UI
[2025-07-03 09:54:24.003] [DEBUG] [Testing] [18] Test registered: WindowCreation in UI
[2025-07-03 09:54:24.039] [INFO] [Testing] [18] Core tests initialized
[2025-07-03 09:54:24.197] [INFO] [Documentation] [18] Initializing documentation system
[2025-07-03 09:54:24.273] [INFO] [Documentation] [18] Starting documentation generation for: api
[2025-07-03 09:54:24.488] [INFO] [Documentation] [18] Documentation saved: Documentation\API_Reference.html
[2025-07-03 09:54:24.561] [INFO] [Documentation] [18] Documentation saved: Documentation\API_Reference.md
[2025-07-03 09:54:24.614] [INFO] [Documentation] [18] Documentation generation completed for: api
[2025-07-03 09:54:24.657] [INFO] [Documentation] [18] API documentation generated: 2 files
[2025-07-03 09:54:24.698] [INFO] [Documentation] [18] Starting documentation generation for: help
[2025-07-03 09:54:24.741] [INFO] [Documentation] [18] Documentation generation completed for: help
[2025-07-03 09:54:24.775] [INFO] [Documentation] [18] Help system documentation generated
[2025-07-03 09:54:24.807] [INFO] [Documentation] [18] Documentation system initialized
[2025-07-03 09:54:25.177] [INFO] [Application] [18] Initializing enhanced UI components...
[2025-07-03 09:54:25.719] [DEBUG] [Application] [18] Theme applied to controls
[2025-07-03 09:54:25.881] [DEBUG] [Application] [18] Modern theme applied successfully
[2025-07-03 09:54:26.024] [DEBUG] [Application] [18] Field validation initialized
[2025-07-03 09:54:26.118] [DEBUG] [Application] [18] Responsive layout initialized
[2025-07-03 09:54:26.464] [DEBUG] [Application] [18] Workflow step 'Connection' status: Pending
[2025-07-03 09:54:26.637] [INFO] [Application] [18] Enhanced UI initialization completed
[2025-07-03 09:54:26.809] [INFO] [Application] [18] Welcome! Please connect to Jira to begin.
[2025-07-03 09:54:26.874] [INFO] [Application] [18] OnboardingFromJiraGUI started successfully
[2025-07-03 09:54:26.945] [INFO] [Application] [18] [OK] Monitoring system initialized
[2025-07-03 09:54:27.068] [INFO] [Audit] [18] Audit: akinje performed PermissionInit on UserPermissions - Success | Context: SessionId=Unknown; Action=PermissionInit; Timestamp=07/03/2025 09:54:27; Result=Success; User=akinje; Detected=Standard user privileges; Target=UserPermissions; PrivilegeLevel=Standard
[2025-07-03 09:54:27.138] [INFO] [Application] [18] [OK] User permissions initialized for JERAGM\akinje
[2025-07-03 09:54:27.191] [DEBUG] [Application] [18]  Cache cleanup scheduled every 30 minutes
[2025-07-03 09:54:27.239] [INFO] [Application] [18] Applying Modern UI Integration...
[2025-07-03 09:54:27.700] [DEBUG] [Application] [18] Auto-completion setup for LastNameBox
[2025-07-03 09:54:27.741] [DEBUG] [Application] [18] Auto-completion setup for JobTitleBox
[2025-07-03 09:54:27.781] [DEBUG] [Application] [18] Auto-completion setup for FirstNameBox
[2025-07-03 09:54:27.822] [DEBUG] [Application] [18] Auto-completion setup for DepartmentBox
[2025-07-03 09:54:27.903] [DEBUG] [Application] [18] Auto-completion setup for ModelAccountBox
[2025-07-03 09:54:27.978] [DEBUG] [Application] [18] Auto-completion integration completed
[2025-07-03 09:54:28.105] [INFO] [Application] [18] Modern UI Integration completed successfully
[2025-07-03 09:54:54.108] [INFO] [Application] [18] Step 1: Connect to Jira
[2025-07-03 09:54:54.141] [DEBUG] [Application] [18] Workflow step 'Connection' status: InProgress
[2025-07-03 09:54:54.248] [INFO] [Application] [18] Connecting to https://jeragm.atlassian.net...
[2025-07-03 09:54:55.726] [DEBUG] [Application] [18] Workflow step 'Connection' status: Completed
[2025-07-03 09:54:55.759] [INFO] [Application] [18] Successfully connected to Jira
[2025-07-03 09:55:03.404] [INFO] [Application] [18] Step 2: Fetch Ticket Data
[2025-07-03 09:55:03.428] [DEBUG] [Application] [18] Workflow step 'FetchData' status: InProgress
[2025-07-03 09:55:03.444] [INFO] [Application] [18] Fetching data for ticket TESTIT-49342...
[2025-07-03 09:55:06.554] [DEBUG] [Application] [18] Cached ticket: TESTIT-49342
[2025-07-03 09:55:06.614] [DEBUG] [Application] [18] Issue object properties: customfield_10553, customfield_10058, customfield_10305, customfield_10149, customfield_10723, customfield_10928, customfield_10194, customfield_10363, customfield_10750, customfield_10147, customfield_11280, customfield_10729, customfield_10153, customfield_10369, customfield_10145, customfield_10783, customfield_10727, duedate, customfield_10151, customfield_10367, customfield_10370, customfield_10486, customfield_10373, worklog, customfield_10157, customfield_10371, customfield_10008, customfield_10155, customfield_10519, customfield_10202, customfield_10306, customfield_10380, customfield_10062, ID, customfield_10375, customfield_10072, customfield_10069, customfield_10685, customfield_11251, customfield_10200, customfield_10187, customfield_11113, customfield_11245, components, customfield_11253, Transition, customfield_11250, customfield_11247, resolutiondate, customfield_10101, LastViewed, customfield_10385, customfield_11249, customfield_11279, customfield_10344, customfield_10209, customfield_10189, customfield_11212, customfield_10651, Project, customfield_10211, customfield_10105, customfield_11214, customfield_10012, customfield_10213, customfield_10395, customfield_10684, customfield_10116, customfield_10172, timeoriginalestimate, customfield_10203, customfield_10174, customfield_10025, customfield_10336, customfield_10065, customfield_10330, customfield_10223, customfield_10027, customfield_10686, fixVersions, customfield_10126, customfield_10182, customfield_10356, progress, customfield_10128, customfield_10042, customfield_10180, customfield_10562, customfield_10196, customfield_10858, customfield_10629, customfield_10261, customfield_10004, customfield_10049, customfield_10627, aggregatetimeestimate, customfield_10663, customfield_10185, customfield_10237, customfield_10557, customfield_10081, timespent, security, customfield_10057, aggregateprogress, aggregatetimespent, customfield_10314, environment, customfield_10722, customfield_10195, customfield_10239, customfield_10368, customfield_10229, customfield_10037, issuelinks, customfield_10348, customfield_10056, customfield_10131, customfield_10199, customfield_10054, Status, customfield_10618, customfield_10751, customfield_10161, Comment, Key, customfield_11254, customfield_10063, customfield_10060, customfield_10066, customfield_10226, customfield_10068, labels, customfield_10520, customfield_10489, statuscategorychangedate, customfield_10160, customfield_10222, customfield_10354, customfield_11252, customfield_10159, customfield_10393, customfield_11248, RestUrl, resolution, customfield_10384, customfield_10216, customfield_10379, customfield_10208, customfield_10386, customfield_10341, customfield_10132, customfield_10559, customfield_10171, customfield_10013, customfield_10100, customfield_10212, customfield_10204, customfield_10725, customfield_10117, customfield_10173, customfield_10011, customfield_10075, customfield_10398, customfield_10115, customfield_10347, customfield_10337, customfield_10321, customfield_10177, customfield_10215, customfield_10224, customfield_10352, Assignee, customfield_10130, customfield_10717, customfield_10129, customfield_10181, customfield_10000, customfield_10453, customfield_11181, customfield_10178, customfield_10029, customfield_10276, watches, customfield_10560, customfield_10133, customfield_10120, customfield_10102, customfield_10634, customfield_10249, customfield_10320, customfield_10048, customfield_11278, customfield_10346, customfield_10383, customfield_10184, customfield_11281, customfield_10726, customfield_10030, priority, customfield_10235, customfield_10720, customfield_10134, customfield_10047, customfield_10190, customfield_10721, customfield_10045, customfield_10448, customfield_10238, customfield_10882, customfield_10250, customfield_10198, customfield_10118, customfield_10619, customfield_10197, customfield_10251, customfield_10318, customfield_10055, Summary, Description, customfield_10447, customfield_10304, customfield_10255, customfield_10059, customfield_10053, customfield_10148, customfield_10382, customfield_10193, customfield_10488, attachment, customfield_10728, customfield_10225, customfield_10158, workratio, customfield_10372, customfield_10206, customfield_10156, customfield_10664, customfield_10201, customfield_10154, customfield_10521, statusCategory, Created, customfield_10064, issuetype, customfield_10254, customfield_10374, customfield_10326, customfield_10342, customfield_10317, customfield_10554, customfield_10227, customfield_10210, customfield_10214, customfield_10391, customfield_11246, customfield_10585, customfield_10070, customfield_11182, customfield_10630, customfield_10135, aggregatetimeoriginalestimate, customfield_10631, customfield_10010, customfield_10061, customfield_11215, timeestimate, customfield_10662, customfield_11213, customfield_10556, customfield_10028, customfield_10635, customfield_10396, customfield_10275, customfield_10719, timetracking, customfield_10175, customfield_10024, Creator, customfield_10381, customfield_10023, Updated, customfield_11179, customfield_10127, customfield_10183, customfield_11122, customfield_10561, customfield_10043, customfield_10322, customfield_10046, customfield_10192, customfield_10041, Reporter, customfield_10044, customfield_10628, customfield_10335, customfield_10040, subtasks, customfield_10191, customfield_10626, customfield_10343, versions, customfield_10186, HttpUrl, customfield_10319, customfield_10303, customfield_10001, customfield_10555, customfield_10252, customfield_10050
[2025-07-03 09:55:06.673] [DEBUG] [Application] [18] Found Comment property with 18 comments
[2025-07-03 09:55:06.707] [INFO] [Application] [18] Starting data extraction from ticket TESTIT-49342
[2025-07-03 09:55:06.788] [DEBUG] [Application] [18] Extracted data - Name: 'Test1 Test2', Job: 'Intern', Dept: 'IT', Model: 'test', Location: ''
[2025-07-03 09:55:07.338] [DEBUG] [Application] [18] Generating SAM account name for Test1 Test2
[2025-07-03 09:55:07.359] [DEBUG] [Application] [18] DIAGNOSTIC: proposedSam just generated = 'test2t'
[2025-07-03 09:55:07.386] [DEBUG] [Application] [18] Checking if SAM account name 'test2t' exists
[2025-07-03 09:55:07.422] [INFO] [Application] [18] Generated SAM account name: test2t (simulation mode - could not verify uniqueness)
[2025-07-03 09:55:07.466] [DEBUG] [Application] [18] DIAGNOSTIC: About to return proposedSam = 'test2t'
[2025-07-03 09:55:07.497] [DEBUG] [Application] [18] DIAGNOSTIC: returnValue after cast = 'test2t'
[2025-07-03 09:55:07.521] [DEBUG] [Application] [18] DIAGNOSTIC: generatedSam = '31 32 33 34 35 36 test2t'
[2025-07-03 09:55:07.562] [DEBUG] [Application] [18] DIAGNOSTIC: samForGui = '31 32 33 34 35 36 test2t'
[2025-07-03 09:55:07.592] [DEBUG] [Application] [18] DIAGNOSTIC: samForGui length = 24
[2025-07-03 09:55:07.691] [DEBUG] [Application] [18] DIAGNOSTIC: Original value = '31 32 33 34 35 36 test2t'
[2025-07-03 09:55:07.730] [DEBUG] [Application] [18] DIAGNOSTIC: SamBox.Text after assignment = '31 32 33 34 35 36 test2t'
[2025-07-03 09:55:07.766] [DEBUG] [Application] [18] DIAGNOSTIC: Values match = True
[2025-07-03 09:55:07.951] [DEBUG] [Application] [18] Generated account details from Jira ticket successfully
[2025-07-03 09:55:08.010] [INFO] [Application] [18] Data extraction completed. Please verify the populated fields.
[2025-07-03 09:55:08.058] [DEBUG] [Application] [18] Workflow step 'FetchData' status: Completed
[2025-07-03 09:55:08.101] [INFO] [Application] [18] Step 3: Validate Information
[2025-07-03 09:55:08.153] [DEBUG] [Application] [18] Workflow step 'Validation' status: InProgress
[2025-07-03 09:55:08.186] [INFO] [Application] [18] Successfully fetched and populated data from ticket TESTIT-49342
[2025-07-03 09:57:27.378] [INFO] [Application] [18] Step 3: Validate Information
[2025-07-03 09:57:27.408] [DEBUG] [Application] [18] Workflow step 'FetchData' status: InProgress
[2025-07-03 09:57:27.429] [INFO] [Application] [18] Fetching data for ticket IT-46455...
[2025-07-03 09:57:29.992] [DEBUG] [Application] [18] Cached ticket: IT-46455
[2025-07-03 09:57:30.030] [DEBUG] [Application] [18] Issue object properties: customfield_10553, customfield_10058, customfield_10305, customfield_10149, customfield_10723, customfield_10928, customfield_10194, customfield_10363, customfield_10750, customfield_10147, customfield_11280, customfield_10729, customfield_10153, customfield_10369, customfield_10145, customfield_10783, customfield_10727, duedate, customfield_10151, customfield_10367, customfield_10370, customfield_10486, customfield_10373, worklog, customfield_10157, customfield_10371, customfield_10008, customfield_10155, customfield_10519, customfield_10202, customfield_10306, customfield_10380, customfield_10062, ID, customfield_10375, customfield_10072, customfield_10069, customfield_10685, customfield_11251, customfield_10200, customfield_10187, customfield_11113, customfield_11245, components, customfield_11253, Transition, customfield_11250, customfield_11247, resolutiondate, customfield_10101, LastViewed, customfield_10385, customfield_11249, customfield_11279, customfield_10344, customfield_10209, customfield_10189, customfield_11212, customfield_10651, Project, customfield_10211, customfield_10105, customfield_11214, customfield_10012, customfield_10213, customfield_10395, customfield_10116, customfield_10172, timeoriginalestimate, customfield_10203, customfield_10174, customfield_10025, customfield_10336, customfield_10065, customfield_10684, customfield_10223, customfield_10027, customfield_10686, fixVersions, customfield_10126, customfield_10182, customfield_10356, progress, customfield_10128, customfield_10042, customfield_10180, customfield_10562, customfield_10196, customfield_10858, customfield_10629, customfield_10261, customfield_10004, customfield_10049, customfield_10627, aggregatetimeestimate, customfield_10663, customfield_10185, customfield_10237, customfield_10557, customfield_10081, timespent, security, customfield_10057, aggregateprogress, aggregatetimespent, customfield_10314, environment, customfield_10722, customfield_10195, customfield_10239, customfield_10368, customfield_10229, customfield_10037, issuelinks, customfield_10348, customfield_10056, customfield_10131, customfield_10199, customfield_10054, Status, customfield_10618, customfield_10751, customfield_10161, Comment, Key, customfield_11254, customfield_10063, customfield_10060, customfield_10066, customfield_10226, customfield_10068, labels, customfield_10520, customfield_10489, statuscategorychangedate, customfield_10160, customfield_10222, customfield_10354, customfield_11252, customfield_10159, customfield_10393, customfield_11248, RestUrl, resolution, customfield_10384, customfield_10216, customfield_10379, customfield_10208, customfield_10386, customfield_10341, customfield_10132, customfield_10559, customfield_10171, customfield_10013, customfield_10100, customfield_10212, customfield_10204, customfield_10725, customfield_10117, customfield_10173, customfield_10011, customfield_10075, customfield_10398, customfield_10115, customfield_10347, customfield_10337, customfield_10321, customfield_10177, customfield_10215, customfield_10224, customfield_10352, Assignee, customfield_10130, customfield_10717, customfield_10129, customfield_10181, customfield_10000, customfield_10453, customfield_11181, customfield_10178, customfield_10029, customfield_10276, watches, customfield_10560, customfield_10133, customfield_10120, customfield_10102, customfield_10634, customfield_10249, customfield_10320, customfield_10048, customfield_11278, customfield_10346, customfield_10383, customfield_10184, customfield_11281, customfield_10726, customfield_10030, priority, customfield_10235, customfield_10720, customfield_10134, customfield_10047, customfield_10190, customfield_10721, customfield_10045, customfield_10448, customfield_10238, customfield_10882, customfield_10250, customfield_10198, customfield_10118, customfield_10619, customfield_10197, customfield_10251, customfield_10318, customfield_10055, Summary, Description, customfield_10447, customfield_10304, customfield_10255, customfield_10059, customfield_10053, customfield_10148, customfield_10382, customfield_10193, customfield_10488, attachment, customfield_10728, customfield_10225, customfield_10158, workratio, customfield_10372, customfield_10206, customfield_10156, customfield_10664, customfield_10201, customfield_10154, customfield_10521, statusCategory, Created, customfield_10064, issuetype, customfield_10254, customfield_10374, customfield_10342, customfield_10317, customfield_10554, customfield_10227, customfield_10210, customfield_10214, customfield_10391, customfield_11246, customfield_10585, customfield_10070, customfield_11182, customfield_10630, customfield_10135, aggregatetimeoriginalestimate, customfield_10631, customfield_10010, customfield_10061, customfield_11215, timeestimate, customfield_10662, customfield_11213, customfield_10556, customfield_10028, customfield_10635, customfield_10396, customfield_10275, customfield_10719, timetracking, customfield_10175, customfield_10024, Creator, customfield_10381, customfield_10023, Updated, customfield_11179, customfield_10127, customfield_10183, customfield_11122, customfield_10561, customfield_10043, customfield_10322, customfield_10046, customfield_10192, customfield_10041, Reporter, customfield_10044, customfield_10628, customfield_10335, customfield_10040, subtasks, customfield_10191, customfield_10626, customfield_10343, versions, customfield_10186, HttpUrl, customfield_10319, customfield_10303, customfield_10001, customfield_10555, customfield_10252, customfield_10050
[2025-07-03 09:57:30.068] [DEBUG] [Application] [18] Found Comment property with 4 comments
[2025-07-03 09:57:30.104] [INFO] [Application] [18] Starting data extraction from ticket IT-46455
[2025-07-03 09:57:30.151] [DEBUG] [Application] [18] Extracted data - Name: 'Herberto De Souza', Job: 'LNG Structured Trading Intern', Dept: 'LNG', Model: 'NA', Location: ''
[2025-07-03 09:57:30.186] [DEBUG] [Application] [18] Generating SAM account name for Herberto Test2
[2025-07-03 09:57:30.220] [DEBUG] [Application] [18] DIAGNOSTIC: proposedSam just generated = 'test2h'
[2025-07-03 09:57:30.260] [DEBUG] [Application] [18] Checking if SAM account name 'test2h' exists
[2025-07-03 09:57:30.294] [INFO] [Application] [18] Generated SAM account name: test2h (simulation mode - could not verify uniqueness)
[2025-07-03 09:57:30.334] [DEBUG] [Application] [18] DIAGNOSTIC: About to return proposedSam = 'test2h'
[2025-07-03 09:57:30.364] [DEBUG] [Application] [18] DIAGNOSTIC: returnValue after cast = 'test2h'
[2025-07-03 09:57:30.395] [DEBUG] [Application] [18] DIAGNOSTIC: Event handler setting SAM = '58 59 60 61 62 63 test2h'
[2025-07-03 09:57:30.491] [DEBUG] [Application] [18] DIAGNOSTIC: Event handler SamBox.Text after assignment = '58 59 60 61 62 63 test2h'
[2025-07-03 09:57:30.658] [DEBUG] [Application] [18] Auto-generated account details: SAM='58 59 60 61 62 63 test2h', UPN='<EMAIL>'
[2025-07-03 09:57:30.752] [DEBUG] [Application] [18] Removed spaces from names: 'Herberto De Souza' -> 'Herberto DeSouza'
[2025-07-03 09:57:30.779] [DEBUG] [Application] [18] Generating SAM account name for Herberto DeSouza
[2025-07-03 09:57:30.800] [DEBUG] [Application] [18] DIAGNOSTIC: proposedSam just generated = 'desouh'
[2025-07-03 09:57:30.832] [DEBUG] [Application] [18] Checking if SAM account name 'desouh' exists
[2025-07-03 09:57:30.867] [INFO] [Application] [18] Generated SAM account name: desouh (simulation mode - could not verify uniqueness)
[2025-07-03 09:57:30.901] [DEBUG] [Application] [18] DIAGNOSTIC: About to return proposedSam = 'desouh'
[2025-07-03 09:57:30.934] [DEBUG] [Application] [18] DIAGNOSTIC: returnValue after cast = 'desouh'
[2025-07-03 09:57:30.970] [DEBUG] [Application] [18] DIAGNOSTIC: Event handler setting SAM = '67 68 69 70 71 72 73 desouh'
[2025-07-03 09:57:31.075] [DEBUG] [Application] [18] DIAGNOSTIC: Event handler SamBox.Text after assignment = '67 68 69 70 71 72 73 desouh'
[2025-07-03 09:57:31.114] [DEBUG] [Application] [18] Removed spaces from names for UPN: 'Herberto De Souza' -> 'Herberto DeSouza'
[2025-07-03 09:57:31.267] [DEBUG] [Application] [18] Auto-generated account details: SAM='67 68 69 70 71 72 73 desouh', UPN='76 <EMAIL>'
[2025-07-03 09:57:31.530] [DEBUG] [Application] [18] Removed spaces from names: 'Herberto De Souza' -> 'Herberto DeSouza'
[2025-07-03 09:57:31.545] [DEBUG] [Application] [18] Generating SAM account name for Herberto DeSouza
[2025-07-03 09:57:31.559] [DEBUG] [Application] [18] DIAGNOSTIC: proposedSam just generated = 'desouh'
[2025-07-03 09:57:31.577] [DEBUG] [Application] [18] Checking if SAM account name 'desouh' exists
[2025-07-03 09:57:31.604] [INFO] [Application] [18] Generated SAM account name: desouh (simulation mode - could not verify uniqueness)
[2025-07-03 09:57:31.633] [DEBUG] [Application] [18] DIAGNOSTIC: About to return proposedSam = 'desouh'
[2025-07-03 09:57:31.667] [DEBUG] [Application] [18] DIAGNOSTIC: returnValue after cast = 'desouh'
[2025-07-03 09:57:31.697] [DEBUG] [Application] [18] DIAGNOSTIC: generatedSam = '78 79 80 81 82 83 84 desouh'
[2025-07-03 09:57:31.728] [DEBUG] [Application] [18] DIAGNOSTIC: samForGui = '78 79 80 81 82 83 84 desouh'
[2025-07-03 09:57:31.755] [DEBUG] [Application] [18] DIAGNOSTIC: samForGui length = 27
[2025-07-03 09:57:31.845] [DEBUG] [Application] [18] DIAGNOSTIC: Original value = '78 79 80 81 82 83 84 desouh'
[2025-07-03 09:57:31.860] [DEBUG] [Application] [18] DIAGNOSTIC: SamBox.Text after assignment = '78 79 80 81 82 83 84 desouh'
[2025-07-03 09:57:31.881] [DEBUG] [Application] [18] DIAGNOSTIC: Values match = True
[2025-07-03 09:57:31.904] [DEBUG] [Application] [18] Removed spaces from names for UPN: 'Herberto De Souza' -> 'Herberto DeSouza'
[2025-07-03 09:57:32.042] [DEBUG] [Application] [18] Generated account details from Jira ticket successfully
[2025-07-03 09:57:32.072] [INFO] [Application] [18] Data extraction completed. Please verify the populated fields.
[2025-07-03 09:57:32.088] [INFO] [Application] [18] Step 3: Validate Information
[2025-07-03 09:57:32.111] [DEBUG] [Application] [18] Workflow step 'FetchData' status: Completed
[2025-07-03 09:57:32.139] [INFO] [Application] [18] Step 3: Validate Information
[2025-07-03 09:57:32.159] [DEBUG] [Application] [18] Workflow step 'Validation' status: InProgress
[2025-07-03 09:57:32.199] [INFO] [Application] [18] Successfully fetched and populated data from ticket IT-46455
[2025-07-03 10:01:54.997] [DEBUG] [Testing] [16] Test registered: ConfigurationValidation in suite Unit
[2025-07-03 10:01:55.080] [DEBUG] [Testing] [16] Test registered: ConfigurationValidation in Unit
[2025-07-03 10:01:55.089] [DEBUG] [Testing] [16] Test registered: LoggingSystem in suite Unit
[2025-07-03 10:01:55.095] [DEBUG] [Testing] [16] Test registered: LoggingSystem in Unit
[2025-07-03 10:01:55.106] [DEBUG] [Testing] [16] Test registered: VersionManager in suite Unit
[2025-07-03 10:01:55.115] [DEBUG] [Testing] [16] Test registered: VersionManager in Unit
[2025-07-03 10:01:55.124] [DEBUG] [Testing] [16] Test registered: JiraConnection in suite Integration
[2025-07-03 10:01:55.133] [DEBUG] [Testing] [16] Test registered: JiraConnection in Integration
[2025-07-03 10:01:55.142] [DEBUG] [Testing] [16] Test registered: ActiveDirectoryConnection in suite Integration
[2025-07-03 10:01:55.148] [DEBUG] [Testing] [16] Test registered: ActiveDirectoryConnection in Integration
[2025-07-03 10:01:55.158] [DEBUG] [Testing] [16] Test registered: StartupTime in suite Performance
[2025-07-03 10:01:55.167] [DEBUG] [Testing] [16] Test registered: StartupTime in Performance
[2025-07-03 10:01:55.231] [DEBUG] [Testing] [16] Test registered: MemoryUsage in suite Performance
[2025-07-03 10:01:55.237] [DEBUG] [Testing] [16] Test registered: MemoryUsage in Performance
[2025-07-03 10:01:55.244] [DEBUG] [Testing] [16] Test registered: CredentialHandling in suite Security
[2025-07-03 10:01:55.249] [DEBUG] [Testing] [16] Test registered: CredentialHandling in Security
[2025-07-03 10:01:55.256] [DEBUG] [Testing] [16] Test registered: InputValidation in suite Security
[2025-07-03 10:01:55.266] [DEBUG] [Testing] [16] Test registered: InputValidation in Security
[2025-07-03 10:01:55.280] [DEBUG] [Testing] [16] Test registered: WindowCreation in suite UI
[2025-07-03 10:01:55.289] [DEBUG] [Testing] [16] Test registered: WindowCreation in UI
[2025-07-03 10:01:55.297] [INFO] [Testing] [16] Core tests initialized
[2025-07-03 10:01:55.323] [INFO] [Documentation] [16] Initializing documentation system
[2025-07-03 10:01:55.347] [INFO] [Documentation] [16] Starting documentation generation for: api
[2025-07-03 10:01:55.394] [INFO] [Documentation] [16] Documentation saved: Documentation\API_Reference.html
[2025-07-03 10:01:55.410] [INFO] [Documentation] [16] Documentation saved: Documentation\API_Reference.md
[2025-07-03 10:01:55.423] [INFO] [Documentation] [16] Documentation generation completed for: api
[2025-07-03 10:01:55.433] [INFO] [Documentation] [16] API documentation generated: 2 files
[2025-07-03 10:01:55.441] [INFO] [Documentation] [16] Starting documentation generation for: help
[2025-07-03 10:01:55.454] [INFO] [Documentation] [16] Documentation generation completed for: help
[2025-07-03 10:01:55.459] [INFO] [Documentation] [16] Help system documentation generated
[2025-07-03 10:01:55.468] [INFO] [Documentation] [16] Documentation system initialized
[2025-07-03 10:01:55.548] [INFO] [Application] [16] Initializing enhanced UI components...
[2025-07-03 10:01:55.618] [DEBUG] [Application] [16] Theme applied to controls
[2025-07-03 10:01:55.630] [DEBUG] [Application] [16] Modern theme applied successfully
[2025-07-03 10:01:55.661] [DEBUG] [Application] [16] Field validation initialized
[2025-07-03 10:01:55.690] [DEBUG] [Application] [16] Responsive layout initialized
[2025-07-03 10:01:55.755] [DEBUG] [Application] [16] Workflow step 'Connection' status: Pending
[2025-07-03 10:01:55.790] [INFO] [Application] [16] Enhanced UI initialization completed
[2025-07-03 10:01:55.818] [INFO] [Application] [16] Welcome! Please connect to Jira to begin.
[2025-07-03 10:01:55.830] [INFO] [Application] [16] OnboardingFromJiraGUI started successfully
[2025-07-03 10:01:55.850] [INFO] [Application] [16] [OK] Monitoring system initialized
[2025-07-03 10:01:55.998] [INFO] [Audit] [16] Audit: akinje performed PermissionInit on UserPermissions - Success | Context: SessionId=Unknown; Action=PermissionInit; Timestamp=07/03/2025 10:01:55; Result=Success; User=akinje; Detected=Standard user privileges; Target=UserPermissions; PrivilegeLevel=Standard
[2025-07-03 10:01:56.018] [INFO] [Application] [16] [OK] User permissions initialized for JERAGM\akinje
[2025-07-03 10:01:56.036] [DEBUG] [Application] [16]  Cache cleanup scheduled every 30 minutes
[2025-07-03 10:01:56.047] [INFO] [Application] [16] Applying Modern UI Integration...
[2025-07-03 10:01:56.102] [DEBUG] [Application] [16] Auto-completion setup for LastNameBox
[2025-07-03 10:01:56.113] [DEBUG] [Application] [16] Auto-completion setup for JobTitleBox
[2025-07-03 10:01:56.126] [DEBUG] [Application] [16] Auto-completion setup for FirstNameBox
[2025-07-03 10:01:56.145] [DEBUG] [Application] [16] Auto-completion setup for DepartmentBox
[2025-07-03 10:01:56.164] [DEBUG] [Application] [16] Auto-completion setup for ModelAccountBox
[2025-07-03 10:01:56.174] [DEBUG] [Application] [16] Auto-completion integration completed
[2025-07-03 10:01:56.206] [INFO] [Application] [16] Modern UI Integration completed successfully
[2025-07-03 10:02:07.890] [INFO] [Application] [16] Step 1: Connect to Jira
[2025-07-03 10:02:07.901] [DEBUG] [Application] [16] Workflow step 'Connection' status: InProgress
[2025-07-03 10:02:07.927] [INFO] [Application] [16] Connecting to https://jeragm.atlassian.net...
[2025-07-03 10:02:08.771] [DEBUG] [Application] [16] Workflow step 'Connection' status: Completed
[2025-07-03 10:02:08.780] [INFO] [Application] [16] Successfully connected to Jira
[2025-07-03 10:02:12.910] [INFO] [Application] [16] Step 2: Fetch Ticket Data
[2025-07-03 10:02:12.950] [DEBUG] [Application] [16] Workflow step 'FetchData' status: InProgress
[2025-07-03 10:02:12.973] [INFO] [Application] [16] Fetching data for ticket IT-46455...
[2025-07-03 10:02:14.832] [DEBUG] [Application] [16] Cached ticket: IT-46455
[2025-07-03 10:02:14.863] [DEBUG] [Application] [16] Issue object properties: customfield_10553, customfield_10058, customfield_10305, customfield_10149, customfield_10723, customfield_10928, customfield_10194, customfield_10363, customfield_10750, customfield_10147, customfield_11280, customfield_10729, customfield_10153, customfield_10369, customfield_10145, customfield_10783, customfield_10727, duedate, customfield_10151, customfield_10367, customfield_10370, customfield_10486, customfield_10373, worklog, customfield_10157, customfield_10371, customfield_10008, customfield_10155, customfield_10519, customfield_10202, customfield_10306, customfield_10380, customfield_10062, ID, customfield_10375, customfield_10072, customfield_10069, customfield_10685, customfield_11251, customfield_10200, customfield_10187, customfield_11113, customfield_11245, components, customfield_11253, Transition, customfield_11250, customfield_11247, resolutiondate, customfield_10101, LastViewed, customfield_10385, customfield_11249, customfield_11279, customfield_10344, customfield_10209, customfield_10189, customfield_11212, customfield_10651, Project, customfield_10211, customfield_10105, customfield_11214, customfield_10012, customfield_10213, customfield_10395, customfield_10116, customfield_10172, timeoriginalestimate, customfield_10203, customfield_10174, customfield_10025, customfield_10336, customfield_10065, customfield_10684, customfield_10223, customfield_10027, customfield_10686, fixVersions, customfield_10126, customfield_10182, customfield_10356, progress, customfield_10128, customfield_10042, customfield_10180, customfield_10562, customfield_10196, customfield_10858, customfield_10629, customfield_10261, customfield_10004, customfield_10049, customfield_10627, aggregatetimeestimate, customfield_10663, customfield_10185, customfield_10237, customfield_10557, customfield_10081, timespent, security, customfield_10057, aggregateprogress, aggregatetimespent, customfield_10314, environment, customfield_10722, customfield_10195, customfield_10239, customfield_10368, customfield_10229, customfield_10037, issuelinks, customfield_10348, customfield_10056, customfield_10131, customfield_10199, customfield_10054, Status, customfield_10618, customfield_10751, customfield_10161, Comment, Key, customfield_11254, customfield_10063, customfield_10060, customfield_10066, customfield_10226, customfield_10068, labels, customfield_10520, customfield_10489, statuscategorychangedate, customfield_10160, customfield_10222, customfield_10354, customfield_11252, customfield_10159, customfield_10393, customfield_11248, RestUrl, resolution, customfield_10384, customfield_10216, customfield_10379, customfield_10208, customfield_10386, customfield_10341, customfield_10132, customfield_10559, customfield_10171, customfield_10013, customfield_10100, customfield_10212, customfield_10204, customfield_10725, customfield_10117, customfield_10173, customfield_10011, customfield_10075, customfield_10398, customfield_10115, customfield_10347, customfield_10337, customfield_10321, customfield_10177, customfield_10215, customfield_10224, customfield_10352, Assignee, customfield_10130, customfield_10717, customfield_10129, customfield_10181, customfield_10000, customfield_10453, customfield_11181, customfield_10178, customfield_10029, customfield_10276, watches, customfield_10560, customfield_10133, customfield_10120, customfield_10102, customfield_10634, customfield_10249, customfield_10320, customfield_10048, customfield_11278, customfield_10346, customfield_10383, customfield_10184, customfield_11281, customfield_10726, customfield_10030, priority, customfield_10235, customfield_10720, customfield_10134, customfield_10047, customfield_10190, customfield_10721, customfield_10045, customfield_10448, customfield_10238, customfield_10882, customfield_10250, customfield_10198, customfield_10118, customfield_10619, customfield_10197, customfield_10251, customfield_10318, customfield_10055, Summary, Description, customfield_10447, customfield_10304, customfield_10255, customfield_10059, customfield_10053, customfield_10148, customfield_10382, customfield_10193, customfield_10488, attachment, customfield_10728, customfield_10225, customfield_10158, workratio, customfield_10372, customfield_10206, customfield_10156, customfield_10664, customfield_10201, customfield_10154, customfield_10521, statusCategory, Created, customfield_10064, issuetype, customfield_10254, customfield_10374, customfield_10342, customfield_10317, customfield_10554, customfield_10227, customfield_10210, customfield_10214, customfield_10391, customfield_11246, customfield_10585, customfield_10070, customfield_11182, customfield_10630, customfield_10135, aggregatetimeoriginalestimate, customfield_10631, customfield_10010, customfield_10061, customfield_11215, timeestimate, customfield_10662, customfield_11213, customfield_10556, customfield_10028, customfield_10635, customfield_10396, customfield_10275, customfield_10719, timetracking, customfield_10175, customfield_10024, Creator, customfield_10381, customfield_10023, Updated, customfield_11179, customfield_10127, customfield_10183, customfield_11122, customfield_10561, customfield_10043, customfield_10322, customfield_10046, customfield_10192, customfield_10041, Reporter, customfield_10044, customfield_10628, customfield_10335, customfield_10040, subtasks, customfield_10191, customfield_10626, customfield_10343, versions, customfield_10186, HttpUrl, customfield_10319, customfield_10303, customfield_10001, customfield_10555, customfield_10252, customfield_10050
[2025-07-03 10:02:14.881] [DEBUG] [Application] [16] Found Comment property with 4 comments
[2025-07-03 10:02:14.898] [INFO] [Application] [16] Starting data extraction from ticket IT-46455
[2025-07-03 10:02:14.937] [DEBUG] [Application] [16] Extracted data - Name: 'Herberto De Souza', Job: 'LNG Structured Trading Intern', Dept: 'LNG', Model: 'NA', Location: ''
[2025-07-03 10:02:14.951] [DEBUG] [Application] [16] DIAGNOSTIC: About to assign firstName = 'Herberto'
[2025-07-03 10:02:15.070] [DEBUG] [Application] [16] DIAGNOSTIC: FirstNameBox.Text = 'Herberto'
[2025-07-03 10:02:15.079] [DEBUG] [Application] [16] DIAGNOSTIC: About to assign lastName = 'De Souza'
[2025-07-03 10:02:15.150] [DEBUG] [Application] [16] DIAGNOSTIC: LastNameBox.Text = 'De Souza'
[2025-07-03 10:02:15.161] [DEBUG] [Application] [16] DIAGNOSTIC: About to assign jobTitle = 'LNG Structured Trading Intern'
[2025-07-03 10:02:15.229] [DEBUG] [Application] [16] DIAGNOSTIC: JobTitleBox.Text = 'LNG Structured Trading Intern'
[2025-07-03 10:02:15.239] [DEBUG] [Application] [16] DIAGNOSTIC: About to assign department = 'LNG'
[2025-07-03 10:02:15.307] [DEBUG] [Application] [16] DIAGNOSTIC: DepartmentBox.Text = 'LNG'
[2025-07-03 10:02:15.319] [DEBUG] [Application] [16] DIAGNOSTIC: About to assign modelAccount = 'NA'
[2025-07-03 10:02:15.386] [DEBUG] [Application] [16] DIAGNOSTIC: ModelAccountBox.Text = 'NA'
[2025-07-03 10:02:15.406] [DEBUG] [Application] [16] Removed spaces from names: 'Herberto De Souza' -> 'Herberto DeSouza'
[2025-07-03 10:02:15.422] [DEBUG] [Application] [16] Generating SAM account name for Herberto DeSouza
[2025-07-03 10:02:15.432] [DEBUG] [Application] [16] DIAGNOSTIC: proposedSam just generated = 'desouh'
[2025-07-03 10:02:15.439] [DEBUG] [Application] [16] Checking if SAM account name 'desouh' exists
[2025-07-03 10:02:15.448] [INFO] [Application] [16] Generated SAM account name: desouh (simulation mode - could not verify uniqueness)
[2025-07-03 10:02:15.460] [DEBUG] [Application] [16] DIAGNOSTIC: About to return proposedSam = 'desouh'
[2025-07-03 10:02:15.470] [DEBUG] [Application] [16] DIAGNOSTIC: returnValue after cast = 'desouh'
[2025-07-03 10:02:15.479] [DEBUG] [Application] [16] DIAGNOSTIC: generatedSam = '41 42 43 44 45 46 47 desouh'
[2025-07-03 10:02:15.486] [DEBUG] [Application] [16] DIAGNOSTIC: samForGui = '41 42 43 44 45 46 47 desouh'
[2025-07-03 10:02:15.493] [DEBUG] [Application] [16] DIAGNOSTIC: samForGui length = 27
[2025-07-03 10:02:15.557] [DEBUG] [Application] [16] DIAGNOSTIC: Original value = '41 42 43 44 45 46 47 desouh'
[2025-07-03 10:02:15.566] [DEBUG] [Application] [16] DIAGNOSTIC: SamBox.Text after assignment = '41 42 43 44 45 46 47 desouh'
[2025-07-03 10:02:15.574] [DEBUG] [Application] [16] DIAGNOSTIC: Values match = True
[2025-07-03 10:02:15.586] [DEBUG] [Application] [16] Removed spaces from names for UPN: 'Herberto De Souza' -> 'Herberto DeSouza'
[2025-07-03 10:02:15.596] [DEBUG] [Application] [16] DIAGNOSTIC: baseUpn = '54 herberto.desouza'
[2025-07-03 10:02:15.611] [DEBUG] [Application] [16] DIAGNOSTIC: emailAddress = '54 <EMAIL>'
[2025-07-03 10:02:15.668] [DEBUG] [Application] [16] DIAGNOSTIC: UpnBox.Text after assignment = '54 <EMAIL>'
[2025-07-03 10:02:15.745] [DEBUG] [Application] [16] DIAGNOSTIC: EmailBox.Text after assignment = '54 <EMAIL>'
[2025-07-03 10:02:15.755] [DEBUG] [Application] [16] Generated account details from Jira ticket successfully
[2025-07-03 10:02:15.772] [INFO] [Application] [16] Data extraction completed. Please verify the populated fields.
[2025-07-03 10:02:15.782] [DEBUG] [Application] [16] Workflow step 'FetchData' status: Completed
[2025-07-03 10:02:15.789] [INFO] [Application] [16] Step 3: Validate Information
[2025-07-03 10:02:15.803] [DEBUG] [Application] [16] Workflow step 'Validation' status: InProgress
[2025-07-03 10:02:15.814] [INFO] [Application] [16] Successfully fetched and populated data from ticket IT-46455
[2025-07-03 10:09:05.693] [DEBUG] [Testing] [16] Test registered: ConfigurationValidation in suite Unit
[2025-07-03 10:09:05.758] [DEBUG] [Testing] [16] Test registered: ConfigurationValidation in Unit
[2025-07-03 10:09:05.765] [DEBUG] [Testing] [16] Test registered: LoggingSystem in suite Unit
[2025-07-03 10:09:05.767] [DEBUG] [Testing] [16] Test registered: LoggingSystem in Unit
[2025-07-03 10:09:05.767] [DEBUG] [Testing] [16] Test registered: VersionManager in suite Unit
[2025-07-03 10:09:05.767] [DEBUG] [Testing] [16] Test registered: VersionManager in Unit
[2025-07-03 10:09:05.786] [DEBUG] [Testing] [16] Test registered: JiraConnection in suite Integration
[2025-07-03 10:09:05.790] [DEBUG] [Testing] [16] Test registered: JiraConnection in Integration
[2025-07-03 10:09:05.794] [DEBUG] [Testing] [16] Test registered: ActiveDirectoryConnection in suite Integration
[2025-07-03 10:09:05.798] [DEBUG] [Testing] [16] Test registered: ActiveDirectoryConnection in Integration
[2025-07-03 10:09:05.801] [DEBUG] [Testing] [16] Test registered: StartupTime in suite Performance
[2025-07-03 10:09:05.801] [DEBUG] [Testing] [16] Test registered: StartupTime in Performance
[2025-07-03 10:09:05.834] [DEBUG] [Testing] [16] Test registered: MemoryUsage in suite Performance
[2025-07-03 10:09:05.834] [DEBUG] [Testing] [16] Test registered: MemoryUsage in Performance
[2025-07-03 10:09:05.834] [DEBUG] [Testing] [16] Test registered: CredentialHandling in suite Security
[2025-07-03 10:09:05.850] [DEBUG] [Testing] [16] Test registered: CredentialHandling in Security
[2025-07-03 10:09:05.855] [DEBUG] [Testing] [16] Test registered: InputValidation in suite Security
[2025-07-03 10:09:05.861] [DEBUG] [Testing] [16] Test registered: InputValidation in Security
[2025-07-03 10:09:05.866] [DEBUG] [Testing] [16] Test registered: WindowCreation in suite UI
[2025-07-03 10:09:05.867] [DEBUG] [Testing] [16] Test registered: WindowCreation in UI
[2025-07-03 10:09:05.867] [INFO] [Testing] [16] Core tests initialized
[2025-07-03 10:09:05.896] [INFO] [Documentation] [16] Initializing documentation system
[2025-07-03 10:09:05.908] [INFO] [Documentation] [16] Starting documentation generation for: api
[2025-07-03 10:09:05.934] [INFO] [Documentation] [16] Documentation saved: Documentation\API_Reference.html
[2025-07-03 10:09:05.955] [INFO] [Documentation] [16] Documentation saved: Documentation\API_Reference.md
[2025-07-03 10:09:05.967] [INFO] [Documentation] [16] Documentation generation completed for: api
[2025-07-03 10:09:05.967] [INFO] [Documentation] [16] API documentation generated: 2 files
[2025-07-03 10:09:05.967] [INFO] [Documentation] [16] Starting documentation generation for: help
[2025-07-03 10:09:05.990] [INFO] [Documentation] [16] Documentation generation completed for: help
[2025-07-03 10:09:05.994] [INFO] [Documentation] [16] Help system documentation generated
[2025-07-03 10:09:05.998] [INFO] [Documentation] [16] Documentation system initialized
[2025-07-03 10:09:06.068] [INFO] [Application] [16] Initializing enhanced UI components...
[2025-07-03 10:09:06.122] [DEBUG] [Application] [16] Theme applied to controls
[2025-07-03 10:09:06.132] [DEBUG] [Application] [16] Modern theme applied successfully
[2025-07-03 10:09:06.157] [DEBUG] [Application] [16] Field validation initialized
[2025-07-03 10:09:06.168] [DEBUG] [Application] [16] Responsive layout initialized
[2025-07-03 10:09:06.255] [DEBUG] [Application] [16] Workflow step 'Connection' status: Pending
[2025-07-03 10:09:06.293] [INFO] [Application] [16] Enhanced UI initialization completed
[2025-07-03 10:09:06.300] [INFO] [Application] [16] Welcome! Please connect to Jira to begin.
[2025-07-03 10:09:06.325] [INFO] [Application] [16] OnboardingFromJiraGUI started successfully
[2025-07-03 10:09:06.334] [INFO] [Application] [16] [OK] Monitoring system initialized
[2025-07-03 10:09:06.400] [INFO] [Audit] [16] Audit: akinje performed PermissionInit on UserPermissions - Success | Context: SessionId=Unknown; Action=PermissionInit; Timestamp=07/03/2025 10:09:06; Result=Success; User=akinje; Detected=Standard user privileges; Target=UserPermissions; PrivilegeLevel=Standard
[2025-07-03 10:09:06.440] [INFO] [Application] [16] [OK] User permissions initialized for JERAGM\akinje
[2025-07-03 10:09:06.448] [DEBUG] [Application] [16]  Cache cleanup scheduled every 30 minutes
[2025-07-03 10:09:06.456] [INFO] [Application] [16] Applying Modern UI Integration...
[2025-07-03 10:09:06.492] [DEBUG] [Application] [16] Auto-completion setup for LastNameBox
[2025-07-03 10:09:06.499] [DEBUG] [Application] [16] Auto-completion setup for JobTitleBox
[2025-07-03 10:09:06.500] [DEBUG] [Application] [16] Auto-completion setup for FirstNameBox
[2025-07-03 10:09:06.500] [DEBUG] [Application] [16] Auto-completion setup for DepartmentBox
[2025-07-03 10:09:06.520] [DEBUG] [Application] [16] Auto-completion setup for ModelAccountBox
[2025-07-03 10:09:06.527] [DEBUG] [Application] [16] Auto-completion integration completed
[2025-07-03 10:09:06.535] [INFO] [Application] [16] Modern UI Integration completed successfully
[2025-07-03 10:09:24.929] [INFO] [Application] [16] Step 1: Connect to Jira
[2025-07-03 10:09:24.935] [DEBUG] [Application] [16] Workflow step 'Connection' status: InProgress
[2025-07-03 10:09:24.956] [INFO] [Application] [16] Connecting to https://jeragm.atlassian.net...
[2025-07-03 10:09:25.785] [DEBUG] [Application] [16] Workflow step 'Connection' status: Completed
[2025-07-03 10:09:25.800] [INFO] [Application] [16] Successfully connected to Jira
[2025-07-03 10:09:31.528] [INFO] [Application] [16] Step 2: Fetch Ticket Data
[2025-07-03 10:09:31.535] [DEBUG] [Application] [16] Workflow step 'FetchData' status: InProgress
[2025-07-03 10:09:31.551] [INFO] [Application] [16] Fetching data for ticket IT-46455...
[2025-07-03 10:09:32.641] [DEBUG] [Application] [16] Cached ticket: IT-46455
[2025-07-03 10:09:32.652] [DEBUG] [Application] [16] Issue object properties: customfield_10553, customfield_10058, customfield_10305, customfield_10149, customfield_10723, customfield_10928, customfield_10194, customfield_10363, customfield_10750, customfield_10147, customfield_11280, customfield_10729, customfield_10153, customfield_10369, customfield_10145, customfield_10783, customfield_10727, duedate, customfield_10151, customfield_10367, customfield_10370, customfield_10486, customfield_10373, worklog, customfield_10157, customfield_10371, customfield_10008, customfield_10155, customfield_10519, customfield_10202, customfield_10306, customfield_10380, customfield_10062, ID, customfield_10375, customfield_10072, customfield_10069, customfield_10685, customfield_11251, customfield_10200, customfield_10187, customfield_11113, customfield_11245, components, customfield_11253, Transition, customfield_11250, customfield_11247, resolutiondate, customfield_10101, LastViewed, customfield_10385, customfield_11249, customfield_11279, customfield_10344, customfield_10209, customfield_10189, customfield_11212, customfield_10651, Project, customfield_10211, customfield_10105, customfield_11214, customfield_10012, customfield_10213, customfield_10395, customfield_10116, customfield_10172, timeoriginalestimate, customfield_10203, customfield_10174, customfield_10025, customfield_10336, customfield_10065, customfield_10684, customfield_10223, customfield_10027, customfield_10686, fixVersions, customfield_10126, customfield_10182, customfield_10356, progress, customfield_10128, customfield_10042, customfield_10180, customfield_10562, customfield_10196, customfield_10858, customfield_10629, customfield_10261, customfield_10004, customfield_10049, customfield_10627, aggregatetimeestimate, customfield_10663, customfield_10185, customfield_10237, customfield_10557, customfield_10081, timespent, security, customfield_10057, aggregateprogress, aggregatetimespent, customfield_10314, environment, customfield_10722, customfield_10195, customfield_10239, customfield_10368, customfield_10229, customfield_10037, issuelinks, customfield_10348, customfield_10056, customfield_10131, customfield_10199, customfield_10054, Status, customfield_10618, customfield_10751, customfield_10161, Comment, Key, customfield_11254, customfield_10063, customfield_10060, customfield_10066, customfield_10226, customfield_10068, labels, customfield_10520, customfield_10489, statuscategorychangedate, customfield_10160, customfield_10222, customfield_10354, customfield_11252, customfield_10159, customfield_10393, customfield_11248, RestUrl, resolution, customfield_10384, customfield_10216, customfield_10379, customfield_10208, customfield_10386, customfield_10341, customfield_10132, customfield_10559, customfield_10171, customfield_10013, customfield_10100, customfield_10212, customfield_10204, customfield_10725, customfield_10117, customfield_10173, customfield_10011, customfield_10075, customfield_10398, customfield_10115, customfield_10347, customfield_10337, customfield_10321, customfield_10177, customfield_10215, customfield_10224, customfield_10352, Assignee, customfield_10130, customfield_10717, customfield_10129, customfield_10181, customfield_10000, customfield_10453, customfield_11181, customfield_10178, customfield_10029, customfield_10276, watches, customfield_10560, customfield_10133, customfield_10120, customfield_10102, customfield_10634, customfield_10249, customfield_10320, customfield_10048, customfield_11278, customfield_10346, customfield_10383, customfield_10184, customfield_11281, customfield_10726, customfield_10030, priority, customfield_10235, customfield_10720, customfield_10134, customfield_10047, customfield_10190, customfield_10721, customfield_10045, customfield_10448, customfield_10238, customfield_10882, customfield_10250, customfield_10198, customfield_10118, customfield_10619, customfield_10197, customfield_10251, customfield_10318, customfield_10055, Summary, Description, customfield_10447, customfield_10304, customfield_10255, customfield_10059, customfield_10053, customfield_10148, customfield_10382, customfield_10193, customfield_10488, attachment, customfield_10728, customfield_10225, customfield_10158, workratio, customfield_10372, customfield_10206, customfield_10156, customfield_10664, customfield_10201, customfield_10154, customfield_10521, statusCategory, Created, customfield_10064, issuetype, customfield_10254, customfield_10374, customfield_10342, customfield_10317, customfield_10554, customfield_10227, customfield_10210, customfield_10214, customfield_10391, customfield_11246, customfield_10585, customfield_10070, customfield_11182, customfield_10630, customfield_10135, aggregatetimeoriginalestimate, customfield_10631, customfield_10010, customfield_10061, customfield_11215, timeestimate, customfield_10662, customfield_11213, customfield_10556, customfield_10028, customfield_10635, customfield_10396, customfield_10275, customfield_10719, timetracking, customfield_10175, customfield_10024, Creator, customfield_10381, customfield_10023, Updated, customfield_11179, customfield_10127, customfield_10183, customfield_11122, customfield_10561, customfield_10043, customfield_10322, customfield_10046, customfield_10192, customfield_10041, Reporter, customfield_10044, customfield_10628, customfield_10335, customfield_10040, subtasks, customfield_10191, customfield_10626, customfield_10343, versions, customfield_10186, HttpUrl, customfield_10319, customfield_10303, customfield_10001, customfield_10555, customfield_10252, customfield_10050
[2025-07-03 10:09:32.656] [DEBUG] [Application] [16] Found Comment property with 4 comments
[2025-07-03 10:09:32.676] [INFO] [Application] [16] Starting data extraction from ticket IT-46455
[2025-07-03 10:09:32.692] [DEBUG] [Application] [16] Extracted data - Name: 'Herberto De Souza', Job: 'LNG Structured Trading Intern', Dept: 'LNG', Model: 'NA', Location: ''
[2025-07-03 10:09:32.700] [DEBUG] [Application] [16] DIAGNOSTIC: About to assign firstName = 'Herberto'
[2025-07-03 10:09:32.837] [DEBUG] [Application] [16] DIAGNOSTIC: FirstNameBox.Text = 'Herberto'
[2025-07-03 10:09:32.851] [DEBUG] [Application] [16] DIAGNOSTIC: About to assign lastName = 'De Souza'
[2025-07-03 10:09:32.885] [DEBUG] [Application] [16] DIAGNOSTIC: LastNameBox.Text = 'De Souza'
[2025-07-03 10:09:32.885] [DEBUG] [Application] [16] DIAGNOSTIC: About to assign jobTitle = 'LNG Structured Trading Intern'
[2025-07-03 10:09:32.937] [DEBUG] [Application] [16] DIAGNOSTIC: JobTitleBox.Text = 'LNG Structured Trading Intern'
[2025-07-03 10:09:32.946] [DEBUG] [Application] [16] DIAGNOSTIC: About to assign department = 'LNG'
[2025-07-03 10:09:33.002] [DEBUG] [Application] [16] DIAGNOSTIC: DepartmentBox.Text = 'LNG'
[2025-07-03 10:09:33.007] [DEBUG] [Application] [16] DIAGNOSTIC: About to assign modelAccount = 'NA'
[2025-07-03 10:09:33.035] [DEBUG] [Application] [16] DIAGNOSTIC: ModelAccountBox.Text = 'NA'
[2025-07-03 10:09:33.046] [DEBUG] [Application] [16] Removed spaces from names: 'Herberto De Souza' -> 'Herberto DeSouza'
[2025-07-03 10:09:33.056] [DEBUG] [Application] [16] Generating SAM account name for Herberto DeSouza
[2025-07-03 10:09:33.056] [DEBUG] [Application] [16] Checking if SAM account name 'desouh' exists
[2025-07-03 10:09:33.075] [INFO] [Application] [16] Generated SAM account name: desouh (simulation mode - could not verify uniqueness)
[2025-07-03 10:09:33.082] [DEBUG] [Application] [16] DIAGNOSTIC: generatedSam = '41 42 43 44 desouh'
[2025-07-03 10:09:33.085] [DEBUG] [Application] [16] DIAGNOSTIC: samForGui = '41 42 43 44 desouh'
[2025-07-03 10:09:33.085] [DEBUG] [Application] [16] DIAGNOSTIC: samForGui length = 18
[2025-07-03 10:09:33.185] [DEBUG] [Application] [16] DIAGNOSTIC: Original value = '41 42 43 44 desouh'
[2025-07-03 10:09:33.194] [DEBUG] [Application] [16] DIAGNOSTIC: SamBox.Text after assignment = '41 42 43 44 desouh'
[2025-07-03 10:09:33.204] [DEBUG] [Application] [16] DIAGNOSTIC: Values match = True
[2025-07-03 10:09:33.214] [DEBUG] [Application] [16] Removed spaces from names for UPN: 'Herberto De Souza' -> 'Herberto DeSouza'
[2025-07-03 10:09:33.221] [DEBUG] [Application] [16] DIAGNOSTIC: baseUpn = '51 herberto.desouza'
[2025-07-03 10:09:33.228] [DEBUG] [Application] [16] DIAGNOSTIC: emailAddress = '51 <EMAIL>'
[2025-07-03 10:09:33.286] [DEBUG] [Application] [16] DIAGNOSTIC: UpnBox.Text after assignment = '51 <EMAIL>'
[2025-07-03 10:09:33.368] [DEBUG] [Application] [16] DIAGNOSTIC: EmailBox.Text after assignment = '51 <EMAIL>'
[2025-07-03 10:09:33.376] [DEBUG] [Application] [16] Generated account details from Jira ticket successfully
[2025-07-03 10:09:33.401] [INFO] [Application] [16] Data extraction completed. Please verify the populated fields.
[2025-07-03 10:09:33.418] [DEBUG] [Application] [16] Workflow step 'FetchData' status: Completed
[2025-07-03 10:09:33.426] [INFO] [Application] [16] Step 3: Validate Information
[2025-07-03 10:09:33.432] [DEBUG] [Application] [16] Workflow step 'Validation' status: InProgress
[2025-07-03 10:09:33.435] [INFO] [Application] [16] Successfully fetched and populated data from ticket IT-46455
[2025-07-03 10:14:39.441] [DEBUG] [Testing] [16] Test registered: ConfigurationValidation in suite Unit
[2025-07-03 10:14:39.495] [DEBUG] [Testing] [16] Test registered: ConfigurationValidation in Unit
[2025-07-03 10:14:39.495] [DEBUG] [Testing] [16] Test registered: LoggingSystem in suite Unit
[2025-07-03 10:14:39.511] [DEBUG] [Testing] [16] Test registered: LoggingSystem in Unit
[2025-07-03 10:14:39.511] [DEBUG] [Testing] [16] Test registered: VersionManager in suite Unit
[2025-07-03 10:14:39.511] [DEBUG] [Testing] [16] Test registered: VersionManager in Unit
[2025-07-03 10:14:39.526] [DEBUG] [Testing] [16] Test registered: JiraConnection in suite Integration
[2025-07-03 10:14:39.526] [DEBUG] [Testing] [16] Test registered: JiraConnection in Integration
[2025-07-03 10:14:39.526] [DEBUG] [Testing] [16] Test registered: ActiveDirectoryConnection in suite Integration
[2025-07-03 10:14:39.526] [DEBUG] [Testing] [16] Test registered: ActiveDirectoryConnection in Integration
[2025-07-03 10:14:39.546] [DEBUG] [Testing] [16] Test registered: StartupTime in suite Performance
[2025-07-03 10:14:39.546] [DEBUG] [Testing] [16] Test registered: StartupTime in Performance
[2025-07-03 10:14:39.594] [DEBUG] [Testing] [16] Test registered: MemoryUsage in suite Performance
[2025-07-03 10:14:39.594] [DEBUG] [Testing] [16] Test registered: MemoryUsage in Performance
[2025-07-03 10:14:39.615] [DEBUG] [Testing] [16] Test registered: CredentialHandling in suite Security
[2025-07-03 10:14:39.619] [DEBUG] [Testing] [16] Test registered: CredentialHandling in Security
[2025-07-03 10:14:39.623] [DEBUG] [Testing] [16] Test registered: InputValidation in suite Security
[2025-07-03 10:14:39.630] [DEBUG] [Testing] [16] Test registered: InputValidation in Security
[2025-07-03 10:14:39.634] [DEBUG] [Testing] [16] Test registered: WindowCreation in suite UI
[2025-07-03 10:14:39.641] [DEBUG] [Testing] [16] Test registered: WindowCreation in UI
[2025-07-03 10:14:39.647] [INFO] [Testing] [16] Core tests initialized
[2025-07-03 10:14:39.663] [INFO] [Documentation] [16] Initializing documentation system
[2025-07-03 10:14:39.663] [INFO] [Documentation] [16] Starting documentation generation for: api
[2025-07-03 10:14:39.713] [INFO] [Documentation] [16] Documentation saved: Documentation\API_Reference.html
[2025-07-03 10:14:39.713] [INFO] [Documentation] [16] Documentation saved: Documentation\API_Reference.md
[2025-07-03 10:14:39.730] [INFO] [Documentation] [16] Documentation generation completed for: api
[2025-07-03 10:14:39.730] [INFO] [Documentation] [16] API documentation generated: 2 files
[2025-07-03 10:14:39.746] [INFO] [Documentation] [16] Starting documentation generation for: help
[2025-07-03 10:14:39.746] [INFO] [Documentation] [16] Documentation generation completed for: help
[2025-07-03 10:14:39.761] [INFO] [Documentation] [16] Help system documentation generated
[2025-07-03 10:14:39.763] [INFO] [Documentation] [16] Documentation system initialized
[2025-07-03 10:14:39.832] [INFO] [Application] [16] Initializing enhanced UI components...
[2025-07-03 10:14:39.864] [DEBUG] [Application] [16] Theme applied to controls
[2025-07-03 10:14:39.879] [DEBUG] [Application] [16] Modern theme applied successfully
[2025-07-03 10:14:39.895] [DEBUG] [Application] [16] Field validation initialized
[2025-07-03 10:14:39.911] [DEBUG] [Application] [16] Responsive layout initialized
[2025-07-03 10:14:39.958] [DEBUG] [Application] [16] Workflow step 'Connection' status: Pending
[2025-07-03 10:14:39.982] [INFO] [Application] [16] Enhanced UI initialization completed
[2025-07-03 10:14:39.996] [INFO] [Application] [16] Welcome! Please connect to Jira to begin.
[2025-07-03 10:14:39.996] [INFO] [Application] [16] OnboardingFromJiraGUI started successfully
[2025-07-03 10:14:40.012] [INFO] [Application] [16] [OK] Monitoring system initialized
[2025-07-03 10:14:40.082] [INFO] [Audit] [16] Audit: akinje performed PermissionInit on UserPermissions - Success | Context: SessionId=Unknown; Action=PermissionInit; Timestamp=07/03/2025 10:14:40; Result=Success; User=akinje; Detected=Standard user privileges; Target=UserPermissions; PrivilegeLevel=Standard
[2025-07-03 10:14:40.100] [INFO] [Application] [16] [OK] User permissions initialized for JERAGM\akinje
[2025-07-03 10:14:40.113] [DEBUG] [Application] [16]  Cache cleanup scheduled every 30 minutes
[2025-07-03 10:14:40.119] [INFO] [Application] [16] Applying Modern UI Integration...
[2025-07-03 10:14:40.154] [DEBUG] [Application] [16] Auto-completion setup for LastNameBox
[2025-07-03 10:14:40.159] [DEBUG] [Application] [16] Auto-completion setup for JobTitleBox
[2025-07-03 10:14:40.165] [DEBUG] [Application] [16] Auto-completion setup for FirstNameBox
[2025-07-03 10:14:40.170] [DEBUG] [Application] [16] Auto-completion setup for DepartmentBox
[2025-07-03 10:14:40.175] [DEBUG] [Application] [16] Auto-completion setup for ModelAccountBox
[2025-07-03 10:14:40.181] [DEBUG] [Application] [16] Auto-completion integration completed
[2025-07-03 10:14:40.202] [INFO] [Application] [16] Modern UI Integration completed successfully
[2025-07-03 10:14:58.497] [INFO] [Application] [16] Step 1: Connect to Jira
[2025-07-03 10:14:58.497] [DEBUG] [Application] [16] Workflow step 'Connection' status: InProgress
[2025-07-03 10:14:58.533] [INFO] [Application] [16] Connecting to https://jeragm.atlassian.net...
[2025-07-03 10:14:59.361] [DEBUG] [Application] [16] Workflow step 'Connection' status: Completed
[2025-07-03 10:14:59.364] [INFO] [Application] [16] Successfully connected to Jira
[2025-07-03 10:15:17.547] [INFO] [Application] [16] Step 2: Fetch Ticket Data
[2025-07-03 10:15:17.563] [DEBUG] [Application] [16] Workflow step 'FetchData' status: InProgress
[2025-07-03 10:15:17.563] [INFO] [Application] [16] Fetching data for ticket IT-46455...
[2025-07-03 10:15:18.619] [DEBUG] [Application] [16] Cached ticket: IT-46455
[2025-07-03 10:15:18.627] [DEBUG] [Application] [16] Issue object properties: customfield_10553, customfield_10058, customfield_10305, customfield_10149, customfield_10723, customfield_10928, customfield_10194, customfield_10363, customfield_10750, customfield_10147, customfield_11280, customfield_10729, customfield_10153, customfield_10369, customfield_10145, customfield_10783, customfield_10727, duedate, customfield_10151, customfield_10367, customfield_10370, customfield_10486, customfield_10373, worklog, customfield_10157, customfield_10371, customfield_10008, customfield_10155, customfield_10519, customfield_10202, customfield_10306, customfield_10380, customfield_10062, ID, customfield_10375, customfield_10072, customfield_10069, customfield_10685, customfield_11251, customfield_10200, customfield_10187, customfield_11113, customfield_11245, components, customfield_11253, Transition, customfield_11250, customfield_11247, resolutiondate, customfield_10101, LastViewed, customfield_10385, customfield_11249, customfield_11279, customfield_10344, customfield_10209, customfield_10189, customfield_11212, customfield_10651, Project, customfield_10211, customfield_10105, customfield_11214, customfield_10012, customfield_10213, customfield_10395, customfield_10116, customfield_10172, timeoriginalestimate, customfield_10203, customfield_10174, customfield_10025, customfield_10336, customfield_10065, customfield_10684, customfield_10223, customfield_10027, customfield_10686, fixVersions, customfield_10126, customfield_10182, customfield_10356, progress, customfield_10128, customfield_10042, customfield_10180, customfield_10562, customfield_10196, customfield_10858, customfield_10629, customfield_10261, customfield_10004, customfield_10049, customfield_10627, aggregatetimeestimate, customfield_10663, customfield_10185, customfield_10237, customfield_10557, customfield_10081, timespent, security, customfield_10057, aggregateprogress, aggregatetimespent, customfield_10314, environment, customfield_10722, customfield_10195, customfield_10239, customfield_10368, customfield_10229, customfield_10037, issuelinks, customfield_10348, customfield_10056, customfield_10131, customfield_10199, customfield_10054, Status, customfield_10618, customfield_10751, customfield_10161, Comment, Key, customfield_11254, customfield_10063, customfield_10060, customfield_10066, customfield_10226, customfield_10068, labels, customfield_10520, customfield_10489, statuscategorychangedate, customfield_10160, customfield_10222, customfield_10354, customfield_11252, customfield_10159, customfield_10393, customfield_11248, RestUrl, resolution, customfield_10384, customfield_10216, customfield_10379, customfield_10208, customfield_10386, customfield_10341, customfield_10132, customfield_10559, customfield_10171, customfield_10013, customfield_10100, customfield_10212, customfield_10204, customfield_10725, customfield_10117, customfield_10173, customfield_10011, customfield_10075, customfield_10398, customfield_10115, customfield_10347, customfield_10337, customfield_10321, customfield_10177, customfield_10215, customfield_10224, customfield_10352, Assignee, customfield_10130, customfield_10717, customfield_10129, customfield_10181, customfield_10000, customfield_10453, customfield_11181, customfield_10178, customfield_10029, customfield_10276, watches, customfield_10560, customfield_10133, customfield_10120, customfield_10102, customfield_10634, customfield_10249, customfield_10320, customfield_10048, customfield_11278, customfield_10346, customfield_10383, customfield_10184, customfield_11281, customfield_10726, customfield_10030, priority, customfield_10235, customfield_10720, customfield_10134, customfield_10047, customfield_10190, customfield_10721, customfield_10045, customfield_10448, customfield_10238, customfield_10882, customfield_10250, customfield_10198, customfield_10118, customfield_10619, customfield_10197, customfield_10251, customfield_10318, customfield_10055, Summary, Description, customfield_10447, customfield_10304, customfield_10255, customfield_10059, customfield_10053, customfield_10148, customfield_10382, customfield_10193, customfield_10488, attachment, customfield_10728, customfield_10225, customfield_10158, workratio, customfield_10372, customfield_10206, customfield_10156, customfield_10664, customfield_10201, customfield_10154, customfield_10521, statusCategory, Created, customfield_10064, issuetype, customfield_10254, customfield_10374, customfield_10342, customfield_10317, customfield_10554, customfield_10227, customfield_10210, customfield_10214, customfield_10391, customfield_11246, customfield_10585, customfield_10070, customfield_11182, customfield_10630, customfield_10135, aggregatetimeoriginalestimate, customfield_10631, customfield_10010, customfield_10061, customfield_11215, timeestimate, customfield_10662, customfield_11213, customfield_10556, customfield_10028, customfield_10635, customfield_10396, customfield_10275, customfield_10719, timetracking, customfield_10175, customfield_10024, Creator, customfield_10381, customfield_10023, Updated, customfield_11179, customfield_10127, customfield_10183, customfield_11122, customfield_10561, customfield_10043, customfield_10322, customfield_10046, customfield_10192, customfield_10041, Reporter, customfield_10044, customfield_10628, customfield_10335, customfield_10040, subtasks, customfield_10191, customfield_10626, customfield_10343, versions, customfield_10186, HttpUrl, customfield_10319, customfield_10303, customfield_10001, customfield_10555, customfield_10252, customfield_10050
[2025-07-03 10:15:18.637] [DEBUG] [Application] [16] Found Comment property with 4 comments
[2025-07-03 10:15:18.654] [INFO] [Application] [16] Starting data extraction from ticket IT-46455
[2025-07-03 10:15:18.670] [DEBUG] [Application] [16] Extracted data - Name: 'Herberto De Souza', Job: 'LNG Structured Trading Intern', Dept: 'LNG', Model: 'NA', Location: ''
[2025-07-03 10:15:18.678] [DEBUG] [Application] [16] DIAGNOSTIC: About to assign firstName = 'Herberto'
[2025-07-03 10:15:18.785] [DEBUG] [Application] [16] DIAGNOSTIC: FirstNameBox.Text = 'Herberto'
[2025-07-03 10:15:18.785] [DEBUG] [Application] [16] DIAGNOSTIC: About to assign lastName = 'De Souza'
[2025-07-03 10:15:18.848] [DEBUG] [Application] [16] DIAGNOSTIC: LastNameBox.Text = 'De Souza'
[2025-07-03 10:15:18.848] [DEBUG] [Application] [16] DIAGNOSTIC: About to assign jobTitle = 'LNG Structured Trading Intern'
[2025-07-03 10:15:18.926] [DEBUG] [Application] [16] DIAGNOSTIC: JobTitleBox.Text = 'LNG Structured Trading Intern'
[2025-07-03 10:15:18.926] [DEBUG] [Application] [16] DIAGNOSTIC: About to assign department = 'LNG'
[2025-07-03 10:15:19.000] [DEBUG] [Application] [16] DIAGNOSTIC: DepartmentBox.Text = 'LNG'
[2025-07-03 10:15:19.008] [DEBUG] [Application] [16] DIAGNOSTIC: About to assign modelAccount = 'NA'
[2025-07-03 10:15:19.086] [DEBUG] [Application] [16] DIAGNOSTIC: ModelAccountBox.Text = 'NA'
[2025-07-03 10:15:19.101] [DEBUG] [Application] [16] Removed spaces from names: 'Herberto De Souza' -> 'Herberto DeSouza'
[2025-07-03 10:15:19.101] [DEBUG] [Application] [16] Generating SAM account name for Herberto DeSouza
[2025-07-03 10:15:19.117] [DEBUG] [Application] [16] Checking if SAM account name 'desouh' exists
[2025-07-03 10:15:19.132] [INFO] [Application] [16] Generated SAM account name: desouh (simulation mode - could not verify uniqueness)
[2025-07-03 10:15:19.139] [DEBUG] [Application] [16] DIAGNOSTIC: generatedSam = 'desouh'
[2025-07-03 10:15:19.139] [DEBUG] [Application] [16] DIAGNOSTIC: samForGui = 'desouh'
[2025-07-03 10:15:19.139] [DEBUG] [Application] [16] DIAGNOSTIC: samForGui length = 6
[2025-07-03 10:15:19.208] [DEBUG] [Application] [16] DIAGNOSTIC: Original value = 'desouh'
[2025-07-03 10:15:19.213] [DEBUG] [Application] [16] DIAGNOSTIC: SamBox.Text after assignment = 'desouh'
[2025-07-03 10:15:19.221] [DEBUG] [Application] [16] DIAGNOSTIC: Values match = True
[2025-07-03 10:15:19.228] [DEBUG] [Application] [16] Removed spaces from names for UPN: 'Herberto De Souza' -> 'Herberto DeSouza'
[2025-07-03 10:15:19.232] [DEBUG] [Application] [16] DIAGNOSTIC: baseUpn = 'herberto.desouza'
[2025-07-03 10:15:19.239] [DEBUG] [Application] [16] DIAGNOSTIC: emailAddress = '<EMAIL>'
[2025-07-03 10:15:19.302] [DEBUG] [Application] [16] DIAGNOSTIC: UpnBox.Text after assignment = '<EMAIL>'
[2025-07-03 10:15:19.366] [DEBUG] [Application] [16] DIAGNOSTIC: EmailBox.Text after assignment = '<EMAIL>'
[2025-07-03 10:15:19.381] [DEBUG] [Application] [16] Generated account details from Jira ticket successfully
[2025-07-03 10:15:19.397] [INFO] [Application] [16] Data extraction completed. Please verify the populated fields.
[2025-07-03 10:15:19.397] [DEBUG] [Application] [16] Workflow step 'FetchData' status: Completed
[2025-07-03 10:15:19.413] [INFO] [Application] [16] Step 3: Validate Information
[2025-07-03 10:15:19.413] [DEBUG] [Application] [16] Workflow step 'Validation' status: InProgress
[2025-07-03 10:15:19.428] [INFO] [Application] [16] Successfully fetched and populated data from ticket IT-46455
[2025-07-03 10:28:09.290] [DEBUG] [Testing] [18] Test registered: ConfigurationValidation in suite Unit
[2025-07-03 10:28:09.373] [DEBUG] [Testing] [18] Test registered: ConfigurationValidation in Unit
[2025-07-03 10:28:09.373] [DEBUG] [Testing] [18] Test registered: LoggingSystem in suite Unit
[2025-07-03 10:28:09.390] [DEBUG] [Testing] [18] Test registered: LoggingSystem in Unit
[2025-07-03 10:28:09.390] [DEBUG] [Testing] [18] Test registered: VersionManager in suite Unit
[2025-07-03 10:28:09.405] [DEBUG] [Testing] [18] Test registered: VersionManager in Unit
[2025-07-03 10:28:09.407] [DEBUG] [Testing] [18] Test registered: JiraConnection in suite Integration
[2025-07-03 10:28:09.407] [DEBUG] [Testing] [18] Test registered: JiraConnection in Integration
[2025-07-03 10:28:09.423] [DEBUG] [Testing] [18] Test registered: ActiveDirectoryConnection in suite Integration
[2025-07-03 10:28:09.423] [DEBUG] [Testing] [18] Test registered: ActiveDirectoryConnection in Integration
[2025-07-03 10:28:09.440] [DEBUG] [Testing] [18] Test registered: StartupTime in suite Performance
[2025-07-03 10:28:09.440] [DEBUG] [Testing] [18] Test registered: StartupTime in Performance
[2025-07-03 10:28:09.473] [DEBUG] [Testing] [18] Test registered: MemoryUsage in suite Performance
[2025-07-03 10:28:09.473] [DEBUG] [Testing] [18] Test registered: MemoryUsage in Performance
[2025-07-03 10:28:09.490] [DEBUG] [Testing] [18] Test registered: CredentialHandling in suite Security
[2025-07-03 10:28:09.490] [DEBUG] [Testing] [18] Test registered: CredentialHandling in Security
[2025-07-03 10:28:09.490] [DEBUG] [Testing] [18] Test registered: InputValidation in suite Security
[2025-07-03 10:28:09.515] [DEBUG] [Testing] [18] Test registered: InputValidation in Security
[2025-07-03 10:28:09.522] [DEBUG] [Testing] [18] Test registered: WindowCreation in suite UI
[2025-07-03 10:28:09.529] [DEBUG] [Testing] [18] Test registered: WindowCreation in UI
[2025-07-03 10:28:09.536] [INFO] [Testing] [18] Core tests initialized
[2025-07-03 10:28:09.558] [INFO] [Documentation] [18] Initializing documentation system
[2025-07-03 10:28:09.573] [INFO] [Documentation] [18] Starting documentation generation for: api
[2025-07-03 10:28:09.606] [INFO] [Documentation] [18] Documentation saved: Documentation\API_Reference.html
[2025-07-03 10:28:09.640] [INFO] [Documentation] [18] Documentation saved: Documentation\API_Reference.md
[2025-07-03 10:28:09.658] [INFO] [Documentation] [18] Documentation generation completed for: api
[2025-07-03 10:28:09.675] [INFO] [Documentation] [18] API documentation generated: 2 files
[2025-07-03 10:28:09.687] [INFO] [Documentation] [18] Starting documentation generation for: help
[2025-07-03 10:28:09.705] [INFO] [Documentation] [18] Documentation generation completed for: help
[2025-07-03 10:28:09.715] [INFO] [Documentation] [18] Help system documentation generated
[2025-07-03 10:28:09.722] [INFO] [Documentation] [18] Documentation system initialized
[2025-07-03 10:28:09.823] [INFO] [Application] [18] Initializing enhanced UI components...
[2025-07-03 10:28:09.895] [DEBUG] [Application] [18] Theme applied to controls
[2025-07-03 10:28:09.907] [DEBUG] [Application] [18] Modern theme applied successfully
[2025-07-03 10:28:09.940] [DEBUG] [Application] [18] Field validation initialized
[2025-07-03 10:28:09.973] [DEBUG] [Application] [18] Responsive layout initialized
[2025-07-03 10:28:10.073] [DEBUG] [Application] [18] Workflow step 'Connection' status: Pending
[2025-07-03 10:28:10.123] [INFO] [Application] [18] Enhanced UI initialization completed
[2025-07-03 10:28:10.156] [INFO] [Application] [18] Welcome! Please connect to Jira to begin.
[2025-07-03 10:28:10.159] [INFO] [Application] [18] OnboardingFromJiraGUI started successfully
[2025-07-03 10:28:10.187] [INFO] [Application] [18] [OK] Monitoring system initialized
[2025-07-03 10:28:10.290] [INFO] [Audit] [18] Audit: akinje performed PermissionInit on UserPermissions - Success | Context: SessionId=Unknown; Action=PermissionInit; Timestamp=07/03/2025 10:28:10; Result=Success; User=akinje; Detected=Standard user privileges; Target=UserPermissions; PrivilegeLevel=Standard
[2025-07-03 10:28:10.323] [INFO] [Application] [18] [OK] User permissions initialized for JERAGM\akinje
[2025-07-03 10:28:10.340] [DEBUG] [Application] [18]  Cache cleanup scheduled every 30 minutes
[2025-07-03 10:28:10.359] [INFO] [Application] [18] Applying Modern UI Integration...
[2025-07-03 10:28:10.459] [DEBUG] [Application] [18] Auto-completion setup for LastNameBox
[2025-07-03 10:28:10.483] [DEBUG] [Application] [18] Auto-completion setup for JobTitleBox
[2025-07-03 10:28:10.504] [DEBUG] [Application] [18] Auto-completion setup for FirstNameBox
[2025-07-03 10:28:10.513] [DEBUG] [Application] [18] Auto-completion setup for DepartmentBox
[2025-07-03 10:28:10.538] [DEBUG] [Application] [18] Auto-completion setup for ModelAccountBox
[2025-07-03 10:28:10.550] [DEBUG] [Application] [18] Auto-completion integration completed
[2025-07-03 10:28:10.586] [INFO] [Application] [18] Modern UI Integration completed successfully
[2025-07-03 10:28:10.595] [INFO] [Interface] [18] Starting traditional tabbed interface mode
[2025-07-03 15:52:13.847] [DEBUG] [Testing] [16] Test registered: ConfigurationValidation in suite Unit
[2025-07-03 15:52:13.977] [DEBUG] [Testing] [16] Test registered: ConfigurationValidation in Unit
[2025-07-03 15:52:13.985] [DEBUG] [Testing] [16] Test registered: LoggingSystem in suite Unit
[2025-07-03 15:52:14.001] [DEBUG] [Testing] [16] Test registered: LoggingSystem in Unit
[2025-07-03 15:52:26.043] [DEBUG] [Testing] [15] Test registered: ConfigurationValidation in suite Unit
[2025-07-03 15:52:26.117] [DEBUG] [Testing] [15] Test registered: ConfigurationValidation in Unit
[2025-07-03 15:52:26.124] [DEBUG] [Testing] [15] Test registered: LoggingSystem in suite Unit
[2025-07-03 15:52:26.127] [DEBUG] [Testing] [15] Test registered: LoggingSystem in Unit
[2025-07-03 15:52:26.127] [DEBUG] [Testing] [15] Test registered: VersionManager in suite Unit
[2025-07-03 15:52:26.127] [DEBUG] [Testing] [15] Test registered: VersionManager in Unit
[2025-07-03 15:52:26.144] [DEBUG] [Testing] [15] Test registered: JiraConnection in suite Integration
[2025-07-03 15:52:26.144] [DEBUG] [Testing] [15] Test registered: JiraConnection in Integration
[2025-07-03 15:52:26.144] [DEBUG] [Testing] [15] Test registered: ActiveDirectoryConnection in suite Integration
[2025-07-03 15:52:26.160] [DEBUG] [Testing] [15] Test registered: ActiveDirectoryConnection in Integration
[2025-07-03 15:52:26.161] [DEBUG] [Testing] [15] Test registered: StartupTime in suite Performance
[2025-07-03 15:52:26.198] [DEBUG] [Testing] [15] Test registered: StartupTime in Performance
[2025-07-03 15:52:26.204] [DEBUG] [Testing] [15] Test registered: MemoryUsage in suite Performance
[2025-07-03 15:52:26.206] [DEBUG] [Testing] [15] Test registered: MemoryUsage in Performance
[2025-07-03 15:52:26.211] [DEBUG] [Testing] [15] Test registered: CredentialHandling in suite Security
[2025-07-03 15:52:26.211] [DEBUG] [Testing] [15] Test registered: CredentialHandling in Security
[2025-07-03 15:52:26.226] [DEBUG] [Testing] [15] Test registered: InputValidation in suite Security
[2025-07-03 15:52:26.227] [DEBUG] [Testing] [15] Test registered: InputValidation in Security
[2025-07-03 15:52:26.227] [DEBUG] [Testing] [15] Test registered: WindowCreation in suite UI
[2025-07-03 15:52:26.243] [DEBUG] [Testing] [15] Test registered: WindowCreation in UI
[2025-07-03 15:52:26.243] [INFO] [Testing] [15] Core tests initialized
[2025-07-03 15:52:26.259] [INFO] [Documentation] [15] Initializing documentation system
[2025-07-03 15:52:26.288] [INFO] [Documentation] [15] Starting documentation generation for: api
[2025-07-03 15:52:26.342] [ERROR] [Documentation] [15] Failed to save documentation: Documentation\API_Reference.html - The process cannot access the file 'C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\Utilities\Documentation\API_Reference.html' because it is being used by another process. | Stack: Write-StructuredLog:8805 -> :11041 -> :10781 -> :10725 -> New-Documentation:11228 -> New-APIDocumentation:11234 -> Initialize-DocumentationSystem:11609 -> OnboardingFromJiraGUI.ps1:11623 -> Launch-OnboardingGUI.ps1:18
[2025-07-03 15:52:26.394] [ERROR] [Documentation] [15] Documentation generation failed: The process cannot access the file 'C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\Utilities\Documentation\API_Reference.html' because it is being used by another process. | Stack: Write-StructuredLog:8805 -> :10748 -> New-Documentation:11228 -> New-APIDocumentation:11234 -> Initialize-DocumentationSystem:11609 -> OnboardingFromJiraGUI.ps1:11623 -> Launch-OnboardingGUI.ps1:18
[2025-07-03 15:52:26.410] [INFO] [Documentation] [15] Documentation generation completed for: api
[2025-07-03 15:52:26.410] [INFO] [Documentation] [15] API documentation generated: 0 files
[2025-07-03 15:52:26.426] [INFO] [Documentation] [15] Starting documentation generation for: help
[2025-07-03 15:52:26.428] [INFO] [Documentation] [15] Documentation generation completed for: help
[2025-07-03 15:52:26.444] [INFO] [Documentation] [15] Help system documentation generated
[2025-07-03 15:52:26.444] [INFO] [Documentation] [15] Documentation system initialized
[2025-07-03 15:52:26.543] [INFO] [Application] [15] Initializing enhanced UI components...
[2025-07-03 15:52:26.609] [DEBUG] [Application] [15] Theme applied to controls
[2025-07-03 15:52:26.625] [DEBUG] [Application] [15] Modern theme applied successfully
[2025-07-03 15:52:26.660] [DEBUG] [Application] [15] Field validation initialized
[2025-07-03 15:52:26.686] [DEBUG] [Application] [15] Responsive layout initialized
[2025-07-03 15:52:26.777] [DEBUG] [Application] [15] Workflow step 'Connection' status: Pending
[2025-07-03 15:52:26.827] [INFO] [Application] [15] Enhanced UI initialization completed
[2025-07-03 15:52:26.844] [INFO] [Application] [15] Welcome! Please connect to Jira to begin.
[2025-07-03 15:52:26.861] [INFO] [Application] [15] OnboardingFromJiraGUI started successfully
[2025-07-03 15:52:26.873] [INFO] [Application] [15] [OK] Monitoring system initialized
[2025-07-03 15:52:26.961] [INFO] [Audit] [15] Audit: akinje performed PermissionInit on UserPermissions - Success | Context: SessionId=Unknown; Action=PermissionInit; Timestamp=07/03/2025 15:52:26; Result=Success; User=akinje; Detected=Standard user privileges; Target=UserPermissions; PrivilegeLevel=Standard
[2025-07-03 15:52:26.994] [INFO] [Application] [15] [OK] User permissions initialized for JERAGM\akinje
[2025-07-03 15:52:26.994] [DEBUG] [Application] [15]  Cache cleanup scheduled every 30 minutes
[2025-07-03 15:52:27.010] [INFO] [Application] [15] Applying Modern UI Integration...
[2025-07-03 15:52:27.057] [DEBUG] [Application] [15] Auto-completion setup for LastNameBox
[2025-07-03 15:52:27.064] [DEBUG] [Application] [15] Auto-completion setup for JobTitleBox
[2025-07-03 15:52:27.073] [DEBUG] [Application] [15] Auto-completion setup for FirstNameBox
[2025-07-03 15:52:27.077] [DEBUG] [Application] [15] Auto-completion setup for DepartmentBox
[2025-07-03 15:52:27.077] [DEBUG] [Application] [15] Auto-completion setup for ModelAccountBox
[2025-07-03 15:52:27.088] [DEBUG] [Application] [15] Auto-completion integration completed
[2025-07-03 15:52:27.110] [INFO] [Application] [15] Modern UI Integration completed successfully
[2025-07-03 15:52:27.110] [INFO] [Interface] [15] Starting traditional tabbed interface mode
[2025-07-03 15:52:54.176] [INFO] [Application] [15] Step 1: Connect to Jira
[2025-07-03 15:52:54.189] [DEBUG] [Application] [15] Workflow step 'Connection' status: InProgress
[2025-07-03 15:52:54.208] [INFO] [Application] [15] Connecting to https://jeragm.atlassian.net...
[2025-07-03 15:52:55.076] [DEBUG] [Application] [15] Workflow step 'Connection' status: Completed
[2025-07-03 15:52:55.084] [INFO] [Application] [15] Successfully connected to Jira
[2025-07-03 15:53:17.792] [INFO] [Application] [15] Step 2: Fetch Ticket Data
[2025-07-03 15:53:17.801] [DEBUG] [Application] [15] Workflow step 'FetchData' status: InProgress
[2025-07-03 15:53:17.808] [INFO] [Application] [15] Fetching data for ticket IT-46455...
[2025-07-03 15:53:18.860] [DEBUG] [Application] [15] Cached ticket: IT-46455
[2025-07-03 15:53:18.874] [DEBUG] [Application] [15] Issue object properties: customfield_10553, customfield_10058, customfield_10305, customfield_10149, customfield_10723, customfield_10928, customfield_10194, customfield_10363, customfield_10750, customfield_10147, customfield_11280, customfield_10729, customfield_10153, customfield_10369, customfield_10145, customfield_10783, customfield_10727, duedate, customfield_10151, customfield_10367, customfield_10370, customfield_10486, customfield_10373, worklog, customfield_10157, customfield_10371, customfield_10008, customfield_10155, customfield_10519, customfield_10202, customfield_10306, customfield_10380, customfield_10062, ID, customfield_10375, customfield_10072, customfield_10069, customfield_10685, customfield_11251, customfield_10200, customfield_10187, customfield_11113, customfield_11245, components, customfield_11253, Transition, customfield_11250, customfield_11247, resolutiondate, customfield_10101, LastViewed, customfield_10385, customfield_11249, customfield_11279, customfield_10344, customfield_10209, customfield_10189, customfield_11212, customfield_10651, Project, customfield_10211, customfield_10105, customfield_11214, customfield_10012, customfield_10213, customfield_10395, customfield_10116, customfield_10172, timeoriginalestimate, customfield_10203, customfield_10174, customfield_10025, customfield_10336, customfield_10065, customfield_10684, customfield_10223, customfield_10027, customfield_10686, fixVersions, customfield_10126, customfield_10182, customfield_10356, progress, customfield_10128, customfield_10042, customfield_10180, customfield_10562, customfield_10196, customfield_10858, customfield_10629, customfield_10261, customfield_10004, customfield_10049, customfield_10627, aggregatetimeestimate, customfield_10663, customfield_10185, customfield_10237, customfield_10557, customfield_10081, timespent, security, customfield_10057, aggregateprogress, aggregatetimespent, customfield_10314, environment, customfield_10722, customfield_10195, customfield_10239, customfield_10368, customfield_10229, customfield_10037, issuelinks, customfield_10348, customfield_10056, customfield_10131, customfield_10199, customfield_10054, Status, customfield_10618, customfield_10751, customfield_10161, Comment, Key, customfield_11254, customfield_10063, customfield_10060, customfield_10066, customfield_10226, customfield_10068, labels, customfield_10520, customfield_10489, statuscategorychangedate, customfield_10160, customfield_10222, customfield_10354, customfield_11252, customfield_10159, customfield_10393, customfield_11248, RestUrl, resolution, customfield_10384, customfield_10216, customfield_10379, customfield_10208, customfield_10386, customfield_10341, customfield_10132, customfield_10559, customfield_10171, customfield_10013, customfield_10100, customfield_10212, customfield_10204, customfield_10725, customfield_10117, customfield_10173, customfield_10011, customfield_10075, customfield_10398, customfield_10115, customfield_10347, customfield_10337, customfield_10321, customfield_10177, customfield_10215, customfield_10224, customfield_10352, Assignee, customfield_10130, customfield_10717, customfield_10129, customfield_10181, customfield_10000, customfield_10453, customfield_11181, customfield_10178, customfield_10029, customfield_10276, watches, customfield_10560, customfield_10133, customfield_10120, customfield_10102, customfield_10634, customfield_10249, customfield_10320, customfield_10048, customfield_11278, customfield_10346, customfield_10383, customfield_10184, customfield_11281, customfield_10726, customfield_10030, priority, customfield_10235, customfield_10720, customfield_10134, customfield_10047, customfield_10190, customfield_10721, customfield_10045, customfield_10448, customfield_10238, customfield_10882, customfield_10250, customfield_10198, customfield_10118, customfield_10619, customfield_10197, customfield_10251, customfield_10318, customfield_10055, Summary, Description, customfield_10447, customfield_10304, customfield_10255, customfield_10059, customfield_10053, customfield_10148, customfield_10382, customfield_10193, customfield_10488, attachment, customfield_10728, customfield_10225, customfield_10158, workratio, customfield_10372, customfield_10206, customfield_10156, customfield_10664, customfield_10201, customfield_10154, customfield_10521, statusCategory, Created, customfield_10064, issuetype, customfield_10254, customfield_10374, customfield_10342, customfield_10317, customfield_10554, customfield_10227, customfield_10210, customfield_10214, customfield_10391, customfield_11246, customfield_10585, customfield_10070, customfield_11182, customfield_10630, customfield_10135, aggregatetimeoriginalestimate, customfield_10631, customfield_10010, customfield_10061, customfield_11215, timeestimate, customfield_10662, customfield_11213, customfield_10556, customfield_10028, customfield_10635, customfield_10396, customfield_10275, customfield_10719, timetracking, customfield_10175, customfield_10024, Creator, customfield_10381, customfield_10023, Updated, customfield_11179, customfield_10127, customfield_10183, customfield_11122, customfield_10561, customfield_10043, customfield_10322, customfield_10046, customfield_10192, customfield_10041, Reporter, customfield_10044, customfield_10628, customfield_10335, customfield_10040, subtasks, customfield_10191, customfield_10626, customfield_10343, versions, customfield_10186, HttpUrl, customfield_10319, customfield_10303, customfield_10001, customfield_10555, customfield_10252, customfield_10050
[2025-07-03 15:53:18.879] [DEBUG] [Application] [15] Found Comment property with 4 comments
[2025-07-03 15:53:18.879] [INFO] [Application] [15] Starting data extraction from ticket IT-46455
[2025-07-03 15:53:18.894] [DEBUG] [Application] [15] Extracted data - Name: 'Herberto De Souza', Job: 'LNG Structured Trading Intern', Dept: 'LNG', Model: 'NA', Location: ''
[2025-07-03 15:53:18.910] [DEBUG] [Application] [15] DIAGNOSTIC: About to assign firstName = 'Herberto'
[2025-07-03 15:53:19.028] [DEBUG] [Application] [15] DIAGNOSTIC: FirstNameBox.Text = 'Herberto'
[2025-07-03 15:53:19.040] [DEBUG] [Application] [15] DIAGNOSTIC: About to assign lastName = 'De Souza'
[2025-07-03 15:53:19.110] [DEBUG] [Application] [15] DIAGNOSTIC: LastNameBox.Text = 'De Souza'
[2025-07-03 15:53:19.110] [DEBUG] [Application] [15] DIAGNOSTIC: About to assign jobTitle = 'LNG Structured Trading Intern'
[2025-07-03 15:53:19.177] [DEBUG] [Application] [15] DIAGNOSTIC: JobTitleBox.Text = 'LNG Structured Trading Intern'
[2025-07-03 15:53:19.180] [DEBUG] [Application] [15] DIAGNOSTIC: About to assign department = 'LNG'
[2025-07-03 15:53:19.227] [DEBUG] [Application] [15] DIAGNOSTIC: DepartmentBox.Text = 'LNG'
[2025-07-03 15:53:19.227] [DEBUG] [Application] [15] DIAGNOSTIC: About to assign modelAccount = 'NA'
[2025-07-03 15:53:19.310] [DEBUG] [Application] [15] DIAGNOSTIC: ModelAccountBox.Text = 'NA'
[2025-07-03 15:53:19.310] [DEBUG] [Application] [15] Removed spaces from names: 'Herberto De Souza' -> 'Herberto DeSouza'
[2025-07-03 15:53:19.326] [DEBUG] [Application] [15] Generating SAM account name for Herberto DeSouza
[2025-07-03 15:53:19.342] [DEBUG] [Application] [15] Checking if SAM account name 'desouh' exists
[2025-07-03 15:53:19.343] [INFO] [Application] [15] Generated SAM account name: desouh (simulation mode - could not verify uniqueness)
[2025-07-03 15:53:19.343] [DEBUG] [Application] [15] DIAGNOSTIC: generatedSam = 'desouh'
[2025-07-03 15:53:19.360] [DEBUG] [Application] [15] DIAGNOSTIC: samForGui = 'desouh'
[2025-07-03 15:53:19.369] [DEBUG] [Application] [15] DIAGNOSTIC: samForGui length = 6
[2025-07-03 15:53:19.460] [DEBUG] [Application] [15] DIAGNOSTIC: Original value = 'desouh'
[2025-07-03 15:53:19.460] [DEBUG] [Application] [15] DIAGNOSTIC: SamBox.Text after assignment = 'desouh'
[2025-07-03 15:53:19.460] [DEBUG] [Application] [15] DIAGNOSTIC: Values match = True
[2025-07-03 15:53:19.480] [DEBUG] [Application] [15] Removed spaces from names for UPN: 'Herberto De Souza' -> 'Herberto DeSouza'
[2025-07-03 15:53:19.480] [DEBUG] [Application] [15] DIAGNOSTIC: baseUpn = 'herberto.desouza'
[2025-07-03 15:53:19.493] [DEBUG] [Application] [15] DIAGNOSTIC: emailAddress = '<EMAIL>'
[2025-07-03 15:53:19.558] [DEBUG] [Application] [15] DIAGNOSTIC: UpnBox.Text after assignment = '<EMAIL>'
[2025-07-03 15:53:19.643] [DEBUG] [Application] [15] DIAGNOSTIC: EmailBox.Text after assignment = '<EMAIL>'
[2025-07-03 15:53:19.643] [DEBUG] [Application] [15] Generated account details from Jira ticket successfully
[2025-07-03 15:53:19.681] [INFO] [Application] [15] Data extraction completed. Please verify the populated fields.
[2025-07-03 15:53:19.681] [DEBUG] [Application] [15] Workflow step 'FetchData' status: Completed
[2025-07-03 15:53:19.681] [INFO] [Application] [15] Step 3: Validate Information
[2025-07-03 15:53:19.696] [DEBUG] [Application] [15] Workflow step 'Validation' status: InProgress
[2025-07-03 15:53:19.709] [INFO] [Application] [15] Successfully fetched and populated data from ticket IT-46455
[2025-07-04 09:16:06.696] [DEBUG] [Testing] [15] Test registered: ConfigurationValidation in suite Unit
[2025-07-04 09:16:06.851] [DEBUG] [Testing] [15] Test registered: ConfigurationValidation in Unit
[2025-07-04 09:16:06.868] [DEBUG] [Testing] [15] Test registered: LoggingSystem in suite Unit
[2025-07-04 09:16:06.876] [DEBUG] [Testing] [15] Test registered: LoggingSystem in Unit
[2025-07-04 09:16:06.888] [DEBUG] [Testing] [15] Test registered: VersionManager in suite Unit
[2025-07-04 09:16:06.901] [DEBUG] [Testing] [15] Test registered: VersionManager in Unit
[2025-07-04 09:16:06.917] [DEBUG] [Testing] [15] Test registered: JiraConnection in suite Integration
[2025-07-04 09:16:06.917] [DEBUG] [Testing] [15] Test registered: JiraConnection in Integration
[2025-07-04 09:16:06.933] [DEBUG] [Testing] [15] Test registered: ActiveDirectoryConnection in suite Integration
[2025-07-04 09:16:07.004] [DEBUG] [Testing] [15] Test registered: ActiveDirectoryConnection in Integration
[2025-07-04 09:16:07.019] [DEBUG] [Testing] [15] Test registered: StartupTime in suite Performance
[2025-07-04 09:16:07.035] [DEBUG] [Testing] [15] Test registered: StartupTime in Performance
[2025-07-04 09:16:07.052] [DEBUG] [Testing] [15] Test registered: MemoryUsage in suite Performance
[2025-07-04 09:16:07.060] [DEBUG] [Testing] [15] Test registered: MemoryUsage in Performance
[2025-07-04 09:16:07.068] [DEBUG] [Testing] [15] Test registered: CredentialHandling in suite Security
[2025-07-04 09:16:07.084] [DEBUG] [Testing] [15] Test registered: CredentialHandling in Security
[2025-07-04 09:16:07.097] [DEBUG] [Testing] [15] Test registered: InputValidation in suite Security
[2025-07-04 09:16:07.114] [DEBUG] [Testing] [15] Test registered: InputValidation in Security
[2025-07-04 09:16:07.114] [DEBUG] [Testing] [15] Test registered: WindowCreation in suite UI
[2025-07-04 09:16:07.129] [DEBUG] [Testing] [15] Test registered: WindowCreation in UI
[2025-07-04 09:16:07.145] [INFO] [Testing] [15] Core tests initialized
[2025-07-04 09:16:07.188] [INFO] [Documentation] [15] Initializing documentation system
[2025-07-04 09:16:07.204] [INFO] [Documentation] [15] Starting documentation generation for: api
[2025-07-04 09:16:07.288] [INFO] [Documentation] [15] Documentation saved: Documentation\API_Reference.html
[2025-07-04 09:16:07.335] [INFO] [Documentation] [15] Documentation saved: Documentation\API_Reference.md
[2025-07-04 09:16:07.365] [INFO] [Documentation] [15] Documentation generation completed for: api
[2025-07-04 09:16:07.389] [INFO] [Documentation] [15] API documentation generated: 2 files
[2025-07-04 09:16:07.410] [INFO] [Documentation] [15] Starting documentation generation for: help
[2025-07-04 09:16:07.434] [INFO] [Documentation] [15] Documentation generation completed for: help
[2025-07-04 09:16:07.449] [INFO] [Documentation] [15] Help system documentation generated
[2025-07-04 09:16:07.465] [INFO] [Documentation] [15] Documentation system initialized
[2025-07-04 09:16:07.664] [INFO] [Application] [15] Initializing enhanced UI components...
[2025-07-04 09:16:07.761] [DEBUG] [Application] [15] Theme applied to controls
[2025-07-04 09:16:07.805] [DEBUG] [Application] [15] Modern theme applied successfully
[2025-07-04 09:16:07.868] [DEBUG] [Application] [15] Field validation initialized
[2025-07-04 09:16:07.927] [DEBUG] [Application] [15] Responsive layout initialized
[2025-07-04 09:16:08.128] [DEBUG] [Application] [15] Workflow step 'Connection' status: Pending
[2025-07-04 09:16:08.208] [INFO] [Application] [15] Enhanced UI initialization completed
[2025-07-04 09:16:08.269] [INFO] [Application] [15] Welcome! Please connect to Jira to begin.
[2025-07-04 09:16:08.393] [INFO] [Application] [15] OnboardingFromJiraGUI started successfully
[2025-07-04 09:16:08.424] [INFO] [Application] [15] [OK] Monitoring system initialized
[2025-07-04 09:16:08.469] [INFO] [Audit] [15] Audit: akinje performed PermissionInit on UserPermissions - Success | Context: SessionId=Unknown; Action=PermissionInit; Timestamp=07/04/2025 09:16:08; Result=Success; User=akinje; Detected=Standard user privileges; Target=UserPermissions; PrivilegeLevel=Standard
[2025-07-04 09:16:08.547] [INFO] [Application] [15] [OK] User permissions initialized for JERAGM\akinje
[2025-07-04 09:16:08.567] [DEBUG] [Application] [15]  Cache cleanup scheduled every 30 minutes
[2025-07-04 09:16:08.598] [INFO] [Application] [15] Applying Modern UI Integration...
[2025-07-04 09:16:08.667] [DEBUG] [Application] [15] Auto-completion setup for LastNameBox
[2025-07-04 09:16:08.683] [DEBUG] [Application] [15] Auto-completion setup for JobTitleBox
[2025-07-04 09:16:08.714] [DEBUG] [Application] [15] Auto-completion setup for FirstNameBox
[2025-07-04 09:16:08.752] [DEBUG] [Application] [15] Auto-completion setup for DepartmentBox
[2025-07-04 09:16:08.767] [DEBUG] [Application] [15] Auto-completion setup for ModelAccountBox
[2025-07-04 09:16:08.794] [DEBUG] [Application] [15] Auto-completion integration completed
[2025-07-04 09:16:08.852] [INFO] [Application] [15] Modern UI Integration completed successfully
[2025-07-04 09:16:08.852] [INFO] [Interface] [15] Starting traditional tabbed interface mode
[2025-07-04 09:48:26.410] [INFO] [Application] [15] Step 1: Connect to Jira
[2025-07-04 09:48:26.426] [DEBUG] [Application] [15] Workflow step 'Connection' status: InProgress
[2025-07-04 09:48:26.482] [INFO] [Application] [15] Connecting to https://jeragm.atlassian.net...
[2025-07-04 09:48:27.987] [DEBUG] [Application] [15] Workflow step 'Connection' status: Completed
[2025-07-04 09:48:28.022] [INFO] [Application] [15] Successfully connected to Jira
[2025-07-04 09:58:56.866] [INFO] [Application] [15] Step 2: Fetch Ticket Data
[2025-07-04 09:58:56.897] [DEBUG] [Application] [15] Workflow step 'FetchData' status: InProgress
[2025-07-04 09:58:56.921] [INFO] [Application] [15] Fetching data for ticket IT-46455...
[2025-07-04 09:58:58.880] [DEBUG] [Application] [15] Cached ticket: IT-46455
[2025-07-04 09:58:58.914] [DEBUG] [Application] [15] Issue object properties: customfield_10553, customfield_10058, customfield_10305, customfield_10149, customfield_10723, customfield_10928, customfield_10194, customfield_10363, customfield_10750, customfield_10147, customfield_11280, customfield_10729, customfield_10153, customfield_10369, customfield_10145, customfield_10783, customfield_10727, duedate, customfield_10151, customfield_10367, customfield_10370, customfield_10486, customfield_10373, worklog, customfield_10157, customfield_10371, customfield_10008, customfield_10155, customfield_10519, customfield_10202, customfield_10306, customfield_10380, customfield_10062, ID, customfield_10375, customfield_10072, customfield_10069, customfield_10685, customfield_11251, customfield_10200, customfield_10187, customfield_11113, customfield_11245, components, customfield_11253, Transition, customfield_11250, customfield_11247, resolutiondate, customfield_10101, LastViewed, customfield_10385, customfield_11249, customfield_11279, customfield_10344, customfield_10209, customfield_10189, customfield_11212, customfield_10651, Project, customfield_10211, customfield_10105, customfield_11214, customfield_10012, customfield_10213, customfield_10395, customfield_10116, customfield_10172, timeoriginalestimate, customfield_10203, customfield_10174, customfield_10025, customfield_10336, customfield_10065, customfield_10684, customfield_10223, customfield_10027, customfield_10686, fixVersions, customfield_10126, customfield_10182, customfield_10356, progress, customfield_10128, customfield_10042, customfield_10180, customfield_10562, customfield_10196, customfield_10858, customfield_10629, customfield_10261, customfield_10004, customfield_10049, customfield_10627, aggregatetimeestimate, customfield_10663, customfield_10185, customfield_10237, customfield_10557, customfield_10081, timespent, security, customfield_10057, aggregateprogress, aggregatetimespent, customfield_10314, environment, customfield_10722, customfield_10195, customfield_10239, customfield_10368, customfield_10229, customfield_10037, issuelinks, customfield_10348, customfield_10056, customfield_10131, customfield_10199, customfield_10054, Status, customfield_10618, customfield_10751, customfield_10161, Comment, Key, customfield_11254, customfield_10063, customfield_10060, customfield_10066, customfield_10226, customfield_10068, labels, customfield_10520, customfield_10489, statuscategorychangedate, customfield_10160, customfield_10222, customfield_10354, customfield_11252, customfield_10159, customfield_10393, customfield_11248, RestUrl, resolution, customfield_10384, customfield_10216, customfield_10379, customfield_10208, customfield_10386, customfield_10341, customfield_10132, customfield_10559, customfield_10171, customfield_10013, customfield_10100, customfield_10212, customfield_10204, customfield_10725, customfield_10117, customfield_10173, customfield_10011, customfield_10075, customfield_10398, customfield_10115, customfield_10347, customfield_10337, customfield_10321, customfield_10177, customfield_10215, customfield_10224, customfield_10352, Assignee, customfield_10130, customfield_10717, customfield_10129, customfield_10181, customfield_10000, customfield_10453, customfield_11181, customfield_10178, customfield_10029, customfield_10276, watches, customfield_10560, customfield_10133, customfield_10120, customfield_10102, customfield_10634, customfield_10249, customfield_10320, customfield_10048, customfield_11278, customfield_10346, customfield_10383, customfield_10184, customfield_11281, customfield_10726, customfield_10030, priority, customfield_10235, customfield_10720, customfield_10134, customfield_10047, customfield_10190, customfield_10721, customfield_10045, customfield_10448, customfield_10238, customfield_10882, customfield_10250, customfield_10198, customfield_10118, customfield_10619, customfield_10197, customfield_10251, customfield_10318, customfield_10055, Summary, Description, customfield_10447, customfield_10304, customfield_10255, customfield_10059, customfield_10053, customfield_10148, customfield_10382, customfield_10193, customfield_10488, attachment, customfield_10728, customfield_10225, customfield_10158, workratio, customfield_10372, customfield_10206, customfield_10156, customfield_10664, customfield_10201, customfield_10154, customfield_10521, statusCategory, Created, customfield_10064, issuetype, customfield_10254, customfield_10374, customfield_10342, customfield_10317, customfield_10554, customfield_10227, customfield_10210, customfield_10214, customfield_10391, customfield_11246, customfield_10585, customfield_10070, customfield_11182, customfield_10630, customfield_10135, aggregatetimeoriginalestimate, customfield_10631, customfield_10010, customfield_10061, customfield_11215, timeestimate, customfield_10662, customfield_11213, customfield_10556, customfield_10028, customfield_10635, customfield_10396, customfield_10275, customfield_10719, timetracking, customfield_10175, customfield_10024, Creator, customfield_10381, customfield_10023, Updated, customfield_11179, customfield_10127, customfield_10183, customfield_11122, customfield_10561, customfield_10043, customfield_10322, customfield_10046, customfield_10192, customfield_10041, Reporter, customfield_10044, customfield_10628, customfield_10335, customfield_10040, subtasks, customfield_10191, customfield_10626, customfield_10343, versions, customfield_10186, HttpUrl, customfield_10319, customfield_10303, customfield_10001, customfield_10555, customfield_10252, customfield_10050
[2025-07-04 09:58:58.949] [DEBUG] [Application] [15] Found Comment property with 4 comments
[2025-07-04 09:58:58.965] [INFO] [Application] [15] Starting data extraction from ticket IT-46455
[2025-07-04 09:58:59.021] [DEBUG] [Application] [15] Extracted data - Name: 'Herberto De Souza', Job: 'LNG Structured Trading Intern', Dept: 'LNG', Model: 'NA', Location: ''
[2025-07-04 09:58:59.041] [DEBUG] [Application] [15] DIAGNOSTIC: About to assign firstName = 'Herberto'
[2025-07-04 09:58:59.282] [DEBUG] [Application] [15] DIAGNOSTIC: FirstNameBox.Text = 'Herberto'
[2025-07-04 09:58:59.313] [DEBUG] [Application] [15] DIAGNOSTIC: About to assign lastName = 'De Souza'
[2025-07-04 09:58:59.389] [DEBUG] [Application] [15] DIAGNOSTIC: LastNameBox.Text = 'De Souza'
[2025-07-04 09:58:59.408] [DEBUG] [Application] [15] DIAGNOSTIC: About to assign jobTitle = 'LNG Structured Trading Intern'
[2025-07-04 09:58:59.486] [DEBUG] [Application] [15] DIAGNOSTIC: JobTitleBox.Text = 'LNG Structured Trading Intern'
[2025-07-04 09:58:59.506] [DEBUG] [Application] [15] DIAGNOSTIC: About to assign department = 'LNG'
[2025-07-04 09:58:59.578] [DEBUG] [Application] [15] DIAGNOSTIC: DepartmentBox.Text = 'LNG'
[2025-07-04 09:58:59.597] [DEBUG] [Application] [15] DIAGNOSTIC: About to assign modelAccount = 'NA'
[2025-07-04 09:58:59.673] [DEBUG] [Application] [15] DIAGNOSTIC: ModelAccountBox.Text = 'NA'
[2025-07-04 09:58:59.732] [DEBUG] [Application] [15] Removed spaces from names: 'Herberto De Souza' -> 'Herberto DeSouza'
[2025-07-04 09:58:59.759] [DEBUG] [Application] [15] Generating SAM account name for Herberto DeSouza
[2025-07-04 09:58:59.778] [DEBUG] [Application] [15] Checking if SAM account name 'desouh' exists
[2025-07-04 09:58:59.807] [INFO] [Application] [15] Generated SAM account name: desouh (simulation mode - could not verify uniqueness)
[2025-07-04 09:58:59.822] [DEBUG] [Application] [15] DIAGNOSTIC: generatedSam = 'desouh'
[2025-07-04 09:58:59.834] [DEBUG] [Application] [15] DIAGNOSTIC: samForGui = 'desouh'
[2025-07-04 09:58:59.847] [DEBUG] [Application] [15] DIAGNOSTIC: samForGui length = 6
[2025-07-04 09:59:00.000] [DEBUG] [Application] [15] DIAGNOSTIC: Original value = 'desouh'
[2025-07-04 09:59:00.014] [DEBUG] [Application] [15] DIAGNOSTIC: SamBox.Text after assignment = 'desouh'
[2025-07-04 09:59:00.039] [DEBUG] [Application] [15] DIAGNOSTIC: Values match = True
[2025-07-04 09:59:00.068] [DEBUG] [Application] [15] Removed spaces from names for UPN: 'Herberto De Souza' -> 'Herberto DeSouza'
[2025-07-04 09:59:00.111] [DEBUG] [Application] [15] DIAGNOSTIC: baseUpn = 'herberto.desouza'
[2025-07-04 09:59:00.137] [DEBUG] [Application] [15] DIAGNOSTIC: emailAddress = '<EMAIL>'
[2025-07-04 09:59:00.215] [DEBUG] [Application] [15] DIAGNOSTIC: UpnBox.Text after assignment = '<EMAIL>'
[2025-07-04 09:59:00.340] [DEBUG] [Application] [15] DIAGNOSTIC: EmailBox.Text after assignment = '<EMAIL>'
[2025-07-04 09:59:00.362] [DEBUG] [Application] [15] Generated account details from Jira ticket successfully
[2025-07-04 09:59:00.431] [INFO] [Application] [15] Data extraction completed. Please verify the populated fields.
[2025-07-04 09:59:00.453] [DEBUG] [Application] [15] Workflow step 'FetchData' status: Completed
[2025-07-04 09:59:00.472] [INFO] [Application] [15] Step 3: Validate Information
[2025-07-04 09:59:00.485] [DEBUG] [Application] [15] Workflow step 'Validation' status: InProgress
[2025-07-04 09:59:00.506] [INFO] [Application] [15] Successfully fetched and populated data from ticket IT-46455
[2025-07-04 09:59:30.285] [DEBUG] [Application] [15] Workflow step 'Validation' status: Completed
[2025-07-04 09:59:30.308] [INFO] [Application] [15] Field validation completed successfully
[2025-07-04 09:59:56.116] [INFO] [Application] [15] Step 2: Fetch Ticket Data
[2025-07-04 09:59:56.134] [DEBUG] [Application] [15] Workflow step 'FetchData' status: InProgress
[2025-07-04 09:59:56.170] [INFO] [Application] [15] Fetching data for ticket TESTIT-49342...
[2025-07-04 09:59:58.031] [DEBUG] [Application] [15] Cached ticket: TESTIT-49342
[2025-07-04 09:59:58.076] [DEBUG] [Application] [15] Issue object properties: customfield_10553, customfield_10058, customfield_10305, customfield_10149, customfield_10723, customfield_10928, customfield_10194, customfield_10363, customfield_10750, customfield_10147, customfield_11280, customfield_10729, customfield_10153, customfield_10369, customfield_10145, customfield_10783, customfield_10727, duedate, customfield_10151, customfield_10367, customfield_10370, customfield_10486, customfield_10373, worklog, customfield_10157, customfield_10371, customfield_10008, customfield_10155, customfield_10519, customfield_10202, customfield_10306, customfield_10380, customfield_10062, ID, customfield_10375, customfield_10072, customfield_10069, customfield_10685, customfield_11251, customfield_10200, customfield_10187, customfield_11113, customfield_11245, components, customfield_11253, Transition, customfield_11250, customfield_11247, resolutiondate, customfield_10101, LastViewed, customfield_10385, customfield_11249, customfield_11279, customfield_10344, customfield_10209, customfield_10189, customfield_11212, customfield_10651, Project, customfield_10211, customfield_10105, customfield_11214, customfield_10012, customfield_10213, customfield_10395, customfield_10684, customfield_10116, customfield_10172, timeoriginalestimate, customfield_10203, customfield_10174, customfield_10025, customfield_10336, customfield_10065, customfield_10330, customfield_10223, customfield_10027, customfield_10686, fixVersions, customfield_10126, customfield_10182, customfield_10356, progress, customfield_10128, customfield_10042, customfield_10180, customfield_10562, customfield_10196, customfield_10858, customfield_10629, customfield_10261, customfield_10004, customfield_10049, customfield_10627, aggregatetimeestimate, customfield_10663, customfield_10185, customfield_10237, customfield_10557, customfield_10081, timespent, security, customfield_10057, aggregateprogress, aggregatetimespent, customfield_10314, environment, customfield_10722, customfield_10195, customfield_10239, customfield_10368, customfield_10229, customfield_10037, issuelinks, customfield_10348, customfield_10056, customfield_10131, customfield_10199, customfield_10054, Status, customfield_10618, customfield_10751, customfield_10161, Comment, Key, customfield_11254, customfield_10063, customfield_10060, customfield_10066, customfield_10226, customfield_10068, labels, customfield_10520, customfield_10489, statuscategorychangedate, customfield_10160, customfield_10222, customfield_10354, customfield_11252, customfield_10159, customfield_10393, customfield_11248, RestUrl, resolution, customfield_10384, customfield_10216, customfield_10379, customfield_10208, customfield_10386, customfield_10341, customfield_10132, customfield_10559, customfield_10171, customfield_10013, customfield_10100, customfield_10212, customfield_10204, customfield_10725, customfield_10117, customfield_10173, customfield_10011, customfield_10075, customfield_10398, customfield_10115, customfield_10347, customfield_10337, customfield_10321, customfield_10177, customfield_10215, customfield_10224, customfield_10352, Assignee, customfield_10130, customfield_10717, customfield_10129, customfield_10181, customfield_10000, customfield_10453, customfield_11181, customfield_10178, customfield_10029, customfield_10276, watches, customfield_10560, customfield_10133, customfield_10120, customfield_10102, customfield_10634, customfield_10249, customfield_10320, customfield_10048, customfield_11278, customfield_10346, customfield_10383, customfield_10184, customfield_11281, customfield_10726, customfield_10030, priority, customfield_10235, customfield_10720, customfield_10134, customfield_10047, customfield_10190, customfield_10721, customfield_10045, customfield_10448, customfield_10238, customfield_10882, customfield_10250, customfield_10198, customfield_10118, customfield_10619, customfield_10197, customfield_10251, customfield_10318, customfield_10055, Summary, Description, customfield_10447, customfield_10304, customfield_10255, customfield_10059, customfield_10053, customfield_10148, customfield_10382, customfield_10193, customfield_10488, attachment, customfield_10728, customfield_10225, customfield_10158, workratio, customfield_10372, customfield_10206, customfield_10156, customfield_10664, customfield_10201, customfield_10154, customfield_10521, statusCategory, Created, customfield_10064, issuetype, customfield_10254, customfield_10374, customfield_10326, customfield_10342, customfield_10317, customfield_10554, customfield_10227, customfield_10210, customfield_10214, customfield_10391, customfield_11246, customfield_10585, customfield_10070, customfield_11182, customfield_10630, customfield_10135, aggregatetimeoriginalestimate, customfield_10631, customfield_10010, customfield_10061, customfield_11215, timeestimate, customfield_10662, customfield_11213, customfield_10556, customfield_10028, customfield_10635, customfield_10396, customfield_10275, customfield_10719, timetracking, customfield_10175, customfield_10024, Creator, customfield_10381, customfield_10023, Updated, customfield_11179, customfield_10127, customfield_10183, customfield_11122, customfield_10561, customfield_10043, customfield_10322, customfield_10046, customfield_10192, customfield_10041, Reporter, customfield_10044, customfield_10628, customfield_10335, customfield_10040, subtasks, customfield_10191, customfield_10626, customfield_10343, versions, customfield_10186, HttpUrl, customfield_10319, customfield_10303, customfield_10001, customfield_10555, customfield_10252, customfield_10050
[2025-07-04 09:59:58.110] [DEBUG] [Application] [15] Found Comment property with 18 comments
[2025-07-04 09:59:58.128] [INFO] [Application] [15] Starting data extraction from ticket TESTIT-49342
[2025-07-04 09:59:58.148] [DEBUG] [Application] [15] Extracted data - Name: 'Test1 Test2', Job: 'Intern', Dept: 'IT', Model: 'test', Location: ''
[2025-07-04 09:59:58.164] [DEBUG] [Application] [15] DIAGNOSTIC: About to assign firstName = 'Test1'
[2025-07-04 09:59:58.185] [DEBUG] [Application] [15] Removed spaces from names: 'Test1 De Souza' -> 'Test1 DeSouza'
[2025-07-04 09:59:58.208] [DEBUG] [Application] [15] Generating SAM account name for Test1 DeSouza
[2025-07-04 09:59:58.222] [DEBUG] [Application] [15] Checking if SAM account name 'desout' exists
[2025-07-04 09:59:58.237] [INFO] [Application] [15] Generated SAM account name: desout (simulation mode - could not verify uniqueness)
[2025-07-04 09:59:58.256] [DEBUG] [Application] [15] DIAGNOSTIC: Event handler setting SAM = 'desout'
[2025-07-04 09:59:58.277] [DEBUG] [Application] [15] DIAGNOSTIC: Event handler SamBox.Text after assignment = 'desout'
[2025-07-04 09:59:58.292] [DEBUG] [Application] [15] Removed spaces from names for UPN: 'Test1 De Souza' -> 'Test1 DeSouza'
[2025-07-04 09:59:58.312] [DEBUG] [Application] [15] Auto-generated account details: SAM='desout', UPN='<EMAIL>'
[2025-07-04 09:59:58.384] [DEBUG] [Application] [15] DIAGNOSTIC: FirstNameBox.Text = 'Test1'
[2025-07-04 09:59:58.397] [DEBUG] [Application] [15] DIAGNOSTIC: About to assign lastName = 'Test2'
[2025-07-04 09:59:58.418] [DEBUG] [Application] [15] Generating SAM account name for Test1 Test2
[2025-07-04 09:59:58.431] [DEBUG] [Application] [15] Checking if SAM account name 'test2t' exists
[2025-07-04 09:59:58.450] [INFO] [Application] [15] Generated SAM account name: test2t (simulation mode - could not verify uniqueness)
[2025-07-04 09:59:58.469] [DEBUG] [Application] [15] DIAGNOSTIC: Event handler setting SAM = 'test2t'
[2025-07-04 09:59:58.556] [DEBUG] [Application] [15] DIAGNOSTIC: Event handler SamBox.Text after assignment = 'test2t'
[2025-07-04 09:59:58.698] [DEBUG] [Application] [15] Auto-generated account details: SAM='test2t', UPN='<EMAIL>'
[2025-07-04 09:59:58.773] [DEBUG] [Application] [15] DIAGNOSTIC: LastNameBox.Text = 'Test2'
[2025-07-04 09:59:58.792] [DEBUG] [Application] [15] DIAGNOSTIC: About to assign jobTitle = 'Intern'
[2025-07-04 09:59:58.869] [DEBUG] [Application] [15] DIAGNOSTIC: JobTitleBox.Text = 'Intern'
[2025-07-04 09:59:58.882] [DEBUG] [Application] [15] DIAGNOSTIC: About to assign department = 'IT'
[2025-07-04 09:59:58.949] [DEBUG] [Application] [15] DIAGNOSTIC: DepartmentBox.Text = 'IT'
[2025-07-04 09:59:58.964] [DEBUG] [Application] [15] DIAGNOSTIC: About to assign modelAccount = 'test'
[2025-07-04 09:59:59.029] [DEBUG] [Application] [15] DIAGNOSTIC: ModelAccountBox.Text = 'test'
[2025-07-04 09:59:59.050] [DEBUG] [Application] [15] Generating SAM account name for Test1 Test2
[2025-07-04 09:59:59.069] [DEBUG] [Application] [15] Checking if SAM account name 'test2t' exists
[2025-07-04 09:59:59.088] [INFO] [Application] [15] Generated SAM account name: test2t (simulation mode - could not verify uniqueness)
[2025-07-04 09:59:59.109] [DEBUG] [Application] [15] DIAGNOSTIC: generatedSam = 'test2t'
[2025-07-04 09:59:59.122] [DEBUG] [Application] [15] DIAGNOSTIC: samForGui = 'test2t'
[2025-07-04 09:59:59.136] [DEBUG] [Application] [15] DIAGNOSTIC: samForGui length = 6
[2025-07-04 09:59:59.150] [DEBUG] [Application] [15] DIAGNOSTIC: Original value = 'test2t'
[2025-07-04 09:59:59.172] [DEBUG] [Application] [15] DIAGNOSTIC: SamBox.Text after assignment = 'test2t'
[2025-07-04 09:59:59.186] [DEBUG] [Application] [15] DIAGNOSTIC: Values match = True
[2025-07-04 09:59:59.201] [DEBUG] [Application] [15] DIAGNOSTIC: baseUpn = 'test1.test2'
[2025-07-04 09:59:59.217] [DEBUG] [Application] [15] DIAGNOSTIC: emailAddress = '<EMAIL>'
[2025-07-04 09:59:59.230] [DEBUG] [Application] [15] DIAGNOSTIC: UpnBox.Text after assignment = '<EMAIL>'
[2025-07-04 09:59:59.247] [DEBUG] [Application] [15] DIAGNOSTIC: EmailBox.Text after assignment = '<EMAIL>'
[2025-07-04 09:59:59.269] [DEBUG] [Application] [15] Generated account details from Jira ticket successfully
[2025-07-04 09:59:59.323] [INFO] [Application] [15] Data extraction completed. Please verify the populated fields.
[2025-07-04 09:59:59.343] [DEBUG] [Application] [15] Workflow step 'FetchData' status: Completed
[2025-07-04 09:59:59.370] [INFO] [Application] [15] Step 3: Validate Information
[2025-07-04 09:59:59.389] [DEBUG] [Application] [15] Workflow step 'Validation' status: InProgress
[2025-07-04 09:59:59.405] [INFO] [Application] [15] Successfully fetched and populated data from ticket TESTIT-49342
[2025-07-04 10:00:06.958] [WARN] [Application] [15] Field validation errors found: First Name: Invalid format; Last Name: Invalid format
[2025-07-04 10:00:07.004] [WARN] [Application] [15] Some fields have validation errors
[2025-07-04 10:00:07.086] [WARN] [Application] [15] Field validation found errors
[2025-07-04 10:00:07.239] [ERROR] [Application] [15] Error during validation: The term 'Show-ValidationErrors' is not recognized as the name of a cmdlet, function, script file, or operable program. Check the spelling of the name, or if a path was included, verify that the path is correct and try again. | Stack: Write-StructuredLog:8805 -> Write-AppLog:9024 -> Write-Log:7060 -> Write-EnhancedLog:7294 -> :11982 -> OnboardingFromJiraGUI.ps1:12634 -> Launch-OnboardingGUI.ps1:18 -> <ScriptBlock>:1
[2025-07-04 10:00:19.022] [DEBUG] [Application] [15] Generating SAM account name for Test Test2
[2025-07-04 10:00:19.046] [DEBUG] [Application] [15] Checking if SAM account name 'test2t' exists
[2025-07-04 10:00:19.074] [INFO] [Application] [15] Generated SAM account name: test2t (simulation mode - could not verify uniqueness)
[2025-07-04 10:00:19.095] [DEBUG] [Application] [15] DIAGNOSTIC: Event handler setting SAM = 'test2t'
[2025-07-04 10:00:19.110] [DEBUG] [Application] [15] DIAGNOSTIC: Event handler SamBox.Text after assignment = 'test2t'
[2025-07-04 10:00:19.138] [DEBUG] [Application] [15] Auto-generated account details: SAM='test2t', UPN='<EMAIL>'
[2025-07-04 10:00:20.502] [DEBUG] [Application] [15] Generating SAM account name for Testt Test2
[2025-07-04 10:00:20.521] [DEBUG] [Application] [15] Checking if SAM account name 'test2t' exists
[2025-07-04 10:00:20.538] [INFO] [Application] [15] Generated SAM account name: test2t (simulation mode - could not verify uniqueness)
[2025-07-04 10:00:20.554] [DEBUG] [Application] [15] DIAGNOSTIC: Event handler setting SAM = 'test2t'
[2025-07-04 10:00:20.571] [DEBUG] [Application] [15] DIAGNOSTIC: Event handler SamBox.Text after assignment = 'test2t'
[2025-07-04 10:00:20.598] [DEBUG] [Application] [15] Auto-generated account details: SAM='test2t', UPN='<EMAIL>'
[2025-07-04 10:00:20.643] [DEBUG] [Application] [15] Generating SAM account name for Testtt Test2
[2025-07-04 10:00:20.659] [DEBUG] [Application] [15] Checking if SAM account name 'test2t' exists
[2025-07-04 10:00:20.682] [INFO] [Application] [15] Generated SAM account name: test2t (simulation mode - could not verify uniqueness)
[2025-07-04 10:00:20.698] [DEBUG] [Application] [15] DIAGNOSTIC: Event handler setting SAM = 'test2t'
[2025-07-04 10:00:20.715] [DEBUG] [Application] [15] DIAGNOSTIC: Event handler SamBox.Text after assignment = 'test2t'
[2025-07-04 10:00:20.746] [DEBUG] [Application] [15] Auto-generated account details: SAM='test2t', UPN='<EMAIL>'
[2025-07-04 10:00:22.057] [DEBUG] [Application] [15] Generating SAM account name for Testtt Test
[2025-07-04 10:00:22.077] [WARN] [Application] [15] SAM account name 'testt' is less than 6 characters, adding more letters from first name
[2025-07-04 10:00:22.091] [DEBUG] [Application] [15] Checking if SAM account name 'testte' exists
[2025-07-04 10:00:22.109] [INFO] [Application] [15] Generated SAM account name: testte (simulation mode - could not verify uniqueness)
[2025-07-04 10:00:22.127] [DEBUG] [Application] [15] DIAGNOSTIC: Event handler setting SAM = 'testte'
[2025-07-04 10:00:22.155] [DEBUG] [Application] [15] DIAGNOSTIC: Event handler SamBox.Text after assignment = 'testte'
[2025-07-04 10:00:22.191] [DEBUG] [Application] [15] Auto-generated account details: SAM='testte', UPN='<EMAIL>'
[2025-07-04 10:00:22.436] [DEBUG] [Application] [15] Generating SAM account name for Testtt Testv
[2025-07-04 10:00:22.456] [DEBUG] [Application] [15] Checking if SAM account name 'testvt' exists
[2025-07-04 10:00:22.478] [INFO] [Application] [15] Generated SAM account name: testvt (simulation mode - could not verify uniqueness)
[2025-07-04 10:00:22.500] [DEBUG] [Application] [15] DIAGNOSTIC: Event handler setting SAM = 'testvt'
[2025-07-04 10:00:22.527] [DEBUG] [Application] [15] DIAGNOSTIC: Event handler SamBox.Text after assignment = 'testvt'
[2025-07-04 10:00:22.557] [DEBUG] [Application] [15] Auto-generated account details: SAM='testvt', UPN='<EMAIL>'
[2025-07-04 10:00:22.598] [DEBUG] [Application] [15] Generating SAM account name for Testtt Testvv
[2025-07-04 10:00:22.619] [DEBUG] [Application] [15] Checking if SAM account name 'testvt' exists
[2025-07-04 10:00:22.646] [INFO] [Application] [15] Generated SAM account name: testvt (simulation mode - could not verify uniqueness)
[2025-07-04 10:00:22.660] [DEBUG] [Application] [15] DIAGNOSTIC: Event handler setting SAM = 'testvt'
[2025-07-04 10:00:22.676] [DEBUG] [Application] [15] DIAGNOSTIC: Event handler SamBox.Text after assignment = 'testvt'
[2025-07-04 10:00:22.705] [DEBUG] [Application] [15] Auto-generated account details: SAM='testvt', UPN='<EMAIL>'
[2025-07-04 10:00:24.984] [DEBUG] [Application] [15] Workflow step 'Validation' status: Completed
[2025-07-04 10:00:25.081] [INFO] [Application] [15] Field validation completed successfully
[2025-07-04 10:01:10.994] [INFO] [Application] [15] User preview displayed
[2025-07-04 10:01:14.108] [INFO] [Application] [15] Step 4: Create AD User
[2025-07-04 10:01:14.136] [DEBUG] [Application] [15] Workflow step 'CreateUser' status: InProgress
[2025-07-04 10:01:16.578] [INFO] [Application] [15] Starting user simulate process...
[2025-07-04 10:01:16.671] [INFO] [Application] [15] Starting user creation process for Testtt Testvv
[2025-07-04 10:01:16.757] [INFO] [Application] [15] SIMULATION: New-ADUser with params:

Name                           Value                                                                                   
----                           -----                                                                                   
ErrorAction                    Stop                                                                                    
EmailAddress                   <EMAIL>                                                                
SamAccountName                 testvt                                                                                  
DisplayName                    Testtt Testvv                                                                           
Surname                        Testvv                                                                                  
UserPrincipalName              <EMAIL>                                                                
Name                           Testtt Testvv                                                                           
GivenName                      Testtt                                                                                  
Enabled                        True                                                                                    
Path                           OU=Tokyo,OU=Users,OU=Accounts,OU=JeraGM,DC=JERAGM,DC=COM                                
Department                     IT                                                                                      
AccountPassword                System.Security.SecureString                                                            
Title                          Intern                                                                                  



[2025-07-04 10:01:16.845] [INFO] [Application] [15] SIMULATION: Add-ADGroupMember: Adding testvt to CN=Group1,DC=jeragm,DC=com
[2025-07-04 10:01:16.877] [INFO] [Application] [15] SIMULATION: Add-ADGroupMember: Adding testvt to CN=Group2,DC=jeragm,DC=com
[2025-07-04 10:01:16.904] [INFO] [Application] [15] Copied groups from test.
[2025-07-04 10:01:16.923] [INFO] [Application] [15] Copied groups from model account: test
[2025-07-04 10:01:17.006] [DEBUG] [Application] [15] Attempting to add comment to ticket: TESTIT-49342
[2025-07-04 10:01:20.040] [INFO] [Application] [15] Successfully added comment to Jira ticket TESTIT-49342
[2025-07-04 10:01:20.065] [DEBUG] [Application] [15] Workflow step 'CreateUser' status: Completed
[2025-07-04 10:01:20.087] [INFO] [Application] [15] User creation completed successfully for Testtt Testvv
[2025-07-04 23:15:17.680] [DEBUG] [Testing] [17] Test registered: ConfigurationValidation in suite Unit
[2025-07-04 23:15:17.774] [DEBUG] [Testing] [17] Test registered: ConfigurationValidation in Unit
[2025-07-04 23:15:17.807] [DEBUG] [Testing] [17] Test registered: LoggingSystem in suite Unit
[2025-07-04 23:15:17.812] [DEBUG] [Testing] [17] Test registered: LoggingSystem in Unit
[2025-07-04 23:15:17.824] [DEBUG] [Testing] [17] Test registered: VersionManager in suite Unit
[2025-07-04 23:15:17.839] [DEBUG] [Testing] [17] Test registered: VersionManager in Unit
[2025-07-04 23:15:17.848] [DEBUG] [Testing] [17] Test registered: JiraConnection in suite Integration
[2025-07-04 23:15:17.848] [DEBUG] [Testing] [17] Test registered: JiraConnection in Integration
[2025-07-04 23:15:17.864] [DEBUG] [Testing] [17] Test registered: ActiveDirectoryConnection in suite Integration
[2025-07-04 23:15:17.872] [DEBUG] [Testing] [17] Test registered: ActiveDirectoryConnection in Integration
[2025-07-04 23:15:17.897] [DEBUG] [Testing] [17] Test registered: StartupTime in suite Performance
[2025-07-04 23:15:17.906] [DEBUG] [Testing] [17] Test registered: StartupTime in Performance
[2025-07-04 23:15:17.920] [DEBUG] [Testing] [17] Test registered: MemoryUsage in suite Performance
[2025-07-04 23:15:17.955] [DEBUG] [Testing] [17] Test registered: MemoryUsage in Performance
[2025-07-04 23:15:17.970] [DEBUG] [Testing] [17] Test registered: CredentialHandling in suite Security
[2025-07-04 23:15:17.978] [DEBUG] [Testing] [17] Test registered: CredentialHandling in Security
[2025-07-04 23:15:17.987] [DEBUG] [Testing] [17] Test registered: InputValidation in suite Security
[2025-07-04 23:15:18.001] [DEBUG] [Testing] [17] Test registered: InputValidation in Security
[2025-07-04 23:15:18.016] [DEBUG] [Testing] [17] Test registered: WindowCreation in suite UI
[2025-07-04 23:15:18.026] [DEBUG] [Testing] [17] Test registered: WindowCreation in UI
[2025-07-04 23:15:18.034] [INFO] [Testing] [17] Core tests initialized
[2025-07-04 23:15:18.058] [INFO] [Documentation] [17] Initializing documentation system
[2025-07-04 23:15:18.074] [INFO] [Documentation] [17] Starting documentation generation for: api
[2025-07-04 23:15:18.112] [INFO] [Documentation] [17] Documentation saved: Documentation\API_Reference.html
[2025-07-04 23:15:18.145] [INFO] [Documentation] [17] Documentation saved: Documentation\API_Reference.md
[2025-07-04 23:15:18.165] [INFO] [Documentation] [17] Documentation generation completed for: api
[2025-07-04 23:15:18.182] [INFO] [Documentation] [17] API documentation generated: 2 files
[2025-07-04 23:15:18.192] [INFO] [Documentation] [17] Starting documentation generation for: help
[2025-07-04 23:15:18.206] [INFO] [Documentation] [17] Documentation generation completed for: help
[2025-07-04 23:15:18.212] [INFO] [Documentation] [17] Help system documentation generated
[2025-07-04 23:15:18.222] [INFO] [Documentation] [17] Documentation system initialized
[2025-07-04 23:15:18.344] [INFO] [Application] [17] Initializing enhanced UI components...
[2025-07-04 23:15:18.413] [DEBUG] [Application] [17] Theme applied to controls
[2025-07-04 23:15:18.507] [DEBUG] [Application] [17] Modern theme applied successfully
[2025-07-04 23:15:18.573] [DEBUG] [Application] [17] Field validation initialized
[2025-07-04 23:15:18.618] [DEBUG] [Application] [17] Responsive layout initialized
[2025-07-04 23:15:18.823] [DEBUG] [Application] [17] Workflow step 'Connection' status: Pending
[2025-07-04 23:15:18.901] [INFO] [Application] [17] Enhanced UI initialization completed
[2025-07-04 23:15:18.939] [INFO] [Application] [17] Welcome! Please connect to Jira to begin.
[2025-07-04 23:15:18.955] [INFO] [Application] [17] OnboardingFromJiraGUI started successfully
[2025-07-04 23:15:18.986] [INFO] [Application] [17] [OK] Monitoring system initialized
[2025-07-04 23:15:19.029] [INFO] [Audit] [17] Audit: akinje performed PermissionInit on UserPermissions - Success | Context: SessionId=Unknown; Action=PermissionInit; Timestamp=07/04/2025 23:15:19; Result=Success; User=akinje; Detected=Standard user privileges; Target=UserPermissions; PrivilegeLevel=Standard
[2025-07-04 23:15:19.177] [INFO] [Application] [17] [OK] User permissions initialized for JERAGM\akinje
[2025-07-04 23:15:19.200] [DEBUG] [Application] [17]  Cache cleanup scheduled every 30 minutes
[2025-07-04 23:15:19.222] [INFO] [Application] [17] Applying Modern UI Integration...
[2025-07-04 23:15:19.282] [DEBUG] [Application] [17] Auto-completion setup for LastNameBox
[2025-07-04 23:15:19.314] [DEBUG] [Application] [17] Auto-completion setup for JobTitleBox
[2025-07-04 23:15:19.361] [DEBUG] [Application] [17] Auto-completion setup for FirstNameBox
[2025-07-04 23:15:19.392] [DEBUG] [Application] [17] Auto-completion setup for DepartmentBox
[2025-07-04 23:15:19.414] [DEBUG] [Application] [17] Auto-completion setup for ModelAccountBox
[2025-07-04 23:15:19.449] [DEBUG] [Application] [17] Auto-completion integration completed
[2025-07-04 23:15:19.505] [INFO] [Application] [17] Modern UI Integration completed successfully
[2025-07-04 23:15:19.521] [INFO] [Interface] [17] Starting traditional tabbed interface mode
[2025-07-06 03:46:55.043] [DEBUG] [Testing] [17] Test registered: ConfigurationValidation in suite Unit
[2025-07-06 03:46:55.158] [DEBUG] [Testing] [17] Test registered: ConfigurationValidation in Unit
[2025-07-06 03:46:55.173] [DEBUG] [Testing] [17] Test registered: LoggingSystem in suite Unit
[2025-07-06 03:46:55.182] [DEBUG] [Testing] [17] Test registered: LoggingSystem in Unit
[2025-07-06 03:46:55.191] [DEBUG] [Testing] [17] Test registered: VersionManager in suite Unit
[2025-07-06 03:46:55.205] [DEBUG] [Testing] [17] Test registered: VersionManager in Unit
[2025-07-06 03:46:55.248] [DEBUG] [Testing] [17] Test registered: JiraConnection in suite Integration
[2025-07-06 03:46:55.262] [DEBUG] [Testing] [17] Test registered: JiraConnection in Integration
[2025-07-06 03:46:55.276] [DEBUG] [Testing] [17] Test registered: ActiveDirectoryConnection in suite Integration
[2025-07-06 03:46:55.290] [DEBUG] [Testing] [17] Test registered: ActiveDirectoryConnection in Integration
[2025-07-06 03:46:55.306] [DEBUG] [Testing] [17] Test registered: StartupTime in suite Performance
[2025-07-06 03:46:55.324] [DEBUG] [Testing] [17] Test registered: StartupTime in Performance
[2025-07-06 03:46:55.338] [DEBUG] [Testing] [17] Test registered: MemoryUsage in suite Performance
[2025-07-06 03:46:55.353] [DEBUG] [Testing] [17] Test registered: MemoryUsage in Performance
[2025-07-06 03:46:55.369] [DEBUG] [Testing] [17] Test registered: CredentialHandling in suite Security
[2025-07-06 03:46:55.384] [DEBUG] [Testing] [17] Test registered: CredentialHandling in Security
[2025-07-06 03:46:55.398] [DEBUG] [Testing] [17] Test registered: InputValidation in suite Security
[2025-07-06 03:46:55.417] [DEBUG] [Testing] [17] Test registered: InputValidation in Security
[2025-07-06 03:46:55.432] [DEBUG] [Testing] [17] Test registered: WindowCreation in suite UI
[2025-07-06 03:46:55.445] [DEBUG] [Testing] [17] Test registered: WindowCreation in UI
[2025-07-06 03:46:55.464] [INFO] [Testing] [17] Core tests initialized
[2025-07-06 03:46:55.504] [INFO] [Documentation] [17] Initializing documentation system
[2025-07-06 03:46:55.551] [INFO] [Documentation] [17] Starting documentation generation for: api
[2025-07-06 03:46:55.647] [ERROR] [Documentation] [17] Failed to save documentation: Documentation\API_Reference.html - The process cannot access the file 'C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\Utilities\Documentation\API_Reference.html' because it is being used by another process. | Stack: Write-StructuredLog:8805 -> :11041 -> :10781 -> :10725 -> New-Documentation:11228 -> New-APIDocumentation:11234 -> Initialize-DocumentationSystem:11609 -> ONBOAR~1.PS1:11623 -> <ScriptBlock>:1
[2025-07-06 03:46:55.766] [ERROR] [Documentation] [17] Documentation generation failed: The process cannot access the file 'C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\Utilities\Documentation\API_Reference.html' because it is being used by another process. | Stack: Write-StructuredLog:8805 -> :10748 -> New-Documentation:11228 -> New-APIDocumentation:11234 -> Initialize-DocumentationSystem:11609 -> ONBOAR~1.PS1:11623 -> <ScriptBlock>:1
[2025-07-06 03:46:55.789] [INFO] [Documentation] [17] Documentation generation completed for: api
[2025-07-06 03:46:55.805] [INFO] [Documentation] [17] API documentation generated: 0 files
[2025-07-06 03:46:55.820] [INFO] [Documentation] [17] Starting documentation generation for: help
[2025-07-06 03:46:55.844] [INFO] [Documentation] [17] Documentation generation completed for: help
[2025-07-06 03:46:55.859] [INFO] [Documentation] [17] Help system documentation generated
[2025-07-06 03:46:55.873] [INFO] [Documentation] [17] Documentation system initialized
[2025-07-06 03:46:56.036] [INFO] [Application] [17] Initializing enhanced UI components...
[2025-07-06 03:46:56.119] [DEBUG] [Application] [17] Theme applied to controls
[2025-07-06 03:46:56.151] [DEBUG] [Application] [17] Modern theme applied successfully
[2025-07-06 03:46:56.191] [DEBUG] [Application] [17] Field validation initialized
[2025-07-06 03:46:56.216] [DEBUG] [Application] [17] Responsive layout initialized
[2025-07-06 03:46:56.350] [DEBUG] [Application] [17] Workflow step 'Connection' status: Pending
[2025-07-06 03:46:56.428] [INFO] [Application] [17] Enhanced UI initialization completed
[2025-07-06 03:46:56.456] [INFO] [Application] [17] Welcome! Please connect to Jira to begin.
[2025-07-06 03:46:56.469] [INFO] [Application] [17] OnboardingFromJiraGUI started successfully
[2025-07-06 03:46:56.489] [INFO] [Application] [17]  Monitoring system initialized
[2025-07-06 03:46:56.515] [INFO] [Audit] [17] Audit: akinje performed PermissionInit on UserPermissions - Success | Context: Timestamp=07/06/2025 03:46:56; Target=UserPermissions; Result=Success; Action=PermissionInit; Detected=Standard user privileges; SessionId=Unknown; PrivilegeLevel=Standard; User=akinje
[2025-07-06 03:46:56.563] [INFO] [Application] [17]  User permissions initialized for JERAGM\akinje
[2025-07-06 03:46:56.578] [DEBUG] [Application] [17]  Cache cleanup scheduled every 30 minutes
[2025-07-06 03:46:56.606] [INFO] [Application] [17] Applying Modern UI Integration...
[2025-07-06 03:46:56.682] [DEBUG] [Application] [17] Auto-completion setup for FirstNameBox
[2025-07-06 03:46:56.695] [DEBUG] [Application] [17] Auto-completion setup for DepartmentBox
[2025-07-06 03:46:56.703] [DEBUG] [Application] [17] Auto-completion setup for LastNameBox
[2025-07-06 03:46:56.712] [DEBUG] [Application] [17] Auto-completion setup for JobTitleBox
[2025-07-06 03:46:56.721] [DEBUG] [Application] [17] Auto-completion setup for ModelAccountBox
[2025-07-06 03:46:56.728] [DEBUG] [Application] [17] Auto-completion integration completed
[2025-07-06 03:46:56.783] [INFO] [Application] [17] Modern UI Integration completed successfully
[2025-07-06 03:46:56.795] [INFO] [Interface] [17] Starting traditional tabbed interface mode
