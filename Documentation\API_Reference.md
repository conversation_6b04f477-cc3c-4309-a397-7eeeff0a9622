﻿# API Reference

## Functions

### ValidateConnectionStep

No synopsis available

### ValidateTicketSelectionStep

No synopsis available

### ValidateUserInformationStep

No synopsis available

### ValidateADConfigurationStep

No synopsis available

### ValidateFinalReviewStep

No synopsis available

### Write-AppLog

No synopsis available

### Test-ConfigurationCompatibility

No synopsis available

### Update-ConfigurationToLatest

No synopsis available

### Get-VersionInfo

No synopsis available

### Get-CompatibilityIcons

No synopsis available

### Get-SmartSuggestions

No synopsis available

### Get-ValidValueSuggestions

No synopsis available

### Get-ContextualSuggestions

No synopsis available

### Get-GeneratedSuggestions

No synopsis available

### Get-ActiveDirectorySuggestions

No synopsis available

### Get-DateSuggestions

No synopsis available

### Get-FuzzyMatchScore

No synopsis available

### Show-AutoCompletionPopup

No synopsis available

### Get-CurrentFormContext

No synopsis available

### Enable-AccessibilityFeatures

No synopsis available

### Add-KeyboardShortcuts

No synopsis available

### Enable-ScreenReaderSupport

No synopsis available

### Initialize-ResponsiveDesign

No synopsis available

### Update-ResponsiveLayout

No synopsis available

### Set-CompactLayout

No synopsis available

### Set-MediumLayout

No synopsis available

### Set-LargeLayout

No synopsis available

### Apply-LargeTextSizes

No synopsis available

### Optimize-ForLargeScreen

No synopsis available

### Optimize-ForNormalScreen

No synopsis available

### New-AppError

No synopsis available

### Invoke-ErrorRecovery

No synopsis available

### Handle-AppError

No synopsis available

### Write-ErrorLog

No synopsis available

### Get-FriendlyErrorMessage

No synopsis available

### Show-FriendlyError

No synopsis available

### New-PerformanceCache

No synopsis available

### Get-CacheEntry

No synopsis available

### Set-CacheEntry

No synopsis available

### Clear-CacheExpired

No synopsis available

### Get-CacheStatistics

No synopsis available

### New-FieldValidator

No synopsis available

### Test-Required

No synopsis available

### Test-MinLength

No synopsis available

### Test-MaxLength

No synopsis available

### Test-NoSpecialChars

No synopsis available

### Test-ValidSamAccountName

No synopsis available

### Test-ValidDate

No synopsis available

### Test-FutureDate

No synopsis available

### Test-ValidDepartment

No synopsis available

### Test-ValidLocation

No synopsis available

### Invoke-FieldValidation

No synopsis available

### Invoke-FormValidation

No synopsis available

### Add-RealTimeValidation

No synopsis available

### Update-FieldValidationUI

No synopsis available

### Show-FieldSuggestions

No synopsis available

### Show-ValidationTooltip

No synopsis available

### Invoke-FormSubmitValidation

No synopsis available

### Get-EnhancedSuggestions

No synopsis available

### Get-EmailSuggestions

No synopsis available

### Get-SamAccountSuggestions

No synopsis available

### Get-ModelAccountSuggestions

No synopsis available

### New-ProgressDialog

No synopsis available

### Show-ProgressDialog

No synopsis available

### Update-ProgressDialog

No synopsis available

### Close-ProgressDialog

No synopsis available

### Invoke-AsyncOperation

No synopsis available

### New-SecurityManager

No synopsis available

### Write-AuditLog

No synopsis available

### Test-Permission

No synopsis available

### Initialize-UserPermissions

No synopsis available

### Protect-SensitiveData

No synopsis available

### Test-SessionSecurity

No synopsis available

### Invoke-DataSanitization

No synopsis available

### Add-BatchUserCreation

No synopsis available

### Add-BatchJiraProcessing

No synopsis available

### Add-BatchGroupAssignment

No synopsis available

### Show-BatchResults

No synopsis available

### Start-BatchOperations

No synopsis available

### Clear-BatchQueue

No synopsis available

### Get-BatchQueueStatus

No synopsis available

### New-MonitoringManager

No synopsis available

### Record-OperationMetric

No synopsis available

### Update-CacheMetrics

No synopsis available

### Record-ValidationError

No synopsis available

### Record-SecurityEvent

No synopsis available

### Invoke-ThresholdCheck

No synopsis available

### New-Alert

No synopsis available

### Show-Alert

No synopsis available

### Get-MonitoringReport

No synopsis available

### Initialize-Monitoring

No synopsis available

### Invoke-AsyncJiraOperation

No synopsis available

### Invoke-AsyncADOperation

No synopsis available

### Invoke-AsyncFormValidation

No synopsis available

### Invoke-AsyncCacheCleanup

No synopsis available

### New-AsyncOperationQueue

No synopsis available

### Add-AsyncOperationToQueue

No synopsis available

### Start-AsyncOperationQueue

No synopsis available

### Connect-EnhancedJira

No synopsis available

### Get-EnhancedTicketData

No synopsis available

### Add-EnhancedJiraComment

No synopsis available

### Update-TicketCustomFields

No synopsis available

### Add-TicketAttachment

No synopsis available

### Initialize-ModernUIComponents

No synopsis available

### Add-FadeInAnimation

No synopsis available

### Get-CurrentTheme

No synopsis available

### Apply-ThemeToControl

No synopsis available

### Install-NuGetProviderSilently

No synopsis available

### Set-PSGalleryTrusted

No synopsis available

### Write-Log

No synopsis available

### Get-ValueOrDefault

No synopsis available

### Update-Status

No synopsis available

### Set-UiLock

No synopsis available

### Test-PowerShellCompatibility

No synopsis available

### Initialize-ModernTheme

No synopsis available

### Apply-ThemeToControls

No synopsis available

### Update-WorkflowProgress

No synopsis available

### Update-ProgressDisplay

No synopsis available

### Update-SessionStatistics

No synopsis available

### Update-StatisticsDisplay

No synopsis available

### Write-EnhancedLog

No synopsis available

### Update-LogDisplay

No synopsis available

### Initialize-FieldValidation

No synopsis available

### Validate-Field

No synopsis available

### Clear-AllFields

No synopsis available

### Validate-AllFields

No synopsis available

### Initialize-ResponsiveLayout

No synopsis available

### Adjust-LayoutForSize

No synopsis available

### Test-SessionValid

No synopsis available

### Test-JiraConnection

No synopsis available

### Reset-Session

No synopsis available

### Test-JiraUrl

No synopsis available

### Test-TicketKey

No synopsis available

### Get-CachedTicket

No synopsis available

### Set-CachedTicket

No synopsis available

### Get-ADUser-Simulated

No synopsis available

### New-ADUser-Simulated

No synopsis available

### Add-ADGroupMember-Simulated

No synopsis available

### Get-SamAccountName

No synopsis available

### Get-UserLogonName

No synopsis available

### Get-EmailAddress

No synopsis available

### Get-UpnFromNames

No synopsis available

### New-SecurePassword

No synopsis available

### Get-RecommendedADGroups

No synopsis available

### Test-ADGroupMembership

No synopsis available

### Test-ADOUPath

No synopsis available

### Add-UserToADGroups

No synopsis available

### Remove-UserFromADGroups

No synopsis available

### Get-ADUserGroupMembership

No synopsis available

### Test-ADPermissions

No synopsis available

### Write-StructuredLog

No synopsis available

### Start-LoggingSession

No synopsis available

### Stop-LoggingSession

No synopsis available

### Write-PerformanceLog

No synopsis available

### Write-AuditLog

No synopsis available

### Write-SecurityLog

No synopsis available

### Get-LoggingMetrics

No synopsis available

### Get-LoggingConfiguration

No synopsis available

### Set-LoggingLevel

No synopsis available

### Enable-LoggingOutput

No synopsis available

### Disable-LoggingOutput

No synopsis available

### Export-LoggingReport

No synopsis available

### Write-AppLog

No synopsis available

### Get-ScriptVersion

No synopsis available

### Get-ScriptChangeLog

No synopsis available

### Test-SystemCompatibility

No synopsis available

### Start-ScriptUpgrade

No synopsis available

### Invoke-PreUpgradeChecks

No synopsis available

### Invoke-UpgradeSteps

No synopsis available

### New-ConfigurationBackup

No synopsis available

### Restore-ConfigurationBackup

No synopsis available

### Get-UpgradeHistory

No synopsis available

### Export-VersionReport

No synopsis available

### Register-Test

No synopsis available

### Invoke-TestSuite

No synopsis available

### Invoke-AllTests

No synopsis available

### Get-TestResults

No synopsis available

### Get-TestMetrics

No synopsis available

### Export-TestReport

No synopsis available

### ConvertTo-HtmlTestReport

No synopsis available

### Initialize-CoreTests

No synopsis available

### Test-JiraConnection

No synopsis available

### Test-ActiveDirectoryConnection

No synopsis available

### Protect-StringInput

No synopsis available

### New-Documentation

No synopsis available

### New-APIDocumentation

No synopsis available

### New-UserGuideDocumentation

No synopsis available

### New-ConfigurationDocumentation

No synopsis available

### New-HelpSystemDocumentation

No synopsis available

### Get-DocumentationStatus

No synopsis available

### Export-DocumentationReport

No synopsis available

### Show-ScriptHelp

No synopsis available

### Get-HelpContent

No synopsis available

### Show-HelpWindow

No synopsis available

### Initialize-DocumentationSystem

No synopsis available

### Update-AccountDetailsFromNames

No synopsis available

### Initialize-EnhancedUI

No synopsis available

### Apply-ModernUIIntegration

No synopsis available

### Setup-AutoCompletionForFields

No synopsis available

## Classes

### ConfigurationManager

Core Application Settings
            Application = @{
                Name = "OnboardingFromJiraGUI"
                Version = "5.0.0"
                Title = "Enterprise User Onboarding System v5.0"
                MinPowerShellVersion = "5.1"
                MaxConcurrentOperations = 10
                DefaultTimeout = 300
                EnableTelemetry = $true
            }

            # UI Configuration
            UI = @{
                Theme = "Professional"
                WindowWidth = 1200
                WindowHeight = 800
                MinWindowWidth = 800
                MinWindowHeight = 600
                EnableAnimations = $true
                ShowProgressBars = $true
                EnableTooltips = $true
                FontFamily = "Segoe UI"
                FontSize = 12
                EnableHighContrast = $false
                EnableAccessibility = $true
            }

            # Performance Settings
            Performance = @{
                CacheEnabled = $true
                CacheExpiryMinutes = 30
                MaxCacheSize = 100
                EnableAsyncOperations = $true
                MaxRetryAttempts = 3
                RetryDelaySeconds = 2
                EnableCompression = $true
                OptimizeMemoryUsage = $true
            }

            # Security Configuration
            Security = @{
                EnableAuditLogging = $true
                SessionTimeoutMinutes = 60
                RequireSecureConnection = $true
                EnableEncryption = $true
                MaxFailedAttempts = 5
                LockoutDurationMinutes = 15
                EnableTwoFactorAuth = $false
                AuditLogRetentionDays = 90
            }

            # Validation Settings
            Validation = @{
                Level = "Enterprise"
                EnableRealTimeValidation = $true
                ShowValidationTooltips = $true
                EnableFieldSuggestions = $true
                ValidateOnBlur = $true
                ValidateOnSubmit = $true
                EnableDependencyChecking = $true
                CustomValidationRules = @{}
            }

            # Logging Configuration
            Logging = @{
                Level = "INFO"
                EnableFileLogging = $true
                EnableConsoleLogging = $true
                EnableEventLogging = $false
                MaxLogFileSize = 10MB
                LogRetentionDays = 30
                EnableStructuredLogging = $true
                EnablePerformanceLogging = $true
            }

            # Integration Settings
            Integration = @{
                Jira = @{
                    MaxRetries = 3
                    TimeoutSeconds = 30
                    EnableCaching = $true
                    CacheExpiryMinutes = 15
                    EnableBatchOperations = $true
                    MaxBatchSize = 50
                }
                ActiveDirectory = @{
                    EnableSimulationMode = $false
                    ValidateOUExists = $true
                    EnableGroupValidation = $true
                    MaxSearchResults = 1000
                    SearchTimeoutSeconds = 30
                }
            }
        }
    }

### WizardSessionManager

0=Welcome, 1-5=Wizard Steps
                CompletedSteps = @()
                WizardMode = "None"  # None, Single, Batch
                ValidationResults = @{}
            }
            
            ConnectionInfo = @{
                JiraUrl = ""
                Username = ""
                IsConnected = $false
                ConnectionTime = $null
            }
            
            TicketInfo = @{
                ProcessingMode = "Single"
                SingleTicketId = ""
                BatchTicketIds = @()
                FetchedData = @{}
                ValidationStatus = @{}
            }
            
            UserDetails = @{
                FirstName = ""
                LastName = ""
                Email = ""
                UPN = ""
                SAMAccount = ""
                JobTitle = ""
                Department = ""
                OUPath = ""
                ModelAccount = ""
                CopyModelGroups = $false
            }
            
            ExecutionResults = @{
                StartTime = $null
                EndTime = $null
                SuccessfulUsers = @()
                FailedUsers = @()
                BatchResults = @{}
            }
        }
    }

### WizardInterfaceController

Update session state
            $script:WizardSession.UpdateStepProgress($StepNumber, $false)
            
            Write-StructuredLog "Navigated to step $StepNumber ($($this.StepDefinitions[$StepNumber].Name))" -Level "INFO" -Category "WizardNavigation"
        }
        else {
            Write-StructuredLog "Navigation to step $StepNumber blocked - validation required" -Level "WARN" -Category "WizardNavigation"
            # Use string-based invocation to avoid parse-time type resolution
            $messageBoxType = [System.Type]::GetType("System.Windows.MessageBox")
            $messageBoxType.GetMethod("Show", [System.Type[]]@([string], [string], [string], [string])).Invoke($null, @("Please complete the current step before proceeding.", "Navigation Blocked", "OK", "Warning"))
        }
    }

### AutoCompletionHistory

Add to field history
        if (-not $this.FieldHistory.ContainsKey($FieldName)) {
            $this.FieldHistory[$FieldName] = @()
        }

        # Remove if already exists to avoid duplicates
        $this.FieldHistory[$FieldName] = $this.FieldHistory[$FieldName] | Where-Object { $_ -ne $Value }

        # Add to beginning of array
        $this.FieldHistory[$FieldName] = @($Value) + $this.FieldHistory[$FieldName]

        # Trim to max items
        if ($this.FieldHistory[$FieldName].Count -gt $this.MaxHistoryItems) {
            $this.FieldHistory[$FieldName] = $this.FieldHistory[$FieldName][0..($this.MaxHistoryItems - 1)]
        }

        # Update frequency map
        if (-not $this.FrequencyMap[$FieldName].ContainsKey($Value)) {
            $this.FrequencyMap[$FieldName][$Value] = 0
        }
        $this.FrequencyMap[$FieldName][$Value]++

        # Add contextual history
        if ($Context.Count -gt 0) {
            $contextKey = ($Context.Keys | Sort-Object | ForEach-Object { "$_=$($Context[$_])" }) -join ";"
            if (-not $this.ContextualHistory.ContainsKey($contextKey)) {
                $this.ContextualHistory[$contextKey] = @{}
            }
            if (-not $this.ContextualHistory[$contextKey].ContainsKey($FieldName)) {
                $this.ContextualHistory[$contextKey][$FieldName] = @()
            }

            $this.ContextualHistory[$contextKey][$FieldName] = @($Value) + ($this.ContextualHistory[$contextKey][$FieldName] | Where-Object { $_ -ne $Value })

            if ($this.ContextualHistory[$contextKey][$FieldName].Count -gt 20) {
                $this.ContextualHistory[$contextKey][$FieldName] = $this.ContextualHistory[$contextKey][$FieldName][0..19]
            }
        }
    }

### OrganizationalDataManager

Get department-specific titles
        if ($Department -and $this.DepartmentData.ContainsKey($Department)) {
            $deptTitles = $this.DepartmentData[$Department].CommonTitles
            foreach ($title in $deptTitles) {
                if ($title -like "*$PartialInput*") {
                    $suggestions += @{
                        Value = $title
                        DisplayText = $title
                        Description = "Common in $Department"
                        Priority = 15
                        Category = "JobTitle"
                    }
                }
            }
        }

        # Get role hierarchy titles
        foreach ($roleLevel in $this.RoleHierarchy.Keys) {
            $roleTitles = $this.RoleHierarchy[$roleLevel].Titles
            foreach ($title in $roleTitles) {
                if ($title -like "*$PartialInput*") {
                    $suggestions += @{
                        Value = $title
                        DisplayText = $title
                        Description = "$roleLevel level position"
                        Priority = 12
                        Category = "JobTitle"
                    }
                }
            }
        }

        return $suggestions | Sort-Object Priority -Descending
    }

### BatchOperationManager

Process operations in background
        $batchJob = Start-Job -ScriptBlock {
            param($Operations, $MaxConcurrent, $CancellationToken)

            $runningJobs = @()
            $completedOperations = @()

            foreach ($operation in $Operations) {
                # Check for cancellation
                if ($CancellationToken.IsCancellationRequested) {
                    break
                }

                # Wait if we've reached max concurrent operations
                while ($runningJobs.Count -ge $MaxConcurrent) {
                    $completedJobs = $runningJobs | Where-Object { $_.State -eq 'Completed' -or $_.State -eq 'Failed' }
                    foreach ($job in $completedJobs) {
                        $runningJobs = $runningJobs | Where-Object { $_.Id -ne $job.Id }
                        $completedOperations += Receive-Job -Job $job
                        Remove-Job -Job $job
                    }
                    Start-Sleep -Milliseconds 100
                }

                # Start new operation
                $operationJob = Start-Job -ScriptBlock {
                    param($Op)
                    return @{
                        Id = $Op.Id
                        Type = $Op.Type
                        Status = "Processing"
                        StartTime = Get-Date
                        Result = "Simulated completion for $($Op.Type)"
                    }
                } -ArgumentList $operation

                $runningJobs += $operationJob
            }

            # Wait for remaining jobs to complete
            while ($runningJobs.Count -gt 0) {
                $completedJobs = $runningJobs | Where-Object { $_.State -eq 'Completed' -or $_.State -eq 'Failed' }
                foreach ($job in $completedJobs) {
                    $runningJobs = $runningJobs | Where-Object { $_.Id -ne $job.Id }
                    $completedOperations += Receive-Job -Job $job
                    Remove-Job -Job $job
                }
                Start-Sleep -Milliseconds 100
            }

            return $completedOperations
        } -ArgumentList $this.OperationQueue, $this.MaxConcurrentOperations, $this.CancellationTokenSource.Token

        # Monitor batch job progress
        $this.MonitorBatchProgress($batchJob)
    }

### EnhancedJiraManager

Create authentication header
        $credentials = [Convert]::ToBase64String([Text.Encoding]::ASCII.GetBytes("${Username}:${Password}"))
        $this.Headers = @{
            "Authorization" = "Basic $credentials"
            "Content-Type" = "application/json"
            "Accept" = "application/json"
        }

        $this.InitializeCustomFieldMapping()
        $this.InitializeAttachmentSettings()
        $this.InitializeCommentTemplates()
    }

### AdvancedADGroupManager

Add department-specific groups
        if ($this.GroupHierarchy.ContainsKey("${Department}_Department")) {
            $deptInfo = $this.GroupHierarchy["${Department}_Department"]
            $recommendedGroups += $deptInfo.RequiredGroups

            # Add role-specific groups within department
            foreach ($childGroup in $deptInfo.Children) {
                if ($JobTitle -like "*$($childGroup.Replace('${Department}_', ''))*") {
                    $recommendedGroups += $childGroup
                }
            }
        }

        # Add access level groups
        if ($this.AccessLevelMapping.ContainsKey($AccessLevel)) {
            $accessInfo = $this.AccessLevelMapping[$AccessLevel]
            $recommendedGroups += $accessInfo.Groups
        }

        # Add basic groups for all users
        $recommendedGroups += @("All_Employees", "Domain_Users", "Office365_Users")

        return ($recommendedGroups | Sort-Object -Unique)
    }

### AdvancedLoggingManager

Initialize category metrics
        foreach ($category in $this.LogConfiguration.Categories.Keys) {
            $this.LogMetrics.MessagesByCategory[$category] = 0
        }
    }

### VersionManager

Check PowerShell version
        $psVersion = $global:PSVersionTable.PSVersion.ToString()
        $psCheck = @{
            Component = "PowerShell"
            Version = $psVersion
            Status = "Unknown"
            Supported = $false
            Tested = $false
            Recommended = $false
        }

        foreach ($version in $this.CompatibilityMatrix.PowerShell.Keys) {
            if ($psVersion -like "$version*") {
                $versionData = $this.CompatibilityMatrix.PowerShell[$version]
                $psCheck.Status = "Found"
                $psCheck.Supported = $versionData.Supported
                $psCheck.Tested = $versionData.Tested
                $psCheck.Recommended = $versionData.Recommended
                break
            }
        }

        if (-not $psCheck.Supported) {
            $compatibilityReport.IsCompatible = $false
            $compatibilityReport.Errors += "PowerShell version $psVersion is not supported"
        } elseif (-not $psCheck.Recommended) {
            $compatibilityReport.Warnings += "PowerShell version $psVersion is supported but not recommended"
        }

        $compatibilityReport.Checks += $psCheck

        # Check Operating System
        $osVersion = [System.Environment]::OSVersion.Version
        $osName = (Get-CimInstance Win32_OperatingSystem).Caption

        $osCheck = @{
            Component = "OperatingSystem"
            Name = $osName
            Version = $osVersion.ToString()
            Status = "Detected"
            Supported = $true  # Assume supported unless proven otherwise
        }

        $compatibilityReport.Checks += $osCheck

        return $compatibilityReport
    }

### TestingFramework

5 minutes
                "Parallel" = $true
                "Priority" = 1
            }
            "Integration" = @{
                "Name" = "Integration Tests"
                "Description" = "Tests for component interactions and workflows"
                "Tests" = @()
                "Enabled" = $true
                "Timeout" = 600  # 10 minutes
                "Parallel" = $false
                "Priority" = 2
            }
            "Performance" = @{
                "Name" = "Performance Tests"
                "Description" = "Tests for performance benchmarks and load testing"
                "Tests" = @()
                "Enabled" = $true
                "Timeout" = 900  # 15 minutes
                "Parallel" = $false
                "Priority" = 3
            }
            "Security" = @{
                "Name" = "Security Tests"
                "Description" = "Tests for security vulnerabilities and compliance"
                "Tests" = @()
                "Enabled" = $true
                "Timeout" = 300  # 5 minutes
                "Parallel" = $true
                "Priority" = 4
            }
            "UI" = @{
                "Name" = "User Interface Tests"
                "Description" = "Tests for UI components and user interactions"
                "Tests" = @()
                "Enabled" = $true
                "Timeout" = 600  # 10 minutes
                "Parallel" = $false
                "Priority" = 5
            }
        }
    }

### DocumentationGenerator

f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background-color: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { border-bottom: 3px solid #007acc; padding-bottom: 20px; margin-bottom: 30px; }
        .header h1 { color: #007acc; margin: 0; font-size: 2.5em; }
        .header .subtitle { color: #666; font-size: 1.2em; margin-top: 10px; }
        .toc { background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0; }
        .toc h2 { margin-top: 0; color: #007acc; }
        .toc ul { list-style-type: none; padding-left: 0; }
        .toc li { margin: 5px 0; }
        .toc a { text-decoration: none; color: #007acc; }
        .toc a:hover { text-decoration: underline; }
        .section { margin: 30px 0; }
        .section h2 { color: #007acc; border-bottom: 2px solid #e9ecef; padding-bottom: 10px; }
        .section h3 { color: #495057; margin-top: 25px; }
        .code-block { background-color: #f8f9fa; border: 1px solid #e9ecef; border-radius: 4px; padding: 15px; margin: 15px 0; overflow-x: auto; }
        .code-block code { font-family: 'Consolas', 'Monaco', 'Courier New', monospace; font-size: 14px; }
        .parameter-table { width: 100%; border-collapse: collapse; margin: 15px 0; }
        .parameter-table th, .parameter-table td { border: 1px solid #dee2e6; padding: 12px; text-align: left; }
        .parameter-table th { background-color: #e9ecef; font-weight: bold; }
        .example { background-color: #e7f3ff; border-left: 4px solid #007acc; padding: 15px; margin: 15px 0; }
        .warning { background-color: #fff3cd; border-left: 4px solid #ffc107; padding: 15px; margin: 15px 0; }
        .note { background-color: #d1ecf1; border-left: 4px solid #17a2b8; padding: 15px; margin: 15px 0; }
        .footer { border-top: 1px solid #e9ecef; padding-top: 20px; margin-top: 40px; text-align: center; color: #6c757d; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>{{TITLE}}</h1>
            <div class="subtitle">{{PROJECT_NAME}} v{{VERSION}} - {{DESCRIPTION}}</div>
            <div class="subtitle">Generated: {{TIMESTAMP}}</div>
        </div>
"@
                "Footer" = @"
        <div class="footer">
            <p>Generated by {{PROJECT_NAME}} Documentation System v{{VERSION}}</p>
            <p>Last updated: {{TIMESTAMP}}</p>
        </div>
    </div>
</body>
</html>
"@
                "Section" = @"
        <div class="section" id="{{SECTION_ID}}">
            <h2>{{SECTION_TITLE}}</h2>

