# Comprehensive WPF Type Resolution Fix Script
param(
    [string]$ScriptPath = "OnboardingFromJiraGUI.ps1"
)

Write-Host "=== Starting WPF Type Resolution Fixes ===" -ForegroundColor Cyan

# Read the script content
$content = Get-Content $ScriptPath -Raw

Write-Host "Original file size: $($content.Length) characters" -ForegroundColor Yellow

# Fix 1: Function parameters - Replace [System.Windows.Controls.Grid] with [Object]
Write-Host "Fixing function parameters..." -ForegroundColor Yellow
$content = $content -replace '\[System\.Windows\.Controls\.Grid\]\$', '[Object]$'
$content = $content -replace '\[System\.Windows\.Controls\.DockPanel\]\$', '[Object]$'

# Fix 2: Static method calls - Use Invoke-Expression for runtime execution
Write-Host "Fixing static method calls..." -ForegroundColor Yellow
$content = $content -replace '\[System\.Windows\.Controls\.Grid\]::SetRow\(([^)]+)\)', 'Invoke-Expression "[System.Windows.Controls.Grid]::SetRow($1)"'
$content = $content -replace '\[System\.Windows\.Controls\.Grid\]::SetColumn\(([^)]+)\)', 'Invoke-Expression "[System.Windows.Controls.Grid]::SetColumn($1)"'
$content = $content -replace '\[System\.Windows\.Controls\.DockPanel\]::SetDock\(([^)]+)\)', 'Invoke-Expression "[System.Windows.Controls.DockPanel]::SetDock($1)"'

# Fix 3: Brushes - Use Invoke-Expression for runtime resolution
Write-Host "Fixing Brushes references..." -ForegroundColor Yellow
$content = $content -replace '\[System\.Windows\.Media\.Brushes\]::(\w+)', 'Invoke-Expression "[System.Windows.Media.Brushes]::$1"'

# Fix 4: GridUnitType - Use Invoke-Expression
Write-Host "Fixing GridUnitType references..." -ForegroundColor Yellow
$content = $content -replace '\[System\.Windows\.GridUnitType\]::(\w+)', 'Invoke-Expression "[System.Windows.GridUnitType]::$1"'

# Fix 5: Thickness constructors - Use New-Object consistently
Write-Host "Fixing Thickness constructors..." -ForegroundColor Yellow
$content = $content -replace '\[System\.Windows\.Thickness\]::new\(([^)]+)\)', 'New-Object System.Windows.Thickness($1)'

# Fix 6: SolidColorBrush constructors
Write-Host "Fixing SolidColorBrush constructors..." -ForegroundColor Yellow
$content = $content -replace '\[System\.Windows\.Media\.SolidColorBrush\]::new\(([^)]+)\)', 'New-Object System.Windows.Media.SolidColorBrush($1)'

# Write the fixed content back
Write-Host "Writing fixed content back to file..." -ForegroundColor Yellow
$content | Set-Content $ScriptPath -Encoding UTF8

Write-Host "=== WPF Type Resolution Fixes Complete ===" -ForegroundColor Green
Write-Host "Fixed file size: $($content.Length) characters" -ForegroundColor Yellow

# Test the fixes
Write-Host "Testing script parsing..." -ForegroundColor Yellow
try {
    $null = [System.Management.Automation.PSParser]::Tokenize($content, [ref]$null)
    Write-Host "✓ Script parses successfully!" -ForegroundColor Green
} catch {
    Write-Host "✗ Parse error still exists: $($_.Exception.Message)" -ForegroundColor Red
}