# JML Automated Credential Management System

## Overview

The JML (<PERSON><PERSON>, Mover, Leaver) Admin Account Management System now includes a comprehensive automated credential management system that addresses the Active Directory dependency issue and provides seamless credential storage using Microsoft.PowerShell.SecretStore.

## Key Features

### ✅ **Automated Setup**
- **One-time setup process** that runs automatically on first execution
- **Intelligent detection** of existing credentials to prevent duplicate setup
- **User-friendly prompts** with skip options for each credential type
- **Setup completion tracking** to prevent re-running completed setup

### ✅ **SecretStore Integration**
- **Automatic installation** of Microsoft.PowerShell.SecretManagement and Microsoft.PowerShell.SecretStore modules
- **Secure vault creation** with proper configuration for automated access
- **Credential validation** including Jira connection testing
- **Fallback support** to encrypted file storage if SecretStore fails

### ✅ **UAT Mode Support**
- **No Active Directory dependency** - script runs fully in UAT environments
- **Operation simulation** for all AD-dependent functions
- **Clear UAT mode indicators** in all output messages
- **Seamless fallback** when AD module is not available

### ✅ **Enhanced Security**
- **Secure credential collection** using Read-Host -AsSecureString
- **Format validation** for usernames and API tokens
- **Encrypted storage** with user-scoped access
- **Audit trail integration** for all credential operations

## Quick Start Guide

### First Run (Automated Setup)

1. **Run the script normally:**
   ```powershell
   .\JML_v1.12.ps1
   ```

2. **Follow the automated setup prompts:**
   - The system will detect this is the first run
   - You'll be prompted to set up automated credential management
   - Enter 'Y' to proceed with setup

3. **Provide credentials when prompted:**
   - **Jira Username**: Your email address for Jira access
   - **Jira API Token**: Generate at https://id.atlassian.com/manage-profile/security/api-tokens
   - **SMTP Credentials**: Only if email notifications are enabled
   - Type 'skip' for any credential you want to configure later

4. **Setup completion:**
   - The system will test your Jira connection
   - A completion marker will be created to prevent re-setup
   - You can now use the script normally

### Manual Setup

If you prefer to run setup manually or need to reconfigure:

```powershell
# Run full setup with module installation
.\JML_v1.12.ps1 -RunSetup

# Then select option 3 (Full setup)
```

### UAT Mode (Without Active Directory)

The script now runs seamlessly in UAT environments:

```powershell
# Explicitly disable AD (optional - auto-detected)
.\JML_v1.12.ps1 -DisableActiveDirectory

# Or run normally - AD absence is auto-detected
.\JML_v1.12.ps1
```

## Configuration

### Default Settings

The system uses these default configurations (from AdminAccountConfig.psd1):

```powershell
Security = @{
    CredentialStorage = @{
        PrimaryMethod = "SecretManagement"
        FallbackMethods = @("CredentialManager", "EncryptedFile")
        SecretVaultName = "AdminAccountVault"
        CredentialNames = @{
            JiraUsername = "AdminScript-JiraUsername"
            JiraApiToken = "AdminScript-JiraApiToken"
            SmtpCredentials = "AdminScript-SmtpCredentials"
        }
    }
}
```

### Customization

You can customize the credential storage by modifying AdminAccountConfig.psd1:

- **Change vault name**: Modify `SecretVaultName`
- **Change credential names**: Update the `CredentialNames` hashtable
- **Change primary method**: Set `PrimaryMethod` to "EncryptedFile" or "CredentialManager"
- **Modify fallback order**: Reorder the `FallbackMethods` array

## Troubleshooting

### Common Issues

**1. SecretStore Module Installation Fails**
```
Solution: Run PowerShell as Administrator or use manual installation:
Install-Module Microsoft.PowerShell.SecretManagement -Scope CurrentUser
Install-Module Microsoft.PowerShell.SecretStore -Scope CurrentUser
```

**2. Vault Access Requires Password**
```
Solution: The system tries to configure password-free access. If prompted:
- Enter a password when requested
- Or reconfigure: Set-SecretStoreConfiguration -Authentication None -Interaction None
```

**3. Jira Connection Test Fails**
```
Solution: Credentials are still stored. Verify manually:
- Check your API token is correct
- Verify Jira server URL in configuration
- Ensure your account has API access
```

**4. Setup Runs Every Time**
```
Solution: Check if completion marker exists:
- Look for .\Modules\.credential-setup-complete
- If missing, setup will re-run automatically
- Delete file to force re-setup
```

### Reset Setup

To completely reset the credential setup:

```powershell
# Remove completion marker
Remove-Item ".\Modules\.credential-setup-complete" -Force

# Remove vault (optional)
Unregister-SecretVault -Name "AdminAccountVault"

# Run setup again
.\JML_v1.12.ps1 -RunSetup
```

## Testing

Use the provided test script to validate your setup:

```powershell
# Test all functionality
.\Test-AutomatedCredentialManagement.ps1

# Test only SecretStore functionality
.\Test-AutomatedCredentialManagement.ps1 -TestSecretStoreOnly

# Test only UAT mode functionality
.\Test-AutomatedCredentialManagement.ps1 -TestUATModeOnly

# Clean up test environment first
.\Test-AutomatedCredentialManagement.ps1 -CleanupFirst
```

## Benefits

### For Administrators
- **Reduced setup time**: One-time automated configuration
- **Enhanced security**: Encrypted credential storage
- **UAT compatibility**: No AD dependency for testing
- **Simplified deployment**: Works out-of-the-box in most environments

### For End Users
- **Seamless experience**: Credentials stored securely after first setup
- **No repeated prompts**: Automated credential retrieval
- **Clear feedback**: Informative messages about UAT vs Production mode
- **Graceful fallbacks**: System continues working even if components fail

### For IT Operations
- **Reduced support tickets**: Self-configuring system
- **Better audit trails**: All credential operations logged
- **Flexible deployment**: Works in various environments
- **Maintainable code**: Modular architecture with clear separation

## Security Considerations

- **Credentials are encrypted** using Windows DPAPI (user-scoped)
- **No plaintext storage** - all sensitive data uses SecureString
- **Audit logging** tracks all credential access attempts
- **Vault isolation** - each user has their own credential vault
- **Fallback security** - encrypted file storage as backup method

## Support

For issues or questions:
1. Check the troubleshooting section above
2. Run the test script to identify specific problems
3. Review log files for detailed error information
4. Use `-RunSetup` to reconfigure if needed

The automated credential management system is designed to be self-healing and user-friendly while maintaining enterprise-grade security standards.