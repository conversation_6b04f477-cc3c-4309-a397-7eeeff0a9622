# Minimal WPF fix - only fix what prevents parsing
$scriptPath = "OnboardingFromJiraGUI.ps1"

Write-Host "Applying minimal WPF parsing fix..." -ForegroundColor Yellow

# Read as lines to avoid issues with large content
$lines = Get-Content $scriptPath

for ($i = 0; $i -lt $lines.Count; $i++) {
    $line = $lines[$i]
    
    # Only fix function parameter declarations
    if ($line -match '\[System\.Windows\.Controls\.Grid\]\$') {
        $lines[$i] = $line -replace '\[System\.Windows\.Controls\.Grid\]', '[Object]'
        Write-Host "Fixed Grid parameter on line $($i+1)" -ForegroundColor Yellow
    }
    
    if ($line -match '\[System\.Windows\.Controls\.DockPanel\]\$') {
        $lines[$i] = $line -replace '\[System\.Windows\.Controls\.DockPanel\]', '[Object]'
        Write-Host "Fixed DockPanel parameter on line $($i+1)" -ForegroundColor Yellow
    }
}

# Write back
$lines | Set-Content $scriptPath -Encoding UTF8

Write-Host "Applied minimal WPF parsing fix" -ForegroundColor Green