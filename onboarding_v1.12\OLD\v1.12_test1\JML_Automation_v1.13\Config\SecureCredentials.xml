<Objs Version="*******" xmlns="http://schemas.microsoft.com/powershell/2004/04">
  <Obj RefId="0">
    <TN RefId="0">
      <T>System.Collections.Hashtable</T>
      <T>System.Object</T>
    </TN>
    <DCT>
      <En>
        <S N="Key">AdminScript-JiraUsername</S>
        <S N="Value"><EMAIL></S>
      </En>
      <En>
        <S N="Key">AdminScript-JiraServerUrl</S>
        <S N="Value">https://jeragm.atlassian.net</S>
      </En>
      <En>
        <S N="Key">AdminScript-JiraApiToken</S>
        <SS N="Value">01000000d08c9ddf0115d1118c7a00c04fc297eb01000000ca806fdaf180dc46a2c68d22cb558b51000000000200000000001066000000010000200000008a83ab8804d495858aeeb12e1c61c370f8a0eb6ce6df1dab5ac98c3c79d28034000000000e800000000200002000000007626ec3378bf0e8824eaab52c6d743d8a21ca0cd602fa58b49dec2a0282942f9001000000ea22d025f26e4c58567e893880f799d355bd8f868e7a919a6534c448f75740b6f349e74bfef4a9926a94af4cbc08c8a53ecbf96153961d9305d7a67e6744d9b53d7146cfab93ca63241280fd9a09fcedae495df419b3d79039c910b3cf838034f3f3322918ebdf09a4e29437e6b311b31c9c2509db19eee8e609bd54d9293dd73016d9c2c8f175937213291dcec89436d4f740cb908e2c92065bc7e6ac5e6d9c159626cfafc63d0c0c43f8ae8074f6aa783ad7390dc53fb53f9111ea7c0fe2f7934fefa52e5e46afe2d671769475672082fa1d84992127712a1b3dc648c41dbc13ad54b4081c7f27101ad615675482d7d7d0144118e73e910b83d70e4c15a171e102cf21d784f9467387501ad5b2aae7e598185f71a0c579e3cebf1ebbcf00e5b6fbfa6d728edc2b26020f4c48b1d9e2b1cc63529111e0cee82bdee26c5629a4b0f3f538cf13b20e07c862bb067702f024dafc4ae3c0d7b8ca007a6fa499244ffec670895780431e87f2acb4ce5410460f37092743dd8609732ca4b8383e72503e0c48a93e0bc30e6eb017cfb3d2b84000000056ea64349f3ee6055563cd060e06102b05d1f58a8901c341fff63a6e92ec63c903c7ef15f8f84715571c66a41369b923b2a631b551b5c74046d6d4c962ae241d</SS>
      </En>
    </DCT>
  </Obj>
</Objs>