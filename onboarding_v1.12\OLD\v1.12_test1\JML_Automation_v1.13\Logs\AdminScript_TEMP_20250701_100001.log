﻿2025-07-01 10:00:01.687 | akinje          | INFO     | JML Automation Script v1.13 starting...
2025-07-01 10:00:01.701 | akinje          | DEBUG    | Script path: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13
2025-07-01 10:00:01.715 | akinje          | DEBUG    | PowerShell version: 5.1.19041.5848
2025-07-01 10:00:01.967 | akinje          | DEBUG    | Execution policy: Bypass
2025-07-01 10:00:01.975 | akinje          | DEBUG    | User: akinje
2025-07-01 10:00:01.988 | akinje          | DEBUG    | Computer: JGM-CG7THR3
2025-07-01 10:00:02.298 | akinje          | INFO     | === JML ENVIRONMENT VALIDATION ===
2025-07-01 10:00:02.316 | akinje          | INFO     | Verifying PowerShell Version...
2025-07-01 10:00:02.335 | akinje          | INFO     | === JML ENVIRONMENT VALIDATION ===
2025-07-01 10:00:02.344 | akinje          | INFO     | Verifying PowerShell Version...
2025-07-01 10:00:02.420 | akinje          | WARNING  | WARNING: PowerShell 7 or higher is recommended for optimal performance and security.
2025-07-01 10:00:02.502 | akinje          | INFO     | Current version: PowerShell 5.1.19041.5848
2025-07-01 10:00:02.555 | akinje          | WARNING  | PowerShell 7+ recommended. Current: 5.1.19041.5848
2025-07-01 10:00:02.679 | akinje          | INFO     | Searching for PowerShell 7 installation...
2025-07-01 10:00:02.970 | akinje          | INFO     |   Checking: C:\Program Files\PowerShell\7\pwsh.exe
2025-07-01 10:00:05.986 | akinje          | INFO     |   Found PowerShell 7.5.1 at: C:\Program Files\PowerShell\7\pwsh.exe
2025-07-01 10:00:06.394 | akinje          | INFO     | PowerShell 7 is installed at: C:\Program Files\PowerShell\7\pwsh.exe
2025-07-01 10:00:06.806 | akinje          | INFO     | Options:
2025-07-01 10:00:06.873 | akinje          | INFO     |   1. Continue with current PowerShell 5.x
2025-07-01 10:00:08.172 | akinje          | INFO     |   3. Exit and manually switch
2025-07-01 10:00:08.563 | akinje          | INFO     | Select option (1-3) [Default: 2 in 10 seconds]: 
2025-07-01 10:00:18.773 | akinje          | INFO     | 2 (default)
2025-07-01 10:00:18.960 | akinje          | INFO     | === POWERSHELL 7 EXECUTION OPTIONS ===
2025-07-01 10:00:19.206 | akinje          | INFO     | PowerShell 7 is available at: C:\Program Files\PowerShell\7\pwsh.exe
2025-07-01 10:00:19.338 | akinje          | INFO     | How would you like to run the script in PowerShell 7?
2025-07-01 10:00:19.487 | akinje          | INFO     |   [N] New Window - Launch in a new PowerShell 7 window
2025-07-01 10:00:19.593 | akinje          | INFO     |   [C] Current Window - Run PowerShell 7 in this window (default)
2025-07-01 10:00:19.675 | akinje          | INFO     | Choose option (N/C) - Waiting 5 seconds (default: Current Window)...
2025-07-01 10:00:24.996 | akinje          | INFO     | Selected: Current Window (default)
2025-07-01 10:00:25.099 | akinje          | INFO     | Starting PowerShell 7 in current window...
2025-07-01 10:00:25.159 | akinje          | INFO     | Script will continue in PowerShell 7...
