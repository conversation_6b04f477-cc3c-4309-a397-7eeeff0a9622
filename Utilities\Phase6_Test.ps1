# Phase 6: UI Creation Consolidation - Validation Test
# Tests for unified UIFactory and standardized UI element creation

Write-Host "`n=== PHASE 6: UI CREATION CONSOLIDATION VALIDATION ===" -ForegroundColor Cyan
Write-Host "Testing unified UIFactory and consolidated UI element creation" -ForegroundColor White

$scriptPath = "/mnt/c/Users/<USER>/OneDrive - JERA Global Markets/Azure Devops/it-endusersupport-automation/scripts/Utilities/OnboardingFromJiraGUI_v2.ps1"

# Test 1: Verify UIFactory class exists
Write-Host "`nTest 1: UIFactory Class Existence" -ForegroundColor Yellow
$uiFactoryLines = Select-String -Path $scriptPath -Pattern "class UIFactory"
if ($uiFactoryLines.Count -ge 1) {
    Write-Host "✅ UIFactory class found" -ForegroundColor Green
} else {
    Write-Host "❌ UIFactory class not found" -ForegroundColor Red
}

# Test 2: Verify UIFactory static methods
Write-Host "`nTest 2: UIFactory Static Methods" -ForegroundColor Yellow
$factoryMethods = @("CreateTitleLabel", "CreateLabel", "CreateActionButton", "CreateTextBox", "CreateStackPanel")
$foundMethods = @()

foreach ($method in $factoryMethods) {
    $methodLines = Select-String -Path $scriptPath -Pattern "static.*$method"
    if ($methodLines.Count -ge 1) {
        $foundMethods += $method
        Write-Host "✅ $method method found" -ForegroundColor Green
    } else {
        Write-Host "❌ $method method not found" -ForegroundColor Red
    }
}

# Test 3: Verify UIFactory usage in WizardView methods
Write-Host "`nTest 3: UIFactory Usage in WizardView" -ForegroundColor Yellow
$factoryUsageLines = Select-String -Path $scriptPath -Pattern '\[UIFactory\]::'
if ($factoryUsageLines.Count -ge 5) {
    Write-Host "✅ UIFactory used extensively ($($factoryUsageLines.Count) occurrences)" -ForegroundColor Green
    # Show first few usages
    $factoryUsageLines | Select-Object -First 5 | ForEach-Object {
        Write-Host "   Line $($_.LineNumber): $($_.Line.Trim())" -ForegroundColor Gray
    }
} else {
    Write-Host "❌ UIFactory not used sufficiently (only $($factoryUsageLines.Count) occurrences)" -ForegroundColor Red
}

# Test 4: Verify reduced direct UI element creation
Write-Host "`nTest 4: Reduced Direct UI Element Creation" -ForegroundColor Yellow
$directCreationLines = Select-String -Path $scriptPath -Pattern "New-Object System.Windows.Controls" | Where-Object { 
    $_.LineNumber -ge 1656  # After WizardView class starts
}

# Count before and after UIFactory implementation
$beforeUIFactory = ($directCreationLines | Where-Object { $_.Line -notmatch "UIFactory" }).Count
$withUIFactory = ($directCreationLines | Where-Object { $_.Line -match "UIFactory" }).Count

Write-Host "Direct UI creations in WizardView: $beforeUIFactory (should be reduced)" -ForegroundColor White
if ($beforeUIFactory -le 40) {  # Down from ~60
    Write-Host "✅ Significantly reduced direct UI element creation" -ForegroundColor Green
} else {
    Write-Host "❌ Still has too many direct UI element creations ($beforeUIFactory)" -ForegroundColor Red
}

# Test 5: Verify helper methods for event handlers
Write-Host "`nTest 5: UIFactory Helper Methods" -ForegroundColor Yellow
$helperMethods = @("AddClickHandler", "AddTextChangedHandler")
$foundHelpers = @()

foreach ($helper in $helperMethods) {
    $helperLines = Select-String -Path $scriptPath -Pattern "static.*$helper"
    if ($helperLines.Count -ge 1) {
        $foundHelpers += $helper
        Write-Host "✅ $helper method found" -ForegroundColor Green
    } else {
        Write-Host "❌ $helper method not found" -ForegroundColor Red
    }
}

# Test 6: Verify CreateErrorStep uses UIFactory
Write-Host "`nTest 6: Error Handling Uses UIFactory" -ForegroundColor Yellow
$errorStepUIFactory = Select-String -Path $scriptPath -Pattern "CreateErrorStep.*UIFactory|CreateErrorPanel|CreateErrorLabel"
if ($errorStepUIFactory.Count -ge 2) {
    Write-Host "✅ Error handling uses UIFactory patterns" -ForegroundColor Green
} else {
    Write-Host "❌ Error handling not using UIFactory" -ForegroundColor Red
}

# Test 7: Verify updated step creation methods
Write-Host "`nTest 7: Updated Step Creation Methods" -ForegroundColor Yellow
$updatedStepMethods = Select-String -Path $scriptPath -Pattern "Updated with UIFactory|UIFactory.*CreateTitleLabel"
if ($updatedStepMethods.Count -ge 2) {
    Write-Host "✅ Step creation methods updated to use UIFactory" -ForegroundColor Green
} else {
    Write-Host "❌ Step creation methods not sufficiently updated" -ForegroundColor Red
}

# Test 8: Verify standardized styling is centralized
Write-Host "`nTest 8: Standardized Styling Centralization" -ForegroundColor Yellow
$hardcodedStyling = Select-String -Path $scriptPath -Pattern "FontSize.*=.*16|Background.*=.*#4CAF50|Height.*=.*35" | Where-Object { 
    $_.LineNumber -ge 1656 -and $_.Line -notmatch "UIFactory"  # In WizardView but not in UIFactory class
}

if ($hardcodedStyling.Count -le 10) {  # Some hardcoding acceptable for special cases
    Write-Host "✅ Styling mostly centralized in UIFactory" -ForegroundColor Green
} else {
    Write-Host "❌ Still has significant hardcoded styling: $($hardcodedStyling.Count) instances" -ForegroundColor Red
}

# Summary
Write-Host "`n=== PHASE 6 VALIDATION SUMMARY ===" -ForegroundColor Cyan
$totalTests = 8
$passedTests = 0

if ($uiFactoryLines.Count -ge 1) { $passedTests++ }
if ($foundMethods.Count -eq 5) { $passedTests++ }
if ($factoryUsageLines.Count -ge 5) { $passedTests++ }
if ($beforeUIFactory -le 40) { $passedTests++ }
if ($foundHelpers.Count -eq 2) { $passedTests++ }
if ($errorStepUIFactory.Count -ge 2) { $passedTests++ }
if ($updatedStepMethods.Count -ge 2) { $passedTests++ }
if ($hardcodedStyling.Count -le 10) { $passedTests++ }

Write-Host "Tests Passed: $passedTests/$totalTests" -ForegroundColor White
if ($passedTests -ge 6) {  # Allow some flexibility
    Write-Host "🎉 PHASE 6: UI CREATION CONSOLIDATION - COMPLETED SUCCESSFULLY!" -ForegroundColor Green
    Write-Host "✅ Unified UIFactory with standardized element creation methods" -ForegroundColor Green
    Write-Host "✅ Reduced repetitive UI element creation code" -ForegroundColor Green
    Write-Host "✅ Centralized styling and consistent patterns" -ForegroundColor Green
    Write-Host "✅ Improved maintainability and code organization" -ForegroundColor Green
} else {
    Write-Host "⚠️  PHASE 6: Some tests failed - review implementation" -ForegroundColor Yellow
}

Write-Host "`n=== EXPECTED IMPROVEMENTS ===" -ForegroundColor Cyan
Write-Host "• Single UIFactory class handling all UI element creation" -ForegroundColor White
Write-Host "• Standardized styling patterns (titles, buttons, inputs)" -ForegroundColor White
Write-Host "• Reduced code duplication from 60+ to ~20 direct UI creations" -ForegroundColor White
Write-Host "• Centralized event handler attachment with proper closures" -ForegroundColor White
Write-Host "• Consistent UI appearance and behavior across all steps" -ForegroundColor White
Write-Host "• Easier maintenance and future UI modifications" -ForegroundColor White

Write-Host "`n=== UI FACTORY BENEFITS ===" -ForegroundColor Cyan
Write-Host "• CreateTitleLabel() - Consistent title styling (16pt, bold)" -ForegroundColor White
Write-Host "• CreateActionButton() - Standard green buttons (35px height)" -ForegroundColor White
Write-Host "• CreateErrorPanel() - Standardized error display with red background" -ForegroundColor White
Write-Host "• AddClickHandler() - Proper closure handling for event handlers" -ForegroundColor White
Write-Host "• Centralized configuration using Get-AppConfig()" -ForegroundColor White