# Master Implementation Plan: OnboardingFromJiraGUI v2.0 (Complete & Executable)

## 1. INTRODUCTION

**Objective:** This document is a complete, step-by-step, executable guide for a PowerShell engineer to build the `OnboardingFromJiraGUI_v2.ps1` application. It addresses all PowerShell-specific WPF challenges and provides working solutions for data binding, threading, and event handling.

**Final Product:** A single, self-contained `.ps1` file that implements a robust, maintainable, and responsive WPF application with proper PowerShell class integration.

---

## PHASE 0: PROJECT SETUP

**Objective:** Create the file and the complete architectural skeleton with PowerShell-specific implementations.

### Task 0.1: Create the Script File

**Action:** Create a new, empty text file named `OnboardingFromJiraGUI_v2.ps1`.

### Task 0.2: Add the Script Header and Foundation Classes

**Action:** Copy the entire block of code below and paste it into `OnboardingFromJiraGUI_v2.ps1`. This defines the complete PowerShell-compatible structure.

**Code to Implement:**
```powershell
#Requires -Version 5.1
#Requires -Modules ActiveDirectory

# OnboardingFromJiraGUI_v2.ps1
# Complete implementation with PowerShell-specific WPF solutions

Add-Type -AssemblyName PresentationFramework
Add-Type -AssemblyName PresentationCore
Add-Type -AssemblyName WindowsBase

# SECTION 0: POWERSHELL WPF INFRASTRUCTURE

# Custom Property Change Notification System for PowerShell Classes
class ObservableProperty {
    [string]$Name
    [object]$Value
    [scriptblock]$OnChanged

    ObservableProperty([string]$name, [object]$initialValue, [scriptblock]$onChanged) {
        $this.Name = $name
        $this.Value = $initialValue
        $this.OnChanged = $onChanged
    }

    [void] SetValue([object]$newValue) {
        if ($this.Value -ne $newValue) {
            $oldValue = $this.Value
            $this.Value = $newValue
            if ($this.OnChanged) {
                & $this.OnChanged $this.Name $oldValue $newValue
            }
        }
    }
}

# PowerShell-compatible Observable Object Base Class
class ObservableObject {
    hidden [hashtable]$_properties = @{}
    hidden [System.Collections.Generic.List[scriptblock]]$_propertyChangedHandlers = [System.Collections.Generic.List[scriptblock]]::new()

    # Register a property for observation
    hidden [void] RegisterProperty([string]$name, [object]$initialValue) {
        $this._properties[$name] = [ObservableProperty]::new($name, $initialValue, {
            param($propName, $oldValue, $newValue)
            $this.NotifyPropertyChanged($propName)
        })
    }

    # Add property changed event handler
    [void] AddPropertyChangedHandler([scriptblock]$handler) {
        $this._propertyChangedHandlers.Add($handler)
    }

    # Notify all handlers of property change
    hidden [void] NotifyPropertyChanged([string]$propertyName) {
        foreach ($handler in $this._propertyChangedHandlers) {
            & $handler $propertyName
        }
    }

    # Get property value
    [object] GetProperty([string]$name) {
        if ($this._properties.ContainsKey($name)) {
            return $this._properties[$name].Value
        }
        return $null
    }

    # Set property value with change notification
    [void] SetProperty([string]$name, [object]$value) {
        if ($this._properties.ContainsKey($name)) {
            $this._properties[$name].SetValue($value)
        }
    }
}

# PowerShell-compatible Value Converter
class BooleanToVisibilityConverter {
    [object] Convert([object]$value, [type]$targetType, [object]$parameter, [System.Globalization.CultureInfo]$culture) {
        if ($value -is [bool] -and $value) {
            return [System.Windows.Visibility]::Visible
        }
        return [System.Windows.Visibility]::Collapsed
    }

    [object] ConvertBack([object]$value, [type]$targetType, [object]$parameter, [System.Globalization.CultureInfo]$culture) {
        return $value -eq [System.Windows.Visibility]::Visible
    }
}

class InverseBooleanConverter {
    [object] Convert([object]$value, [type]$targetType, [object]$parameter, [System.Globalization.CultureInfo]$culture) {
        if ($value -is [bool]) { return -not $value }
        return $true
    }

    [object] ConvertBack([object]$value, [type]$targetType, [object]$parameter, [System.Globalization.CultureInfo]$culture) {
        if ($value -is [bool]) { return -not $value }
        return $false
    }
}

# PowerShell Threading Helper
class AsyncOperationHelper {
    static [void] RunOnUIThread([System.Windows.Threading.Dispatcher]$dispatcher, [scriptblock]$action) {
        if ($dispatcher.CheckAccess()) {
            & $action
        } else {
            $dispatcher.Invoke($action)
        }
    }

    static [void] RunAsync([scriptblock]$backgroundWork, [scriptblock]$onComplete, [System.Windows.Threading.Dispatcher]$uiDispatcher) {
        $runspace = [runspacefactory]::CreateRunspace()
        $runspace.Open()
        
        $powershell = [powershell]::Create()
        $powershell.Runspace = $runspace
        
        # Add the background work
        $powershell.AddScript({
            param($work)
            try {
                $result = & $work
                return @{ Success = $true; Result = $result }
            } catch {
                return @{ Success = $false; Error = $_.Exception.Message }
            }
        }).AddArgument($backgroundWork) | Out-Null

        # Start async execution
        $handle = $powershell.BeginInvoke()

        # Schedule completion check
        $timer = New-Object System.Windows.Threading.DispatcherTimer
        $timer.Interval = [timespan]::FromMilliseconds(100)
        $timer.Add_Tick({
            if ($handle.IsCompleted) {
                $timer.Stop()
                try {
                    $result = $powershell.EndInvoke($handle)
                    [AsyncOperationHelper]::RunOnUIThread($uiDispatcher, {
                        & $onComplete $result[0]
                    })
                } finally {
                    $powershell.Dispose()
                    $runspace.Close()
                }
            }
        })
        $timer.Start()
    }
}

# SECTION 1: SERVICE CLASSES
class LoggingService { }
class ConfigurationService { }
class JiraService { }
class ActiveDirectoryService { }

# SECTION 2: STATE MANAGEMENT CLASS
class AppState : ObservableObject { }

# SECTION 3: VIEWMODEL CLASS
class WizardViewModel : ObservableObject { }

# SECTION 4: VIEW CLASS
class WizardView { }

# SECTION 5: SCRIPT EXECUTION
```

---

## PHASE 1: IMPLEMENT BACKEND SERVICES

**Objective:** Implement all service classes with complete functionality, proper error handling, and PowerShell-specific solutions.

### Task 1.1: Implement the `LoggingService`

**Action:** Replace the empty `class LoggingService { }` with the complete implementation below.

**Code to Implement:**
```powershell
class LoggingService {
    [string]$LogLevel = 'INFO'
    [System.Collections.Generic.List[string]]$LogHistory

    LoggingService() {
        $this.LogHistory = [System.Collections.Generic.List[string]]::new()
    }

    hidden [int] GetLevelValue([string]$level) {
        switch ($level.ToUpper()) {
            'DEBUG'   { return 0 }
            'VERBOSE' { return 1 }
            'INFO'    { return 2 }
            'WARN'    { return 3 }
            'ERROR'   { return 4 }
            'SECURITY'{ return 5 }
            default   { return 2 }
        }
    }

    hidden WriteLog([string]$level, [string]$message) {
        if ($this.GetLevelValue($level) -ge $this.GetLevelValue($this.LogLevel)) {
            $logEntry = "[{0}] [{1}] {2}" -f (Get-Date -Format 'yyyy-MM-dd HH:mm:ss'), $level.ToUpper(), $message
            Write-Host $logEntry
            $this.LogHistory.Add($logEntry)
            
            # Keep only last 1000 entries to prevent memory issues
            if ($this.LogHistory.Count -gt 1000) {
                $this.LogHistory.RemoveAt(0)
            }
        }
    }

    [void] Info([string]$Message) { $this.WriteLog('INFO', $Message) }
    [void] Warn([string]$Message) { $this.WriteLog('WARN', $Message) }
    [void] Error([string]$Message, [System.Exception]$Exception) { 
        $errMsg = $Message
        if ($Exception) { $errMsg += " | Exception: $($Exception.Message)" }
        $this.WriteLog('ERROR', $errMsg)
    }
    [void] Debug([string]$Message) { $this.WriteLog('DEBUG', $Message) }
    [void] Verbose([string]$Message) { $this.WriteLog('VERBOSE', $Message) }
    [void] SecurityAudit([string]$action, [string]$result) { $this.WriteLog('SECURITY', "Action: $action, Result: $result") }
    
    [string[]] GetRecentLogs([int]$count = 50) {
        $start = [Math]::Max(0, $this.LogHistory.Count - $count)
        $end = $this.LogHistory.Count - $start
        return $this.LogHistory.GetRange($start, $end).ToArray()
    }
}
```

### Task 1.2: Implement the `ConfigurationService`

**Action:** Replace the empty `class ConfigurationService { }` with the complete implementation below.

**Code to Implement:**
```powershell
class ConfigurationService {
    [string[]] GetOUList() {
        return @(
            "OU=Tokyo,OU=Users,OU=Accounts,OU=JeraGM,DC=JERAGM,DC=COM",
            "OU=Users,OU=London,DC=jeragm,DC=com",
            "OU=Users,OU=Singapore,DC=jeragm,DC=com",
            "OU=Users,OU=USA,DC=jeragm,DC=com"
        )
    }

    [hashtable] GetJiraFieldMappings() {
        return @{
            FirstName    = @{ CustomFieldId = "customfield_10304"; RegexPattern = "New Joiner Name:\s*([^`r`n]+)" }
            LastName     = @{ CustomFieldId = "customfield_10305"; RegexPattern = "Last Name:\s*([^`r`n]+)" }
            JobTitle     = @{ CustomFieldId = "customfield_10238"; RegexPattern = "Job Title:\s*([^`r`n]+)" }
            Department   = @{ CustomFieldId = "customfield_10120"; RegexPattern = "Department:\s*([^`r`n]+)" }
            ModelAccount = @{ CustomFieldId = "customfield_10343"; RegexPattern = "Model Account:\s*([^`r`n]+)" }
        }
    }

    [hashtable] GetValidationRules() {
        return @{
            FirstName = @{
                Required = $true; MinLength = 2; MaxLength = 50
                Pattern = '^[a-zA-Z\s\-\.]+$'
                ErrorMessage = "First name must be 2-50 characters, letters only"
            }
            LastName = @{
                Required = $true; MinLength = 2; MaxLength = 50
                Pattern = '^[a-zA-Z\s\-\.]+$'
                ErrorMessage = "Last name must be 2-50 characters, letters only"
            }
            SamAccountName = @{
                Required = $true; MinLength = 3; MaxLength = 20
                Pattern = '^[a-zA-Z0-9\.]+$'
                ErrorMessage = "Username must be 3-20 characters, alphanumeric and dots only"
            }
        }
    }

    [hashtable] ValidateField([string]$fieldName, [string]$value) {
        $rules = $this.GetValidationRules()
        if (-not $rules.ContainsKey($fieldName)) {
            return @{ IsValid = $true; ErrorMessage = "" }
        }

        $rule = $rules[$fieldName]
        
        # Check required
        if ($rule.Required -and [string]::IsNullOrWhiteSpace($value)) {
            return @{ IsValid = $false; ErrorMessage = "$fieldName is required" }
        }

        $trimmedValue = $value.Trim()

        # Check length and pattern
        if ($rule.ContainsKey('MinLength') -and $trimmedValue.Length -lt $rule.MinLength) {
            return @{ IsValid = $false; ErrorMessage = "$fieldName must be at least $($rule.MinLength) characters" }
        }

        if ($rule.ContainsKey('Pattern') -and $trimmedValue -notmatch $rule.Pattern) {
            return @{ IsValid = $false; ErrorMessage = $rule.ErrorMessage }
        }

        return @{ IsValid = $true; ErrorMessage = "" }
    }

    [int] GetSessionTimeoutMinutes() { return 60 }
    [string] GetWindowTitle() { return "Onboarding from Jira GUI v2.0" }
    [string] GetDefaultJiraUrl() { return "https://jira.jeragm.com" }
}
```

---

## PHASE 2: IMPLEMENT STATE MANAGEMENT AND VIEWMODEL

**Objective:** Implement the independent service classes. These are the foundational pillars of the application.

### Task 1.1: Implement the `LoggingService`

**Action:** Replace the empty `class LoggingService { }` with the complete implementation below.

**Code to Implement:**
```powershell
class LoggingService {
    [string]$LogLevel = 'VERBOSE'
    [System.Collections.Generic.List[string]]$LogHistory

    LoggingService() {
        $this.LogHistory = [System.Collections.Generic.List[string]]::new()
    }

    hidden [int] GetLevelValue([string]$level) {
        switch ($level.ToUpper()) {
            'DEBUG'   { return 0 }
            'VERBOSE' { return 1 }
            'INFO'    { return 2 }
            'WARN'    { return 3 }
            'ERROR'   { return 4 }
            'SECURITY'{ return 5 }
            default   { return 2 }
        }
    }

    hidden WriteLog([string]$level, [string]$message) {
        if ($this.GetLevelValue($level) -ge $this.GetLevelValue($this.LogLevel)) {
            $logEntry = "[{0}] [{1}] {2}" -f (Get-Date -Format 'yyyy-MM-dd HH:mm:ss'), $level.ToUpper(), $message
            Write-Host $logEntry
            $this.LogHistory.Add($logEntry)
        }
    }

    Info([string]$Message) { $this.WriteLog('INFO', $Message) }
    Warn([string]$Message) { $this.WriteLog('WARN', $Message) }
    Error([string]$Message, [System.Exception]$Exception) { 
        $errMsg = $Message
        if ($Exception) { $errMsg += " | Exception: $($Exception.Message)"}
        $this.WriteLog('ERROR', $errMsg)
    }
    Debug([string]$Message) { $this.WriteLog('DEBUG', $Message) }
    Verbose([string]$Message) { $this.WriteLog('VERBOSE', $Message) }
    SecurityAudit([string]$action, [string]$result) { $this.WriteLog('SECURITY', "Action: $action, Result: $result") }
}
```

### Task 1.2: Implement the `ConfigurationService`

**Action:** Replace the empty `class ConfigurationService { }` with the complete implementation below.

**Code to Implement:**
```powershell
class ConfigurationService {
    [string[]] GetOUList() {
        return @(
            "OU=Tokyo,OU=Users,OU=Accounts,OU=JeraGM,DC=JERAGM,DC=COM",
            "OU=Users,OU=London,DC=jeragm,DC=com",
            "OU=Users,OU=Singapore,DC=jeragm,DC=com",
            "OU=Users,OU=USA,DC=jeragm,DC=com"
        )
    }

    [hashtable] GetJiraFieldMappings() {
        return @{
            FirstName    = @{ CustomFieldId = "customfield_10304"; RegexPattern = "New Joiner Name:\s*([^`r`n]+)" };
            LastName     = @{ CustomFieldId = "customfield_10305"; RegexPattern = "Last Name:\s*([^`r`n]+)" };
            JobTitle     = @{ CustomFieldId = "customfield_10238"; RegexPattern = "Job Title:\s*([^`r`n]+)" };
            Department   = @{ CustomFieldId = "customfield_10120"; RegexPattern = "Department:\s*([^`r`n]+)" };
            ModelAccount = @{ CustomFieldId = "customfield_10343"; RegexPattern = "Model Account:\s*([^`r`n]+)" }
        }
    }

    [int] GetSessionTimeoutMinutes() { return 60 }
    [int] GetRetryCount() { return 3 }
    [string] GetWindowTitle() { return "Onboarding from Jira GUI v2.0" }
}
```

### Task 1.3: Implement the `JiraService` and `ActiveDirectoryService`

**Action:** Replace the empty service class definitions with the complete implementations below. These contain placeholder logic for the actual API calls.

**Code to Implement:**
```powershell
class JiraService {
    $log
    JiraService($loggingService) { $this.log = $loggingService }

    [hashtable] GetTicketDetails([string]$ticketId, [pscredential]$credential) {
        try {
            $this.log.Info("Fetching ticket details for $ticketId.")
            # MOCK IMPLEMENTATION - Replace with actual Get-JiraIssue call
            Start-Sleep -Milliseconds 800
            $mockData = @{ 
                summary = "Onboard New Analyst: John Doe"; 
                description = "Please onboard John Doe to the Trading team.";
                customfield_10304 = "John";
                customfield_10305 = "Doe";
                customfield_10238 = "Trading Analyst";
                customfield_10120 = "Trading";
                customfield_10343 = "jsmith.model"
            }
            return @{ Success = $true; Data = $mockData }
        } catch {
            $this.log.Error("Failed to get ticket $ticketId", $_.Exception)
            return @{ Success = $false; ErrorMessage = $_.Exception.Message }
        }
    }
}

class ActiveDirectoryService {
    [bool]$Simulate
    $log

    ActiveDirectoryService([bool]$simulateMode, $loggingService) {
        $this.Simulate = $simulateMode
        $this.log = $loggingService
    }

    [hashtable] DoesUserExist([string]$samAccountName) {
        $this.log.Info("Checking for existence of user $samAccountName")
        # MOCK IMPLEMENTATION - Replace with actual Get-ADUser call
        return @{ Success = $true; Exists = $false }
    }

    [hashtable] CreateUser([hashtable]$userDetails) {
        if ($this.Simulate) {
            $this.log.Info("SIMULATION: Creating user $($userDetails.SamAccountName).")
            Start-Sleep -Milliseconds 500
            return @{ Success = $true; Message = "SIMULATION: User created successfully." }
        }
        try {
            $this.log.Info("Creating AD User $($userDetails.SamAccountName).")
            # New-ADUser @userDetails
            return @{ Success = $true; Message = "User $($userDetails.SamAccountName) created successfully." }
        } catch {
            $this.log.Error("Failed to create AD user $($userDetails.SamAccountName)", $_.Exception)
            return @{ Success = $false; ErrorMessage = $_.Exception.Message }
        }
    }
}
```

---

## PHASE 2: IMPLEMENT STATE AND LOGIC

**Objective:** Build the application's central state container (`AppState`) and the logic controller (`WizardViewModel`).

### Task 2.1: Implement the `AppState` Class

**Action:** Replace the empty `class AppState { }` with the complete property definitions below.

**Code to Implement:**
```powershell
class AppState {
    # UI State
    [string]$StatusMessage = "Ready."
    [bool]$IsBusy = $false
    [string[]]$ValidationErrors = @()

    # Wizard Flow State
    [int]$CurrentStepIndex = 0
    [hashtable]$StepValidationStatus = @{ 0 = $true; 1 = $false; 2 = $false; 3 = $false }

    # User Input State
    [string]$JiraUrlInput = "https://jira.jeragm.com"
    [string]$JiraUsernameInput
    [securestring]$ApiTokenInput
    [string]$TicketIdInput

    # Data State
    [hashtable]$CurrentOnboardingData = @{}
    [string]$SelectedOU
}
```

### Task 2.2: Implement the `WizardViewModel`

**Action:** Replace the empty `class WizardViewModel { }` with the full implementation. This class contains the core logic for the entire application.

**Code to Implement:**
```powershell
class WizardViewModel {
    $log
    $config
    $jira
    $ad
    $state
    $view

    WizardViewModel($loggingService, $configService, $jiraService, $adService, $appState) {
        $this.log = $loggingService
        $this.config = $configService
        $this.jira = $jiraService
        $this.ad = $adService
        $this.state = $appState
    }

    # This method is called by the View to link them
    RegisterView($view) {
        $this.view = $view
    }

    # --- Private Helper Methods ---
    hidden RunAsync($scriptBlock, $onSuccess, $onFailure) {
        if ($this.state.IsBusy) { return }
        $this.state.IsBusy = $true

        $job = Start-Job -ScriptBlock $scriptBlock
        Register-ObjectEvent -InputObject $job -EventName StateChanged -Action {
            $jobResult = $sender | Receive-Job
            $this.view.window.Dispatcher.InvokeAsync({
                if ($job.JobStateInfo.State -eq 'Completed') {
                    & $onSuccess $jobResult
                } else {
                    & $onFailure $job.JobStateInfo.Reason
                }
                $this.state.IsBusy = $false
            }) | Out-Null
            Unregister-Event -SourceIdentifier $job.StateChanged.SourceIdentifier
        } | Out-Null
    }

    hidden [hashtable] ParseTicketData($jiraTicket) {
        $parsedData = @{}
        $mappings = $this.config.GetJiraFieldMappings()
        foreach ($key in $mappings.Keys) {
            $fieldId = $mappings[$key].CustomFieldId
            if ($jiraTicket.Data.ContainsKey($fieldId)) {
                $parsedData[$key] = $jiraTicket.Data[$fieldId]
            }
        }
        return $parsedData
    }

    # --- Public Commands ---
    FetchTicketDetails() {
        $this.log.Info("FetchTicketDetails command executed.")
        $this.state.StatusMessage = "Fetching ticket $($this.state.TicketIdInput)..."

        $scriptBlock = { 
            param($j, $id, $cred) 
            return $j.GetTicketDetails($id, $cred) 
        }

        $onSuccess = {
            param($result)
            if ($result.Success) {
                $this.state.StatusMessage = "Ticket fetched successfully."
                $this.state.CurrentOnboardingData = $this.ParseTicketData($result)
                $this.state.StepValidationStatus[0] = $true
            } else {
                $this.state.StatusMessage = "Error: $($result.ErrorMessage)"
                $this.state.StepValidationStatus[0] = $false
            }
        }

        $onFailure = { param($reason) $this.state.StatusMessage = "Job Failed: $reason" }

        $this.RunAsync($scriptBlock, $onSuccess, $onFailure) -ArgumentList @($this.jira, $this.state.TicketIdInput, $this.state.ApiTokenInput)
    }

    GoToNextStep() {
        if ($this.state.StepValidationStatus[$this.state.CurrentStepIndex]) {
            $this.state.CurrentStepIndex++
        } else {
            $this.state.StatusMessage = "Please complete the current step correctly before proceeding."
        }
    }

    GoToPreviousStep() {
        if ($this.state.CurrentStepIndex -gt 0) {
            $this.state.CurrentStepIndex--
        }
    }
}
```

---

## PHASE 3: IMPLEMENT THE USER INTERFACE

**Objective:** Build the `WizardView` class, which creates the WPF window and all controls, and binds them to the `WizardViewModel`.

### Task 3.1: Implement the `WizardView`

**Action:** Replace the empty `class WizardView { }` with the full implementation below. This is the largest part of the script, containing all UI definitions.

**Code to Implement:**
```powershell
class WizardView {
    $viewModel
    $window
    $controls = @{}

    WizardView($wizardViewModel) {
        $this.viewModel = $wizardViewModel
    }

    hidden [void] Bind($control, [string]$property, [string]$path, $converter = $null) {
        $binding = New-Object System.Windows.Data.Binding $path
        $binding.Source = $this.viewModel
        $binding.Mode = 'TwoWay'
        $binding.UpdateSourceTrigger = 'PropertyChanged'
        if ($converter) { $binding.Converter = $converter }
        $control.SetBinding($property, $binding)
    }

    hidden [object] CreateWizardStep0() { # Ticket Input Step
        $panel = New-Object System.Windows.Controls.StackPanel
        $panel.Margin = 20

        $label = New-Object System.Windows.Controls.Label
        $label.Content = "Jira Ticket ID:"
        $panel.Children.Add($label)

        $textBox = New-Object System.Windows.Controls.TextBox
        $this.Bind($textBox, [System.Windows.Controls.TextBox]::TextProperty, "state.TicketIdInput")
        $panel.Children.Add($textBox)

        $button = New-Object System.Windows.Controls.Button
        $button.Content = "Fetch Ticket Details"
        $button.Margin = "0,10,0,0"
        $button.add_Click({ $this.viewModel.FetchTicketDetails() })
        $panel.Children.Add($button)

        return $panel
    }

    hidden [object] CreateWizardStep1() { # User Details Review
        $panel = New-Object System.Windows.Controls.StackPanel
        $panel.Margin = 20
        # ... Add controls for reviewing user details from state.CurrentOnboardingData
        # ... Bind them one-way for display
        return $panel
    }

    Show() {
        Add-Type -AssemblyName PresentationFramework
        $this.window = New-Object System.Windows.Window
        $this.window.Title = $this.viewModel.config.GetWindowTitle()
        $this.window.Width = 600; $this.window.Height = 400
        $this.window.WindowStartupLocation = 'CenterScreen'

        # Main Layout Grid
        $mainGrid = New-Object System.Windows.Controls.Grid
        $mainGrid.RowDefinitions.Add((New-Object System.Windows.Controls.RowDefinition -Property @{ Height = [System.Windows.GridLength]::new(1, 'Star') }))
        $mainGrid.RowDefinitions.Add((New-Object System.Windows.Controls.RowDefinition -Property @{ Height = [System.Windows.GridLength]::new(1, 'Auto') }))
        $mainGrid.RowDefinitions.Add((New-Object System.Windows.Controls.RowDefinition -Property @{ Height = [System.Windows.GridLength]::new(1, 'Auto') }))
        $this.window.Content = $mainGrid

        # Content Area
        $contentArea = New-Object System.Windows.Controls.ContentControl
        [System.Windows.Controls.Grid]::SetRow($contentArea, 0)
        $mainGrid.Children.Add($contentArea)

        # Status Bar
        $statusBar = New-Object System.Windows.Controls.Primitives.StatusBar
        [System.Windows.Controls.Grid]::SetRow($statusBar, 1)
        $statusText = New-Object System.Windows.Controls.TextBlock
        $this.Bind($statusText, [System.Windows.Controls.TextBlock]::TextProperty, "state.StatusMessage")
        $statusBar.Items.Add($statusText)
        $mainGrid.Children.Add($statusBar)

        # Navigation
        $navPanel = New-Object System.Windows.Controls.StackPanel
        $navPanel.Orientation = 'Horizontal'
        $navPanel.HorizontalAlignment = 'Right'
        [System.Windows.Controls.Grid]::SetRow($navPanel, 2)
        $mainGrid.Children.Add($navPanel)

        $prevButton = New-Object System.Windows.Controls.Button
        $prevButton.Content = "Previous"
        $prevButton.add_Click({ $this.viewModel.GoToPreviousStep() })
        $navPanel.Children.Add($prevButton)

        $nextButton = New-Object System.Windows.Controls.Button
        $nextButton.Content = "Next"
        $nextButton.add_Click({ $this.viewModel.GoToNextStep() })
        $navPanel.Children.Add($nextButton)

        # Logic to switch wizard steps based on state.CurrentStepIndex
        # This requires a more advanced binding or a trigger system.
        # For simplicity in this plan, we will manually update the content.
        $this.viewModel.state.add_PropertyChanged({
            if ($_.PropertyName -eq 'CurrentStepIndex') {
                $stepIndex = $this.viewModel.state.CurrentStepIndex
                if ($stepIndex -eq 0) { $contentArea.Content = this.CreateWizardStep0() }
                if ($stepIndex -eq 1) { $contentArea.Content = this.CreateWizardStep1() }
            }
        })

        # Initial content load
        $contentArea.Content = this.CreateWizardStep0()

        # Bind the main grid's IsEnabled property to the inverse of IsBusy
        $this.Bind($mainGrid, [System.Windows.Controls.Grid]::IsEnabledProperty, "state.IsBusy", [BooleanToOppositeBooleanConverter]::new())

        $this.window.ShowDialog() | Out-Null
    }
}
```

---

## PHASE 4: FINAL INTEGRATION

**Objective:** Write the script's entry point to initialize and launch the application.

### Task 4.1: Implement the Script Entry Point

**Action:** Add the final execution block to the end of the `.ps1` file.

**Code to Implement:**
```powershell
# SECTION 5: SCRIPT EXECUTION

# 1. Instantiate services and state
$logService = [LoggingService]::new()
$configService = [ConfigurationService]::new()
$jiraService = [JiraService]::new($logService)
$adService = [ActiveDirectoryService]::new($true, $logService) # Start in simulation mode
$appState = [AppState]::new()

# 2. Instantiate ViewModel, injecting dependencies
$viewModel = [WizardViewModel]::new($logService, $configService, $jiraService, $adService, $appState)

# 3. Instantiate View, injecting ViewModel
$view = [WizardView]::new($viewModel)

# 4. Link View back to ViewModel (for dispatcher access)
$viewModel.RegisterView($view)

# 5. Start the application
$logService.Info("Application starting...")
$view.Show()
$logService.Info("Application closed.")
```
