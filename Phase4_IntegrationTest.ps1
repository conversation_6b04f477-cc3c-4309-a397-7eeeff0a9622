#Requires -Version 5.1

<#
.SYNOPSIS
    Phase 4 Integration Testing Script for OnboardingFromJiraGUI v5.0

.DESCRIPTION
    Comprehensive testing script to validate Phase 4 enterprise features integration
    with existing functionality from Phases 1-3.
#>

param(
    [switch]$Detailed = $false,
    [switch]$ExportReport = $true
)

# Test Results Collection
$script:TestResults = @{
    StartTime = Get-Date
    TotalTests = 0
    PassedTests = 0
    FailedTests = 0
    SkippedTests = 0
    TestDetails = @()
    Errors = @()
}

function Write-TestResult {
    param(
        [string]$TestName,
        [string]$Status,
        [string]$Message = "",
        [object]$Details = $null
    )
    
    $script:TestResults.TotalTests++
    
    switch ($Status) {
        "PASSED" { 
            $script:TestResults.PassedTests++
            $color = "Green"
        }
        "FAILED" { 
            $script:TestResults.FailedTests++
            $color = "Red"
            $script:TestResults.Errors += "$TestName`: $Message"
        }
        "SKIPPED" { 
            $script:TestResults.SkippedTests++
            $color = "Yellow"
        }
    }
    
    Write-Host "[$Status] $TestName" -ForegroundColor $color
    if ($Message) {
        Write-Host "    $Message" -ForegroundColor Gray
    }
    
    $script:TestResults.TestDetails += @{
        Name = $TestName
        Status = $Status
        Message = $Message
        Details = $Details
        Timestamp = Get-Date
    }
}

function Test-ScriptSyntax {
    Write-Host "`n=== SYNTAX VALIDATION ===" -ForegroundColor Cyan
    
    try {
        $scriptPath = "Utilities\OnboardingFromJiraGUI.ps1"
        
        # Test file existence
        if (-not (Test-Path $scriptPath)) {
            Write-TestResult "File Existence" "FAILED" "Script file not found: $scriptPath"
            return
        }
        Write-TestResult "File Existence" "PASSED" "Script file found"
        
        # Test file size
        $fileInfo = Get-Item $scriptPath
        $fileSizeMB = [math]::Round($fileInfo.Length / 1MB, 2)
        Write-TestResult "File Size" "PASSED" "Script size: $fileSizeMB MB ($($fileInfo.Length) bytes)"
        
        # Test line count
        $lineCount = (Get-Content $scriptPath).Count
        Write-TestResult "Line Count" "PASSED" "Total lines: $lineCount"
        
        # Basic syntax check using PowerShell parser
        try {
            $errors = $null
            $tokens = $null
            $ast = [System.Management.Automation.Language.Parser]::ParseFile($scriptPath, [ref]$tokens, [ref]$errors)
            
            if ($errors.Count -eq 0) {
                Write-TestResult "PowerShell Parser" "PASSED" "No syntax errors detected"
            } else {
                $errorMessages = $errors | ForEach-Object { $_.Message }
                Write-TestResult "PowerShell Parser" "FAILED" "Found $($errors.Count) syntax errors: $($errorMessages -join '; ')"
            }
        } catch {
            Write-TestResult "PowerShell Parser" "FAILED" "Parser exception: $($_.Exception.Message)"
        }
        
    } catch {
        Write-TestResult "Syntax Validation" "FAILED" "Exception during syntax validation: $($_.Exception.Message)"
    }
}

function Test-ScriptLoading {
    Write-Host "`n=== SCRIPT LOADING TEST ===" -ForegroundColor Cyan
    
    try {
        # Test script loading in a separate PowerShell session
        $testScript = @"
try {
    `$ErrorActionPreference = 'Stop'
    . 'Utilities\OnboardingFromJiraGUI.ps1' 2>`$null
    Write-Output 'SUCCESS: Script loaded without errors'
} catch {
    Write-Output "FAILED: `$(`$_.Exception.Message)"
}
"@
        
        $result = powershell -Command $testScript
        
        if ($result -like "SUCCESS:*") {
            Write-TestResult "Script Loading" "PASSED" "Script loaded successfully in separate session"
        } else {
            Write-TestResult "Script Loading" "FAILED" "Script loading failed: $result"
        }
        
    } catch {
        Write-TestResult "Script Loading" "FAILED" "Exception during script loading test: $($_.Exception.Message)"
    }
}

function Test-Phase4Features {
    Write-Host "`n=== PHASE 4 FEATURES TEST ===" -ForegroundColor Cyan
    
    # Test class definitions
    $phase4Classes = @(
        "AdvancedLoggingManager",
        "VersionManager", 
        "TestingFramework",
        "DocumentationGenerator"
    )
    
    foreach ($className in $phase4Classes) {
        try {
            $classFound = Select-String -Path "Utilities\OnboardingFromJiraGUI.ps1" -Pattern "^class $className" -Quiet
            if ($classFound) {
                Write-TestResult "Class Definition: $className" "PASSED" "Class definition found"
            } else {
                Write-TestResult "Class Definition: $className" "FAILED" "Class definition not found"
            }
        } catch {
            Write-TestResult "Class Definition: $className" "FAILED" "Error checking class: $($_.Exception.Message)"
        }
    }
    
    # Test function definitions
    $phase4Functions = @(
        "Write-StructuredLog",
        "Get-ScriptVersion",
        "Register-Test",
        "New-Documentation",
        "Show-ScriptHelp"
    )
    
    foreach ($functionName in $phase4Functions) {
        try {
            $functionFound = Select-String -Path "Utilities\OnboardingFromJiraGUI.ps1" -Pattern "^function $functionName" -Quiet
            if ($functionFound) {
                Write-TestResult "Function Definition: $functionName" "PASSED" "Function definition found"
            } else {
                Write-TestResult "Function Definition: $functionName" "FAILED" "Function definition not found"
            }
        } catch {
            Write-TestResult "Function Definition: $functionName" "FAILED" "Error checking function: $($_.Exception.Message)"
        }
    }
}

function Test-IntegrationWithPreviousPhases {
    Write-Host "`n=== INTEGRATION WITH PREVIOUS PHASES ===" -ForegroundColor Cyan
    
    # Test Phase 1 features still exist
    $phase1Features = @(
        "ConfigurationManager",
        "Get-ConfigurationValue",
        "Write-ErrorLog"
    )
    
    foreach ($feature in $phase1Features) {
        try {
            $featureFound = Select-String -Path "Utilities\OnboardingFromJiraGUI.ps1" -Pattern $feature -Quiet
            if ($featureFound) {
                Write-TestResult "Phase 1 Integration: $feature" "PASSED" "Feature preserved"
            } else {
                Write-TestResult "Phase 1 Integration: $feature" "FAILED" "Feature missing"
            }
        } catch {
            Write-TestResult "Phase 1 Integration: $feature" "FAILED" "Error checking feature: $($_.Exception.Message)"
        }
    }
    
    # Test Phase 2 features still exist
    $phase2Features = @(
        "AutoCompletionHistory",
        "Apply-ModernUIIntegration",
        "Show-ProgressDialog"
    )
    
    foreach ($feature in $phase2Features) {
        try {
            $featureFound = Select-String -Path "Utilities\OnboardingFromJiraGUI.ps1" -Pattern $feature -Quiet
            if ($featureFound) {
                Write-TestResult "Phase 2 Integration: $feature" "PASSED" "Feature preserved"
            } else {
                Write-TestResult "Phase 2 Integration: $feature" "FAILED" "Feature missing"
            }
        } catch {
            Write-TestResult "Phase 2 Integration: $feature" "FAILED" "Error checking feature: $($_.Exception.Message)"
        }
    }
    
    # Test Phase 3 features still exist
    $phase3Features = @(
        "OrganizationalDataManager",
        "BatchOperationManager",
        "EnhancedJiraManager",
        "AdvancedADGroupManager"
    )
    
    foreach ($feature in $phase3Features) {
        try {
            $featureFound = Select-String -Path "Utilities\OnboardingFromJiraGUI.ps1" -Pattern $feature -Quiet
            if ($featureFound) {
                Write-TestResult "Phase 3 Integration: $feature" "PASSED" "Feature preserved"
            } else {
                Write-TestResult "Phase 3 Integration: $feature" "FAILED" "Feature missing"
            }
        } catch {
            Write-TestResult "Phase 3 Integration: $feature" "FAILED" "Error checking feature: $($_.Exception.Message)"
        }
    }
}

function Test-CodeQuality {
    Write-Host "`n=== CODE QUALITY CHECKS ===" -ForegroundColor Cyan
    
    try {
        $scriptContent = Get-Content "Utilities\OnboardingFromJiraGUI.ps1" -Raw
        
        # Test for proper region structure
        $regionCount = ($scriptContent | Select-String "#region" -AllMatches).Matches.Count
        $endregionCount = ($scriptContent | Select-String "#endregion" -AllMatches).Matches.Count
        
        if ($regionCount -eq $endregionCount) {
            Write-TestResult "Region Structure" "PASSED" "Balanced regions: $regionCount regions"
        } else {
            Write-TestResult "Region Structure" "FAILED" "Unbalanced regions: $regionCount regions, $endregionCount endregions"
        }
        
        # Test for proper error handling
        $errorHandlingCount = ($scriptContent | Select-String "try\s*\{" -AllMatches).Matches.Count
        if ($errorHandlingCount -gt 10) {
            Write-TestResult "Error Handling" "PASSED" "Good error handling coverage: $errorHandlingCount try blocks"
        } else {
            Write-TestResult "Error Handling" "FAILED" "Insufficient error handling: $errorHandlingCount try blocks"
        }
        
        # Test for documentation
        $commentLines = ($scriptContent -split "`n" | Where-Object { $_ -match "^\s*#" }).Count
        $totalLines = ($scriptContent -split "`n").Count
        $commentRatio = [math]::Round(($commentLines / $totalLines) * 100, 2)
        
        if ($commentRatio -gt 15) {
            Write-TestResult "Documentation Coverage" "PASSED" "Good documentation: $commentRatio% comment lines"
        } else {
            Write-TestResult "Documentation Coverage" "FAILED" "Low documentation: $commentRatio% comment lines"
        }
        
    } catch {
        Write-TestResult "Code Quality" "FAILED" "Exception during quality checks: $($_.Exception.Message)"
    }
}

# Main Test Execution
Write-Host "OnboardingFromJiraGUI v5.0 - Phase 4 Integration Testing" -ForegroundColor Yellow
Write-Host "================================================================" -ForegroundColor Yellow

Test-ScriptSyntax
Test-ScriptLoading
Test-Phase4Features
Test-IntegrationWithPreviousPhases
Test-CodeQuality

# Generate Test Summary
$script:TestResults.EndTime = Get-Date
$script:TestResults.Duration = ($script:TestResults.EndTime - $script:TestResults.StartTime).TotalSeconds

Write-Host "`n=== TEST SUMMARY ===" -ForegroundColor Cyan
Write-Host "Total Tests: $($script:TestResults.TotalTests)" -ForegroundColor White
Write-Host "Passed: $($script:TestResults.PassedTests)" -ForegroundColor Green
Write-Host "Failed: $($script:TestResults.FailedTests)" -ForegroundColor Red
Write-Host "Skipped: $($script:TestResults.SkippedTests)" -ForegroundColor Yellow
Write-Host "Duration: $([math]::Round($script:TestResults.Duration, 2)) seconds" -ForegroundColor White

if ($script:TestResults.FailedTests -gt 0) {
    Write-Host "`nFAILED TESTS:" -ForegroundColor Red
    foreach ($testError in $script:TestResults.Errors) {
        Write-Host "  - $testError" -ForegroundColor Red
    }
}

# Export detailed report if requested
if ($ExportReport) {
    $reportPath = "TestResults\Phase4_IntegrationTest_$(Get-Date -Format 'yyyyMMdd_HHmmss').json"
    $null = New-Item -Path (Split-Path $reportPath) -ItemType Directory -Force -ErrorAction SilentlyContinue
    $script:TestResults | ConvertTo-Json -Depth 10 | Set-Content -Path $reportPath -Encoding UTF8
    Write-Host "`nDetailed test report exported to: $reportPath" -ForegroundColor Green
}

# Return overall result
if ($script:TestResults.FailedTests -eq 0) {
    Write-Host "`n✅ PHASE 4 INTEGRATION TESTING: PASSED" -ForegroundColor Green
    exit 0
} else {
    Write-Host "`n❌ PHASE 4 INTEGRATION TESTING: FAILED" -ForegroundColor Red
    exit 1
}
