# Comprehensive fix for WPF type resolution issues
$scriptPath = "OnboardingFromJiraGUI.ps1"

# Read the content
$content = Get-Content $scriptPath -Raw

# Replace all System.Windows type references with Add-Type safe alternatives
# This uses Invoke-Expression to delay type resolution until runtime

# System.Windows.Media.Brushes static properties
$content = $content -replace '\[System\.Windows\.Media\.Brushes\]::(\w+)', '(Add-Type -AssemblyName PresentationCore -PassThru | Where-Object {$_.Name -eq "Brushes"})::$1'

# System.Windows.GridUnitType enum values  
$content = $content -replace '\[System\.Windows\.GridUnitType\]::(\w+)', '(Add-Type -AssemblyName PresentationFramework -PassThru | Where-Object {$_.Name -eq "GridUnitType"})::$1'

# System.Windows.Controls.Grid static methods
$content = $content -replace '\[System\.Windows\.Controls\.Grid\]::(\w+)', '(Add-Type -AssemblyName PresentationFramework -PassThru | Where-Object {$_.Name -eq "Grid"})::$1'

# Function parameter type declarations - use Object instead
$content = $content -replace '\[System\.Windows\.Controls\.Grid\]\$(\w+)', '[Object]$$1'

# Write the fixed content back
$content | Set-Content $scriptPath -Encoding UTF8

Write-Host "Applied comprehensive WPF type resolution fix!" -ForegroundColor Green