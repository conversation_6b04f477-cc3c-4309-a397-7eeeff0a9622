2025-07-01 09:26:07.372 | JERAGM\akinje   | DEBUG    | JML Context initialized successfully | AUDIT: Operation=ContextInitialization; ScriptRoot=C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13; SessionId=925c00c1-3238-444b-8a53-53eb35cf25b5
2025-07-01 09:26:07.459 | JERAGM\akinje   | DEBUG    | Configuration loaded successfully | AUDIT: ConfigPath=C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Config\AdminAccountConfig.psd1; Operation=ConfigurationLoading; KeyCount=12; ConfigType=System.Collections.Hashtable
2025-07-01 09:26:07.481 | JERAGM\akinje   | INFO     | Setting up credential management...
2025-07-01 09:26:07.498 | JERAGM\akinje   | INFO     | Using credential storage method: EncryptedFile
2025-07-01 09:26:07.514 | JERAGM\akinje   | INFO     | Encrypted file credential manager registered
2025-07-01 09:26:07.534 | JERAGM\akinje   | INFO     | Validating service health...
2025-07-01 09:26:07.552 | JERAGM\akinje   | INFO     | All services are healthy and ready
2025-07-01 09:26:07.571 | JERAGM\akinje   | INFO     | JML service-oriented architecture initialized successfully!
2025-07-01 09:26:07.590 | JERAGM\akinje   | INFO     | Session ID: 925c00c1-3238-444b-8a53-53eb35cf25b5
2025-07-01 09:26:07.611 | JERAGM\akinje   | INFO     | Initializing UI Manager service...
2025-07-01 09:26:07.821 | JERAGM\akinje   | INFO     | UI Manager service initialized successfully
2025-07-01 09:26:07.840 | JERAGM\akinje   | INFO     | Verifying critical module availability...
2025-07-01 09:26:07.858 | JERAGM\akinje   | DEBUG    | [DEBUG] Checking credential setup status for method: EncryptedFile
2025-07-01 09:26:07.881 | JERAGM\akinje   | DEBUG    | [DEBUG] Checking EncryptedFile credential storage
2025-07-01 09:26:07.905 | JERAGM\akinje   | DEBUG    | [DEBUG] Checking for encrypted file at: [REDACTED_PATH]s\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Config\SecureCredentials.xml
2025-07-01 09:26:07.928 | JERAGM\akinje   | DEBUG    | [DEBUG] EncryptedFile credentials appear to be configured and accessible
2025-07-01 09:26:08.455 | JERAGM\akinje   | INFO     | Credential setup complete. Loading main menu...
2025-07-01 09:26:08.480 | JERAGM\akinje   | INFO     | [INFO] Initializing Jira session...
2025-07-01 09:26:08.507 | JERAGM\akinje   | DEBUG    | [DEBUG] Received credential types:
2025-07-01 09:26:08.528 | JERAGM\akinje   | DEBUG    | [DEBUG] - jiraUsername type: String
2025-07-01 09:26:08.546 | JERAGM\akinje   | DEBUG    | [DEBUG] - jiraApiToken type: SecureString
2025-07-01 09:26:08.567 | JERAGM\akinje   | DEBUG    | [DEBUG] Credential validation passed
2025-07-01 09:26:08.589 | JERAGM\akinje   | DEBUG    | [DEBUG] Username format: Valid email
2025-07-01 09:26:08.610 | JERAGM\akinje   | DEBUG    | [DEBUG] API token=[REDACTED] (length: 192)
2025-07-01 09:26:08.630 | JERAGM\akinje   | DEBUG    | [DEBUG] Final PSCredential object:
2025-07-01 09:26:08.652 | JERAGM\akinje   | DEBUG    | [DEBUG] - Username: emmanuel.akinjomo***@jeragm.com
2025-07-01 09:26:08.671 | JERAGM\akinje   | DEBUG    | [DEBUG] - Password type: SecureString
2025-07-01 09:26:08.692 | JERAGM\akinje   | INFO     | [INFO] Connecting to Jira server: [SERVER]
2025-07-01 09:26:08.711 | JERAGM\akinje   | INFO     | [INFO] Using username: emmanuel.akinjomo***@jeragm.com
2025-07-01 09:26:08.731 | JERAGM\akinje   | INFO     | Initializing Jira connection | AUDIT: Username=emmanuel.akinjomo***@jeragm.com; Operation=JiraConnectionInit; ServerUrl=[SERVER]
2025-07-01 09:26:08.750 | JERAGM\akinje   | INFO     | Loading JiraPS module
2025-07-01 09:26:11.481 | JERAGM\akinje   | INFO     | Setting Jira server configuration
2025-07-01 09:26:11.565 | JERAGM\akinje   | INFO     | Creating Jira session using direct authentication
2025-07-01 09:26:11.704 | JERAGM\akinje   | INFO     | Testing Jira connection using GDPR-compliant endpoint
2025-07-01 09:26:12.748 | JERAGM\akinje   | INFO     | Jira connection test successful | AUDIT: UserDisplayName=Emmanuel Akinjomo; Status=Success; Operation=JiraConnectionTest; UserAccountId=712020:************************e1dd4dd40efa
2025-07-01 09:26:12.910 | JERAGM\akinje   | INFO     | Jira connection established successfully.
2025-07-01 09:26:12.995 | JERAGM\akinje   | INFO     | Connected as: Emmanuel Akinjomo (emmanuel.akinjomo***@jeragm.com)
2025-07-01 09:26:13.078 | JERAGM\akinje   | INFO     | Jira connection initialized successfully using JiraPS module
2025-07-01 09:26:13.139 | JERAGM\akinje   | INFO     | [SUCCESS] Jira session initialized successfully
2025-07-01 09:26:13.176 | JERAGM\akinje   | INFO     | Jira authentication and connection successful!
2025-07-01 09:26:13.209 | JERAGM\akinje   | INFO     | JML System started in interactive mode | AUDIT: Operation=SystemStartup; Version=1.13; ComputerName=JGM-CG7THR3; SecretStoreConfigured=False; CredentialsAvailable=True; ExecutingUser=JERAGM\akinje; SkipJiraIntegration=False
2025-07-01 09:26:13.743 | JERAGM\akinje   | INFO     | +---------------------------------------------------------------------------------------------------------------+
2025-07-01 09:26:13.798 | JERAGM\akinje   | INFO     | |         JML v1.13 | STATUS: Issues | AD: Unavailable | JIRA: Available | CREDS: Error | USER: akinje          |
2025-07-01 09:26:13.846 | JERAGM\akinje   | INFO     | +---------------------------------------------------------------------------------------------------------------+
2025-07-01 09:26:14.015 | JERAGM\akinje   | INFO     | Available Operations:
2025-07-01 09:26:14.048 | JERAGM\akinje   | INFO     |   [1] Create Admin Account
2025-07-01 09:26:14.078 | JERAGM\akinje   | INFO     |   [2] Delete Admin Account
2025-07-01 09:26:14.112 | JERAGM\akinje   | INFO     |   [3] Reset Admin Account Password
2025-07-01 09:26:14.140 | JERAGM\akinje   | INFO     |   [4] System Information
2025-07-01 09:26:14.171 | JERAGM\akinje   | INFO     |   [5] Run Setup / Repair Credentials
2025-07-01 09:26:14.204 | JERAGM\akinje   | INFO     |   [7] Exit
2025-07-01 09:26:14.333 | JERAGM\akinje   | INFO     | +-[ Real-time Log Viewer ]----------------------------------------------------------------------------------------+
2025-07-01 09:26:14.369 | JERAGM\akinje   | INFO     | | 09:26:07 [INFO] UIManager service initialized                                                                 |
2025-07-01 09:26:14.401 | JERAGM\akinje   | INFO     | | 09:26:07 [INFO] UI Manager service started                                                                    |
2025-07-01 09:26:14.433 | JERAGM\akinje   | INFO     | +---------------------------------------------------------------------------------------------------------------+
2025-07-01 09:26:14.496 | JERAGM\akinje   | INFO     | > 
2025-07-01 09:26:14.545 | JERAGM\akinje   | WARNING  | WARNING: UIManager: Slow screen draw detected: 1246.1969ms
