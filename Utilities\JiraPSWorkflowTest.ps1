# Enhanced Workflow Test Suite with JiraPS Support
# This version includes JiraPS integration patterns for easy replacement
param(
    [string]$JiraUrl = "https://jeragm.atlassian.net",
    [string]$JiraUsername = "",
    [string]$JiraApiToken = "",
    [string]$TestTicketId = "",
    [switch]$UseRealJira = $false,
    [switch]$UseJiraPS = $false,
    [switch]$Verbose = $false
)

Write-Host "=== ENHANCED ONBOARDING WORKFLOW TEST SUITE ===" -ForegroundColor Yellow
$testMode = if ($UseJiraPS) { "JIRAPS MODULE" } elseif ($UseRealJira) { "REST API" } else { "MOCK DATA" }
Write-Host "Testing Mode: $testMode" -ForegroundColor $( if ($UseRealJira -or $UseJiraPS) { 'Red' } else { 'Green' } )

# Global test results
$script:TestResults = @()
$script:TestData = @{}

function Write-TestLog {
    param([string]$Message, [string]$Level = "INFO")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $color = switch ($Level) {
        "ERROR" { "Red" }
        "WARN" { "Yellow" }
        "SUCCESS" { "Green" }
        "DEBUG" { "Gray" }
        default { "White" }
    }
    if ($Verbose -or $Level -ne "DEBUG") {
        Write-Host "[$timestamp] [$Level] $Message" -ForegroundColor $color
    }
}

function Add-TestResult {
    param([string]$TestName, [bool]$Passed, [string]$Details = "")
    $script:TestResults += @{
        TestName = $TestName
        Passed = $Passed
        Details = $Details
        Timestamp = Get-Date
    }
    $status = if ($Passed) { "PASS" } else { "FAIL" }
    $color = if ($Passed) { "Green" } else { "Red" }
    Write-TestLog "${TestName}: $status - $Details" $status
}

# ==============================================
# JIRAPS INTEGRATION LAYER
# ==============================================

Write-TestLog "=== TESTING JIRAPS INTEGRATION ===" "INFO"

function Test-JiraPSAvailability {
    try {
        if ($UseJiraPS) {
            # Check if JiraPS module is available
            $jiraPSModule = Get-Module -ListAvailable -Name JiraPS
            if ($jiraPSModule) {
                Write-TestLog "JiraPS module found: Version $($jiraPSModule.Version)" "SUCCESS"
                Import-Module JiraPS -Force
                return $true
            } else {
                Write-TestLog "JiraPS module not found. Install with: Install-Module JiraPS" "ERROR"
                return $false
            }
        }
        return $true
    } catch {
        Write-TestLog "JiraPS availability check failed: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

function Connect-JiraPS {
    param($ServerUrl, $Username, $ApiToken)
    try {
        if ($UseJiraPS) {
            Write-TestLog "Connecting to Jira using JiraPS..." "DEBUG"
            # This is the pattern for JiraPS authentication
            $secureToken = ConvertTo-SecureString $ApiToken -AsPlainText -Force
            $credential = New-Object System.Management.Automation.PSCredential($Username, $secureToken)
            
            # JiraPS connection command
            Set-JiraConfigServer -Server $ServerUrl
            New-JiraSession -Credential $credential
            
            Write-TestLog "JiraPS connection successful" "SUCCESS"
            return $true
        }
        return $true # Mock mode
    } catch {
        Write-TestLog "JiraPS connection failed: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

function Get-JiraTicketUsingPS {
    param([string]$TicketId)
    try {
        if ($UseJiraPS) {
            Write-TestLog "Fetching ticket $TicketId using JiraPS..." "DEBUG"
            # This is the JiraPS command you would use
            $issue = Get-JiraIssue -Key $TicketId
            
            # Convert to our standard format
            $ticketData = @{
                summary = $issue.Summary
                description = $issue.Description
            }
            
            # Add custom fields
            foreach ($field in $issue.CustomFields) {
                $ticketData[$field.Id] = $field.Value
            }
            
            return @{ Success = $true; Data = $ticketData }
        } else {
            # Mock data for testing
            Start-Sleep -Milliseconds 500
            $mockData = @{ 
                summary = "Onboard New User: Test User ($TicketId)"
                description = "Please onboard Test User to the appropriate team."
                customfield_10304 = "Test"
                customfield_10305 = "User"
                customfield_10238 = "Analyst"
                customfield_10120 = "IT"
                customfield_10343 = "model.account"
            }
            return @{ Success = $true; Data = $mockData }
        }
    } catch {
        Write-TestLog "JiraPS ticket fetch failed: $($_.Exception.Message)" "ERROR"
        return @{ Success = $false; ErrorMessage = $_.Exception.Message }
    }
}

function Add-JiraCommentUsingPS {
    param([string]$TicketId, [string]$Comment)
    try {
        if ($UseJiraPS) {
            Write-TestLog "Adding comment to $TicketId using JiraPS..." "DEBUG"
            # This is the JiraPS command you would use
            Add-JiraComment -Issue $TicketId -Comment $Comment
            return @{ Success = $true }
        } else {
            # Mock comment posting
            Start-Sleep -Milliseconds 300
            Write-TestLog "MOCK: Comment added to $TicketId" "DEBUG"
            return @{ Success = $true }
        }
    } catch {
        Write-TestLog "JiraPS comment posting failed: $($_.Exception.Message)" "ERROR"
        return @{ Success = $false; ErrorMessage = $_.Exception.Message }
    }
}

# ==============================================
# TEST JIRAPS WORKFLOW
# ==============================================

# Test JiraPS availability
$jiraPSAvailable = Test-JiraPSAvailability
Add-TestResult "JiraPS Module Availability" $jiraPSAvailable "Module loaded: $jiraPSAvailable"

# Test JiraPS connection
if ($UseJiraPS -and $jiraPSAvailable) {
    $jiraPSConnected = Connect-JiraPS $JiraUrl $JiraUsername $JiraApiToken
    Add-TestResult "JiraPS Connection" $jiraPSConnected "Connected to $JiraUrl"
} else {
    Add-TestResult "JiraPS Connection" $true "Skipped (mock mode)"
}

# Test ticket fetching
$testTicket = if ($TestTicketId) { $TestTicketId } else { "TESTIT-49342" }
Write-TestLog "Testing ticket fetch for: $testTicket" "DEBUG"

$ticketResult = Get-JiraTicketUsingPS $testTicket
$script:TestData.TicketData = $ticketResult
Add-TestResult "Jira Ticket Fetch (JiraPS)" $ticketResult.Success "Ticket: $testTicket, Success: $($ticketResult.Success)"

if ($ticketResult.Success) {
    Write-TestLog "Ticket Summary: $($ticketResult.Data.summary)" "DEBUG"
    $customFields = $ticketResult.Data.Keys | Where-Object { $_ -like 'customfield_*' }
    Write-TestLog "Custom Fields Found: $($customFields.Count)" "DEBUG"
    foreach ($field in $customFields) {
        Write-TestLog "  $field : $($ticketResult.Data[$field])" "DEBUG"
    }
}

# ==============================================
# DATA PARSING AND VALIDATION (SAME AS BEFORE)
# ==============================================

function ParseTicketData($jiraTicket) {
    $parsedData = @{}
    $mappings = @{
        FirstName    = @{ CustomFieldId = "customfield_10304" }
        LastName     = @{ CustomFieldId = "customfield_10305" }
        JobTitle     = @{ CustomFieldId = "customfield_10238" }
        Department   = @{ CustomFieldId = "customfield_10120" }
        ModelAccount = @{ CustomFieldId = "customfield_10343" }
    }
    
    foreach ($key in $mappings.Keys) {
        $fieldId = $mappings[$key].CustomFieldId
        if ($jiraTicket.Data.ContainsKey($fieldId)) {
            $parsedData[$key] = $jiraTicket.Data[$fieldId]
        }
    }
    return $parsedData
}

# Test data parsing
if ($script:TestData.TicketData -and $script:TestData.TicketData.Success) {
    $parsedData = ParseTicketData $script:TestData.TicketData
    $script:TestData.ParsedData = $parsedData
    
    $requiredFields = @('FirstName', 'LastName', 'JobTitle', 'Department')
    $foundFields = $requiredFields | Where-Object { $parsedData.ContainsKey($_) -and ![string]::IsNullOrEmpty($parsedData[$_]) }
    
    Add-TestResult "Data Parsing (JiraPS)" ($foundFields.Count -eq $requiredFields.Count) "Found $($foundFields.Count)/$($requiredFields.Count) required fields"
    
    if ($parsedData.Count -gt 0) {
        Write-TestLog "Parsed Data:" "DEBUG"
        foreach ($key in $parsedData.Keys) {
            Write-TestLog "  $key : $($parsedData[$key])" "DEBUG"
        }
    }
}

# Test comment posting
if ($script:TestData.ParsedData) {
    $username = ($script:TestData.ParsedData.FirstName.Substring(0,1) + $script:TestData.ParsedData.LastName).ToLower()
    $comment = @"
User onboarding completed via JiraPS:
- Name: $($script:TestData.ParsedData.FirstName) $($script:TestData.ParsedData.LastName)
- Username: $username
- Department: $($script:TestData.ParsedData.Department)
- Job Title: $($script:TestData.ParsedData.JobTitle)

Account creation process completed successfully.
"@
    
    $commentResult = Add-JiraCommentUsingPS $testTicket $comment
    Add-TestResult "Jira Comment Post (JiraPS)" $commentResult.Success "Posted to $testTicket"
}

# ==============================================
# FINAL RESULTS
# ==============================================

Write-TestLog "=== TEST RESULTS SUMMARY ===" "INFO"

$totalTests = $script:TestResults.Count
$passedTests = ($script:TestResults | Where-Object { $_.Passed }).Count
$failedTests = $totalTests - $passedTests

Write-Host "`n=== DETAILED RESULTS ===" -ForegroundColor Yellow
foreach ($result in $script:TestResults) {
    $status = if ($result.Passed) { "✓ PASS" } else { "✗ FAIL" }
    $color = if ($result.Passed) { "Green" } else { "Red" }
    Write-Host "$status $($result.TestName)" -ForegroundColor $color
    if ($result.Details) {
        Write-Host "    $($result.Details)" -ForegroundColor Gray
    }
}

Write-Host "`n=== SUMMARY ===" -ForegroundColor Yellow
Write-Host "Total Tests: $totalTests" -ForegroundColor White
Write-Host "Passed: $passedTests" -ForegroundColor Green
Write-Host "Failed: $failedTests" -ForegroundColor Red
Write-Host "Success Rate: $([math]::Round(($passedTests/$totalTests)*100, 1))%" -ForegroundColor $(if ($failedTests -eq 0) { "Green" } else { "Yellow" })

if ($failedTests -eq 0) {
    Write-Host "`n🎉 ALL TESTS PASSED! The JiraPS workflow is working correctly." -ForegroundColor Green
} else {
    Write-Host "`n⚠️  Some tests failed. Check the details above." -ForegroundColor Yellow
}

Write-Host "`n=== JIRAPS INTEGRATION INSTRUCTIONS ===" -ForegroundColor Cyan
Write-Host "To use this with real JiraPS on Windows:" -ForegroundColor White
Write-Host "1. Install-Module JiraPS -Force" -ForegroundColor Yellow
Write-Host "2. Run with: -UseJiraPS -JiraUrl 'https://jeragm.atlassian.net' -JiraUsername 'your-email' -JiraApiToken 'your-token' -TestTicketId 'TESTIT-49342'" -ForegroundColor Yellow
Write-Host "3. The exact same commands will work in the main GUI application" -ForegroundColor Yellow

Write-Host "`n=== DATA COLLECTED ===" -ForegroundColor Yellow
if ($script:TestData.ParsedData) {
    Write-Host "User Data Extracted:" -ForegroundColor Cyan
    foreach ($key in $script:TestData.ParsedData.Keys) {
        Write-Host "  $key : $($script:TestData.ParsedData[$key])" -ForegroundColor White
    }
}

Write-Host "`n=== TEST COMPLETE ===" -ForegroundColor Yellow