#!/usr/bin/env pwsh

# Cross-platform Jira API test using REST calls
# Tests the real API integration without requiring JiraPS module

Write-Host "=== CROSS-PLATFORM JIRA API TEST ===" -ForegroundColor Green
Write-Host "Testing real Jira integration via REST API..." -ForegroundColor Cyan

# Jira configuration
$jiraBaseUrl = "https://jeragm.atlassian.net"
$testTicketId = "TESTIT-49342"

# Function to test Jira authentication
function Test-JiraAuth {
    param(
        [string]$Email,
        [string]$ApiToken
    )
    
    $headers = @{
        "Authorization" = "Basic " + [Convert]::ToBase64String([Text.Encoding]::ASCII.GetBytes("$Email`:$ApiToken"))
        "Accept" = "application/json"
    }
    
    try {
        Write-Host "Testing authentication with $Email..." -ForegroundColor Cyan
        $response = Invoke-RestMethod -Uri "$jiraBaseUrl/rest/api/3/myself" -Headers $headers -Method Get
        Write-Host "✅ Authentication successful!" -ForegroundColor Green
        Write-Host "   User: $($response.displayName) ($($response.emailAddress))" -ForegroundColor White
        Write-Host "   Account ID: $($response.accountId)" -ForegroundColor White
        return $headers
    } catch {
        Write-Host "❌ Authentication failed: $($_.Exception.Message)" -ForegroundColor Red
        if ($_.Exception.Response.StatusCode -eq 401) {
            Write-Host "   Check your email and API token" -ForegroundColor Yellow
        }
        return $null
    }
}

# Function to fetch and analyze ticket
function Test-TicketFetch {
    param(
        [hashtable]$Headers,
        [string]$TicketId
    )
    
    try {
        Write-Host "Fetching ticket $TicketId..." -ForegroundColor Cyan
        $response = Invoke-RestMethod -Uri "$jiraBaseUrl/rest/api/3/issue/$TicketId" -Headers $Headers -Method Get
        
        Write-Host "✅ Successfully fetched ticket!" -ForegroundColor Green
        Write-Host "   Key: $($response.key)" -ForegroundColor White
        Write-Host "   Summary: $($response.fields.summary)" -ForegroundColor White
        Write-Host "   Status: $($response.fields.status.name)" -ForegroundColor White
        Write-Host "   Created: $($response.fields.created)" -ForegroundColor White
        
        return $response
    } catch {
        Write-Host "❌ Failed to fetch ticket: $($_.Exception.Message)" -ForegroundColor Red
        if ($_.Exception.Response.StatusCode -eq 404) {
            Write-Host "   Ticket $TicketId not found or no permission" -ForegroundColor Yellow
        }
        return $null
    }
}

# Function to extract custom fields
function Test-CustomFieldExtraction {
    param($Issue)
    
    Write-Host "`n=== CUSTOM FIELD EXTRACTION TEST ===" -ForegroundColor Green
    
    $expectedFields = @{
        "customfield_10304" = "FirstName"
        "customfield_10305" = "LastName"
        "customfield_10238" = "JobTitle"
        "customfield_10120" = "Department"
        "customfield_10343" = "ModelAccount"
        "customfield_10115" = "OfficeLocation"
    }
    
    $extractedData = @{}
    $foundFields = 0
    
    # Extract all custom fields from the issue
    $customFields = $Issue.fields.PSObject.Properties | Where-Object { $_.Name -like "customfield_*" }
    
    Write-Host "📋 Found $($customFields.Count) total custom fields" -ForegroundColor Cyan
    
    foreach ($field in $customFields) {
        $value = $field.Value
        if ($value -and $value -ne $null) {
            $fieldName = if ($expectedFields[$field.Name]) { $expectedFields[$field.Name] } else { $field.Name }
            
            # Handle different value types
            $displayValue = if ($value -is [string]) { 
                $value 
            } elseif ($value -is [System.Collections.Hashtable] -or $value.PSObject) {
                if ($value.value) { $value.value }
                elseif ($value.displayName) { $value.displayName }
                elseif ($value.name) { $value.name }
                else { $value.ToString() }
            } else { 
                $value.ToString() 
            }
            
            if ($expectedFields[$field.Name]) {
                Write-Host "✅ $fieldName ($($field.Name)): '$displayValue'" -ForegroundColor Green
                $extractedData[$field.Name] = $displayValue
                $foundFields++
            } else {
                Write-Host "🔍 Other field ($($field.Name)): '$displayValue'" -ForegroundColor Gray
            }
        }
    }
    
    # Check required fields
    Write-Host "`n=== REQUIRED FIELD VALIDATION ===" -ForegroundColor Green
    $requiredFields = @("customfield_10304", "customfield_10305", "customfield_10238", "customfield_10120", "customfield_10343")
    $foundRequiredFields = 0
    
    foreach ($fieldId in $requiredFields) {
        $fieldName = $expectedFields[$fieldId]
        if ($extractedData[$fieldId]) {
            Write-Host "✅ $fieldName ($fieldId): '$($extractedData[$fieldId])'" -ForegroundColor Green
            $foundRequiredFields++
        } else {
            Write-Host "❌ $fieldName ($fieldId): NOT FOUND" -ForegroundColor Red
        }
    }
    
    # Test username generation
    if ($extractedData["customfield_10304"] -and $extractedData["customfield_10305"]) {
        $firstName = $extractedData["customfield_10304"]
        $lastName = $extractedData["customfield_10305"]
        $username = ($firstName.Substring(0,1) + $lastName).ToLower() -replace '[^a-z0-9]', ''
        Write-Host "`n=== USERNAME GENERATION ===" -ForegroundColor Green
        Write-Host "✅ Generated username: '$username' from '$firstName $lastName'" -ForegroundColor Green
    } else {
        Write-Host "`n❌ Cannot generate username - missing FirstName or LastName" -ForegroundColor Red
    }
    
    return @{
        FoundFields = $foundRequiredFields
        TotalFields = $foundFields
        ExtractedData = $extractedData
    }
}

# Main test execution
Write-Host "Please provide your Jira credentials for testing:" -ForegroundColor Yellow
$email = Read-Host "Email (<EMAIL>)"
if ([string]::IsNullOrEmpty($email)) {
    $email = "<EMAIL>"
}

$apiTokenSecure = Read-Host "API Token" -AsSecureString
$apiToken = [Runtime.InteropServices.Marshal]::PtrToStringAuto([Runtime.InteropServices.Marshal]::SecureStringToBSTR($apiTokenSecure))

# Test authentication
$headers = Test-JiraAuth -Email $email -ApiToken $apiToken

if ($headers) {
    # Test ticket fetch
    $issue = Test-TicketFetch -Headers $headers -TicketId $testTicketId
    
    if ($issue) {
        # Test custom field extraction
        $result = Test-CustomFieldExtraction -Issue $issue
        
        # Final summary
        Write-Host "`n=== TEST SUMMARY ===" -ForegroundColor Green
        Write-Host "🔗 Jira URL: $jiraBaseUrl" -ForegroundColor Cyan
        Write-Host "🎫 Test Ticket: $testTicketId" -ForegroundColor Cyan
        Write-Host "📊 Required fields found: $($result.FoundFields)/5" -ForegroundColor Cyan
        Write-Host "📊 Total custom fields: $($result.TotalFields)" -ForegroundColor Cyan
        
        if ($result.FoundFields -ge 3) {
            Write-Host "✅ JIRA INTEGRATION TEST PASSED!" -ForegroundColor Green
            Write-Host "   Your API token works and the application is ready!" -ForegroundColor White
        } else {
            Write-Host "⚠️  PARTIAL SUCCESS - Some required fields missing" -ForegroundColor Yellow
            Write-Host "   Application will use fallback parsing and mock data" -ForegroundColor White
        }
    }
} else {
    Write-Host "`n❌ AUTHENTICATION FAILED" -ForegroundColor Red
    Write-Host "Please check your credentials and try again" -ForegroundColor White
}

Write-Host "`n=== TEST COMPLETE ===" -ForegroundColor Green