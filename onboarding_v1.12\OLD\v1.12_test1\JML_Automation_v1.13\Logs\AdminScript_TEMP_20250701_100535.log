﻿2025-07-01 10:05:35.852 | akinje          | INFO     | JML Automation Script v1.13 starting...
2025-07-01 10:05:35.899 | akinje          | DEBUG    | Script path: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13
2025-07-01 10:05:35.951 | akinje          | DEBUG    | PowerShell version: 5.1.19041.5848
2025-07-01 10:05:36.234 | akinje          | DEBUG    | Execution policy: Bypass
2025-07-01 10:05:36.245 | akinje          | DEBUG    | User: akinje
2025-07-01 10:05:36.253 | akinje          | DEBUG    | Computer: JGM-CG7THR3
2025-07-01 10:05:36.649 | akinje          | INFO     | === JML ENVIRONMENT VALIDATION ===
2025-07-01 10:05:36.660 | akin<PERSON>          | INFO     | Verifying PowerShell Version...
2025-07-01 10:05:36.680 | akinje          | INFO     | === JML ENVIRONMENT VALIDATION ===
2025-07-01 10:05:36.688 | akinje          | INFO     | Verifying PowerShell Version...
2025-07-01 10:05:36.748 | akinje          | WARNING  | WARNING: PowerShell 7 or higher is recommended for optimal performance and security.
2025-07-01 10:05:36.769 | akinje          | INFO     | Current version: PowerShell 5.1.19041.5848
2025-07-01 10:05:36.781 | akinje          | WARNING  | PowerShell 7+ recommended. Current: 5.1.19041.5848
2025-07-01 10:05:36.816 | akinje          | INFO     | Searching for PowerShell 7 installation...
2025-07-01 10:05:36.852 | akinje          | INFO     |   Checking: C:\Program Files\PowerShell\7\pwsh.exe
2025-07-01 10:05:38.296 | akinje          | INFO     |   Found PowerShell 7.5.1 at: C:\Program Files\PowerShell\7\pwsh.exe
2025-07-01 10:05:38.330 | akinje          | INFO     | PowerShell 7 is installed at: C:\Program Files\PowerShell\7\pwsh.exe
2025-07-01 10:05:38.377 | akinje          | INFO     | Options:
2025-07-01 10:05:38.405 | akinje          | INFO     |   1. Continue with current PowerShell 5.x
2025-07-01 10:05:38.431 | akinje          | INFO     |   2. Switch to PowerShell 7 (recommended)
2025-07-01 10:05:38.460 | akinje          | INFO     |   3. Exit and manually switch
2025-07-01 10:05:38.507 | akinje          | INFO     | Select option (1-3) [Default: 2 in 10 seconds]: 
2025-07-01 10:05:48.556 | akinje          | INFO     | 2 (default)
2025-07-01 10:05:48.598 | akinje          | INFO     | === POWERSHELL 7 EXECUTION OPTIONS ===
2025-07-01 10:05:48.617 | akinje          | INFO     | PowerShell 7 is available at: C:\Program Files\PowerShell\7\pwsh.exe
2025-07-01 10:05:48.648 | akinje          | INFO     | How would you like to run the script in PowerShell 7?
2025-07-01 10:05:48.715 | akinje          | INFO     |   [N] New Window - Launch in a new PowerShell 7 window
2025-07-01 10:05:48.743 | akinje          | INFO     |   [C] Current Window - Run PowerShell 7 in this window (default)
2025-07-01 10:05:48.777 | akinje          | INFO     | Choose option (N/C) - Waiting 5 seconds (default: Current Window)...
2025-07-01 10:05:53.955 | akinje          | INFO     | Selected: Current Window (default)
2025-07-01 10:05:54.047 | akinje          | INFO     | Starting PowerShell 7 in current window...
2025-07-01 10:05:54.099 | akinje          | INFO     | Script will continue in PowerShell 7...
