#!/usr/bin/env pwsh

# Comprehensive test with your real API token
$email = "<EMAIL>"
$apiToken = "ATATT3xFfGF0IoMQCNhFcJBABDwKqPqvK3-MSVJAUF-ocPi5lwuLkfx5JgZ_PH_8IE4fxvuczoti0QbKtpGDIuWLygo3gQXkGV7UHAsPq8OqltraOqOe2UNeq4HDQqV0zoM_S6LoYFXzLLqP-b_X3QNEs8Gt4o_gl6gbnZ8DVGjkn_28C6QamGA=8427942E"
$jiraBaseUrl = "https://jeragm.atlassian.net"
$testTicketId = "TESTIT-49342"

$headers = @{
    "Authorization" = "Basic " + [Convert]::ToBase64String([Text.Encoding]::ASCII.GetBytes("$email`:$apiToken"))
    "Accept" = "application/json"
}

Write-Host "=== COMPREHENSIVE JIRA INTEGRATION TEST ===" -ForegroundColor Green
Write-Host "Fetching ticket $testTicketId..." -ForegroundColor Cyan

try {
    $issue = Invoke-RestMethod -Uri "$jiraBaseUrl/rest/api/3/issue/$testTicketId" -Headers $headers
    
    Write-Host "✅ Successfully fetched ticket!" -ForegroundColor Green
    Write-Host "   Key: $($issue.key)" -ForegroundColor White
    Write-Host "   Summary: $($issue.fields.summary)" -ForegroundColor White
    Write-Host "   Status: $($issue.fields.status.name)" -ForegroundColor White
    Write-Host "   Reporter: $($issue.fields.reporter.displayName)" -ForegroundColor White
    Write-Host "   Created: $($issue.fields.created)" -ForegroundColor White
    
    Write-Host "`n=== CUSTOM FIELD EXTRACTION ===" -ForegroundColor Green
    
    # Our target custom fields (matching your QuickJiraTest results)
    $expectedFields = @{
        "customfield_10304" = "FirstName"
        "customfield_10305" = "LastName"
        "customfield_10238" = "JobTitle"
        "customfield_10120" = "Department"
        "customfield_10343" = "ModelAccount"
        "customfield_10115" = "OfficeLocation"
    }
    
    $extractedData = @{}
    $foundFields = 0
    
    foreach ($fieldId in $expectedFields.Keys) {
        $fieldName = $expectedFields[$fieldId]
        $value = $issue.fields.$fieldId
        
        if ($value) {
            # Handle different value types
            $displayValue = if ($value.value) { $value.value } 
                           elseif ($value.displayName) { $value.displayName } 
                           elseif ($value.name) { $value.name }
                           else { $value.ToString() }
            
            Write-Host "✅ $fieldName ($fieldId): '$displayValue'" -ForegroundColor Green
            $extractedData[$fieldId] = $displayValue
            $foundFields++
        } else {
            Write-Host "❌ $fieldName ($fieldId): NOT FOUND" -ForegroundColor Red
        }
    }
    
    Write-Host "`nFound $foundFields/6 expected custom fields" -ForegroundColor Cyan
    
    # Test username generation
    if ($extractedData["customfield_10304"] -and $extractedData["customfield_10305"]) {
        $firstName = $extractedData["customfield_10304"]
        $lastName = $extractedData["customfield_10305"]
        $username = ($firstName.Substring(0,1) + $lastName).ToLower() -replace '[^a-z0-9]', ''
        Write-Host "`n=== USERNAME GENERATION ===" -ForegroundColor Green
        Write-Host "✅ Generated username: '$username' from '$firstName $lastName'" -ForegroundColor Green
        
        # Business logic validation
        Write-Host "`n=== ONBOARDING DATA SUMMARY ===" -ForegroundColor Green
        Write-Host "📝 Employee Details:" -ForegroundColor Cyan
        Write-Host "   Full Name: $firstName $lastName" -ForegroundColor White
        Write-Host "   Generated Username: $username" -ForegroundColor White
        Write-Host "   Job Title: $($extractedData['customfield_10238'])" -ForegroundColor White
        Write-Host "   Department: $($extractedData['customfield_10120'])" -ForegroundColor White
        Write-Host "   Model Account: $($extractedData['customfield_10343'])" -ForegroundColor White
        
        if ($extractedData["customfield_10115"]) {
            Write-Host "   Office Location: $($extractedData['customfield_10115'])" -ForegroundColor White
        }
        
        # Test OU selection logic
        $department = $extractedData['customfield_10120']
        $ou = switch ($department) {
            "IT" { "OU=IT,OU=Users,DC=jeragm,DC=local" }
            "Finance" { "OU=Finance,OU=Users,DC=jeragm,DC=local" }
            "HR" { "OU=HR,OU=Users,DC=jeragm,DC=local" }
            default { "OU=General,OU=Users,DC=jeragm,DC=local" }
        }
        Write-Host "   Target OU: $ou" -ForegroundColor White
        
        Write-Host "`n✅ ALL ONBOARDING DATA EXTRACTED SUCCESSFULLY!" -ForegroundColor Green
        Write-Host "🎉 The WPF application is ready for production use!" -ForegroundColor Green
        
    } else {
        Write-Host "`n❌ Cannot generate username - missing FirstName or LastName" -ForegroundColor Red
    }
    
    # Final validation matching your test results
    Write-Host "`n=== VALIDATION AGAINST YOUR TEST RESULTS ===" -ForegroundColor Green
    $expectedValues = @{
        "customfield_10343" = "test"          # ModelAccount
        "customfield_10238" = "Intern"        # JobTitle  
        "customfield_10305" = "Test2"         # LastName
        "customfield_10120" = "IT"            # Department
        "customfield_10304" = "Test1"         # FirstName
    }
    
    $allMatch = $true
    foreach ($fieldId in $expectedValues.Keys) {
        $expected = $expectedValues[$fieldId]
        $actual = $extractedData[$fieldId]
        if ($actual -eq $expected) {
            Write-Host "✅ $($expectedFields[$fieldId]): '$actual' (matches expected)" -ForegroundColor Green
        } else {
            Write-Host "⚠️  $($expectedFields[$fieldId]): '$actual' (expected: '$expected')" -ForegroundColor Yellow
            $allMatch = $false
        }
    }
    
    if ($allMatch) {
        Write-Host "`n🎯 PERFECT MATCH! All values match your QuickJiraTest results!" -ForegroundColor Green
        Write-Host "✅ Username 'ttest2' will be generated correctly" -ForegroundColor Green
    }
    
} catch {
    Write-Host "❌ Failed to fetch ticket: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Stack trace: $($_.ScriptStackTrace)" -ForegroundColor Red
}

Write-Host "`n=== TEST COMPLETE ===" -ForegroundColor Green