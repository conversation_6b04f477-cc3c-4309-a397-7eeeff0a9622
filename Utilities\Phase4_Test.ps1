# Phase 4 Test - Jira Authentication Consolidation
# Test that the authentication consolidation is working

Write-Host "=== Phase 4 Jira Authentication Consolidation Test ===" -ForegroundColor Green

# Test 1: Check if CreateAuthHeaders method exists in JiraService
Write-Host "Test 1: Check CreateAuthHeaders method" -ForegroundColor Cyan
$createAuthHeadersLines = Select-String -Path "/mnt/c/Users/<USER>/OneDrive - JERA Global Markets/Azure Devops/it-endusersupport-automation/scripts/Utilities/OnboardingFromJiraGUI_v2.ps1" -Pattern "CreateAuthHeaders.*authInfo"
if ($createAuthHeadersLines) {
    Write-Host "✅ CreateAuthHeaders method found" -ForegroundColor Green
} else {
    Write-Host "❌ CreateAuthHeaders method not found" -ForegroundColor Red
}

# Test 2: Check if TestAuthentication method exists
Write-Host "`nTest 2: Check TestAuthentication method" -ForegroundColor Cyan
$testAuthLines = Select-String -Path "/mnt/c/Users/<USER>/OneDrive - JERA Global Markets/Azure Devops/it-endusersupport-automation/scripts/Utilities/OnboardingFromJiraGUI_v2.ps1" -Pattern "TestAuthentication.*authInfo"
if ($testAuthLines) {
    Write-Host "✅ TestAuthentication method found" -ForegroundColor Green
} else {
    Write-Host "❌ TestAuthentication method not found" -ForegroundColor Red
}

# Test 3: Check if AuthenticateAndStore method exists
Write-Host "`nTest 3: Check AuthenticateAndStore method" -ForegroundColor Cyan
$authAndStoreLines = Select-String -Path "/mnt/c/Users/<USER>/OneDrive - JERA Global Markets/Azure Devops/it-endusersupport-automation/scripts/Utilities/OnboardingFromJiraGUI_v2.ps1" -Pattern "AuthenticateAndStore.*url.*username.*apiToken"
if ($authAndStoreLines) {
    Write-Host "✅ AuthenticateAndStore method found" -ForegroundColor Green
} else {
    Write-Host "❌ AuthenticateAndStore method not found" -ForegroundColor Red
}

# Test 4: Check if WizardViewModel AuthenticateJira uses JiraService
Write-Host "`nTest 4: Check simplified AuthenticateJira in WizardViewModel" -ForegroundColor Cyan
$simplifiedAuthLines = Select-String -Path "/mnt/c/Users/<USER>/OneDrive - JERA Global Markets/Azure Devops/it-endusersupport-automation/scripts/Utilities/OnboardingFromJiraGUI_v2.ps1" -Pattern "jira\.AuthenticateAndStore"
if ($simplifiedAuthLines) {
    Write-Host "✅ WizardViewModel uses JiraService for authentication" -ForegroundColor Green
} else {
    Write-Host "❌ WizardViewModel still has manual authentication" -ForegroundColor Red
}

# Test 5: Check if REST API methods use consolidated auth
Write-Host "`nTest 5: Check REST API methods use CreateAuthHeaders" -ForegroundColor Cyan
$restApiAuthLines = Select-String -Path "/mnt/c/Users/<USER>/OneDrive - JERA Global Markets/Azure Devops/it-endusersupport-automation/scripts/Utilities/OnboardingFromJiraGUI_v2.ps1" -Pattern "CreateAuthHeaders.*authInfo" -Context 2,2 | Where-Object { $_.Context.PreContext -match "REST|Post|Get" -or $_.Context.PostContext -match "REST|API" }
if ($restApiAuthLines) {
    Write-Host "✅ REST API methods use consolidated authentication" -ForegroundColor Green
    Write-Host "Found $($restApiAuthLines.Count) usages" -ForegroundColor Yellow
} else {
    Write-Host "❌ REST API methods not using consolidated auth" -ForegroundColor Red
}

# Test 6: Check if duplicate auth code was removed
Write-Host "`nTest 6: Check for reduction in duplicate authentication code" -ForegroundColor Cyan
$base64AuthLines = Select-String -Path "/mnt/c/Users/<USER>/OneDrive - JERA Global Markets/Azure Devops/it-endusersupport-automation/scripts/Utilities/OnboardingFromJiraGUI_v2.ps1" -Pattern "base64AuthInfo.*Convert.*ToBase64String"
Write-Host "Remaining base64 auth creations: $($base64AuthLines.Count)" -ForegroundColor Yellow
if ($base64AuthLines.Count -le 1) {
    Write-Host "✅ Duplicate authentication code successfully reduced" -ForegroundColor Green
} else {
    Write-Host "❌ Still has multiple duplicate auth implementations" -ForegroundColor Red
}

# Test 7: Check for ErrorHelper usage in authentication methods
Write-Host "`nTest 7: Check authentication methods use ErrorHelper" -ForegroundColor Cyan
$authErrorHelperLines = Select-String -Path "/mnt/c/Users/<USER>/OneDrive - JERA Global Markets/Azure Devops/it-endusersupport-automation/scripts/Utilities/OnboardingFromJiraGUI_v2.ps1" -Pattern "ErrorHelper.*SafeExecute" -Context 1,1 | Where-Object { $_.Context.PreContext -match "Auth|CreateAuth|TestAuth" -or $_.Context.PostContext -match "Authentication|AuthHeaders" }
if ($authErrorHelperLines) {
    Write-Host "✅ Authentication methods use ErrorHelper" -ForegroundColor Green
    Write-Host "Found $($authErrorHelperLines.Count) usages" -ForegroundColor Yellow
} else {
    Write-Host "❌ Authentication methods not using ErrorHelper" -ForegroundColor Red
}

# Test 8: Check debug statement improvements in authentication
Write-Host "`nTest 8: Check authentication debug improvements" -ForegroundColor Cyan
$authDebugLines = Select-String -Path "/mnt/c/Users/<USER>/OneDrive - JERA Global Markets/Azure Devops/it-endusersupport-automation/scripts/Utilities/OnboardingFromJiraGUI_v2.ps1" -Pattern "log\.Debug.*Authentication"
if ($authDebugLines) {
    Write-Host "✅ Authentication uses improved debug logging" -ForegroundColor Green
    Write-Host "Found $($authDebugLines.Count) improved debug statements" -ForegroundColor Yellow
} else {
    Write-Host "❌ Authentication debug not improved" -ForegroundColor Red
}

# Test 9: Check for operation context in authentication
Write-Host "`nTest 9: Check for operation context in authentication" -ForegroundColor Cyan
$authOperationContextLines = Select-String -Path "/mnt/c/Users/<USER>/OneDrive - JERA Global Markets/Azure Devops/it-endusersupport-automation/scripts/Utilities/OnboardingFromJiraGUI_v2.ps1" -Pattern "JiraAuthentication|CreateAuthHeaders|AuthenticateAndStore" | Where-Object { $_.Line -match "ErrorHelper|SafeExecute" }
if ($authOperationContextLines) {
    Write-Host "✅ Authentication has operation context" -ForegroundColor Green
} else {
    Write-Host "❌ Authentication lacks operation context" -ForegroundColor Red
}

Write-Host "`n=== Phase 4 Summary ===" -ForegroundColor Green
Write-Host "🔧 Consolidated authentication methods in JiraService" -ForegroundColor Cyan
Write-Host "🎯 Simplified WizardViewModel authentication delegation" -ForegroundColor Cyan
Write-Host "🔄 REST API methods use unified authentication" -ForegroundColor Cyan
Write-Host "📊 Reduced code duplication in auth handling" -ForegroundColor Cyan
Write-Host "🛡️ Enhanced error handling for authentication" -ForegroundColor Cyan
Write-Host "📝 Improved debug logging with operation context" -ForegroundColor Cyan
Write-Host "🎉 Authentication is now centralized and maintainable!" -ForegroundColor Yellow