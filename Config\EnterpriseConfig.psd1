@{
    Version = "4.0.0"
    
    # Application Settings
    Application = @{
        Name = "Enterprise Onboarding Suite"
        Version = "4.0.0"
        Environment = "Production"
        LogLevel = "INFO"
        MaxConcurrentOperations = 5
        SessionTimeoutMinutes = 120
        SimulationMode = $false
    }
    
    # Jira Configuration
    Jira = @{
        DefaultServer = "https://jeragm.atlassian.net"
        MaxRetryAttempts = 3
        RetryDelaySeconds = 5
        CacheExpiryMinutes = 30
        MaxCacheSize = 100
        
        # Workflow State Mapping
        WorkflowStates = @{
            New = "To Do"
            InProgress = "In Progress"
            Completed = "Done"
            Failed = "Failed"
            OnHold = "On Hold"
        }
        
        # Custom Field Mapping (Jira Field Name -> AD Attribute Configuration)
        CustomFieldMapping = @{
            "New Joiner Name" = @{
                ADAttributes = @("GivenName", "Surname")
                Parser = "FullNameParser"
                Required = $true
                ValidationPattern = "^[A-Za-z\s\-\.']+ [A-Za-z\s\-\.']+$"
            }
            "Job Title" = @{
                ADAttributes = @("Title")
                Parser = "StringTrimmer"
                Required = $false
                MaxLength = 128
            }
            "Department" = @{
                ADAttributes = @("Department")
                Parser = "StringTrimmer"
                Required = $false
                MaxLength = 64
            }
            "Office Location" = @{
                ADAttributes = @("OU")
                Parser = "LocationToOUMapper"
                Required = $true
                ValidValues = @("United Kingdom", "Singapore", "USA", "Japan")
            }
            "Model Account" = @{
                ADAttributes = @("ModelUser")
                Parser = "StringTrimmer"
                Required = $false
                ValidationPattern = "^[a-zA-Z0-9._-]+$"
            }
            "Start Date" = @{
                ADAttributes = @("StartDate")
                Parser = "DateParser"
                Required = $false
                DateFormat = "yyyy-MM-dd"
            }
            "Manager" = @{
                ADAttributes = @("Manager")
                Parser = "ManagerParser"
                Required = $false
                ValidationPattern = "^[A-Za-z\s\-\.']+ [A-Za-z\s\-\.']+$"
            }
            "Employee ID" = @{
                ADAttributes = @("EmployeeID")
                Parser = "StringTrimmer"
                Required = $false
                ValidationPattern = "^[A-Z0-9]{6,10}$"
            }
            "Phone Number" = @{
                ADAttributes = @("TelephoneNumber")
                Parser = "PhoneNumberParser"
                Required = $false
                ValidationPattern = "^\+?[\d\s\-\(\)\.]{10,20}$"
            }
        }
        
        # Attachment Processing
        AttachmentSettings = @{
            AllowedExtensions = @(".csv", ".xlsx", ".txt", ".json")
            MaxFileSizeMB = 10
            DownloadPath = "C:\Temp\JiraAttachments"
            AutoCleanupDays = 7
            VirusScanEnabled = $false
        }
        
        # Text Extraction Patterns
        TextExtractionPatterns = @{
            Name = @{
                Pattern = '(?i)(?:name|full\s*name|employee\s*name):\s*([A-Za-z\s\-\.'']+)'
                Confidence = "High"
            }
            Email = @{
                Pattern = '(?i)(?:email|e-mail):\s*([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})'
                Confidence = "High"
            }
            Department = @{
                Pattern = '(?i)(?:department|dept|division):\s*([A-Za-z\s\-&]+)'
                Confidence = "Medium"
            }
            JobTitle = @{
                Pattern = '(?i)(?:job\s*title|title|position|role):\s*([A-Za-z\s\-&]+)'
                Confidence = "Medium"
            }
            StartDate = @{
                Pattern = '(?i)(?:start\s*date|joining\s*date|commence):\s*(\d{1,2}[\/\-]\d{1,2}[\/\-]\d{2,4}|\d{4}[\/\-]\d{1,2}[\/\-]\d{1,2})'
                Confidence = "High"
            }
            Manager = @{
                Pattern = '(?i)(?:manager|supervisor|reports\s*to):\s*([A-Za-z\s\-\.'']+)'
                Confidence = "Medium"
            }
            Location = @{
                Pattern = '(?i)(?:location|office|site):\s*([A-Za-z\s\-,]+)'
                Confidence = "Medium"
            }
        }
    }
    
    # Active Directory Configuration
    ActiveDirectory = @{
        DefaultDomain = "jeragm.com"
        DefaultOUs = @{
            "Tokyo" = "OU=Tokyo,OU=Users,OU=Accounts,OU=JeraGM,DC=JERAGM,DC=COM"
            "London" = "OU=Users,OU=London,DC=jeragm,DC=com"
            "Singapore" = "OU=Users,OU=Singapore,DC=jeragm,DC=com"
            "USA" = "OU=Users,OU=USA,DC=jeragm,DC=com"
        }
        DisabledOU = "OU=Disabled,DC=jeragm,DC=com"
        
        # Location to OU Mapping
        LocationMapping = @{
            "United Kingdom" = "OU=Users,OU=London,DC=jeragm,DC=com"
            "Singapore" = "OU=Users,OU=Singapore,DC=jeragm,DC=com"
            "USA" = "OU=Users,OU=USA,DC=jeragm,DC=com"
            "Japan" = "OU=Tokyo,OU=Users,OU=Accounts,OU=JeraGM,DC=JERAGM,DC=COM"
        }
        
        # Password Policy
        PasswordSettings = @{
            Length = 24
            RequireComplexity = $true
            ExcludeAmbiguous = $true
            CustomCharacterSet = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*()-_=+[]{};:,.<>?"
            ExcludeCharacters = @("'", '"', '`', '\')
        }
        
        # User Account Settings
        UserDefaults = @{
            Enabled = $true
            ChangePasswordAtLogon = $false
            PasswordNeverExpires = $false
            CannotChangePassword = $false
            AccountExpirationDate = $null
        }
        
        # SAM Account Name Generation
        SamAccountSettings = @{
            MinLength = 6
            MaxLength = 20
            LastNameChars = 5
            FirstNameStartChars = 1
            ConflictResolutionMethod = "IncrementFirstName" # IncrementFirstName, AddNumbers, AddLocation
        }
        
        # Rollback Settings
        RollbackEnabled = $true
        RollbackRetentionDays = 30
        RollbackActions = @{
            UserCreation = "DisableAndMove"
            GroupMembership = "Remove"
            AttributeChange = "Revert"
        }
        
        # Duplicate Detection
        DuplicateDetection = @{
            CheckByName = $true
            CheckByEmail = $true
            CheckBySamAccount = $true
            CheckByEmployeeId = $true
            SimilarityThreshold = 0.8
        }
    }
    
    # UI Configuration
    UI = @{
        Theme = "Light" # Light, Dark, Auto
        Language = "en-US"
        
        # Responsive Breakpoints
        Breakpoints = @{
            Small = 800
            Medium = 1200
            Large = 1600
            ExtraLarge = 2000
        }
        
        # Notification Settings
        Notifications = @{
            DefaultDuration = 5000
            MaxVisible = 5
            Position = "TopRight" # TopLeft, TopRight, BottomLeft, BottomRight
            EnableSound = $false
            EnableAnimation = $true
        }
        
        # Progress Tracking
        Progress = @{
            UpdateInterval = 500
            ShowETA = $true
            ShowPercentage = $true
            ShowStepDetails = $true
            AnimationEnabled = $true
        }
        
        # Drag and Drop
        DragDrop = @{
            Enabled = $true
            AllowedFileTypes = @(".csv", ".xlsx", ".txt", ".json")
            MaxFileSize = 10485760 # 10MB in bytes
            ShowPreview = $true
        }
        
        # Wizard Settings
        Wizard = @{
            ShowProgressSteps = $true
            AllowSkipSteps = $false
            AutoAdvance = $false
            ValidationOnStepChange = $true
        }
    }
    
    # Security Configuration
    Security = @{
        # Multi-Factor Authentication
        MFA = @{
            Enabled = $false
            Provider = "TOTP" # TOTP, SMS, Email
            RequiredForAdminActions = $true
            TokenValidityMinutes = 5
            BackupCodesEnabled = $true
            BackupCodesCount = 10
        }
        
        # Audit Settings
        Audit = @{
            Enabled = $true
            Level = "Detailed" # Basic, Detailed, Verbose
            RetentionDays = 90
            EncryptLogs = $true
            
            # Audit Destinations
            Destinations = @{
                File = $true
                EventLog = $true
                SIEM = $false
                Database = $false
            }
            
            # Audit Categories
            Categories = @{
                Authentication = $true
                Authorization = $true
                DataAccess = $true
                DataModification = $true
                SystemChanges = $true
                Errors = $true
            }
        }
        
        # Encryption Settings
        Encryption = @{
            Algorithm = "AES256"
            KeyRotationDays = 90
            EncryptSensitiveData = $true
            EncryptedFields = @("Password", "APIToken", "PersonalData")
        }
        
        # Access Control
        AccessControl = @{
            RequireElevatedPrivileges = $true
            AllowedUsers = @() # Empty means all authenticated users
            AllowedGroups = @("Domain Admins", "IT Support")
            DenyUsers = @()
            SessionLockoutMinutes = 30
        }
    }
    
    # Performance Settings
    Performance = @{
        # Caching
        Cache = @{
            DefaultExpiryMinutes = 30
            MaxMemoryMB = 100
            CompressionEnabled = $true
            PersistentCache = $false
            CacheLocation = "Memory" # Memory, Disk, Redis
        }
        
        # Threading
        Threading = @{
            MaxWorkerThreads = 10
            ThreadPoolSize = 5
            AsyncOperationsEnabled = $true
            ParallelProcessingEnabled = $true
        }
        
        # Monitoring
        Monitoring = @{
            PerformanceCountersEnabled = $true
            MemoryMonitoringEnabled = $true
            ResponseTimeThresholdMs = 5000
            CPUThresholdPercent = 80
            MemoryThresholdMB = 500
        }
        
        # Optimization
        Optimization = @{
            LazyLoadingEnabled = $true
            DataCompressionEnabled = $true
            BatchProcessingEnabled = $true
            BatchSize = 50
        }
    }
    
    # Integration Settings
    Integrations = @{
        # Email Notifications
        Email = @{
            Enabled = $false
            SMTPServer = ""
            Port = 587
            UseSSL = $true
            FromAddress = "<EMAIL>"
            Templates = @{
                UserCreated = "Templates\UserCreatedEmail.html"
                UserFailed = "Templates\UserFailedEmail.html"
                BulkCompleted = "Templates\BulkCompletedEmail.html"
            }
        }
        
        # Slack Integration
        Slack = @{
            Enabled = $false
            WebhookURL = ""
            Channel = "#it-automation"
            NotificationTypes = @("Success", "Error", "BulkComplete")
        }
        
        # Teams Integration
        Teams = @{
            Enabled = $false
            WebhookURL = ""
            NotificationTypes = @("Success", "Error", "BulkComplete")
        }
        
        # SIEM Integration
        SIEM = @{
            Enabled = $false
            Endpoint = ""
            Format = "CEF" # CEF, JSON, Syslog
            AuthenticationMethod = "Token" # Token, Certificate, Basic
        }
        
        # ServiceNow Integration
        ServiceNow = @{
            Enabled = $false
            Instance = ""
            Username = ""
            TableName = "incident"
            AutoCreateIncidents = $false
        }
    }
    
    # Validation Rules
    Validation = @{
        # Field Validation Rules
        FieldRules = @{
            FirstName = @{
                Required = $true
                MinLength = 1
                MaxLength = 64
                Pattern = "^[A-Za-z\s\-\.']+$"
                InvalidCharacters = @('@', '#', '$', '%', '^', '&', '*')
            }
            LastName = @{
                Required = $true
                MinLength = 1
                MaxLength = 64
                Pattern = "^[A-Za-z\s\-\.']+$"
                InvalidCharacters = @('@', '#', '$', '%', '^', '&', '*')
            }
            Email = @{
                Required = $false
                Pattern = "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"
                MaxLength = 256
            }
            EmployeeID = @{
                Required = $false
                Pattern = "^[A-Z0-9]{6,10}$"
                MinLength = 6
                MaxLength = 10
            }
        }
        
        # Business Rules
        BusinessRules = @{
            RequireManagerForSeniorRoles = $true
            SeniorRoleKeywords = @("Director", "VP", "President", "CEO", "CTO", "CFO")
            RequireApprovalForBulkOperations = $true
            BulkOperationThreshold = 10
            PreventWeekendOperations = $false
            PreventAfterHoursOperations = $false
        }
    }
    
    # Error Handling
    ErrorHandling = @{
        # Retry Settings
        Retry = @{
            MaxAttempts = 3
            DelaySeconds = 5
            ExponentialBackoff = $true
            RetryableErrors = @("TimeoutException", "NetworkException", "TemporaryFailure")
        }
        
        # Error Reporting
        Reporting = @{
            AutoReportCriticalErrors = $true
            IncludeStackTrace = $true
            IncludeEnvironmentInfo = $true
            SanitizeSensitiveData = $true
        }
        
        # Recovery Actions
        Recovery = @{
            AutoRecoveryEnabled = $true
            RecoveryActions = @{
                "ConnectionLost" = "Reconnect"
                "SessionExpired" = "ReAuthenticate"
                "PermissionDenied" = "ElevatePrivileges"
            }
        }
    }
    
    # Development and Testing
    Development = @{
        # Debug Settings
        Debug = @{
            VerboseLogging = $false
            IncludeTimestamps = $true
            LogToConsole = $true
            LogToFile = $true
            BreakOnErrors = $false
        }
        
        # Testing
        Testing = @{
            MockExternalServices = $false
            UseTestData = $false
            TestDataPath = "TestData\"
            SkipValidation = $false
        }
        
        # Feature Flags
        FeatureFlags = @{
            EnableBetaFeatures = $false
            EnableExperimentalUI = $false
            EnableAdvancedLogging = $false
            EnablePerformanceMetrics = $false
        }
    }
}