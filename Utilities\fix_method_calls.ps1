# Fix static method calls with proper Invoke-Expression syntax
$scriptPath = "OnboardingFromJiraGUI.ps1"
$content = Get-Content $scriptPath -Raw

Write-Host "Fixing static method call syntax..." -ForegroundColor Yellow

# Fix static method calls to use proper syntax
$content = $content -replace '\(Invoke-Expression "\[System\.Windows\.Controls\.Grid\]::(\w+)"\)\(([^)]+)\)', 'Invoke-Expression "[System.Windows.Controls.Grid]::$1($2)"'
$content = $content -replace '\(Invoke-Expression "\[System\.Windows\.Controls\.DockPanel\]::(\w+)"\)\(([^)]+)\)', 'Invoke-Expression "[System.Windows.Controls.DockPanel]::$1($2)"'

$content | Set-Content $scriptPath -Encoding UTF8

Write-Host "Fixed static method call syntax" -ForegroundColor Green