2025-07-01 10:00:31.150 | akinje          | INFO     | JML Automation Script v1.13 starting...
2025-07-01 10:00:31.356 | akinje          | DEBUG    | Script path: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13
2025-07-01 10:00:31.503 | akinje          | DEBUG    | PowerShell version: 7.5.1
2025-07-01 10:00:38.628 | akinje          | DEBUG    | Execution policy: Bypass
2025-07-01 10:00:38.675 | akinje          | DEBUG    | User: akinje
2025-07-01 10:00:38.825 | akinje          | DEBUG    | Computer: JGM-CG7THR3
2025-07-01 10:00:40.445 | akinje          | INFO     | === JML ENVIRONMENT VALIDATION ===
2025-07-01 10:00:40.657 | akin<PERSON>          | INFO     | Verifying PowerShell Version...
2025-07-01 10:00:40.906 | akinje          | INFO     | === JML ENVIRONMENT VALIDATION ===
2025-07-01 10:00:40.969 | akinje          | INFO     | Verifying PowerShell Version...
2025-07-01 10:00:41.086 | akinje          | INFO     | PowerShell 7+ detected (version 7.5.1). Checking for updates...
2025-07-01 10:00:41.189 | akinje          | INFO     | PowerShell 7+ detected (version 7.5.1)
2025-07-01 10:00:41.254 | akinje          | INFO     | Checking for PowerShell updates online...
2025-07-01 10:00:41.454 | akinje          | DEBUG    | Checking for PowerShell updates online...
2025-07-01 10:00:42.208 | akinje          | INFO     | A newer version of PowerShell (7.5.2) is available.
2025-07-01 10:00:42.279 | akinje          | INFO     | Current version: 7.5.1
2025-07-01 10:00:42.344 | akinje          | INFO     | Download from: https://github.com/PowerShell/PowerShell/releases
2025-07-01 10:00:42.455 | akinje          | INFO     | === CHECKING REQUIRED MODULES ===
2025-07-01 10:00:42.534 | akinje          | INFO     | === CHECKING REQUIRED MODULES ===
2025-07-01 10:00:42.688 | akinje          | INFO     | Checking for required modules and available updates...
2025-07-01 10:01:08.986 | akinje          | INFO     | Checking module: Microsoft.PowerShell.SecretManagement...
2025-07-01 10:01:09.255 | akinje          | INFO     |   Found installed version 1.1.2
2025-07-01 10:01:11.493 | akinje          | INFO     |   Already up to date
2025-07-01 10:01:11.546 | akinje          | INFO     | Checking module: Microsoft.PowerShell.SecretStore...
2025-07-01 10:01:12.310 | akinje          | INFO     |   Found installed version 1.0.6
2025-07-01 10:01:12.993 | akinje          | INFO     |   Already up to date
2025-07-01 10:01:13.028 | akinje          | INFO     | Checking module: Microsoft.Graph...
2025-07-01 10:01:13.420 | akinje          | INFO     |   Found installed version 2.28.0
2025-07-01 10:01:14.567 | akinje          | INFO     |   Already up to date
2025-07-01 10:01:14.638 | akinje          | INFO     | Checking module: JiraPS...
2025-07-01 10:01:15.683 | akinje          | INFO     |   Found installed version 2.14.7
2025-07-01 10:01:16.835 | akinje          | INFO     |   Already up to date
2025-07-01 10:01:16.908 | akinje          | INFO     | Checking module: ImportExcel...
2025-07-01 10:01:17.570 | akinje          | INFO     |   Found installed version 7.8.10
2025-07-01 10:01:19.138 | akinje          | INFO     |   Already up to date
2025-07-01 10:01:19.303 | akinje          | INFO     | Checking module: PSWriteHTML...
2025-07-01 10:01:22.560 | akinje          | INFO     |   Found installed version 1.36.0
2025-07-01 10:01:24.143 | akinje          | INFO     |   Already up to date
2025-07-01 10:01:24.356 | akinje          | INFO     | All modules are up to date.
2025-07-01 10:01:24.437 | akinje          | INFO     | Verifying module availability...
2025-07-01 10:01:24.995 | akinje          | INFO     |   [VERIFIED] Microsoft.PowerShell.SecretManagement v1.1.2 is available.
2025-07-01 10:01:25.526 | akinje          | INFO     |   [VERIFIED] Microsoft.PowerShell.SecretStore v1.0.6 is available.
2025-07-01 10:01:25.948 | akinje          | INFO     |   [VERIFIED] Microsoft.Graph v2.28.0 is available.
2025-07-01 10:01:26.358 | akinje          | INFO     |   [VERIFIED] JiraPS v2.14.7 is available.
2025-07-01 10:01:27.362 | akinje          | INFO     |   [VERIFIED] ImportExcel v7.8.10 is available.
2025-07-01 10:01:27.714 | akinje          | INFO     |   [VERIFIED] PSWriteHTML v1.36.0 is available.
2025-07-01 10:01:27.771 | akinje          | INFO     | All required modules are successfully installed and available.
2025-07-01 10:01:27.906 | akinje          | INFO     | === JML ENVIRONMENT VALIDATION COMPLETE ===
2025-07-01 10:01:28.025 | akinje          | DEBUG    | [DEBUG] Starting JML script execution...
2025-07-01 10:01:28.116 | akinje          | DEBUG    | [DEBUG] Script directory: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13
2025-07-01 10:01:28.206 | akinje          | DEBUG    | [DEBUG] Checking ActiveDirectory module availability...
2025-07-01 10:01:28.539 | akinje          | INFO     | [INFO] ActiveDirectory module is not available - running in UAT mode
2025-07-01 10:01:28.608 | akinje          | INFO     | [INFO] This is normal for UAT/test environments
2025-07-01 10:01:28.715 | akinje          | DEBUG    | [DEBUG] Initializing JML paths...
2025-07-01 10:01:28.809 | akinje          | DEBUG    | [DEBUG] JML Paths initialized globally.
2025-07-01 10:01:28.918 | akinje          | DEBUG    | [DEBUG] Project Root: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13
2025-07-01 10:01:29.002 | akinje          | DEBUG    | [DEBUG] Modules Root: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Modules
2025-07-01 10:01:29.337 | akinje          | DEBUG    | [DEBUG] Config Dir: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Config
2025-07-01 10:01:29.549 | akinje          | DEBUG    | [DEBUG] Updated ConfigPath to use Config directory: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Config\AdminAccountConfig.psd1
2025-07-01 10:01:29.692 | akinje          | DEBUG    | [DEBUG] Starting enhanced module loading...
2025-07-01 10:01:29.814 | akinje          | INFO     | Loading JML modules from nested structure...
2025-07-01 10:01:29.939 | akinje          | INFO     | Modules Root: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Modules
2025-07-01 10:01:30.392 | akinje          | INFO     |   Loading: JML-Core/JML-Utilities [CRITICAL]
2025-07-01 10:01:30.578 | akinje          | INFO     |     Description: General utility functions
2025-07-01 10:01:31.355 | akinje          | INFO     |     Path: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Modules\JML-Core\JML-Utilities.psd1 (manifest)
2025-07-01 10:01:32.703 | akinje          | INFO     |     Attempting to import module using manifest...
2025-07-01 10:01:36.023 | akinje          | WARNING  | WARNING: JML-Security: JML-Configuration module functions not available. Some features may be limited.
2025-07-01 10:01:37.666 | akinje          | WARNING  | WARNING: JML-Logging: Missing dependencies: Get-ModuleConfiguration (JML-Configuration), Protect-SensitiveData (JML-Security). Some features may be limited.
2025-07-01 10:01:37.791 | akinje          | INFO     |     [SUCCESS] Module loaded: JML-Core/JML-Utilities
2025-07-01 10:01:37.964 | akinje          | INFO     |   Loading: JML-Core/JML-Configuration [CRITICAL]
2025-07-01 10:01:38.107 | akinje          | INFO     |     Description: Core configuration management
2025-07-01 10:01:38.251 | akinje          | INFO     |     Path: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Modules\JML-Core\JML-Configuration.psd1 (manifest)
2025-07-01 10:01:38.364 | akinje          | INFO     |     Attempting to import module using manifest...
2025-07-01 10:01:38.684 | akinje          | INFO     |     [SUCCESS] Module loaded: JML-Core/JML-Configuration
2025-07-01 10:01:38.815 | akinje          | INFO     |     [VERIFIED] Function Initialize-SmartConfiguration is available
2025-07-01 10:01:39.116 | akinje          | INFO     |     [VERIFIED] Function Get-ModuleConfiguration is available
2025-07-01 10:01:39.314 | akinje          | INFO     |   Loading: JML-Core/JML-Security [CRITICAL]
2025-07-01 10:01:39.416 | akinje          | INFO     |     Description: Security and credential management
2025-07-01 10:01:39.515 | akinje          | INFO     |     Path: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Modules\JML-Core\JML-Security.psd1 (manifest)
2025-07-01 10:01:39.678 | akinje          | INFO     |     Attempting to import module using manifest...
2025-07-01 10:01:39.971 | akinje          | INFO     |     [SUCCESS] Module loaded: JML-Core/JML-Security
2025-07-01 10:01:40.123 | akinje          | INFO     |     [VERIFIED] Function Get-SecureCredential is available
2025-07-01 10:01:40.237 | akinje          | INFO     |     [VERIFIED] Function Set-SecureCredential is available
2025-07-01 10:01:40.376 | akinje          | INFO     |     [VERIFIED] Function Get-StringHash is available
2025-07-01 10:01:40.525 | akinje          | INFO     |   Loading: JML-Core/JML-Logging [CRITICAL]
2025-07-01 10:01:40.632 | akinje          | INFO     |     Description: Secure logging functionality
2025-07-01 10:01:40.727 | akinje          | INFO     |     Path: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Modules\JML-Core\JML-Logging.psd1 (manifest)
2025-07-01 10:01:40.875 | akinje          | INFO     |     Attempting to import module using manifest...
2025-07-01 10:01:41.222 | akinje          | INFO     |     [SUCCESS] Module loaded: JML-Core/JML-Logging
2025-07-01 10:01:41.372 | akinje          | INFO     |     [VERIFIED] Function Write-SecureLog is available
2025-07-01 10:01:41.561 | akinje          | INFO     |     [VERIFIED] Function Initialize-SecureLogging is available
2025-07-01 10:01:41.739 | akinje          | INFO     |   Loading: JML-Features/JML-ActiveDirectory [OPTIONAL]
2025-07-01 10:01:41.847 | akinje          | INFO     |     Description: Active Directory operations
2025-07-01 10:01:42.030 | akinje          | INFO     |     Path: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Modules\JML-Features\JML-ActiveDirectory.psd1 (manifest)
2025-07-01 10:01:42.166 | akinje          | INFO     |     Attempting to import module using manifest...
2025-07-01 10:01:42.698 | akinje          | INFO     |     [SUCCESS] Module loaded: JML-Features/JML-ActiveDirectory
2025-07-01 10:01:42.785 | akinje          | INFO     |   Loading: JML-Features/JML-Email [OPTIONAL]
2025-07-01 10:01:42.892 | akinje          | INFO     |     Description: Email notification functionality
2025-07-01 10:01:43.065 | akinje          | INFO     |     Path: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Modules\JML-Features\JML-Email.psd1 (manifest)
2025-07-01 10:01:43.188 | akinje          | INFO     |     Attempting to import module using manifest...
2025-07-01 10:01:43.533 | akinje          | INFO     |     [SUCCESS] Module loaded: JML-Features/JML-Email
2025-07-01 10:01:43.670 | akinje          | INFO     |   Loading: JML-Features/JML-Jira [OPTIONAL]
2025-07-01 10:01:43.762 | akinje          | INFO     |     Description: Jira integration
2025-07-01 10:01:43.884 | akinje          | INFO     |     Path: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Modules\JML-Features\JML-Jira.psd1 (manifest)
2025-07-01 10:01:43.984 | akinje          | INFO     |     Attempting to import module using manifest...
2025-07-01 10:01:44.589 | akinje          | INFO     |     [SUCCESS] Module loaded: JML-Features/JML-Jira
2025-07-01 10:01:44.760 | akinje          | INFO     |   Loading: JML-Infrastructure/JML-Setup [OPTIONAL]
2025-07-01 10:01:44.875 | akinje          | INFO     |     Description: Setup and environment validation
2025-07-01 10:01:45.095 | akinje          | INFO     |     Path: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Modules\JML-Infrastructure\JML-Setup.psd1 (manifest)
2025-07-01 10:01:45.320 | akinje          | INFO     |     Attempting to import module using manifest...
2025-07-01 10:01:45.739 | akinje          | INFO     |     [SUCCESS] Module loaded: JML-Infrastructure/JML-Setup
2025-07-01 10:01:45.807 | akinje          | INFO     |   Loading: JML-Infrastructure/JML-Monitoring [OPTIONAL]
2025-07-01 10:01:45.911 | akinje          | INFO     |     Description: System monitoring
2025-07-01 10:01:46.085 | akinje          | INFO     |     Path: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Modules\JML-Infrastructure\JML-Monitoring.psd1 (manifest)
2025-07-01 10:01:46.248 | akinje          | INFO     |     Attempting to import module using manifest...
2025-07-01 10:01:46.674 | akinje          | INFO     |     [SUCCESS] Module loaded: JML-Infrastructure/JML-Monitoring
2025-07-01 10:01:46.823 | akinje          | INFO     |   Loading: JML-Infrastructure/JML-UIManager [OPTIONAL]
2025-07-01 10:01:47.109 | akinje          | INFO     |     Description: Modern hybrid dashboard UI with real-time status
2025-07-01 10:01:47.256 | akinje          | INFO     |     Path: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Modules\JML-Infrastructure\JML-UIManager.psd1 (manifest)
2025-07-01 10:01:47.450 | akinje          | INFO     |     Attempting to import module using manifest...
2025-07-01 10:01:47.751 | akinje          | INFO     |     [SUCCESS] Module loaded: JML-Infrastructure/JML-UIManager
2025-07-01 10:01:47.829 | akinje          | INFO     | Module Loading Summary:
2025-07-01 10:01:48.052 | akinje          | INFO     |   Successfully loaded: 10/10
2025-07-01 10:01:48.316 | akinje          | DEBUG    | [DEBUG] Enhanced module loading completed successfully
2025-07-01 10:01:48.678 | akinje          | DEBUG    | [DEBUG] Performing post-loading verification...
2025-07-01 10:01:48.776 | akinje          | DEBUG    | [DEBUG] ✓ Initialize-SmartConfiguration is available
2025-07-01 10:01:48.871 | akinje          | DEBUG    | [DEBUG] ✓ Get-ModuleConfiguration is available
2025-07-01 10:02:25.665 | akinje          | INFO     | Configuration loaded from: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Config\AdminAccountConfig.psd1
2025-07-01 10:02:25.849 | akinje          | INFO     | [SUCCESS] Configuration initialized successfully
2025-07-01 10:02:25.959 | akinje          | DEBUG    | [DEBUG] Initializing credential storage selection...
2025-07-01 10:02:26.382 | akinje          | INFO     | ================================================================================
2025-07-01 10:02:26.564 | akinje          | INFO     |                     JML Credential Storage Selection                      
2025-07-01 10:02:26.738 | akinje          | INFO     | ================================================================================
2025-07-01 10:02:26.980 | akinje          | INFO     | Please select your preferred credential storage method:
2025-07-01 10:02:27.513 | akinje          | INFO     |   1. Encrypted File (Recommended for Portability)
2025-07-01 10:02:27.654 | akinje          | INFO     |      - Stores credentials in an encrypted XML file
2025-07-01 10:02:27.773 | akinje          | INFO     |      - Portable across different machines
2025-07-01 10:02:27.883 | akinje          | INFO     |      - Uses Windows DPAPI for encryption
2025-07-01 10:02:28.005 | akinje          | INFO     |   2. PowerShell Secret Management / SecretStore
2025-07-01 10:02:28.135 | akinje          | INFO     |      - Uses Microsoft.PowerShell.SecretStore module
2025-07-01 10:02:28.276 | akinje          | INFO     |      - Requires vault password for access
2025-07-01 10:02:28.414 | akinje          | INFO     |      - More secure but requires setup
2025-07-01 10:02:28.535 | akinje          | INFO     |   3. Windows Credential Manager
2025-07-01 10:02:28.628 | akinje          | INFO     |      - Uses Windows built-in credential storage
2025-07-01 10:02:28.733 | akinje          | INFO     |      - Integrated with Windows security
2025-07-01 10:02:28.903 | akinje          | INFO     |      - Machine-specific storage
2025-07-01 10:02:29.096 | akinje          | INFO     | Enter your choice (1-3) [Default: 1 in 10 seconds]: 
2025-07-01 10:02:39.441 | akinje          | INFO     | 1 (default - Encrypted File)
2025-07-01 10:02:40.112 | akinje          | INFO     | Selected: Encrypted File storage
2025-07-01 10:02:40.371 | akinje          | INFO     | Credentials will be stored in an encrypted XML file for portability.
2025-07-01 10:02:40.889 | akinje          | INFO     | Credential storage method set for this session: EncryptedFile
2025-07-01 10:02:41.126 | akinje          | INFO     | Press any key to continue...
2025-07-01 10:02:41.475 | akinje          | DEBUG    | [DEBUG] Credential storage method selected: EncryptedFile
2025-07-01 10:02:41.681 | akinje          | DEBUG    | [DEBUG] Using credential method for status check: EncryptedFile
2025-07-01 10:02:41.855 | akinje          | DEBUG    | [DEBUG] Checking credential setup status for method: EncryptedFile
2025-07-01 10:02:42.735 | akinje          | DEBUG    | [DEBUG] Checking EncryptedFile credential storage
2025-07-01 10:02:42.952 | akinje          | DEBUG    | [DEBUG] Checking for encrypted file at: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Config\SecureCredentials.xml
2025-07-01 10:02:43.342 | akinje          | DEBUG    | [DEBUG] EncryptedFile credentials appear to be configured and accessible
2025-07-01 10:02:43.746 | akinje          | INFO     | === JML SERVICE-ORIENTED ARCHITECTURE INITIALIZATION ===
2025-07-01 10:02:44.071 | akinje          | INFO     | Creating JML context...
2025-07-01 10:02:44.856 | akinje          | INFO     | Registering core services...
2025-07-01 10:02:45.051 | akinje          | DEBUG    | Transitioned from early logging to full logging system
