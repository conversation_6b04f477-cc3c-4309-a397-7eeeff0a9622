#Requires -Version 5.1

# Console Test Version of OnboardingFromJiraGUI_v2.ps1
# This version tests the core logic without WPF dependencies

Write-Host "=== JERA Global Markets - Onboarding System v3.0 (Console Test) ===" -ForegroundColor Green
Write-Host "PowerShell Version: $($PSVersionTable.PSVersion)" -ForegroundColor Cyan
Write-Host "Platform: $($PSVersionTable.Platform)" -ForegroundColor Cyan
Write-Host ""

# PowerShell version and compatibility check for icon support
if ($PSVersionTable.PSVersion.Major -lt 5) {
    Write-Host "ERROR: This script requires PowerShell 5.1 or higher. Current version: $($PSVersionTable.PSVersion)" -ForegroundColor Red
    Write-Host "Please upgrade PowerShell or use PowerShell Core 6+" -ForegroundColor Yellow
    exit 1
}

# Dynamic icon selection based on PowerShell version - using character codes to avoid parsing issues
$script:UseUnicode = $PSVersionTable.PSVersion.Major -ge 7

function Get-Icon {
    if ($script:UseUnicode) {
        # Unicode emojis for PowerShell 7+
        return @{
            Connection = [char]0x1F510  # Lock emoji
            Login = [char]0x1F511       # Key emoji
            Logout = [char]0x1F6AA      # Door emoji
            Ticket = [char]0x1F3AB      # Ticket emoji
            Fetch = [char]0x1F4CB       # Clipboard emoji
            Refresh = [char]0x1F504     # Refresh emoji
            Validate = [char]0x2705     # Check mark emoji
            Create = [char]0x1F477      # Construction worker emoji
            Comment = [char]0x1F4AC     # Speech bubble emoji
            Export = [char]0x1F4BE      # Floppy disk emoji
            Copy = [char]0x1F4CB        # Clipboard emoji
            Browser = [char]0x1F310     # Globe emoji
            User = [char]0x1F464        # User emoji
            Success = [char]0x1F389     # Party emoji
            Error = [char]0x26A0        # Warning emoji
            Info = [char]0x2139         # Information emoji
            Settings = [char]0x2699     # Gear emoji
            Wizard = [char]0x1F9D9      # Wizard emoji
            Steps = [char]0x1F4CB       # Steps emoji
        }
    } else {
        # ASCII alternatives for PowerShell 5.x
        return @{
            Connection = "[*]"
            Login = "[+]"
            Logout = "[-]"
            Ticket = "[T]"
            Fetch = "[F]"
            Refresh = "[R]"
            Validate = "[V]"
            Create = "[C]"
            Comment = "[M]"
            Export = "[E]"
            Copy = "[X]"
            Browser = "[B]"
            User = "[U]"
            Success = "[OK]"
            Error = "[!]"
            Info = "[i]"
            Settings = "[S]"
            Wizard = "[W]"
            Steps = "[#]"
        }
    }
}

$script:Icons = Get-Icon

Write-Host "Icon Support: $(if ($script:UseUnicode) { 'Unicode (Emojis)' } else { 'ASCII Fallback' })" -ForegroundColor Cyan
Write-Host ""

# CENTRALIZED CONFIGURATION (same as original)
$script:AppConfig = @{
    Jira = @{
        DefaultUrl = "https://jeragm.atlassian.net"
        FieldMappings = @{
            FirstName = @{ CustomFieldId = "customfield_10304"; DisplayName = "First Name" }
            LastName = @{ CustomFieldId = "customfield_10305"; DisplayName = "Last Name" }
            JobTitle = @{ CustomFieldId = "customfield_10238"; DisplayName = "Job Title" }
            Department = @{ CustomFieldId = "customfield_10120"; DisplayName = "Department" }
            ModelAccount = @{ CustomFieldId = "customfield_10343"; DisplayName = "Model Account" }
            OfficeLocation = @{ CustomFieldId = "customfield_10115"; DisplayName = "Office Location" }
            EmployeeType = @{ CustomFieldId = "customfield_10342"; DisplayName = "Employee Type" }
            EffectiveDate = @{ CustomFieldId = "customfield_10344"; DisplayName = "Effective Date" }
        }
        KnownCustomFields = @("customfield_10304", "customfield_10305", "customfield_10238", "customfield_10120", "customfield_10343", "customfield_10115")
    }
    ActiveDirectory = @{
        OUMappings = @{
            "Singapore" = "OU=Users,OU=Singapore,DC=jeragm,DC=com"
            "United Kingdom" = "OU=Users,OU=London,DC=jeragm,DC=com"
            "Japan" = "OU=Tokyo,OU=Users,OU=Accounts,OU=JeraGM,DC=JERAGM,DC=COM"
            "Tokyo" = "OU=Tokyo,OU=Users,OU=Accounts,OU=JeraGM,DC=JERAGM,DC=COM"
            "USA" = "OU=Users,OU=USA,DC=jeragm,DC=com"
            "United States" = "OU=Users,OU=USA,DC=jeragm,DC=com"
        }
        DefaultOU = "OU=Users,OU=Singapore,DC=jeragm,DC=com"
        ValidationRules = @{
            FirstName = @{
                Required = $true; MinLength = 2; MaxLength = 50
                Pattern = '^[a-zA-Z\s\-\.]+$'
                ErrorMessage = "First name must be 2-50 characters, letters only"
            }
            LastName = @{
                Required = $true; MinLength = 2; MaxLength = 50
                Pattern = '^[a-zA-Z\s\-\.]+$'
                ErrorMessage = "Last name must be 2-50 characters, letters only"
            }
            SamAccountName = @{
                Required = $true; MinLength = 3; MaxLength = 20
                Pattern = '^[a-zA-Z0-9\.]+$'
                ErrorMessage = "Username must be 3-20 characters, alphanumeric and dots only"
            }
        }
        Company = "JERA Global Markets"
        DefaultPassword = "TempPass123!"
        Domain = "jeragm.com"
    }
}

# Configuration accessor function
function Get-AppConfig([string]$Path) {
    $parts = $Path -split '\.'
    $current = $script:AppConfig
    foreach ($part in $parts) {
        if ($current.ContainsKey($part)) { 
            $current = $current[$part] 
        } else { 
            return $null 
        }
    }
    return $current
}

# Standardized Error Handling Helper (same as original)
class ErrorHelper {
    static [hashtable] Success([object]$data, [string]$operation = "") {
        $result = @{ Success = $true; Data = $data }
        if ($operation) { $result.Operation = $operation }
        return $result
    }
    
    static [hashtable] Failure([string]$errorMessage, [string]$operation = "", [System.Exception]$exception = $null) {
        $result = @{ Success = $false; ErrorMessage = $errorMessage }
        if ($operation) { $result.Operation = $operation }
        if ($exception) { $result.Exception = $exception }
        return $result
    }
    
    static [hashtable] SafeExecute([scriptblock]$operation, [string]$operationName, $logger = $null) {
        try {
            if ($logger) { $logger.Debug($operationName, "Starting operation") }
            $result = & $operation
            if ($logger) { $logger.Debug($operationName, "Operation completed successfully") }
            return [ErrorHelper]::Success($result, $operationName)
        } catch {
            if ($logger) { $logger.Error("$operationName failed", $_.Exception) }
            return [ErrorHelper]::Failure($_.Exception.Message, $operationName, $_.Exception)
        }
    }
}

# Logging Service (simplified for console)
class LoggingService {
    [string]$LogLevel = 'INFO'
    [bool]$DebugMode = $true
    [System.Collections.Generic.List[string]]$LogHistory

    LoggingService() {
        $this.LogHistory = [System.Collections.Generic.List[string]]::new()
    }

    hidden [int] GetLevelValue([string]$level) {
        switch ($level.ToUpper()) {
            'DEBUG'   { return 0 }
            'VERBOSE' { return 1 }
            'INFO'    { return 2 }
            'WARN'    { return 3 }
            'ERROR'   { return 4 }
            'SECURITY'{ return 5 }
            default   { return 2 }
        }
    }

    hidden WriteLog([string]$level, [string]$message) {
        if ($this.GetLevelValue($level) -ge $this.GetLevelValue($this.LogLevel)) {
            $logEntry = "[{0}] [{1}] {2}" -f (Get-Date -Format 'yyyy-MM-dd HH:mm:ss'), $level.ToUpper(), $message
            Write-Host $logEntry
            $this.LogHistory.Add($logEntry)
            
            if ($this.LogHistory.Count -gt 1000) {
                $this.LogHistory.RemoveAt(0)
            }
        }
    }

    [void] Info([string]$Message) { $this.WriteLog('INFO', $Message) }
    [void] Warn([string]$Message) { $this.WriteLog('WARN', $Message) }
    [void] Error([string]$Message, [System.Exception]$Exception) { 
        $errMsg = $Message
        if ($Exception) { $errMsg += " | Exception: $($Exception.Message)" }
        $this.WriteLog('ERROR', $errMsg)
    }
    [void] Debug([string]$operation, [string]$message) { 
        if ($this.DebugMode) {
            $this.WriteLog('DEBUG', "[$operation] $message")
        }
    }
}

# JiraService (console version with mock data)
class JiraService {
    $log
    
    JiraService($loggingService) { 
        $this.log = $loggingService 
    }
    
    [hashtable] TestAuthentication([hashtable]$authInfo) {
        $this.log.Info("Testing Jira authentication for $($authInfo.Username)")
        
        # Console mock authentication test
        Start-Sleep -Milliseconds 500
        
        return [ErrorHelper]::Success(@{
            Success = $true
            User = @{
                displayName = "Test User"
                emailAddress = "<EMAIL>"
                accountId = "test123"
            }
            DisplayName = "Test User"
            Email = "<EMAIL>"
            AccountId = "test123"
        }, "MockAuthentication")
    }

    [hashtable] GetTicketDetails([string]$ticketId, [hashtable]$authInfo) {
        $this.log.Info("Fetching ticket details for $ticketId")
        
        # Console mock ticket data
        Start-Sleep -Milliseconds 800
        
        $mockData = @{ 
            summary = "Onboarding Request - Test User ($ticketId)"
            description = "New Joiner Name: Test User`nJob Title: Test Analyst`nDepartment: IT`nModel Account: testmodel"
            customfield_10304 = "John"
            customfield_10305 = "Doe"
            customfield_10238 = "Software Analyst"
            customfield_10120 = "IT"
            customfield_10343 = "testmodel"
            customfield_10115 = "Singapore"
            customfield_10342 = "JERAGM Employee"
            customfield_10344 = "$(Get-Date -Format 'ddd, dd MMM yyyy HH:mm:ss +0000')"
        }
        return [ErrorHelper]::Success($mockData, "MockTicketFetch")
    }

    [hashtable] PostComment([string]$ticketId, [string]$comment, [hashtable]$authInfo) {
        $this.log.Info("Posting comment to ticket $ticketId")
        Start-Sleep -Milliseconds 300
        return [ErrorHelper]::Success(@{ CommentId = "mock123" }, "MockCommentPost")
    }
}

# Configuration Service (same as original)
class ConfigurationService {
    [string[]] GetOUList() {
        $ouMappings = Get-AppConfig -Path "ActiveDirectory.OUMappings"
        return $ouMappings.Values
    }
    
    [string] GetOUForLocation([string]$officeLocation) {
        $ouMappings = Get-AppConfig -Path "ActiveDirectory.OUMappings"
        if ($ouMappings.ContainsKey($officeLocation)) {
            return $ouMappings[$officeLocation]
        }
        return Get-AppConfig -Path "ActiveDirectory.DefaultOU"
    }

    [hashtable] GetJiraFieldMappings() {
        return Get-AppConfig -Path "Jira.FieldMappings"
    }

    [hashtable] ValidateField([string]$fieldName, [string]$value) {
        $rules = Get-AppConfig -Path "ActiveDirectory.ValidationRules"
        if (-not $rules.ContainsKey($fieldName)) {
            return @{ IsValid = $true; ErrorMessage = "" }
        }

        $rule = $rules[$fieldName]
        
        if ($rule.Required -and [string]::IsNullOrWhiteSpace($value)) {
            return @{ IsValid = $false; ErrorMessage = "$fieldName is required" }
        }

        $trimmedValue = $value.Trim()

        if ($rule.ContainsKey('MinLength') -and $trimmedValue.Length -lt $rule.MinLength) {
            return @{ IsValid = $false; ErrorMessage = "$fieldName must be at least $($rule.MinLength) characters" }
        }

        if ($rule.ContainsKey('Pattern') -and $trimmedValue -notmatch $rule.Pattern) {
            return @{ IsValid = $false; ErrorMessage = $rule.ErrorMessage }
        }

        return @{ IsValid = $true; ErrorMessage = "" }
    }
}

# ActiveDirectory Service (console version)
class ActiveDirectoryService {
    [bool]$Simulate
    $log

    ActiveDirectoryService([bool]$simulateMode, $loggingService) { 
        $this.Simulate = $simulateMode
        $this.log = $loggingService
    }

    [hashtable] DoesUserExist([string]$samAccountName) {
        $this.log.Info("Checking for existence of user $samAccountName")
        return [ErrorHelper]::Success($false, "MockUserCheck")
    }

    [hashtable] CreateUser([hashtable]$userDetails) {
        if ($this.Simulate) {
            $this.log.Info("SIMULATION: Creating user $($userDetails.SamAccountName)")
            Start-Sleep -Milliseconds 500
            return [ErrorHelper]::Success("SIMULATION: User created successfully.", "UserCreation-Simulation")
        }
        
        $this.log.Info("Creating AD User $($userDetails.SamAccountName)")
        return [ErrorHelper]::Success("User $($userDetails.SamAccountName) created successfully.", "UserCreation")
    }
}

# CONSOLE TEST RUNNER
function Test-OnboardingWorkflow {
    Write-Host "$($script:Icons.Wizard) Starting Onboarding Workflow Test..." -ForegroundColor Yellow
    Write-Host ""

    # 1. Initialize services
    Write-Host "$($script:Icons.Settings) Initializing Services..." -ForegroundColor Cyan
    $logService = [LoggingService]::new()
    $configService = [ConfigurationService]::new()
    $jiraService = [JiraService]::new($logService)
    $adService = [ActiveDirectoryService]::new($true, $logService)
    Write-Host "$($script:Icons.Success) Services initialized successfully" -ForegroundColor Green
    Write-Host ""

    # 2. Test Jira Authentication
    Write-Host "$($script:Icons.Login) Testing Jira Authentication..." -ForegroundColor Cyan
    $authInfo = @{
        Url = "https://jeragm.atlassian.net"
        Username = "<EMAIL>"
        ApiToken = "mock-token"
    }
    
    $authResult = $jiraService.TestAuthentication($authInfo)
    if ($authResult.Success) {
        Write-Host "$($script:Icons.Success) Authentication successful for user: $($authResult.Data.DisplayName)" -ForegroundColor Green
    } else {
        Write-Host "$($script:Icons.Error) Authentication failed: $($authResult.ErrorMessage)" -ForegroundColor Red
    }
    Write-Host ""

    # 3. Test Ticket Fetching
    Write-Host "$($script:Icons.Fetch) Testing Ticket Fetch..." -ForegroundColor Cyan
    $ticketResult = $jiraService.GetTicketDetails("TEST-123", $authInfo)
    if ($ticketResult.Success) {
        Write-Host "$($script:Icons.Success) Ticket fetched successfully" -ForegroundColor Green
        Write-Host "  Summary: $($ticketResult.Data.summary)" -ForegroundColor Gray
        Write-Host "  First Name: $($ticketResult.Data.customfield_10304)" -ForegroundColor Gray
        Write-Host "  Last Name: $($ticketResult.Data.customfield_10305)" -ForegroundColor Gray
        Write-Host "  Job Title: $($ticketResult.Data.customfield_10238)" -ForegroundColor Gray
        Write-Host "  Department: $($ticketResult.Data.customfield_10120)" -ForegroundColor Gray
    } else {
        Write-Host "$($script:Icons.Error) Ticket fetch failed: $($ticketResult.ErrorMessage)" -ForegroundColor Red
    }
    Write-Host ""

    # 4. Test Data Parsing
    Write-Host "$($script:Icons.Validate) Testing Data Parsing..." -ForegroundColor Cyan
    $mappings = $configService.GetJiraFieldMappings()
    $parsedData = @{}
    
    foreach ($key in $mappings.Keys) {
        $fieldId = $mappings[$key].CustomFieldId
        if ($ticketResult.Data.ContainsKey($fieldId)) {
            $parsedData[$key] = $ticketResult.Data[$fieldId]
        }
    }
    
    Write-Host "$($script:Icons.Success) Data parsed successfully:" -ForegroundColor Green
    foreach ($key in $parsedData.Keys) {
        Write-Host "  $key : $($parsedData[$key])" -ForegroundColor Gray
    }
    Write-Host ""

    # 5. Test Validation
    Write-Host "$($script:Icons.Validate) Testing Field Validation..." -ForegroundColor Cyan
    $validationErrors = @()
    
    foreach ($field in @('FirstName', 'LastName')) {
        if ($parsedData.ContainsKey($field)) {
            $validation = $configService.ValidateField($field, $parsedData[$field])
            if (-not $validation.IsValid) {
                $validationErrors += $validation.ErrorMessage
            }
        }
    }
    
    if ($validationErrors.Count -eq 0) {
        Write-Host "$($script:Icons.Success) All validations passed" -ForegroundColor Green
    } else {
        Write-Host "$($script:Icons.Error) Validation errors:" -ForegroundColor Red
        foreach ($error in $validationErrors) {
            Write-Host "  - $error" -ForegroundColor Red
        }
    }
    Write-Host ""

    # 6. Test Username Generation
    Write-Host "$($script:Icons.User) Testing Username Generation..." -ForegroundColor Cyan
    if ($parsedData.ContainsKey('FirstName') -and $parsedData.ContainsKey('LastName')) {
        $firstName = $parsedData.FirstName
        $lastName = $parsedData.LastName
        $cleanFirstName = $firstName -replace '[^a-zA-Z]', ''
        $cleanLastName = $lastName -replace '[^a-zA-Z]', ''
        $username = ($cleanFirstName.Substring(0,1) + $cleanLastName).ToLower()
        if ($username.Length -gt 20) {
            $username = $username.Substring(0, 20)
        }
        Write-Host "$($script:Icons.Success) Generated username: $username" -ForegroundColor Green
    }
    Write-Host ""

    # 7. Test OU Selection
    Write-Host "$($script:Icons.Settings) Testing OU Selection..." -ForegroundColor Cyan
    $officeLocation = if ($parsedData.ContainsKey('OfficeLocation')) { $parsedData.OfficeLocation } else { "Singapore" }
    $selectedOU = $configService.GetOUForLocation($officeLocation)
    Write-Host "$($script:Icons.Success) Selected OU for $officeLocation : $selectedOU" -ForegroundColor Green
    Write-Host ""

    # 8. Test User Creation
    Write-Host "$($script:Icons.Create) Testing User Creation..." -ForegroundColor Cyan
    if ($parsedData.ContainsKey('FirstName') -and $parsedData.ContainsKey('LastName')) {
        $userDetails = @{
            GivenName = $parsedData.FirstName
            Surname = $parsedData.LastName
            Name = "$($parsedData.FirstName) $($parsedData.LastName)"
            SamAccountName = $username
            UserPrincipalName = "$<EMAIL>"
            Title = if ($parsedData.ContainsKey('JobTitle')) { $parsedData.JobTitle } else { "" }
            Department = if ($parsedData.ContainsKey('Department')) { $parsedData.Department } else { "" }
            Company = "JERA Global Markets"
            Path = $selectedOU
        }
        
        $createResult = $adService.CreateUser($userDetails)
        if ($createResult.Success) {
            Write-Host "$($script:Icons.Success) User creation successful: $($createResult.Data)" -ForegroundColor Green
        } else {
            Write-Host "$($script:Icons.Error) User creation failed: $($createResult.ErrorMessage)" -ForegroundColor Red
        }
    }
    Write-Host ""

    # 9. Test Comment Posting
    Write-Host "$($script:Icons.Comment) Testing Comment Posting..." -ForegroundColor Cyan
    $comment = @"
User onboarding completed:
- Name: $($parsedData.FirstName) $($parsedData.LastName)
- Username: $username
- Department: $($parsedData.Department)
- Job Title: $($parsedData.JobTitle)
- OU: $selectedOU

Account has been created and is ready for use.
"@

    $commentResult = $jiraService.PostComment("TEST-123", $comment, $authInfo)
    if ($commentResult.Success) {
        Write-Host "$($script:Icons.Success) Comment posted successfully" -ForegroundColor Green
    } else {
        Write-Host "$($script:Icons.Error) Comment posting failed: $($commentResult.ErrorMessage)" -ForegroundColor Red
    }
    Write-Host ""

    # 10. Test Summary
    Write-Host "$($script:Icons.Success) Workflow Test Complete!" -ForegroundColor Green
    Write-Host "All core components tested successfully." -ForegroundColor Green
    Write-Host ""
    Write-Host "Recent Log Entries:" -ForegroundColor Cyan
    $recentLogs = $logService.GetRecentLogs(10)
    foreach ($logEntry in $recentLogs) {
        Write-Host "  $logEntry" -ForegroundColor Gray
    }
}

# RUN THE TEST
try {
    Test-OnboardingWorkflow
    Write-Host ""
    Write-Host "$($script:Icons.Success) Console test completed successfully!" -ForegroundColor Green
    Write-Host "The script logic appears to be working correctly." -ForegroundColor Green
    Write-Host "To run the full GUI version, use this script on a Windows machine with PowerShell 5.1+." -ForegroundColor Yellow
} catch {
    Write-Host "$($script:Icons.Error) Test failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Stack trace: $($_.ScriptStackTrace)" -ForegroundColor Red
}