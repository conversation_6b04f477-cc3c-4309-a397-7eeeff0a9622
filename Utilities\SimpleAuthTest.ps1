# Simple authentication test for troubleshooting
param(
    [string]$Email = "<EMAIL>",
    [string]$JiraUrl = "https://jeraglobalmarkets.atlassian.net"
)

Write-Host "=== Simple Jira Authentication Test ===" -ForegroundColor Cyan

# Get API token securely
Write-Host "Please enter your API token:" -ForegroundColor Yellow
$apiTokenSecure = Read-Host -AsSecureString

# Convert to plain text (same method as in the script)
$apiToken = [Runtime.InteropServices.Marshal]::PtrToStringAuto([Runtime.InteropServices.Marshal]::SecureStringToBSTR($apiTokenSecure))

Write-Host "Testing authentication..." -ForegroundColor Green

try {
    # Method 1: Test with minimal headers (no Content-Type)
    Write-Host "Test 1: Minimal headers..." -ForegroundColor Yellow
    $authString = $Email + ":" + $apiToken
    $base64AuthInfo = [Convert]::ToBase64String([Text.Encoding]::ASCII.GetBytes($authString))
    
    $headers1 = @{
        "Authorization" = "Basic $base64AuthInfo"
        "Accept" = "application/json"
    }
    
    $response1 = Invoke-RestMethod -Uri "$JiraUrl/rest/api/3/myself" -Headers $headers1 -Method Get -ErrorAction Stop
    Write-Host "SUCCESS with minimal headers" -ForegroundColor Green
    Write-Host "User: $($response1.displayName) ($($response1.emailAddress))" -ForegroundColor Gray
    
    # Test basic ticket fetch
    Write-Host "Test 2: Fetching a ticket..." -ForegroundColor Yellow
    $testTicket = "ITREQ-4068"
    $ticketResponse = Invoke-RestMethod -Uri "$JiraUrl/rest/api/3/issue/$testTicket" -Headers $headers1 -Method Get -ErrorAction Stop
    Write-Host "SUCCESS fetching ticket $testTicket" -ForegroundColor Green
    Write-Host "Status: $($ticketResponse.fields.status.name)" -ForegroundColor Gray
    
} catch {
    Write-Host "FAILED: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        $statusCode = $_.Exception.Response.StatusCode
        Write-Host "Status Code: $statusCode" -ForegroundColor Red
        
        if ($statusCode -eq 401) {
            Write-Host "Debugging 401 Unauthorized:" -ForegroundColor Yellow
            Write-Host "- Check if email address is correct: $Email" -ForegroundColor White
            Write-Host "- Check if API token is valid and not expired" -ForegroundColor White
            Write-Host "- Verify you have permissions to access Jira" -ForegroundColor White
        }
    }
}

Write-Host "=== Test Complete ===" -ForegroundColor Cyan