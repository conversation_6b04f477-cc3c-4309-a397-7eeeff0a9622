# Test SAM Generation Fix - Quick Test
# This script tests if the SAM corruption issue is fixed

Write-Host "Testing SAM Generation Fix..." -ForegroundColor <PERSON><PERSON>

try {
    # Test the SAM function directly without loading the full GUI
    Write-Host "Testing standalone SAM function..." -ForegroundColor Yellow

    # Copy the exact SAM generation logic from the main script
    function Get-SamAccountName {
        param([string]$FirstName, [string]$LastName)

        # Remove spaces from names for SAM account generation
        $FirstName = $FirstName -replace '\s+', ''
        $LastName = $LastName -replace '\s+', ''

        $firstNameIndex = 1
        $isUnique = $false
        $minLengthSatisfied = $false

        while (-not $isUnique -and $firstNameIndex -le $FirstName.Length) {
            # Get increasing portions of the first name
            $firstNamePart = $FirstName.Substring(0, $firstNameIndex).ToLower()
            $proposedSam = $firstNamePart + $LastName.ToLower()

            # Check minimum length requirement (6 characters)
            if ($proposedSam.Length -ge 6) {
                $minLengthSatisfied = $true
            } else {
                if ($firstNameIndex -lt $FirstName.Length) {
                    $firstNameIndex++
                    continue
                } else {
                    $minLengthSatisfied = $false
                }
            }

            # For testing, assume it's unique
            $isUnique = $true
        }

        return [string]$proposedSam
    }

    # Test the exact same scenario from the logs
    Write-Host "`nTesting SAM generation for 'Test1 Test2'..." -ForegroundColor Yellow
    $testSam = Get-SamAccountName -FirstName "Test1" -LastName "Test2"

    Write-Host "Generated SAM: '$testSam'" -ForegroundColor White
    Write-Host "SAM Type: $($testSam.GetType().Name)" -ForegroundColor White
    Write-Host "SAM Length: $($testSam.Length)" -ForegroundColor White

    if ($testSam -eq "test2t") {
        Write-Host "✅ SAM generation test PASSED!" -ForegroundColor Green
    } else {
        Write-Host "❌ SAM generation test FAILED! Expected 'test2t', got '$testSam'" -ForegroundColor Red
    }

    # Test that the variable remains uncorrupted without debug operations
    Write-Host "`nTesting variable integrity without debug operations..." -ForegroundColor Yellow

    # Verify the original variable is still intact
    Write-Host "Original SAM: '$testSam'" -ForegroundColor White
    Write-Host "Original SAM length: $($testSam.Length)" -ForegroundColor White

    if ($testSam -eq "test2t") {
        Write-Host "✅ Variable integrity test PASSED!" -ForegroundColor Green
        Write-Host "✅ SAM corruption fix is working correctly!" -ForegroundColor Green
    } else {
        Write-Host "❌ Variable integrity test FAILED!" -ForegroundColor Red
        Write-Host "Expected: 'test2t', Got: '$testSam'" -ForegroundColor Red
    }

} catch {
    Write-Host "❌ Error during test:" -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Red
    Write-Host "Line: $($_.InvocationInfo.ScriptLineNumber)" -ForegroundColor Red
}

Write-Host "`nSAM fix test complete" -ForegroundColor Cyan
