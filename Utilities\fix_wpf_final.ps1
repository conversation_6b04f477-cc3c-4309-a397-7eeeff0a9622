# Fix WPF type references to avoid parse-time resolution
$scriptPath = "OnboardingFromJiraGUI.ps1"
$content = Get-Content $scriptPath -Raw

# Replace specific problematic type references with runtime-safe alternatives
$content = $content -replace '\[System\.Windows\.Media\.Brushes\]::LightGreen', 'New-Object System.Windows.Media.SolidColorBrush([System.Windows.Media.Colors]::LightGreen)'
$content = $content -replace '\[System\.Windows\.Media\.Brushes\]::Red', 'New-Object System.Windows.Media.SolidColorBrush([System.Windows.Media.Colors]::Red)'
$content = $content -replace '\[System\.Windows\.Media\.Brushes\]::White', 'New-Object System.Windows.Media.SolidColorBrush([System.Windows.Media.Colors]::White)'
$content = $content -replace '\[System\.Windows\.Thickness\]::new\(([^)]+)\)', 'New-Object System.Windows.Thickness($1)'

# Write the fixed content back
$content | Set-Content $scriptPath -Encoding UTF8

Write-Host "Fixed WPF type references successfully!" -ForegroundColor Green