# Fix encoding and ampersand issues in comments
$scriptPath = "OnboardingFromJiraGUI.ps1"
$content = Get-Content $scriptPath -Raw

Write-Host "Fixing encoding and ampersand issues..." -ForegroundColor Yellow

# Fix ampersands in comments by wrapping them in quotes
$content = $content -replace '(#.*?)&(\s)', '$1"&"$2'

# Fix any other problematic characters in comments
$content = $content -replace 'â€¢', '•'
$content = $content -replace 'â€™', "'"
$content = $content -replace 'â€œ', '"'
$content = $content -replace 'â€', '"'

$content | Set-Content $scriptPath -Encoding UTF8

Write-Host "Fixed encoding and ampersand issues" -ForegroundColor Green