# Comprehensive Workflow Test Suite
# This simulates the entire onboarding workflow without WPF UI
param(
    [string]$JiraUrl = "https://jira.jeragm.com",
    [string]$JiraUsername = "",
    [string]$JiraApiToken = "",
    [string]$TestTicketId = "",
    [switch]$UseRealJira = $false,
    [switch]$Verbose = $false
)

Write-Host "=== ONBOARDING WORKFLOW TEST SUITE ===" -ForegroundColor Yellow
Write-Host "Testing Mode: $( if ($UseRealJira) { 'REAL JIRA' } else { 'MOCK DATA' } )" -ForegroundColor $( if ($UseRealJira) { 'Red' } else { 'Green' } )

# Global test results
$script:TestResults = @()
$script:TestData = @{}

function Write-TestLog {
    param([string]$Message, [string]$Level = "INFO")
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $color = switch ($Level) {
        "ERROR" { "Red" }
        "WARN" { "Yellow" }
        "SUCCESS" { "Green" }
        "DEBUG" { "Gray" }
        default { "White" }
    }
    if ($Verbose -or $Level -ne "DEBUG") {
        Write-Host "[$timestamp] [$Level] $Message" -ForegroundColor $color
    }
}

function Add-TestResult {
    param([string]$TestName, [bool]$Passed, [string]$Details = "")
    $script:TestResults += @{
        TestName = $TestName
        Passed = $Passed
        Details = $Details
        Timestamp = Get-Date
    }
    $status = if ($Passed) { "PASS" } else { "FAIL" }
    $color = if ($Passed) { "Green" } else { "Red" }
    Write-TestLog "${TestName}: $status - $Details" $status
}

# ==============================================
# STEP 1: TEST SERVICE CLASSES
# ==============================================

Write-TestLog "=== TESTING SERVICE CLASSES ===" "INFO"

# Test LoggingService
try {
    Write-TestLog "Testing LoggingService..." "DEBUG"
    
    class LoggingService {
        [string]$LogLevel = 'INFO'
        [System.Collections.Generic.List[string]]$LogHistory

        LoggingService() {
            $this.LogHistory = [System.Collections.Generic.List[string]]::new()
        }

        hidden [int] GetLevelValue([string]$level) {
            switch ($level.ToUpper()) {
                'DEBUG'   { return 0 }
                'VERBOSE' { return 1 }
                'INFO'    { return 2 }
                'WARN'    { return 3 }
                'ERROR'   { return 4 }
                'SECURITY'{ return 5 }
                default   { return 2 }
            }
            return 2
        }

        hidden WriteLog([string]$level, [string]$message) {
            if ($this.GetLevelValue($level) -ge $this.GetLevelValue($this.LogLevel)) {
                $logEntry = "[{0}] [{1}] {2}" -f (Get-Date -Format 'yyyy-MM-dd HH:mm:ss'), $level.ToUpper(), $message
                if ($script:Verbose) { Write-Host $logEntry }
                $this.LogHistory.Add($logEntry)
                
                if ($this.LogHistory.Count -gt 1000) {
                    $this.LogHistory.RemoveAt(0)
                }
            }
        }

        [void] Info([string]$Message) { $this.WriteLog('INFO', $Message) }
        [void] Warn([string]$Message) { $this.WriteLog('WARN', $Message) }
        [void] Error([string]$Message, [System.Exception]$Exception) { 
            $errMsg = $Message
            if ($Exception) { $errMsg += " | Exception: $($Exception.Message)" }
            $this.WriteLog('ERROR', $errMsg)
        }
        [void] Debug([string]$Message) { $this.WriteLog('DEBUG', $Message) }
    }
    
    $logService = [LoggingService]::new()
    $logService.Info("Test message")
    $logService.Error("Test error", $null)
    
    Add-TestResult "LoggingService Creation" ($logService.LogHistory.Count -ge 2) "Created with $($logService.LogHistory.Count) log entries"
} catch {
    Add-TestResult "LoggingService Creation" $false $_.Exception.Message
}

# Test ConfigurationService
try {
    Write-TestLog "Testing ConfigurationService..." "DEBUG"
    
    class ConfigurationService {
        [string[]] GetOUList() {
            return @(
                "OU=Tokyo,OU=Users,OU=Accounts,OU=JeraGM,DC=JERAGM,DC=COM",
                "OU=Users,OU=London,DC=jeragm,DC=com",
                "OU=Users,OU=Singapore,DC=jeragm,DC=com",
                "OU=Users,OU=USA,DC=jeragm,DC=com"
            )
        }

        [hashtable] GetJiraFieldMappings() {
            return @{
                FirstName    = @{ CustomFieldId = "customfield_10304"; RegexPattern = "New Joiner Name:\s*([^`r`n]+)" }
                LastName     = @{ CustomFieldId = "customfield_10305"; RegexPattern = "Last Name:\s*([^`r`n]+)" }
                JobTitle     = @{ CustomFieldId = "customfield_10238"; RegexPattern = "Job Title:\s*([^`r`n]+)" }
                Department   = @{ CustomFieldId = "customfield_10120"; RegexPattern = "Department:\s*([^`r`n]+)" }
                ModelAccount = @{ CustomFieldId = "customfield_10343"; RegexPattern = "Model Account:\s*([^`r`n]+)" }
            }
        }

        [hashtable] ValidateField([string]$fieldName, [string]$value) {
            $rules = @{
                FirstName = @{ Required = $true; MinLength = 2; MaxLength = 50; Pattern = '^[a-zA-Z\s\-\.]+$' }
                LastName = @{ Required = $true; MinLength = 2; MaxLength = 50; Pattern = '^[a-zA-Z\s\-\.]+$' }
            }
            
            if (-not $rules.ContainsKey($fieldName)) {
                return @{ IsValid = $true; ErrorMessage = "" }
            }

            $rule = $rules[$fieldName]
            
            if ($rule.Required -and [string]::IsNullOrWhiteSpace($value)) {
                return @{ IsValid = $false; ErrorMessage = "$fieldName is required" }
            }

            $trimmedValue = $value.Trim()
            if ($rule.ContainsKey('MinLength') -and $trimmedValue.Length -lt $rule.MinLength) {
                return @{ IsValid = $false; ErrorMessage = "$fieldName must be at least $($rule.MinLength) characters" }
            }

            if ($rule.ContainsKey('Pattern') -and $trimmedValue -notmatch $rule.Pattern) {
                return @{ IsValid = $false; ErrorMessage = "$fieldName contains invalid characters" }
            }

            return @{ IsValid = $true; ErrorMessage = "" }
        }
    }
    
    $configService = [ConfigurationService]::new()
    $ous = $configService.GetOUList()
    $mappings = $configService.GetJiraFieldMappings()
    
    Add-TestResult "ConfigurationService Creation" ($ous.Count -eq 4 -and $mappings.Count -eq 5) "OUs: $($ous.Count), Mappings: $($mappings.Count)"
} catch {
    Add-TestResult "ConfigurationService Creation" $false $_.Exception.Message
}

# ==============================================
# STEP 2: TEST JIRA INTEGRATION
# ==============================================

Write-TestLog "=== TESTING JIRA INTEGRATION ===" "INFO"

class JiraService {
    $log
    [string]$BaseUrl
    [pscredential]$Credential
    [bool]$UseRealJira
    
    JiraService($loggingService, $baseUrl, $credential, $useReal) { 
        $this.log = $loggingService
        $this.BaseUrl = $baseUrl
        $this.Credential = $credential
        $this.UseRealJira = $useReal
    }

    [hashtable] GetTicketDetails([string]$ticketId, [pscredential]$credential) {
        try {
            $this.log.Info("Fetching ticket details for $ticketId")
            
            if ($this.UseRealJira -and $this.Credential) {
                return $this.GetRealTicketDetails($ticketId)
            } else {
                return $this.GetMockTicketDetails($ticketId)
            }
        } catch {
            $this.log.Error("Failed to get ticket $ticketId", $_.Exception)
            return @{ Success = $false; ErrorMessage = $_.Exception.Message }
        }
    }
    
    [hashtable] GetRealTicketDetails([string]$ticketId) {
        try {
            $authHeader = @{
                Authorization = "Basic " + [Convert]::ToBase64String([Text.Encoding]::ASCII.GetBytes("$($this.Credential.UserName):$($this.Credential.GetNetworkCredential().Password)"))
                Accept = "application/json"
            }
            
            $uri = "$($this.BaseUrl)/rest/api/2/issue/$ticketId"
            Write-TestLog "Making REST call to: $uri" "DEBUG"
            
            $response = Invoke-RestMethod -Uri $uri -Headers $authHeader -Method Get -TimeoutSec 30
            
            $ticketData = @{
                summary = $response.fields.summary
                description = $response.fields.description
            }
            
            # Add custom fields
            foreach ($key in $response.fields.PSObject.Properties.Name) {
                if ($key -like "customfield_*") {
                    $ticketData[$key] = $response.fields.$key
                }
            }
            
            return @{ Success = $true; Data = $ticketData }
        } catch {
            Write-TestLog "REST API call failed: $($_.Exception.Message)" "ERROR"
            return @{ Success = $false; ErrorMessage = $_.Exception.Message }
        }
    }
    
    [hashtable] GetMockTicketDetails([string]$ticketId) {
        Start-Sleep -Milliseconds 500  # Simulate network delay
        $mockData = @{ 
            summary = "Onboard New Analyst: John Doe ($ticketId)"
            description = "Please onboard John Doe to the Trading team."
            customfield_10304 = "John"
            customfield_10305 = "Doe"
            customfield_10238 = "Trading Analyst"
            customfield_10120 = "Trading"
            customfield_10343 = "jsmith.model"
        }
        return @{ Success = $true; Data = $mockData }
    }

    [hashtable] PostComment([string]$ticketId, [string]$comment, [pscredential]$credential) {
        try {
            $this.log.Info("Posting comment to ticket $ticketId")
            if ($this.UseRealJira -and $this.Credential) {
                # Implement real comment posting
                $authHeader = @{
                    Authorization = "Basic " + [Convert]::ToBase64String([Text.Encoding]::ASCII.GetBytes("$($this.Credential.UserName):$($this.Credential.GetNetworkCredential().Password)"))
                    Accept = "application/json"
                    "Content-Type" = "application/json"
                }
                
                $body = @{
                    body = $comment
                } | ConvertTo-Json
                
                $uri = "$($this.BaseUrl)/rest/api/2/issue/$ticketId/comment"
                $response = Invoke-RestMethod -Uri $uri -Headers $authHeader -Method Post -Body $body -TimeoutSec 30
                return @{ Success = $true }
            } else {
                Start-Sleep -Milliseconds 300
                return @{ Success = $true }
            }
        } catch {
            $this.log.Error("Failed to post comment to ticket $ticketId", $_.Exception)
            return @{ Success = $false; ErrorMessage = $_.Exception.Message }
        }
    }
}

# Test Jira Service
try {
    $credential = $null
    if ($UseRealJira -and $JiraUsername -and $JiraApiToken) {
        $secureToken = ConvertTo-SecureString $JiraApiToken -AsPlainText -Force
        $credential = New-Object System.Management.Automation.PSCredential($JiraUsername, $secureToken)
    }
    
    $jiraService = [JiraService]::new($logService, $JiraUrl, $credential, $UseRealJira)
    $testTicket = if ($TestTicketId) { $TestTicketId } else { "TEST-123" }
    
    Write-TestLog "Testing ticket fetch for: $testTicket" "DEBUG"
    $ticketResult = $jiraService.GetTicketDetails($testTicket, $credential)
    
    $script:TestData.TicketData = $ticketResult
    Add-TestResult "Jira Ticket Fetch" $ticketResult.Success "Ticket: $testTicket, Success: $($ticketResult.Success)"
    
    if ($ticketResult.Success) {
        Write-TestLog "Ticket Summary: $($ticketResult.Data.summary)" "DEBUG"
        Write-TestLog "Custom Fields Found: $($ticketResult.Data.Keys | Where-Object { $_ -like 'customfield_*' } | Measure-Object | Select-Object -ExpandProperty Count)" "DEBUG"
    }
} catch {
    Add-TestResult "Jira Service Creation" $false $_.Exception.Message
}

# ==============================================
# STEP 3: TEST DATA PARSING
# ==============================================

Write-TestLog "=== TESTING DATA PARSING ===" "INFO"

function ParseTicketData($jiraTicket) {
    $parsedData = @{}
    $mappings = @{
        FirstName    = @{ CustomFieldId = "customfield_10304" }
        LastName     = @{ CustomFieldId = "customfield_10305" }
        JobTitle     = @{ CustomFieldId = "customfield_10238" }
        Department   = @{ CustomFieldId = "customfield_10120" }
        ModelAccount = @{ CustomFieldId = "customfield_10343" }
    }
    
    foreach ($key in $mappings.Keys) {
        $fieldId = $mappings[$key].CustomFieldId
        if ($jiraTicket.Data.ContainsKey($fieldId)) {
            $parsedData[$key] = $jiraTicket.Data[$fieldId]
        }
    }
    return $parsedData
}

try {
    if ($script:TestData.TicketData -and $script:TestData.TicketData.Success) {
        $parsedData = ParseTicketData $script:TestData.TicketData
        $script:TestData.ParsedData = $parsedData
        
        $requiredFields = @('FirstName', 'LastName', 'JobTitle', 'Department')
        $foundFields = $requiredFields | Where-Object { $parsedData.ContainsKey($_) -and ![string]::IsNullOrEmpty($parsedData[$_]) }
        
        Add-TestResult "Data Parsing" ($foundFields.Count -eq $requiredFields.Count) "Found $($foundFields.Count)/$($requiredFields.Count) required fields"
        
        if ($parsedData.Count -gt 0) {
            Write-TestLog "Parsed Data:" "DEBUG"
            foreach ($key in $parsedData.Keys) {
                Write-TestLog "  $key : $($parsedData[$key])" "DEBUG"
            }
        }
    } else {
        Add-TestResult "Data Parsing" $false "No ticket data available to parse"
    }
} catch {
    Add-TestResult "Data Parsing" $false $_.Exception.Message
}

# ==============================================
# STEP 4: TEST VALIDATION
# ==============================================

Write-TestLog "=== TESTING VALIDATION ===" "INFO"

try {
    if ($script:TestData.ParsedData) {
        $configService = [ConfigurationService]::new()
        $validationResults = @{}
        
        foreach ($field in @('FirstName', 'LastName')) {
            if ($script:TestData.ParsedData.ContainsKey($field)) {
                $value = $script:TestData.ParsedData[$field]
                $validationResults[$field] = $configService.ValidateField($field, $value)
                Write-TestLog "Validation $field '$value': $($validationResults[$field].IsValid)" "DEBUG"
            }
        }
        
        $allValid = ($validationResults.Values | Where-Object { -not $_.IsValid }).Count -eq 0
        Add-TestResult "Field Validation" $allValid "Validated $($validationResults.Count) fields"
        
        $script:TestData.ValidationResults = $validationResults
    } else {
        Add-TestResult "Field Validation" $false "No parsed data available for validation"
    }
} catch {
    Add-TestResult "Field Validation" $false $_.Exception.Message
}

# ==============================================
# STEP 5: TEST USER CREATION (SIMULATION)
# ==============================================

Write-TestLog "=== TESTING USER CREATION (SIMULATION) ===" "INFO"

class ActiveDirectoryService {
    [bool]$Simulate
    $log

    ActiveDirectoryService([bool]$simulateMode, $loggingService) { 
        $this.Simulate = $simulateMode
        $this.log = $loggingService
    }

    [hashtable] DoesUserExist([string]$samAccountName) {
        $this.log.Info("Checking for existence of user $samAccountName")
        # Always return false for testing
        return @{ Success = $true; Exists = $false }
    }

    [hashtable] CreateUser([hashtable]$userDetails) {
        if ($this.Simulate) {
            $this.log.Info("SIMULATION: Creating user $($userDetails.SamAccountName)")
            Start-Sleep -Milliseconds 300
            return @{ Success = $true; Message = "SIMULATION: User created successfully." }
        }
        # In real implementation, would use New-ADUser
        return @{ Success = $true; Message = "User $($userDetails.SamAccountName) created successfully." }
    }
}

function GenerateUsername([string]$firstName, [string]$lastName) {
    if ([string]::IsNullOrEmpty($firstName) -or [string]::IsNullOrEmpty($lastName)) {
        return "tempuser"
    }
    
    $cleanFirstName = $firstName -replace '[^a-zA-Z]', ''
    $cleanLastName = $lastName -replace '[^a-zA-Z]', ''
    
    if ($cleanFirstName.Length -eq 0 -or $cleanLastName.Length -eq 0) {
        return "tempuser"
    }
    
    $username = ($cleanFirstName.Substring(0,1) + $cleanLastName).ToLower()
    if ($username.Length -gt 20) {
        $username = $username.Substring(0, 20)
    }
    return $username
}

try {
    $adService = [ActiveDirectoryService]::new($true, $logService)
    
    if ($script:TestData.ParsedData -and $script:TestData.ParsedData.ContainsKey('FirstName') -and $script:TestData.ParsedData.ContainsKey('LastName')) {
        $firstName = $script:TestData.ParsedData.FirstName
        $lastName = $script:TestData.ParsedData.LastName
        $username = GenerateUsername $firstName $lastName
        
        # Check if user exists
        $userExistsResult = $adService.DoesUserExist($username)
        
        if ($userExistsResult.Success -and -not $userExistsResult.Exists) {
            # Create user details
            $userDetails = @{
                GivenName = $firstName
                Surname = $lastName
                Name = "$firstName $lastName"
                SamAccountName = $username
                UserPrincipalName = "$<EMAIL>"
                Title = if ($script:TestData.ParsedData.ContainsKey('JobTitle')) { $script:TestData.ParsedData.JobTitle } else { "" }
                Department = if ($script:TestData.ParsedData.ContainsKey('Department')) { $script:TestData.ParsedData.Department } else { "" }
                Path = "CN=Users,DC=jeragm,DC=com"
                Enabled = $true
            }
            
            $createResult = $adService.CreateUser($userDetails)
            Add-TestResult "User Creation" $createResult.Success "Username: $username, Result: $($createResult.Message)"
            
            $script:TestData.Username = $username
            $script:TestData.UserDetails = $userDetails
        } else {
            Add-TestResult "User Creation" $false "User $username already exists or check failed"
        }
    } else {
        Add-TestResult "User Creation" $false "Missing required name data"
    }
} catch {
    Add-TestResult "User Creation" $false $_.Exception.Message
}

# ==============================================
# STEP 6: TEST JIRA COMMENT POSTING
# ==============================================

Write-TestLog "=== TESTING JIRA COMMENT POSTING ===" "INFO"

try {
    if ($script:TestData.Username -and $script:TestData.UserDetails) {
        $comment = @"
User onboarding completed:
- Name: $($script:TestData.UserDetails.Name)
- Username: $($script:TestData.Username)
- Department: $($script:TestData.UserDetails.Department)
- Job Title: $($script:TestData.UserDetails.Title)

Account has been created and is ready for use.
"@
        
        $testTicket = if ($TestTicketId) { $TestTicketId } else { "TEST-123" }
        $commentResult = $jiraService.PostComment($testTicket, $comment, $credential)
        
        Add-TestResult "Jira Comment Post" $commentResult.Success "Posted to $testTicket"
    } else {
        Add-TestResult "Jira Comment Post" $false "No user data available for comment"
    }
} catch {
    Add-TestResult "Jira Comment Post" $false $_.Exception.Message
}

# ==============================================
# FINAL RESULTS
# ==============================================

Write-TestLog "=== TEST RESULTS SUMMARY ===" "INFO"

$totalTests = $script:TestResults.Count
$passedTests = ($script:TestResults | Where-Object { $_.Passed }).Count
$failedTests = $totalTests - $passedTests

Write-Host "`n=== DETAILED RESULTS ===" -ForegroundColor Yellow
foreach ($result in $script:TestResults) {
    $status = if ($result.Passed) { "✓ PASS" } else { "✗ FAIL" }
    $color = if ($result.Passed) { "Green" } else { "Red" }
    Write-Host "$status $($result.TestName)" -ForegroundColor $color
    if ($result.Details) {
        Write-Host "    $($result.Details)" -ForegroundColor Gray
    }
}

Write-Host "`n=== SUMMARY ===" -ForegroundColor Yellow
Write-Host "Total Tests: $totalTests" -ForegroundColor White
Write-Host "Passed: $passedTests" -ForegroundColor Green
Write-Host "Failed: $failedTests" -ForegroundColor Red
Write-Host "Success Rate: $([math]::Round(($passedTests/$totalTests)*100, 1))%" -ForegroundColor $(if ($failedTests -eq 0) { "Green" } else { "Yellow" })

if ($failedTests -eq 0) {
    Write-Host "`n🎉 ALL TESTS PASSED! The workflow is working correctly." -ForegroundColor Green
} else {
    Write-Host "`n⚠️  Some tests failed. Check the details above." -ForegroundColor Yellow
}

Write-Host "`n=== DATA COLLECTED ===" -ForegroundColor Yellow
if ($script:TestData.ParsedData) {
    Write-Host "User Data Extracted:" -ForegroundColor Cyan
    foreach ($key in $script:TestData.ParsedData.Keys) {
        Write-Host "  $key : $($script:TestData.ParsedData[$key])" -ForegroundColor White
    }
}

if ($script:TestData.Username) {
    Write-Host "Generated Username: $($script:TestData.Username)" -ForegroundColor Cyan
}

Write-Host "`n=== TEST COMPLETE ===" -ForegroundColor Yellow