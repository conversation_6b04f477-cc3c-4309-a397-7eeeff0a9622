#Requires -Version 5.1

<#
.SYNOPSIS
JML Logging Management Module

.DESCRIPTION
This module provides secure logging functionality for the JML (Joiner, Mover, Leaver) 
admin account management script. It handles log initialization, secure logging with 
data redaction, audit trails, and log file management.

.NOTES
Version:        2.0
Author:         <PERSON>
Creation Date:  2025-01-09
Security Level: Enhanced with comprehensive data protection

Dependencies:
- PowerShell 5.1 or higher
- JML-Configuration module
- JML-Security module
#>

# Import required modules
Import-Module (Join-Path $PSScriptRoot "JML-Configuration.psm1") -Force
Import-Module (Join-Path $PSScriptRoot "JML-Security.psm1") -Force

# Module variables
$script:CurrentLogPath = $null

<#
.SYNOPSIS
Initializes secure logging with comprehensive audit trail and data redaction.

.DESCRIPTION
Creates log files with enhanced security features including data redaction,
audit trail logging, and configurable retention policies. Implements
comprehensive error handling and resource cleanup.

.PARAMETER StandardUserUPN
The UPN of the user for whom the admin account operation is being performed.

.EXAMPLE
Initialize-SecureLogging -StandardUserUPN "<EMAIL>"

.NOTES
Security: Automatically redacts sensitive information and maintains audit trails.
#>
function Initialize-SecureLogging {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true)]
        [ValidateNotNullOrEmpty()]
        [string]$StandardUserUPN
    )

    try {
        Write-Progress -Activity "Initializing Logging" -Status "Setting up secure logging..." -PercentComplete 50

        # Get configuration
        $config = Get-ModuleConfiguration

        # Use configuration or fallback to default
        $logDirectory = if ($config) {
            $config.Logging.LogDirectory
        } else {
            "C:\Temp\Scripts\Desktop Support\Logs"
        }

        # Create log directory if it doesn't exist
        if (-not (Test-Path -Path $logDirectory)) {
            New-Item -ItemType Directory -Path $logDirectory -Force | Out-Null
        }

        # Generate unique log file name with timestamp and redacted user info
        $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
        $redactedUser = Protect-SensitiveData -Text $StandardUserUPN -RedactionType "UPN"
        $userHash = Get-StringHash -InputString $StandardUserUPN
        $script:CurrentLogPath = Join-Path $logDirectory "AdminAccount_${userHash}_${timestamp}.log"

        # Get execution context
        $executionContext = @{
            ExecutingUser = [System.Security.Principal.WindowsIdentity]::GetCurrent().Name
            ComputerName = $env:COMPUTERNAME
            PowerShellVersion = $PSVersionTable.PSVersion.ToString()
            ScriptPath = $PSCommandPath
        }

        # Create comprehensive log header with security information
        $separator = "=" * 100
        $header = @"
$separator
ADMIN ACCOUNT SCRIPT EXECUTION LOG
$separator
Log Started: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')
Script Version: 2.0 (Enhanced Security)
Target User: $redactedUser
User Hash: $userHash
Executed By: $(Protect-SensitiveData -Text $executionContext.ExecutingUser -RedactionType "All")
Computer: $($executionContext.ComputerName)
PowerShell Version: $($executionContext.PowerShellVersion)
Script Path: $(Protect-SensitiveData -Text $executionContext.ScriptPath -RedactionType "All")
Configuration: $(if ($config) { "Loaded" } else { "Default" })
Data Redaction: $(if ($config -and $config.Logging.EnableDataRedaction) { "Enabled" } else { "Disabled" })
$separator

SECURITY NOTICE: This log contains redacted sensitive information for security purposes.
Original data is hashed for audit trail purposes while maintaining privacy.

$separator
"@

        Set-Content -Path $script:CurrentLogPath -Value $header -Force -Encoding UTF8

        # Log the initialization with audit trail
        Write-SecureLog -Message "Secure logging initialized for user operation" -LogLevel "INFO" -AuditTrail @{
            Operation = "LoggingInitialization"
            TargetUser = $userHash
            LogFile = Split-Path $script:CurrentLogPath -Leaf
        }

        # Clean up old log files based on retention policy
        $retentionDays = if ($config) {
            $config.Logging.LogRetentionDays
        } else {
            30
        }

        try {
            $oldLogs = Get-ChildItem -Path $logDirectory -Filter "AdminAccount_*.log" -ErrorAction SilentlyContinue |
                       Where-Object { $_.LastWriteTime -lt (Get-Date).AddDays(-$retentionDays) }

            foreach ($log in $oldLogs) {
                try {
                    Remove-Item $log.FullName -Force -ErrorAction Stop
                    Write-SecureLog -Message "Removed expired log file: $($log.Name)" -LogLevel "DEBUG"
                }
                catch {
                    Write-SecureLog -Message "Failed to remove expired log file $($log.Name): $($_.Exception.Message)" -LogLevel "WARNING"
                }
            }
        }
        catch {
            Write-SecureLog -Message "Log cleanup process encountered an error: $($_.Exception.Message)" -LogLevel "WARNING"
        }

        Write-Progress -Activity "Initializing Logging" -Status "Secure logging initialized" -PercentComplete 100
        return $script:CurrentLogPath
    }
    catch [System.UnauthorizedAccessException] {
        Write-Error "Insufficient permissions to create log directory or files. Check NTFS permissions."
        throw
    }
    catch [System.IO.DirectoryNotFoundException] {
        Write-Error "Log directory path is invalid or inaccessible: $logDirectory"
        throw
    }
    catch {
        Write-Error "Failed to initialize secure logging: $($_.Exception.Message)"
        throw
    }
}

<#
.SYNOPSIS
Writes secure log entries with data redaction and audit trail support.

.DESCRIPTION
Enhanced logging function that automatically redacts sensitive information,
maintains audit trails, and supports multiple output targets with configurable
formatting and security features.

.PARAMETER Message
The message to log.

.PARAMETER LogLevel
The severity level of the log entry.

.PARAMETER AuditTrail
Optional hashtable containing audit trail information.

.PARAMETER SkipRedaction
Switch to skip data redaction for specific scenarios.

.EXAMPLE
Write-SecureLog -Message "User operation completed" -LogLevel "INFO" -AuditTrail @{Operation="Create"; User="hash123"}

.NOTES
Security: Automatically redacts sensitive data unless explicitly disabled.
#>
function Write-SecureLog {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true)]
        [string]$Message,

        [Parameter(Mandatory = $false)]
        [ValidateSet('DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL')]
        [string]$LogLevel = 'INFO',

        [Parameter(Mandatory = $false)]
        [hashtable]$AuditTrail,

        [Parameter(Mandatory = $false)]
        [switch]$SkipRedaction
    )

    try {
        # Get configuration
        $config = Get-ModuleConfiguration

        # If CurrentLogPath is not set, create a temporary log
        if (-not $script:CurrentLogPath) {
            $tempLogDir = if ($config) { $config.Logging.LogDirectory } else { $env:TEMP }
            $script:CurrentLogPath = Join-Path $tempLogDir "AdminScript_TEMP_$(Get-Date -Format 'yyyyMMdd_HHmmss').log"
        }

        # Get timestamp with milliseconds for precise tracking
        $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss.fff"

        # Get current user context
        $currentUser = [System.Security.Principal.WindowsIdentity]::GetCurrent().Name
        $redactedUser = Protect-SensitiveData -Text $currentUser -RedactionType "All"

        # Apply data redaction to the message unless explicitly skipped
        $logMessage = if ($SkipRedaction) { $Message } else { Protect-SensitiveData -Text $Message -RedactionType "All" }

        # Color mapping for console output
        $colorMap = @{
            'DEBUG' = if ($config) { $config.UserExperience.ConsoleOutput.Colors.Debug } else { 'Gray' }
            'INFO' = if ($config) { $config.UserExperience.ConsoleOutput.Colors.Information } else { 'Cyan' }
            'WARNING' = if ($config) { $config.UserExperience.ConsoleOutput.Colors.Warning } else { 'Yellow' }
            'ERROR' = if ($config) { $config.UserExperience.ConsoleOutput.Colors.Error } else { 'Red' }
            'CRITICAL' = 'DarkRed'
        }

        # Construct the comprehensive log entry
        $logEntry = "{0,-23} | {1,-15} | {2,-8} | {3}" -f $timestamp, $redactedUser, $LogLevel.ToUpper(), $logMessage

        # Add audit trail information if provided
        if ($AuditTrail) {
            $auditInfo = ($AuditTrail.GetEnumerator() | ForEach-Object { "$($_.Key)=$($_.Value)" }) -join "; "
            $logEntry += " | AUDIT: $auditInfo"
        }

        # Determine if we should log to file and console based on configuration
        $logToFile = $true
        $logToConsole = $true

        if ($config) {
            $logToFile = $LogLevel -in $config.Logging.FileLogLevels
            $logToConsole = $LogLevel -in $config.Logging.ConsoleLogLevels
        }

        # Write to log file if enabled
        if ($logToFile) {
            try {
                # Create directory if it doesn't exist
                $logDir = Split-Path -Path $script:CurrentLogPath -Parent
                if (-not (Test-Path -Path $logDir)) {
                    New-Item -ItemType Directory -Path $logDir -Force | Out-Null
                }

                # Write to log file with UTF-8 encoding
                Add-Content -Path $script:CurrentLogPath -Value $logEntry -Encoding UTF8 -ErrorAction Stop
            }
            catch [System.UnauthorizedAccessException] {
                Write-Warning "Insufficient permissions to write to log file: $script:CurrentLogPath"
            }
            catch [System.IO.IOException] {
                Write-Warning "I/O error writing to log file: $($_.Exception.Message)"
            }
            catch {
                Write-Warning "Failed to write to log file: $($_.Exception.Message)"
            }
        }

        # Console output if enabled
        if ($logToConsole) {
            $consoleMessage = if ($config -and $config.UserExperience.ConsoleOutput.EnableTimestamps) {
                "[$timestamp] [$LogLevel] $logMessage"
            } else {
                "[$LogLevel] $logMessage"
            }

            Write-Host $consoleMessage -ForegroundColor $colorMap[$LogLevel]
        }
    }
    catch {
        # Fallback error handling - ensure we don't break the script due to logging issues
        $errorMsg = "Logging system error: $($_.Exception.Message)"
        Write-Warning $errorMsg
        Write-Host "[$LogLevel] $Message" -ForegroundColor Red
    }
}

<#
.SYNOPSIS
Gets the current log file path.

.DESCRIPTION
Returns the path to the current log file, or null if logging has not been initialized.

.OUTPUTS
String containing the current log file path, or null.

.EXAMPLE
$logPath = Get-CurrentLogPath

.NOTES
Used by other modules to access the current log file path.
#>
function Get-CurrentLogPath {
    [CmdletBinding()]
    [OutputType([string])]
    param()

    return $script:CurrentLogPath
}

# Maintain backward compatibility with existing Write-Log calls
function Write-Log {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory = $true)]
        [string]$Message,

        [ValidateSet('DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL')]
        [string]$LogLevel = 'INFO'
    )

    Write-SecureLog -Message $Message -LogLevel $LogLevel
}

# Export functions
Export-ModuleMember -Function Initialize-SecureLogging, Write-SecureLog, Get-CurrentLogPath, Write-Log
