# Simple Migration Test
Write-Host "=== Simple Migration Test ===" -ForegroundColor Cyan

# Test 1: Basic configuration loading
Write-Host "`nTest 1: Loading configuration..." -ForegroundColor Yellow
try {
    $config = Import-PowerShellDataFile -Path ".\AdminAccountConfig.psd1"
    Write-Host "✓ Configuration loaded successfully" -ForegroundColor Green
    Write-Host "  Sections found: $($config.Keys.Count)" -ForegroundColor Gray
} catch {
    Write-Host "✗ Configuration load failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Test 2: Check current version
Write-Host "`nTest 2: Checking version..." -ForegroundColor Yellow
$currentVersion = if ($config.ConfigVersion) { $config.ConfigVersion } else { "No version (pre-1.12.0)" }
Write-Host "  Current version: $currentVersion" -ForegroundColor Gray

# Test 3: Test version compatibility function
Write-Host "`nTest 3: Testing version compatibility..." -ForegroundColor Yellow
Import-Module ".\Modules\JML-Configuration.psm1" -Force
$testResult = Test-ConfigurationVersion -Version "1.12.0"
if ($testResult) {
    Write-Host "✓ Version 1.12.0 is supported" -ForegroundColor Green
} else {
    Write-Host "✗ Version 1.12.0 is not supported" -ForegroundColor Red
}

# Test 4: Create backup
Write-Host "`nTest 4: Creating backup..." -ForegroundColor Yellow
try {
    $backupPath = New-ConfigurationBackup -ConfigPath ".\AdminAccountConfig.psd1"
    if ($backupPath -and (Test-Path $backupPath)) {
        Write-Host "✓ Backup created: $backupPath" -ForegroundColor Green
    } else {
        Write-Host "✗ Backup creation failed" -ForegroundColor Red
    }
} catch {
    Write-Host "✗ Backup error: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 5: Simple configuration update (add ConfigVersion manually)
Write-Host "`nTest 5: Manual configuration update..." -ForegroundColor Yellow
try {
    # Read the file content
    $content = Get-Content ".\AdminAccountConfig.psd1" -Raw
    
    # Check if ConfigVersion already exists
    if ($content -notmatch 'ConfigVersion\s*=') {
        # Add ConfigVersion at the beginning of the hashtable
        $newContent = $content -replace '@{', "@{`n    # Configuration Version`n    ConfigVersion = `"1.12.0`"`n"
        
        # Save the updated content
        Set-Content -Path ".\AdminAccountConfig-updated.psd1" -Value $newContent -Encoding UTF8
        
        # Test if the updated file can be loaded
        $updatedConfig = Import-PowerShellDataFile -Path ".\AdminAccountConfig-updated.psd1"
        if ($updatedConfig.ConfigVersion -eq "1.12.0") {
            Write-Host "✓ Manual update successful - ConfigVersion: $($updatedConfig.ConfigVersion)" -ForegroundColor Green
        } else {
            Write-Host "✗ Manual update failed - ConfigVersion not found" -ForegroundColor Red
        }
        
        # Clean up test file
        Remove-Item ".\AdminAccountConfig-updated.psd1" -ErrorAction SilentlyContinue
    } else {
        Write-Host "✓ ConfigVersion already exists in configuration" -ForegroundColor Green
    }
} catch {
    Write-Host "✗ Manual update failed: $($_.Exception.Message)" -ForegroundColor Red
}

# Test 6: Test the main migration function with dry-run approach
Write-Host "`nTest 6: Testing migration function..." -ForegroundColor Yellow
try {
    # Create a test copy for migration
    Copy-Item ".\AdminAccountConfig.psd1" ".\AdminAccountConfig-test.psd1" -Force
    
    $migrationResult = Invoke-ConfigurationMigration -ConfigPath ".\AdminAccountConfig-test.psd1" -CreateBackup $true
    
    if ($migrationResult.Success) {
        Write-Host "✓ Migration completed successfully" -ForegroundColor Green
        Write-Host "  Source Version: $($migrationResult.SourceVersion)" -ForegroundColor Gray
        Write-Host "  Target Version: $($migrationResult.TargetVersion)" -ForegroundColor Gray
        Write-Host "  Migrations Applied: $($migrationResult.MigrationsApplied.Count)" -ForegroundColor Gray
        
        # Try to load the migrated file
        try {
            $migratedConfig = Import-PowerShellDataFile -Path ".\AdminAccountConfig-test.psd1"
            Write-Host "✓ Migrated configuration loads successfully" -ForegroundColor Green
            
            if ($migratedConfig.ConfigVersion) {
                Write-Host "✓ ConfigVersion found: $($migratedConfig.ConfigVersion)" -ForegroundColor Green
            } else {
                Write-Host "✗ ConfigVersion missing in migrated file" -ForegroundColor Red
            }
        } catch {
            Write-Host "✗ Migrated configuration cannot be loaded: $($_.Exception.Message)" -ForegroundColor Red
        }
    } else {
        Write-Host "✗ Migration failed" -ForegroundColor Red
        if ($migrationResult.Errors.Count -gt 0) {
            foreach ($error in $migrationResult.Errors) {
                Write-Host "  Error: $error" -ForegroundColor Red
            }
        }
    }
    
    # Clean up test file
    Remove-Item ".\AdminAccountConfig-test.psd1" -ErrorAction SilentlyContinue
} catch {
    Write-Host "✗ Migration test failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n=== Test Complete ===" -ForegroundColor Cyan
