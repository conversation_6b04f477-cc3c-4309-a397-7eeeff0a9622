# Quick JiraPS Authentication Setup
# Run this before running the main application

Write-Host "Setting up JiraPS authentication..." -ForegroundColor Green

try {
    # Import JiraPS module
    Import-Module <PERSON>raPS -Force
    Write-Host "✅ JiraPS module loaded" -ForegroundColor Green
    
    # Set Jira server
    Set-JiraConfigServer -Server "https://jeragm.atlassian.net"
    Write-Host "✅ Jira server configured" -ForegroundColor Green
    
    # Create credential object with your API token
    $username = "<EMAIL>"
    $apiToken = "ATATT3xFfGF0IoMQCNhFcJBABDwKqPqvK3-MSVJAUF-ocPi5lwuLkfx5JgZ_PH_8IE4fxvuczoti0QbKtpGDIuWLygo3gQXkGV7UHAsPq8OqltraOqOe2UNeq4HDQqV0zoM_S6LoYFXzLLqP-b_X3QNEs8Gt4o_gl6gbnZ8DVGjkn_28C6QamGA=8427942E"
    $secureToken = ConvertTo-SecureString $apiToken -AsPlainText -Force
    $credential = New-Object System.Management.Automation.PSCredential($username, $secureToken)
    
    # Create Jira session
    New-JiraSession -Credential $credential
    Write-Host "✅ Jira session created successfully" -ForegroundColor Green
    
    # Test the connection
    $session = Get-JiraSession
    if ($session) {
        Write-Host "✅ Jira session active: $($session.WebSession.BaseAddress)" -ForegroundColor Green
        
        # Test fetching the test ticket
        $testTicket = Get-JiraIssue -Key "TESTIT-49342"
        if ($testTicket) {
            Write-Host "✅ Successfully fetched test ticket: $($testTicket.Summary)" -ForegroundColor Green
            Write-Host "`n🎉 JiraPS authentication setup complete!" -ForegroundColor Yellow
            Write-Host "You can now run the main application and it will use real Jira data." -ForegroundColor White
        } else {
            Write-Host "⚠️  Could not fetch test ticket, but authentication succeeded" -ForegroundColor Yellow
        }
    } else {
        Write-Host "❌ No active session found" -ForegroundColor Red
    }
    
} catch {
    Write-Host "❌ Error setting up JiraPS: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "The application will fall back to mock data." -ForegroundColor Yellow
}