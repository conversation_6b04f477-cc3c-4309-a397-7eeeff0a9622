#Requires -Version 5.1

# Load WPF Assemblies - MUST be at the top before any WPF types are referenced
try {
    Add-Type -AssemblyName PresentationFramework -ErrorAction Stop
    Add-Type -AssemblyName PresentationCore -ErrorAction Stop  
    Add-Type -AssemblyName WindowsBase -ErrorAction Stop
    Add-Type -AssemblyName System.Windows.Forms -ErrorAction Stop
} catch {
    Write-Error "Failed to load required WPF assemblies: $($_.Exception.Message)"
    exit 1
}

# OnboardingFromJiraGUI_v2.ps1
# Complete implementation with PowerShell-specific WPF solutions

# SECTION 0: POWERSHELL WPF INFRASTRUCTURE

# Custom Property Change Notification System for PowerShell Classes
class ObservableProperty {
    [string]$Name
    [object]$Value
    [scriptblock]$OnChanged

    ObservableProperty([string]$name, [object]$initialValue, [scriptblock]$onChanged) {
        $this.Name = $name
        $this.Value = $initialValue
        $this.OnChanged = $onChanged
    }

    [void] SetValue([object]$newValue) {
        if ($this.Value -ne $newValue) {
            $oldValue = $this.Value
            $this.Value = $newValue
            if ($this.OnChanged) {
                & $this.OnChanged $this.Name $oldValue $newValue
            }
        }
    }
}

# PowerShell-compatible Observable Object Base Class
class ObservableObject {
    hidden [hashtable]$_properties = @{}
    hidden [System.Collections.Generic.List[scriptblock]]$_propertyChangedHandlers = [System.Collections.Generic.List[scriptblock]]::new()

    # Register a property for observation
    hidden [void] RegisterProperty([string]$name, [object]$initialValue) {
        $this._properties[$name] = [ObservableProperty]::new($name, $initialValue, {
            param($propName, $oldValue, $newValue)
            $this.NotifyPropertyChanged($propName)
        })
    }

    # Add property changed event handler
    [void] AddPropertyChangedHandler([scriptblock]$handler) {
        $this._propertyChangedHandlers.Add($handler)
    }

    # Notify all handlers of property change
    hidden [void] NotifyPropertyChanged([string]$propertyName) {
        foreach ($handler in $this._propertyChangedHandlers) {
            & $handler $propertyName
        }
    }

    # Get property value
    [object] GetProperty([string]$name) {
        if ($this._properties.ContainsKey($name)) {
            return $this._properties[$name].Value
        }
        return $null
    }

    # Set property value with change notification
    [void] SetProperty([string]$name, [object]$value) {
        if ($this._properties.ContainsKey($name)) {
            $this._properties[$name].SetValue($value)
        }
    }
}

# PowerShell-compatible Value Converter
class BooleanToVisibilityConverter {
    [object] Convert([object]$value, [type]$targetType, [object]$parameter, [System.Globalization.CultureInfo]$culture) {
        if ($value -is [bool] -and $value) {
            return 'Visible'
        }
        return 'Collapsed'
    }

    [object] ConvertBack([object]$value, [type]$targetType, [object]$parameter, [System.Globalization.CultureInfo]$culture) {
        return $value -eq 'Visible'
    }
}

class InverseBooleanConverter {
    [object] Convert([object]$value, [type]$targetType, [object]$parameter, [System.Globalization.CultureInfo]$culture) {
        if ($value -is [bool]) { return -not $value }
        return $true
    }

    [object] ConvertBack([object]$value, [type]$targetType, [object]$parameter, [System.Globalization.CultureInfo]$culture) {
        if ($value -is [bool]) { return -not $value }
        return $false
    }
}

class BooleanToOppositeBooleanConverter {
    [object] Convert([object]$value, [type]$targetType, [object]$parameter, [System.Globalization.CultureInfo]$culture) { 
        if ($value -is [bool]) { return -not $value }
        return $true
    }
    [object] ConvertBack([object]$value, [type]$targetType, [object]$parameter, [System.Globalization.CultureInfo]$culture) { 
        if ($value -is [bool]) { return -not $value }
        return $false
    }
}

# PowerShell Threading Helper
class AsyncOperationHelper {
    static [void] RunOnUIThread([object]$dispatcher, [scriptblock]$action) {
        if ($dispatcher.CheckAccess()) {
            & $action
        } else {
            $dispatcher.Invoke($action)
        }
    }

    static [void] RunAsync([scriptblock]$backgroundWork, [scriptblock]$onComplete, [object]$uiDispatcher, [object[]]$argumentList = @()) {
        Write-Host "DEBUG: AsyncOperationHelper.RunAsync called" -ForegroundColor Cyan
        try {
            Write-Host "DEBUG: Creating runspace" -ForegroundColor Cyan
            $runspace = [runspacefactory]::CreateRunspace()
            $runspace.Open()
            
            Write-Host "DEBUG: Creating PowerShell instance" -ForegroundColor Cyan
            $powershell = [powershell]::Create()
            $powershell.Runspace = $runspace
            
            # Add the background work
            Write-Host "DEBUG: Adding script to PowerShell instance" -ForegroundColor Cyan
            $powershell.AddScript({
                param($work, $args)
                try {
                    Write-Host "DEBUG: Background script executing" -ForegroundColor Cyan
                    $result = & $work @args
                    Write-Host "DEBUG: Background script completed successfully" -ForegroundColor Cyan
                    return @{ Success = $true; Result = $result }
                } catch {
                    Write-Host "DEBUG: Background script failed: $($_.Exception.Message)" -ForegroundColor Red
                    return @{ Success = $false; Error = $_.Exception.Message }
                }
            }).AddArgument($backgroundWork).AddArgument($argumentList) | Out-Null

            # Start async execution
            Write-Host "DEBUG: Starting async execution" -ForegroundColor Cyan
            $handle = $powershell.BeginInvoke()

            # Schedule completion check
            Write-Host "DEBUG: Setting up completion timer" -ForegroundColor Cyan
            $timer = New-Object System.Windows.Threading.DispatcherTimer
            $timer.Interval = [timespan]::FromMilliseconds(100)
            $timer.Add_Tick({
                Write-Host "DEBUG: Timer tick, checking if completed" -ForegroundColor Gray
                if ($handle.IsCompleted) {
                    Write-Host "DEBUG: Async operation completed" -ForegroundColor Green
                    $timer.Stop()
                    try {
                        $result = $powershell.EndInvoke($handle)
                        Write-Host "DEBUG: Got result, about to dispatch to UI" -ForegroundColor Green
                        if ($uiDispatcher -and $onComplete) {
                            $uiDispatcher.InvokeAsync({
                                Write-Host "DEBUG: UI dispatcher executing completion callback" -ForegroundColor Green
                                & $onComplete $result[0]
                            }.GetNewClosure())
                        }
                    } catch {
                        Write-Warning "Async operation failed: $($_.Exception.Message)"
                    } finally {
                        if ($powershell) { $powershell.Dispose() }
                        if ($runspace) { $runspace.Close() }
                    }
                }
            }.GetNewClosure())
            $timer.Start()
            Write-Host "DEBUG: Timer started" -ForegroundColor Cyan
        } catch {
            Write-Error "Failed to start async operation: $($_.Exception.Message)"
            if ($onComplete) {
                & $onComplete @{ Success = $false; Error = $_.Exception.Message }
            }
        }
    }
}

# SECTION 1: SERVICE CLASSES
class LoggingService {
    [string]$LogLevel = 'INFO'
    [System.Collections.Generic.List[string]]$LogHistory

    LoggingService() {
        $this.LogHistory = [System.Collections.Generic.List[string]]::new()
    }

    hidden [int] GetLevelValue([string]$level) {
        switch ($level.ToUpper()) {
            'DEBUG'   { return 0 }
            'VERBOSE' { return 1 }
            'INFO'    { return 2 }
            'WARN'    { return 3 }
            'ERROR'   { return 4 }
            'SECURITY'{ return 5 }
            default   { return 2 }
        }
        return 2 # Fallback return
    }

    hidden WriteLog([string]$level, [string]$message) {
        if ($this.GetLevelValue($level) -ge $this.GetLevelValue($this.LogLevel)) {
            $logEntry = "[{0}] [{1}] {2}" -f (Get-Date -Format 'yyyy-MM-dd HH:mm:ss'), $level.ToUpper(), $message
            Write-Host $logEntry
            $this.LogHistory.Add($logEntry)
            
            # Keep only last 1000 entries to prevent memory issues
            if ($this.LogHistory.Count -gt 1000) {
                $this.LogHistory.RemoveAt(0)
            }
        }
    }

    [void] Info([string]$Message) { $this.WriteLog('INFO', $Message) }
    [void] Warn([string]$Message) { $this.WriteLog('WARN', $Message) }
    [void] Error([string]$Message, [System.Exception]$Exception) { 
        $errMsg = $Message
        if ($Exception) { $errMsg += " | Exception: $($Exception.Message)" }
        $this.WriteLog('ERROR', $errMsg)
    }
    [void] Debug([string]$Message) { $this.WriteLog('DEBUG', $Message) }
    [void] Verbose([string]$Message) { $this.WriteLog('VERBOSE', $Message) }
    [void] SecurityAudit([string]$action, [string]$result) { $this.WriteLog('SECURITY', "Action: $action, Result: $result") }
    
    [string[]] GetRecentLogs([int]$count = 50) {
        $start = [Math]::Max(0, $this.LogHistory.Count - $count)
        $end = $this.LogHistory.Count - $start
        return $this.LogHistory.GetRange($start, $end).ToArray()
    }
}

class ConfigurationService {
    [string[]] GetOUList() {
        # Hardcoded OUs based on JERAGM Active Directory structure
        # Mapped to Office Locations from Jira tickets
        return @(
            "OU=Users,OU=Singapore,DC=jeragm,DC=com",        # Singapore office
            "OU=Users,OU=London,DC=jeragm,DC=com",           # United Kingdom office  
            "OU=Tokyo,OU=Users,OU=Accounts,OU=JeraGM,DC=JERAGM,DC=COM", # Tokyo office
            "OU=Users,OU=USA,DC=jeragm,DC=com"               # USA office
        )
    }
    
    [string] GetOUForLocation([string]$officeLocation) {
        # Map Jira office locations to correct OUs
        switch ($officeLocation) {
            "Singapore" { return "OU=Users,OU=Singapore,DC=jeragm,DC=com" }
            "United Kingdom" { return "OU=Users,OU=London,DC=jeragm,DC=com" }
            "Japan" { return "OU=Tokyo,OU=Users,OU=Accounts,OU=JeraGM,DC=JERAGM,DC=COM" }
            "Tokyo" { return "OU=Tokyo,OU=Users,OU=Accounts,OU=JeraGM,DC=JERAGM,DC=COM" }
            "USA" { return "OU=Users,OU=USA,DC=jeragm,DC=com" }
            "United States" { return "OU=Users,OU=USA,DC=jeragm,DC=com" }
            default { return "OU=Users,OU=Singapore,DC=jeragm,DC=com" } # Default to Singapore
        }
        return "OU=Users,OU=Singapore,DC=jeragm,DC=com" # Fallback return
    }

    [hashtable] GetJiraFieldMappings() {
        # Hardcoded mappings based on JERAGM Jira system
        # These custom field IDs are consistent across all onboarding tickets
        return @{
            FirstName    = @{ CustomFieldId = "customfield_10304"; DisplayName = "First Name" }
            LastName     = @{ CustomFieldId = "customfield_10305"; DisplayName = "Last Name" }
            JobTitle     = @{ CustomFieldId = "customfield_10238"; DisplayName = "Job Title" }
            Department   = @{ CustomFieldId = "customfield_10120"; DisplayName = "Department" }
            ModelAccount = @{ CustomFieldId = "customfield_10343"; DisplayName = "Model Account" }
            OfficeLocation = @{ CustomFieldId = "customfield_10115"; DisplayName = "Office Location" }
            EmployeeType = @{ CustomFieldId = "customfield_10342"; DisplayName = "Employee Type" }
            EffectiveDate = @{ CustomFieldId = "customfield_10344"; DisplayName = "Effective Date" }
        }
    }

    [hashtable] GetValidationRules() {
        return @{
            FirstName = @{
                Required = $true; MinLength = 2; MaxLength = 50
                Pattern = '^[a-zA-Z\s\-\.]+$'
                ErrorMessage = "First name must be 2-50 characters, letters only"
            }
            LastName = @{
                Required = $true; MinLength = 2; MaxLength = 50
                Pattern = '^[a-zA-Z\s\-\.]+$'
                ErrorMessage = "Last name must be 2-50 characters, letters only"
            }
            SamAccountName = @{
                Required = $true; MinLength = 3; MaxLength = 20
                Pattern = '^[a-zA-Z0-9\.]+$'
                ErrorMessage = "Username must be 3-20 characters, alphanumeric and dots only"
            }
        }
    }

    [hashtable] ValidateField([string]$fieldName, [string]$value) {
        $rules = $this.GetValidationRules()
        if (-not $rules.ContainsKey($fieldName)) {
            return @{ IsValid = $true; ErrorMessage = "" }
        }

        $rule = $rules[$fieldName]
        
        # Check required
        if ($rule.Required -and [string]::IsNullOrWhiteSpace($value)) {
            return @{ IsValid = $false; ErrorMessage = "$fieldName is required" }
        }

        $trimmedValue = $value.Trim()

        # Check length and pattern
        if ($rule.ContainsKey('MinLength') -and $trimmedValue.Length -lt $rule.MinLength) {
            return @{ IsValid = $false; ErrorMessage = "$fieldName must be at least $($rule.MinLength) characters" }
        }

        if ($rule.ContainsKey('Pattern') -and $trimmedValue -notmatch $rule.Pattern) {
            return @{ IsValid = $false; ErrorMessage = $rule.ErrorMessage }
        }

        return @{ IsValid = $true; ErrorMessage = "" }
    }

    [int] GetSessionTimeoutMinutes() { return 60 }
    [string] GetWindowTitle() { return "Onboarding from Jira GUI v2.0" }
    [string] GetDefaultJiraUrl() { return "https://jira.jeragm.com" }
}

class JiraService {
    $log
    [bool]$UseJiraPS = $false
    
    JiraService($loggingService) { 
        $this.log = $loggingService 
        # Check if JiraPS module is available
        try {
            $jiraPSModule = Get-Module -ListAvailable -Name JiraPS
            if ($jiraPSModule) {
                $this.UseJiraPS = $true
                $this.log.Info("JiraPS module detected, will use JiraPS commands")
            } else {
                $this.log.Info("JiraPS module not found, using mock data")
            }
        } catch {
            $this.log.Warn("Could not check for JiraPS module: $($_.Exception.Message)")
        }
    }

    [hashtable] GetTicketDetails([string]$ticketId, [hashtable]$authInfo) {
        try {
            $this.log.Info("Fetching ticket details for $ticketId")
            
            if ($authInfo -and $authInfo.ApiToken) {
                return $this.GetTicketUsingRestAPI($ticketId, $authInfo)
            } elseif ($this.UseJiraPS) {
                return $this.GetTicketUsingJiraPS($ticketId)
            } else {
                return $this.GetTicketUsingMockData($ticketId)
            }
        } catch {
            $this.log.Error("Failed to get ticket $ticketId", $_.Exception)
            return @{ Success = $false; ErrorMessage = $_.Exception.Message }
        }
    }
    
    [hashtable] GetTicketUsingRestAPI([string]$ticketId, [hashtable]$authInfo) {
        try {
            $this.log.Info("Using REST API to fetch ticket $ticketId")
            
            # Extract credentials
            $jiraUrl = $authInfo.Url
            $username = $authInfo.Username
            $apiTokenSecure = $authInfo.ApiToken
            $apiToken = [Runtime.InteropServices.Marshal]::PtrToStringAuto([Runtime.InteropServices.Marshal]::SecureStringToBSTR($apiTokenSecure))
            
            # Create auth header
            $base64AuthInfo = [Convert]::ToBase64String([Text.Encoding]::ASCII.GetBytes("$username`:$apiToken"))
            $headers = @{
                "Authorization" = "Basic $base64AuthInfo"
                "Accept" = "application/json"
            }
            
            # Fetch the issue
            Write-Host "DEBUG: Fetching issue via REST API..." -ForegroundColor Cyan
            $response = Invoke-RestMethod -Uri "$jiraUrl/rest/api/3/issue/$ticketId" -Headers $headers -Method Get -ErrorAction Stop
            
            if ($response) {
                Write-Host "DEBUG: Successfully fetched issue: $($response.key)" -ForegroundColor Green
                Write-Host "DEBUG: Summary: $($response.fields.summary)" -ForegroundColor Gray
                
                # Convert to our format
                $ticketData = @{
                    "summary" = $response.fields.summary
                    "description" = $response.fields.description
                }
                
                # Extract custom fields
                $customFieldsFound = 0
                $customFieldProperties = $response.fields.PSObject.Properties | Where-Object { $_.Name -like "customfield_*" }
                
                foreach ($prop in $customFieldProperties) {
                    $value = $prop.Value
                    if ($value -and $value -ne $null) {
                        # Handle different value types
                        $displayValue = if ($value -is [string]) { 
                            $value 
                        } elseif ($value -is [System.Collections.Hashtable] -or $value.PSObject) {
                            if ($value.value) { $value.value }
                            elseif ($value.displayName) { $value.displayName }
                            elseif ($value.name) { $value.name }
                            else { $value.ToString() }
                        } else { 
                            $value.ToString() 
                        }
                        
                        $ticketData[$prop.Name] = $displayValue
                        $customFieldsFound++
                        Write-Host "DEBUG: Custom field $($prop.Name) = '$displayValue'" -ForegroundColor Yellow
                    }
                }
                
                Write-Host "DEBUG: Found $customFieldsFound custom fields via REST API" -ForegroundColor Green
                $this.log.Info("Successfully fetched ticket $ticketId using REST API")
                return @{ Success = $true; Data = $ticketData }
            } else {
                throw "No data returned from Jira API"
            }
            
        } catch {
            $this.log.Error("REST API ticket fetch failed for $ticketId", $_.Exception)
            Write-Host "DEBUG: REST API fetch failed: $($_.Exception.Message)" -ForegroundColor Red
            $this.log.Info("Falling back to mock data")
            return $this.GetTicketUsingMockData($ticketId)
        }
    }
    
    [hashtable] GetTicketUsingJiraPS([string]$ticketId) {
        try {
            $this.log.Info("Using JiraPS to fetch ticket $ticketId")
            
            # Import JiraPS module
            Import-Module JiraPS -Force
            
            # Check if we have an active session
            $session = Get-JiraSession -ErrorAction SilentlyContinue
            if (-not $session) {
                Write-Host "DEBUG: No active JiraPS session found. Authentication required." -ForegroundColor Yellow
                $this.log.Warn("No active JiraPS session. Please authenticate first with: Set-JiraConfigServer and New-JiraSession")
                throw "No active JiraPS session. Please authenticate first."
            }
            
            Write-Host "DEBUG: Active JiraPS session found for server: $($session.Server)" -ForegroundColor Green
            
            # Get the Jira issue using JiraPS
            Write-Host "DEBUG: Calling Get-JiraIssue for $ticketId" -ForegroundColor Cyan
            $issue = Get-JiraIssue -Key $ticketId -ErrorAction Stop
            
            if (-not $issue) {
                throw "No issue returned from Get-JiraIssue"
            }
            
            Write-Host "DEBUG: Successfully retrieved issue object" -ForegroundColor Green
            
            # Convert to our standard format
            $ticketData = @{
                summary = if ($issue.Summary) { $issue.Summary } else { "" }
                description = if ($issue.Description) { $issue.Description } else { "" }
            }
            
            Write-Host "DEBUG: Issue object retrieved successfully" -ForegroundColor Cyan
            
            # Try multiple ways to get custom fields
            $customFieldsFound = 0
            
            # Method 1: Check CustomFields property
            if ($issue.PSObject.Properties['CustomFields']) {
                Write-Host "DEBUG: CustomFields property exists" -ForegroundColor Cyan
                $customFields = $issue.CustomFields
                if ($customFields) {
                    Write-Host "DEBUG: CustomFields has $($customFields.Count) items" -ForegroundColor Cyan
                    foreach ($field in $customFields) {
                        if ($field -and $field.PSObject.Properties['Id'] -and $field.PSObject.Properties['Value']) {
                            Write-Host "DEBUG: CustomField - Id: $($field.Id), Value: $($field.Value)" -ForegroundColor Yellow
                            $ticketData[$field.Id] = $field.Value
                            $customFieldsFound++
                        }
                    }
                }
            } else {
                Write-Host "DEBUG: No CustomFields property found" -ForegroundColor Red
            }
            
            # Method 2: Check direct properties on issue object
            $customFieldProperties = $issue.PSObject.Properties | Where-Object { $_.Name -like "customfield_*" }
            Write-Host "DEBUG: Found $($customFieldProperties.Count) direct custom field properties" -ForegroundColor Cyan
            foreach ($prop in $customFieldProperties) {
                if ($prop.Value) {
                    Write-Host "DEBUG: Direct CustomField - Name: $($prop.Name), Value: $($prop.Value)" -ForegroundColor Yellow
                    $ticketData[$prop.Name] = $prop.Value
                    $customFieldsFound++
                }
            }
            
            # Method 3: Check Fields property
            if ($issue.PSObject.Properties['Fields'] -and $issue.Fields) {
                Write-Host "DEBUG: Fields property exists" -ForegroundColor Cyan
                $fieldsCustomFields = $issue.Fields.PSObject.Properties | Where-Object { $_.Name -like "customfield_*" }
                Write-Host "DEBUG: Found $($fieldsCustomFields.Count) custom fields in Fields property" -ForegroundColor Cyan
                foreach ($prop in $fieldsCustomFields) {
                    if ($prop.Value) {
                        Write-Host "DEBUG: Fields CustomField - Name: $($prop.Name), Value: $($prop.Value)" -ForegroundColor Yellow
                        $ticketData[$prop.Name] = $prop.Value
                        $customFieldsFound++
                    }
                }
            }
            
            # Method 4: Try accessing known custom fields directly
            $knownCustomFields = @("customfield_10304", "customfield_10305", "customfield_10238", "customfield_10120", "customfield_10343", "customfield_10115")
            foreach ($fieldId in $knownCustomFields) {
                try {
                    $value = $null
                    if ($issue.PSObject.Properties[$fieldId] -and $issue.PSObject.Properties[$fieldId].Value) {
                        $value = $issue.PSObject.Properties[$fieldId].Value
                    } elseif ($issue.Fields -and $issue.Fields.PSObject.Properties[$fieldId] -and $issue.Fields.PSObject.Properties[$fieldId].Value) {
                        $value = $issue.Fields.PSObject.Properties[$fieldId].Value
                    }
                    
                    if ($value) {
                        Write-Host "DEBUG: Direct access CustomField - $fieldId = $value" -ForegroundColor Green
                        $ticketData[$fieldId] = $value
                        $customFieldsFound++
                    }
                } catch {
                    # Continue silently
                }
            }
            
            # Method 5: Get comments and add them for fallback parsing
            try {
                Write-Host "DEBUG: Attempting to fetch comments..." -ForegroundColor Cyan
                $comments = Get-JiraComment -Issue $ticketId -ErrorAction Stop
                if ($comments) {
                    $allComments = ($comments | ForEach-Object { $_.Body }) -join "`n"
                    $ticketData["comments"] = $allComments
                    Write-Host "DEBUG: Added $($comments.Count) comments for fallback parsing" -ForegroundColor Cyan
                }
            } catch {
                Write-Host "DEBUG: Could not fetch comments: $($_.Exception.Message)" -ForegroundColor Yellow
            }
            
            Write-Host "DEBUG: Total custom fields found: $customFieldsFound" -ForegroundColor Green
            Write-Host "DEBUG: Final ticketData keys: $($ticketData.Keys -join ', ')" -ForegroundColor Green
            
            $this.log.Info("Successfully fetched ticket $ticketId using JiraPS")
            return @{ Success = $true; Data = $ticketData }
            
        } catch {
            $this.log.Error("JiraPS ticket fetch failed for $ticketId", $_.Exception)
            Write-Host "DEBUG: Exception details: $($_.Exception.Message)" -ForegroundColor Red
            Write-Host "DEBUG: Exception type: $($_.Exception.GetType().FullName)" -ForegroundColor Red
            if ($_.Exception.InnerException) {
                Write-Host "DEBUG: Inner exception: $($_.Exception.InnerException.Message)" -ForegroundColor Red
            }
            $this.log.Info("Falling back to mock data")
            return $this.GetTicketUsingMockData($ticketId)
        }
    }
    
    [hashtable] GetTicketUsingMockData([string]$ticketId) {
        $this.log.Info("Using mock data for ticket $ticketId")
        Start-Sleep -Milliseconds 800
        
        # Mock data based on real JERAGM Jira ticket structure
        # Using actual field IDs from your Jira system
        $mockData = @{ 
            summary = "Onboarding Request - Test User ($ticketId)"
            description = "New Joiner Name: Test User`nJob Title: Test Analyst`nDepartment: IT`nModel Account: testmodel"
            # Core onboarding fields (these are the exact IDs from your Jira)
            customfield_10304 = "Test"           # First Name
            customfield_10305 = "User"           # Last Name  
            customfield_10238 = "Test Analyst"   # Job Title
            customfield_10120 = "IT"             # Department
            customfield_10343 = "testmodel"      # Model Account
            customfield_10115 = "Singapore"      # Office Location
            customfield_10342 = "JERAGM Employee" # Employee Type
            customfield_10344 = "$(Get-Date -Format 'ddd, dd MMM yyyy HH:mm:ss +0000')" # Effective Date
        }
        return @{ Success = $true; Data = $mockData }
    }

    [hashtable] PostComment([string]$ticketId, [string]$comment, [pscredential]$credential) {
        try {
            $this.log.Info("Posting comment to ticket $ticketId")
            
            if ($this.UseJiraPS) {
                return $this.PostCommentUsingJiraPS($ticketId, $comment)
            } else {
                return $this.PostCommentUsingMock($ticketId, $comment)
            }
        } catch {
            $this.log.Error("Failed to post comment to ticket $ticketId", $_.Exception)
            return @{ Success = $false; ErrorMessage = $_.Exception.Message }
        }
    }
    
    [hashtable] PostCommentUsingJiraPS([string]$ticketId, [string]$comment) {
        try {
            $this.log.Info("Using JiraPS to post comment to $ticketId")
            
            # Add comment using JiraPS
            Add-JiraComment -Issue $ticketId -Comment $comment
            
            $this.log.Info("Successfully posted comment to $ticketId using JiraPS")
            return @{ Success = $true }
            
        } catch {
            $this.log.Error("JiraPS comment posting failed for $ticketId", $_.Exception)
            $this.log.Info("Falling back to mock comment posting")
            return $this.PostCommentUsingMock($ticketId, $comment)
        }
    }
    
    [hashtable] PostCommentUsingMock([string]$ticketId, [string]$comment) {
        $this.log.Info("MOCK: Comment posted to ticket $ticketId")
        Start-Sleep -Milliseconds 300
        return @{ Success = $true }
    }
}

class ActiveDirectoryService {
    [bool]$Simulate
    $log

    ActiveDirectoryService([bool]$simulateMode, $loggingService) { 
        $this.Simulate = $simulateMode
        $this.log = $loggingService
    }

    [hashtable] DoesUserExist([string]$samAccountName) {
        $this.log.Info("Checking for existence of user $samAccountName")
        # MOCK IMPLEMENTATION - Replace with actual Get-ADUser call
        return @{ Success = $true; Exists = $false }
    }

    [hashtable] CreateUser([hashtable]$userDetails) {
        if ($this.Simulate) {
            $this.log.Info("SIMULATION: Creating user $($userDetails.SamAccountName).")
            Start-Sleep -Milliseconds 500
            return @{ Success = $true; Message = "SIMULATION: User created successfully." }
        }
        try {
            $this.log.Info("Creating AD User $($userDetails.SamAccountName).")
            # New-ADUser @userDetails
            return @{ Success = $true; Message = "User $($userDetails.SamAccountName) created successfully." }
        } catch {
            $this.log.Error("Failed to create AD user $($userDetails.SamAccountName)", $_.Exception)
            return @{ Success = $false; ErrorMessage = $_.Exception.Message }
        }
    }
}

# SECTION 2: STATE MANAGEMENT CLASS
class AppState {
    # Direct properties - simpler approach
    [string]$StatusMessage = "Ready."
    [bool]$IsBusy = $false
    [string[]]$ValidationErrors = @()
    [int]$CurrentStepIndex = 0
    [hashtable]$StepValidationStatus = @{ 0 = $false; 1 = $false; 2 = $false; 3 = $false }
    [string]$JiraUrlInput = "https://jeragm.atlassian.net"
    [string]$JiraUsernameInput = "<EMAIL>"
    [securestring]$ApiTokenInput = $null
    [string]$TicketIdInput = ""
    [hashtable]$CurrentOnboardingData = @{}
    [string]$SelectedOU = ""
    [bool]$IsJiraAuthenticated = $false
    [string]$AuthenticationStatus = "Not authenticated"
    
    # Property change event handlers
    hidden [System.Collections.Generic.List[scriptblock]]$PropertyChangedHandlers = [System.Collections.Generic.List[scriptblock]]::new()
    
    [void] AddPropertyChangedHandler([scriptblock]$handler) {
        $this.PropertyChangedHandlers.Add($handler)
    }
    
    [void] NotifyPropertyChanged([string]$propertyName) {
        foreach ($handler in $this.PropertyChangedHandlers) {
            try {
                & $handler $propertyName
            } catch {
                Write-Warning "Property change handler error: $($_.Exception.Message)"
            }
        }
    }
    
    # Helper methods to trigger property change notifications
    [void] SetStatusMessage([string]$value) { 
        $this.StatusMessage = $value
        $this.NotifyPropertyChanged('StatusMessage')
    }
    
    [void] SetIsBusy([bool]$value) { 
        $this.IsBusy = $value
        $this.NotifyPropertyChanged('IsBusy')
    }
    
    [void] SetCurrentStepIndex([int]$value) { 
        $this.CurrentStepIndex = $value
        $this.NotifyPropertyChanged('CurrentStepIndex')
    }
    
    [void] SetCurrentOnboardingData([hashtable]$value) { 
        $this.CurrentOnboardingData = $value
        $this.NotifyPropertyChanged('CurrentOnboardingData')
    }
    
    [void] SetSelectedOU([string]$value) { 
        $this.SelectedOU = $value
        $this.NotifyPropertyChanged('SelectedOU')
    }
    
    # Additional setter methods for input fields
    [void] SetJiraUrlInput([string]$value) { 
        $this.JiraUrlInput = $value
        $this.NotifyPropertyChanged('JiraUrlInput')
    }
    
    [void] SetJiraUsernameInput([string]$value) { 
        $this.JiraUsernameInput = $value
        $this.NotifyPropertyChanged('JiraUsernameInput')
    }
    
    [void] SetTicketIdInput([string]$value) { 
        $this.TicketIdInput = $value
        $this.NotifyPropertyChanged('TicketIdInput')
    }
    
    [void] SetIsJiraAuthenticated([bool]$value) { 
        $this.IsJiraAuthenticated = $value
        $this.NotifyPropertyChanged('IsJiraAuthenticated')
    }
    
    [void] SetAuthenticationStatus([string]$value) { 
        $this.AuthenticationStatus = $value
        $this.NotifyPropertyChanged('AuthenticationStatus')
    }
}

# SECTION 3: VIEWMODEL CLASS
class WizardViewModel {
    $log
    $config
    $jira
    $ad
    $state
    $view

    WizardViewModel($loggingService, $configService, $jiraService, $adService, $appState) {
        $this.log = $loggingService
        $this.config = $configService
        $this.jira = $jiraService
        $this.ad = $adService
        $this.state = $appState
    }

    # This method is called by the View to link them
    RegisterView($view) {
        $this.view = $view
    }

    # --- Private Helper Methods ---
    hidden RunAsync($scriptBlock, $onSuccess, $onFailure, $argumentList = @()) {
        Write-Host "DEBUG: RunAsync called, IsBusy: $($this.state.IsBusy)" -ForegroundColor Yellow
        if ($this.state.IsBusy) { 
            Write-Host "DEBUG: Already busy, returning" -ForegroundColor Red
            return 
        }
        $this.state.SetIsBusy($true)
        Write-Host "DEBUG: Set IsBusy to true" -ForegroundColor Yellow

        try {
            Write-Host "DEBUG: About to call AsyncOperationHelper.RunAsync" -ForegroundColor Yellow
            # Use the AsyncOperationHelper for improved threading
            $capturedSuccess = $onSuccess
            $capturedFailure = $onFailure
            $capturedState = $this.state
            
            [AsyncOperationHelper]::RunAsync(
                $scriptBlock,
                {
                    param($result)
                    try {
                        Write-Host "DEBUG: RunAsync completion handler called" -ForegroundColor Green
                        $capturedState.SetIsBusy($false)
                        if ($capturedSuccess) {
                            Write-Host "DEBUG: About to call success handler" -ForegroundColor Green
                            & $capturedSuccess $result
                        }
                    } catch {
                        Write-Warning "Success handler failed: $($_.Exception.Message)"
                        $capturedState.SetIsBusy($false)
                    }
                }.GetNewClosure(),
                $this.view.window.Dispatcher,
                $argumentList
            )
            Write-Host "DEBUG: AsyncOperationHelper.RunAsync call completed" -ForegroundColor Yellow
        } catch {
            Write-Host "DEBUG: Exception in RunAsync: $($_.Exception.Message)" -ForegroundColor Red
            $this.state.SetIsBusy($false)
            if ($onFailure) {
                & $onFailure $_.Exception.Message
            }
        }
    }

    hidden [hashtable] ParseTicketData($jiraTicket) {
        $parsedData = @{}
        $mappings = $this.config.GetJiraFieldMappings()
        
        Write-Host "DEBUG: Parsing ticket data..." -ForegroundColor Cyan
        Write-Host "DEBUG: Available data keys: $($jiraTicket.Data.Keys -join ', ')" -ForegroundColor Cyan
        
        # First try to get data from custom fields
        foreach ($key in $mappings.Keys) {
            $fieldId = $mappings[$key].CustomFieldId
            Write-Host "DEBUG: Looking for $key in field $fieldId" -ForegroundColor Gray
            if ($jiraTicket.Data.ContainsKey($fieldId)) {
                $value = $jiraTicket.Data[$fieldId]
                Write-Host "DEBUG: Found $key = '$value'" -ForegroundColor Green
                $parsedData[$key] = $value
            } else {
                Write-Host "DEBUG: Field $fieldId not found for $key" -ForegroundColor Red
            }
        }
        
        # If we didn't get the required fields, try parsing from comments or description
        if ($parsedData.Count -lt 4) {
            Write-Host "DEBUG: Custom fields incomplete, trying comment parsing..." -ForegroundColor Yellow
            $parsedFromComments = $this.ParseFromComments($jiraTicket.Data)
            foreach ($key in $parsedFromComments.Keys) {
                if (-not $parsedData.ContainsKey($key)) {
                    $parsedData[$key] = $parsedFromComments[$key]
                    Write-Host "DEBUG: Added from comments: $key = '$($parsedFromComments[$key])'" -ForegroundColor Green
                }
            }
        }
        
        Write-Host "DEBUG: Final parsed data: $($parsedData.Keys -join ', ')" -ForegroundColor Cyan
        return $parsedData
    }
    
    hidden [hashtable] ParseFromComments($ticketData) {
        $parsedData = @{}
        
        try {
            # Look for structured data in description or comments
            $textToSearch = ""
            if ($ticketData.ContainsKey('description')) {
                $textToSearch += $ticketData.description + "`n"
            }
            if ($ticketData.ContainsKey('comments')) {
                $textToSearch += $ticketData.comments + "`n"
            }
            
            Write-Host "DEBUG: Searching in text content for structured data..." -ForegroundColor Yellow
            
            # Parse using regex patterns
            $patterns = @{
                FirstName = @("New Joiner Name:\s*([^\s]+)", "First Name:\s*([^\r\n]+)")
                LastName = @("New Joiner Name:\s*\S+\s+(\S+)", "Last Name:\s*([^\r\n]+)")
                JobTitle = @("Job Title:\s*([^\r\n]+)")
                Department = @("Department:\s*([^\r\n]+)")
                ModelAccount = @("Model Account:\s*([^\r\n]+)")
            }
            
            foreach ($field in $patterns.Keys) {
                foreach ($pattern in $patterns[$field]) {
                    if ($textToSearch -match $pattern) {
                        $value = $matches[1].Trim()
                        if (![string]::IsNullOrEmpty($value)) {
                            $parsedData[$field] = $value
                            Write-Host "DEBUG: Regex found $field = '$value'" -ForegroundColor Green
                            break
                        }
                    }
                }
            }
            
        } catch {
            Write-Host "DEBUG: Comment parsing failed: $($_.Exception.Message)" -ForegroundColor Red
        }
        
        return $parsedData
    }

    # --- Public Commands ---
    AuthenticateJira() {
        $this.log.Info("AuthenticateJira command executed.")
        $this.state.SetStatusMessage("Authenticating with Jira...")
        $this.state.SetIsBusy($true)
        
        try {
            # Get API token from the UI
            $apiToken = $this.view.controls.apiTokenBox.Password
            if ([string]::IsNullOrEmpty($apiToken)) {
                throw "API Token is required for authentication"
            }
            
            # Test authentication by making a simple API call
            $jiraUrl = $this.state.JiraUrlInput
            $username = $this.state.JiraUsernameInput
            
            Write-Host "DEBUG: Testing Jira authentication..." -ForegroundColor Cyan
            Write-Host "DEBUG: URL: $jiraUrl" -ForegroundColor Gray
            Write-Host "DEBUG: Username: $username" -ForegroundColor Gray
            
            # Create basic auth header
            $base64AuthInfo = [Convert]::ToBase64String([Text.Encoding]::ASCII.GetBytes("$username`:$apiToken"))
            $headers = @{
                "Authorization" = "Basic $base64AuthInfo"
                "Accept" = "application/json"
            }
            
            # Test authentication with /myself endpoint
            $response = Invoke-RestMethod -Uri "$jiraUrl/rest/api/3/myself" -Headers $headers -Method Get -ErrorAction Stop
            
            if ($response -and $response.accountId) {
                $this.state.SetIsJiraAuthenticated($true)
                $this.state.SetAuthenticationStatus("✅ Authenticated as $($response.displayName)")
                $this.state.SetStatusMessage("Successfully authenticated with Jira!")
                $this.log.Info("Jira authentication successful for user: $($response.displayName)")
                Write-Host "DEBUG: Authentication successful!" -ForegroundColor Green
                Write-Host "DEBUG: User: $($response.displayName)" -ForegroundColor Green
                Write-Host "DEBUG: Email: $($response.emailAddress)" -ForegroundColor Green
                
                # Store credentials for future use
                $this.state.ApiTokenInput = ConvertTo-SecureString $apiToken -AsPlainText -Force
            } else {
                throw "Invalid response from Jira authentication"
            }
            
        } catch {
            $this.state.SetIsJiraAuthenticated($false)
            $this.state.SetAuthenticationStatus("❌ Authentication failed")
            $this.state.SetStatusMessage("Authentication failed: $($_.Exception.Message)")
            $this.log.Error("Jira authentication failed", $_.Exception)
            Write-Host "DEBUG: Authentication failed: $($_.Exception.Message)" -ForegroundColor Red
        } finally {
            $this.state.SetIsBusy($false)
            # Update UI authentication status
            $this.UpdateAuthenticationStatusDisplay()
        }
    }
    
    # Helper method to update authentication status in UI
    hidden UpdateAuthenticationStatusDisplay() {
        if ($this.view -and $this.view.controls.authStatusLabel) {
            $this.view.controls.authStatusLabel.Content = "Status: $($this.state.AuthenticationStatus)"
            if ($this.state.IsJiraAuthenticated) {
                $this.view.controls.authStatusLabel.Foreground = "#4CAF50"  # Green
                # Enable fetch button when authenticated
                if ($this.view.controls.fetchButton) {
                    $this.view.controls.fetchButton.IsEnabled = $true
                    $this.view.controls.fetchButton.Background = "#2196F3"  # Blue
                    $this.view.controls.fetchButton.Foreground = "White"
                }
            } else {
                $this.view.controls.authStatusLabel.Foreground = "#F44336"  # Red
                # Disable fetch button when not authenticated
                if ($this.view.controls.fetchButton) {
                    $this.view.controls.fetchButton.IsEnabled = $false
                    $this.view.controls.fetchButton.Background = "#CCCCCC"  # Gray
                    $this.view.controls.fetchButton.Foreground = "#666666"
                }
            }
        }
    }
    
    FetchTicketDetails() {
        $this.log.Info("FetchTicketDetails command executed.")
        
        # Check if authenticated first
        if (-not $this.state.IsJiraAuthenticated) {
            $this.state.SetStatusMessage("Please authenticate with Jira first using the Login button.")
            $this.log.Warn("Attempted to fetch ticket without authentication")
            return
        }
        
        $this.state.SetStatusMessage("Fetching ticket $($this.state.TicketIdInput)...")
        $this.state.SetIsBusy($true)

        # Simplified approach - run synchronously for now to avoid threading issues
        try {
            Write-Host "DEBUG: Starting ticket fetch for: $($this.state.TicketIdInput)" -ForegroundColor Cyan
            
            # Prepare authentication info
            $authInfo = $null
            if ($this.state.IsJiraAuthenticated -and $this.state.ApiTokenInput) {
                $authInfo = @{
                    Url = $this.state.JiraUrlInput
                    Username = $this.state.JiraUsernameInput
                    ApiToken = $this.state.ApiTokenInput
                }
            }
            
            $result = $this.jira.GetTicketDetails($this.state.TicketIdInput, $authInfo)
            Write-Host "DEBUG: Ticket fetch result - Success: $($result.Success)" -ForegroundColor Cyan
            
            if ($result.Success) {
                $this.state.SetStatusMessage("Ticket fetched successfully.")
                $parsedData = $this.ParseTicketData($result)
                Write-Host "DEBUG: Parsed data keys: $($parsedData.Keys -join ', ')" -ForegroundColor Green
                $this.state.SetCurrentOnboardingData($parsedData)
                $this.state.StepValidationStatus[0] = $true
                Write-Host "DEBUG: Step 0 validation set to TRUE" -ForegroundColor Green
            } else {
                $this.state.SetStatusMessage("Error: $($result.ErrorMessage)")
                $this.state.StepValidationStatus[0] = $false
                Write-Host "DEBUG: Step 0 validation set to FALSE due to error" -ForegroundColor Red
            }
        } catch {
            Write-Host "DEBUG: Exception in FetchTicketDetails: $($_.Exception.Message)" -ForegroundColor Red
            $this.state.SetStatusMessage("Error: $($_.Exception.Message)")
            $this.state.StepValidationStatus[0] = $false
        } finally {
            $this.state.SetIsBusy($false)
        }
    }

    ValidateUserDetails() {
        $this.log.Info("ValidateUserDetails command executed.")
        $this.state.SetStatusMessage("Validating user details...")
        
        $data = $this.state.CurrentOnboardingData
        $errors = @()
        
        # Ensure we have the required data
        if ($null -eq $data -or $data.Count -eq 0) {
            $this.state.SetStatusMessage("No user data found. Please fetch ticket details first.")
            return
        }
        
        # Validate first name
        if ($data.ContainsKey('FirstName')) {
            $fnValidation = $this.config.ValidateField('FirstName', $data.FirstName)
            if (-not $fnValidation.IsValid) { $errors += $fnValidation.ErrorMessage }
        } else {
            $errors += "First name is required"
        }
        
        # Validate last name
        if ($data.ContainsKey('LastName')) {
            $lnValidation = $this.config.ValidateField('LastName', $data.LastName)
            if (-not $lnValidation.IsValid) { $errors += $lnValidation.ErrorMessage }
        } else {
            $errors += "Last name is required"
        }
        
        # Check if user already exists (if we have both names)
        if ($data.ContainsKey('FirstName') -and $data.ContainsKey('LastName')) {
            $suggestedUsername = $this.GenerateUsername($data.FirstName, $data.LastName)
            $userExistsResult = $this.ad.DoesUserExist($suggestedUsername)
            
            if ($userExistsResult.Success -and $userExistsResult.Exists) {
                $errors += "User with username '$suggestedUsername' already exists"
            }
        }
        
        if ($errors.Count -eq 0) {
            $this.state.SetStatusMessage("Validation successful. Proceed to next step.")
            $this.state.StepValidationStatus[1] = $true
        } else {
            $this.state.SetStatusMessage("Validation failed: $($errors -join '; ')")
            $this.state.ValidationErrors = $errors
            $this.state.StepValidationStatus[1] = $false
        }
    }

    CreateUserAccount() {
        $this.log.Info("CreateUserAccount command executed.")
        $this.state.SetStatusMessage("Creating user account...")
        $this.state.SetIsBusy($true)
        
        try {
            $data = $this.state.CurrentOnboardingData
            
            # Ensure we have the required data
            if ($null -eq $data -or $data.Count -eq 0) {
                $this.state.SetStatusMessage("No user data found. Please complete previous steps first.")
                return
            }
            
            if (-not $data.ContainsKey('FirstName') -or -not $data.ContainsKey('LastName')) {
                $this.state.SetStatusMessage("First name and last name are required.")
                return
            }
            
            $username = $this.GenerateUsername($data.FirstName, $data.LastName)
            
            # Determine OU based on office location from Jira
            $targetOU = $this.state.SelectedOU
            if ([string]::IsNullOrEmpty($targetOU) -and $data.ContainsKey('OfficeLocation')) {
                $targetOU = $this.config.GetOUForLocation($data.OfficeLocation)
                $this.state.SetSelectedOU($targetOU)
                Write-Host "DEBUG: Auto-selected OU based on office location '$($data.OfficeLocation)': $targetOU" -ForegroundColor Green
            }
            
            if ([string]::IsNullOrEmpty($targetOU)) {
                $targetOU = "OU=Users,OU=Singapore,DC=jeragm,DC=com" # Default OU
                $this.state.SetSelectedOU($targetOU)
                Write-Host "DEBUG: Using default OU: $targetOU" -ForegroundColor Yellow
            }
            
            $userDetails = @{
                GivenName = $data.FirstName
                Surname = $data.LastName
                Name = "$($data.FirstName) $($data.LastName)"
                SamAccountName = $username
                UserPrincipalName = "$<EMAIL>"
                Title = if ($data.ContainsKey('JobTitle')) { $data.JobTitle } else { "" }
                Department = if ($data.ContainsKey('Department')) { $data.Department } else { "" }
                Company = "JERA Global Markets"
                Path = $targetOU
                AccountPassword = (ConvertTo-SecureString "TempPass123!" -AsPlainText -Force)
                ChangePasswordAtLogon = $true
                Enabled = $true
            }

            Write-Host "DEBUG: Creating user with details:" -ForegroundColor Cyan
            Write-Host "DEBUG: Username: $username" -ForegroundColor Cyan
            Write-Host "DEBUG: Full Name: $($userDetails.Name)" -ForegroundColor Cyan
            Write-Host "DEBUG: Department: $($userDetails.Department)" -ForegroundColor Cyan
            Write-Host "DEBUG: OU: $targetOU" -ForegroundColor Cyan
            
            $result = $this.ad.CreateUser($userDetails)
            
            if ($result.Success) {
                $this.state.SetStatusMessage("User account created successfully!")
                $this.state.StepValidationStatus[2] = $true
                $this.log.SecurityAudit("User Creation", "SUCCESS: $username")
                Write-Host "DEBUG: Step 2 validation set to TRUE" -ForegroundColor Green
            } else {
                $this.state.SetStatusMessage("Error creating user: $($result.ErrorMessage)")
                $this.state.StepValidationStatus[2] = $false
                $this.log.SecurityAudit("User Creation", "FAILED: $username")
                Write-Host "DEBUG: Step 2 validation set to FALSE" -ForegroundColor Red
            }
        } catch {
            Write-Host "DEBUG: Exception in CreateUserAccount: $($_.Exception.Message)" -ForegroundColor Red
            $this.state.SetStatusMessage("Error: $($_.Exception.Message)")
            $this.state.StepValidationStatus[2] = $false
        } finally {
            $this.state.SetIsBusy($false)
        }
    }

    PostJiraComment() {
        $this.log.Info("PostJiraComment command executed.")
        $this.state.SetStatusMessage("Posting comment to Jira ticket...")
        
        $data = $this.state.CurrentOnboardingData
        $username = $this.GenerateUsername($data.FirstName, $data.LastName)
        
        $comment = @"
User onboarding completed:
- Name: $($data.FirstName) $($data.LastName)
- Username: $username
- Department: $($data.Department)
- Job Title: $($data.JobTitle)
- OU: $($this.state.SelectedOU)

Account has been created and is ready for use.
"@

        $scriptBlock = { 
            param($jiraService, $ticketId, $comment, $credential) 
            return $jiraService.PostComment($ticketId, $comment, $credential)
        }

        $onSuccess = {
            param($result)
            if ($result.Success) {
                $this.state.SetStatusMessage("Comment posted to Jira successfully!")
                $this.state.StepValidationStatus[3] = $true
            } else {
                $this.state.SetStatusMessage("Error posting comment: $($result.ErrorMessage)")
            }
        }.GetNewClosure()

        $onFailure = { 
            param($reason) 
            $this.state.SetStatusMessage("Failed to post comment: $reason")
            $this.state.SetIsBusy($false)
        }.GetNewClosure()

        $argumentList = @($this.jira, $this.state.TicketIdInput, $comment, $null)
        $this.RunAsync($scriptBlock, $onSuccess, $onFailure, $argumentList)
    }

    hidden [string] GenerateUsername([string]$firstName, [string]$lastName) {
        # Generate username as first initial + last name, max 20 chars
        if ([string]::IsNullOrEmpty($firstName) -or [string]::IsNullOrEmpty($lastName)) {
            return "tempuser"
        }
        
        $cleanFirstName = $firstName -replace '[^a-zA-Z]', ''
        $cleanLastName = $lastName -replace '[^a-zA-Z]', ''
        
        if ($cleanFirstName.Length -eq 0 -or $cleanLastName.Length -eq 0) {
            return "tempuser"
        }
        
        $username = ($cleanFirstName.Substring(0,1) + $cleanLastName).ToLower()
        if ($username.Length -gt 20) {
            $username = $username.Substring(0, 20)
        }
        return $username
    }

    GoToNextStep() {
        Write-Host "DEBUG: GoToNextStep called. Current step: $($this.state.CurrentStepIndex)" -ForegroundColor Magenta
        Write-Host "DEBUG: Step 0 validation: $($this.state.StepValidationStatus[0])" -ForegroundColor Magenta
        Write-Host "DEBUG: Step 1 validation: $($this.state.StepValidationStatus[1])" -ForegroundColor Magenta
        Write-Host "DEBUG: Step 2 validation: $($this.state.StepValidationStatus[2])" -ForegroundColor Magenta
        Write-Host "DEBUG: Step 3 validation: $($this.state.StepValidationStatus[3])" -ForegroundColor Magenta
        
        $currentStatus = $this.state.StepValidationStatus
        if ($currentStatus[$this.state.CurrentStepIndex]) {
            Write-Host "DEBUG: Current step is valid, proceeding to next step" -ForegroundColor Green
            if ($this.state.CurrentStepIndex -lt 3) {
                $newStep = $this.state.CurrentStepIndex + 1
                Write-Host "DEBUG: Moving to step $newStep" -ForegroundColor Green
                $this.state.SetCurrentStepIndex($newStep)
                
                # Auto-validate Step 1 (Review) if we have required data
                if ($newStep -eq 1 -and $this.state.CurrentOnboardingData.Count -gt 0) {
                    $requiredFields = @('FirstName', 'LastName', 'Department', 'JobTitle')
                    $hasAllRequired = $true
                    foreach ($field in $requiredFields) {
                        if (-not $this.state.CurrentOnboardingData[$field]) {
                            $hasAllRequired = $false
                            break
                        }
                    }
                    if ($hasAllRequired) {
                        $this.state.StepValidationStatus[1] = $true
                        Write-Host "DEBUG: Auto-validated Step 1 - required data present" -ForegroundColor Green
                    }
                }
            } else {
                Write-Host "DEBUG: On final step, closing window" -ForegroundColor Green
                $this.view.window.Close()
            }
        } else {
            Write-Host "DEBUG: Current step is NOT valid, cannot proceed" -ForegroundColor Red
            $this.state.SetStatusMessage("Please complete the current step correctly before proceeding.")
        }
    }

    GoToPreviousStep() {
        if ($this.state.CurrentStepIndex -gt 0) {
            $this.state.SetCurrentStepIndex($this.state.CurrentStepIndex - 1)
        }
    }
}

# SECTION 4: VIEW CLASS
class WizardView {
    $viewModel
    $window
    $controls = @{}

    WizardView($wizardViewModel) {
        $this.viewModel = $wizardViewModel
        $this.controls = @{}  # Initialize controls dictionary
    }

    hidden [void] Bind($control, $property, [string]$path, $converter = $null) {
        $binding = New-Object System.Windows.Data.Binding $path
        $binding.Source = $this.viewModel.state
        $binding.Mode = 'TwoWay'
        $binding.UpdateSourceTrigger = 'PropertyChanged'
        if ($converter) { $binding.Converter = $converter }
        $control.SetBinding($property, $binding)
    }

    hidden [object] CreateWizardStep0() { # Jira Connection & Ticket Input Step
        $panel = New-Object System.Windows.Controls.StackPanel
        $panel.Margin = 20

        # Title
        $titleLabel = New-Object System.Windows.Controls.Label
        $titleLabel.Content = "Step 1: Connect to Jira and Fetch Ticket"
        $titleLabel.FontSize = 16
        $titleLabel.FontWeight = 'Bold'
        $panel.Children.Add($titleLabel)

        # Jira URL
        $urlLabel = New-Object System.Windows.Controls.Label
        $urlLabel.Content = "Jira URL:"
        $panel.Children.Add($urlLabel)

        $urlTextBox = New-Object System.Windows.Controls.TextBox
        $urlTextBox.Text = $this.viewModel.state.JiraUrlInput
        $capturedViewModelState = $this.viewModel.state
        $urlTextBox.add_TextChanged({ 
            try {
                $capturedViewModelState.SetJiraUrlInput($urlTextBox.Text)
            } catch {
                Write-Warning "Error updating Jira URL: $($_.Exception.Message)"
            }
        }.GetNewClosure())
        $panel.Children.Add($urlTextBox)

        # Username
        $userLabel = New-Object System.Windows.Controls.Label
        $userLabel.Content = "Username:"
        $userLabel.Margin = "0,10,0,0"
        $panel.Children.Add($userLabel)

        $userTextBox = New-Object System.Windows.Controls.TextBox
        $userTextBox.Text = $this.viewModel.state.JiraUsernameInput
        $capturedViewModelState = $this.viewModel.state
        $userTextBox.add_TextChanged({ 
            try {
                $capturedViewModelState.SetJiraUsernameInput($userTextBox.Text)
            } catch {
                Write-Warning "Error updating username: $($_.Exception.Message)"
            }
        }.GetNewClosure())
        $panel.Children.Add($userTextBox)

        # API Token
        $tokenLabel = New-Object System.Windows.Controls.Label
        $tokenLabel.Content = "API Token:"
        $tokenLabel.Margin = "0,10,0,0"
        $panel.Children.Add($tokenLabel)

        $tokenBox = New-Object System.Windows.Controls.PasswordBox
        $this.controls.apiTokenBox = $tokenBox  # Store reference for access
        $panel.Children.Add($tokenBox)
        
        # Login Button
        $loginButton = New-Object System.Windows.Controls.Button
        $loginButton.Content = "[Lock] Login to Jira"
        $loginButton.Margin = "0,10,0,0"
        $loginButton.Height = 35
        $loginButton.Background = "#4CAF50"
        $loginButton.Foreground = "White"
        $loginButton.FontWeight = "Bold"
        $capturedViewModel = $this.viewModel
        $loginButton.add_Click({ $capturedViewModel.AuthenticateJira() }.GetNewClosure())
        $panel.Children.Add($loginButton)
        
        # Authentication Status
        $authStatusLabel = New-Object System.Windows.Controls.Label
        $authStatusLabel.Content = "Status: Not authenticated"
        $authStatusLabel.Margin = "0,5,0,0"
        $authStatusLabel.Foreground = "#666666"
        $this.controls.authStatusLabel = $authStatusLabel  # Store reference for updates
        $panel.Children.Add($authStatusLabel)
        
        # Separator
        $separator = New-Object System.Windows.Controls.Separator
        $separator.Margin = "0,15,0,15"
        $panel.Children.Add($separator)

        # Ticket ID
        $ticketLabel = New-Object System.Windows.Controls.Label
        $ticketLabel.Content = "Jira Ticket ID:"
        $ticketLabel.Margin = "0,10,0,0"
        $panel.Children.Add($ticketLabel)

        $ticketTextBox = New-Object System.Windows.Controls.TextBox
        $ticketTextBox.Text = $this.viewModel.state.TicketIdInput
        $viewModelState = $this.viewModel.state
        $ticketTextBox.add_TextChanged({ 
            try {
                $viewModelState.SetTicketIdInput($ticketTextBox.Text)
            } catch {
                Write-Warning "Error updating ticket ID: $($_.Exception.Message)"
            }
        }.GetNewClosure())
        $panel.Children.Add($ticketTextBox)

        $button = New-Object System.Windows.Controls.Button
        $button.Content = "[Ticket] Fetch Ticket Details"
        $button.Margin = "0,15,0,0"
        $button.Height = 35
        $button.IsEnabled = $false  # Disabled until authenticated
        $button.Background = "#CCCCCC"
        $button.FontWeight = "Bold"
        $this.controls.fetchButton = $button  # Store reference
        $capturedViewModel = $this.viewModel
        $button.add_Click({ $capturedViewModel.FetchTicketDetails() }.GetNewClosure())
        $panel.Children.Add($button)

        return $panel
    }

    hidden [object] CreateWizardStep1() { # User Details Review & Edit
        $panel = New-Object System.Windows.Controls.StackPanel
        $panel.Margin = 20

        # Title
        $titleLabel = New-Object System.Windows.Controls.Label
        $titleLabel.Content = "Step 2: Review and Edit User Details"
        $titleLabel.FontSize = 16
        $titleLabel.FontWeight = 'Bold'
        $panel.Children.Add($titleLabel)

        # Create form fields for user data
        $data = $this.viewModel.state.CurrentOnboardingData
        if ($null -eq $data) { $data = @{} }

        # First Name
        $fnTextBox = New-Object System.Windows.Controls.TextBox
        if ($data.ContainsKey('FirstName')) { $fnTextBox.Text = $data.FirstName }
        $capturedViewModelState = $this.viewModel.state
        $fnTextBox.add_TextChanged({ 
            try {
                if (-not $capturedViewModelState.CurrentOnboardingData) { 
                    $capturedViewModelState.CurrentOnboardingData = @{} 
                }
                $capturedViewModelState.CurrentOnboardingData['FirstName'] = $fnTextBox.Text
            } catch {
                Write-Warning "Error updating first name: $($_.Exception.Message)"
            }
        }.GetNewClosure())
        $panel.Children.Add($fnTextBox)

        # Last Name
        $lnLabel = New-Object System.Windows.Controls.Label
        $lnLabel.Content = "Last Name:"
        $lnLabel.Margin = "0,10,0,0"
        $panel.Children.Add($lnLabel)

        $lnTextBox = New-Object System.Windows.Controls.TextBox
        if ($data.ContainsKey('LastName')) { $lnTextBox.Text = $data.LastName }
        $capturedViewModelState = $this.viewModel.state
        $lnTextBox.add_TextChanged({ 
            try {
                if (-not $capturedViewModelState.CurrentOnboardingData) { 
                    $capturedViewModelState.CurrentOnboardingData = @{} 
                }
                $capturedViewModelState.CurrentOnboardingData['LastName'] = $lnTextBox.Text
            } catch {
                Write-Warning "Error updating last name: $($_.Exception.Message)"
            }
        }.GetNewClosure())
        $panel.Children.Add($lnTextBox)

        # Job Title
        $jtLabel = New-Object System.Windows.Controls.Label
        $jtLabel.Content = "Job Title:"
        $jtLabel.Margin = "0,10,0,0"
        $panel.Children.Add($jtLabel)

        $jtTextBox = New-Object System.Windows.Controls.TextBox
        if ($data.ContainsKey('JobTitle')) { $jtTextBox.Text = $data.JobTitle }
        $capturedViewModelState = $this.viewModel.state
        $jtTextBox.add_TextChanged({ 
            try {
                if (-not $capturedViewModelState.CurrentOnboardingData) { 
                    $capturedViewModelState.CurrentOnboardingData = @{} 
                }
                $capturedViewModelState.CurrentOnboardingData['JobTitle'] = $jtTextBox.Text
            } catch {
                Write-Warning "Error updating job title: $($_.Exception.Message)"
            }
        }.GetNewClosure())
        $panel.Children.Add($jtTextBox)

        # Department
        $deptLabel = New-Object System.Windows.Controls.Label
        $deptLabel.Content = "Department:"
        $deptLabel.Margin = "0,10,0,0"
        $panel.Children.Add($deptLabel)

        $deptTextBox = New-Object System.Windows.Controls.TextBox
        if ($data.ContainsKey('Department')) { $deptTextBox.Text = $data.Department }
        $capturedViewModelState = $this.viewModel.state
        $deptTextBox.add_TextChanged({ 
            try {
                if (-not $capturedViewModelState.CurrentOnboardingData) { 
                    $capturedViewModelState.CurrentOnboardingData = @{} 
                }
                $capturedViewModelState.CurrentOnboardingData['Department'] = $deptTextBox.Text
            } catch {
                Write-Warning "Error updating department: $($_.Exception.Message)"
            }
        }.GetNewClosure())
        $panel.Children.Add($deptTextBox)

        # OU Selection
        $ouLabel = New-Object System.Windows.Controls.Label
        $ouLabel.Content = "Organizational Unit:"
        $ouLabel.Margin = "0,10,0,0"
        $panel.Children.Add($ouLabel)

        $ouComboBox = New-Object System.Windows.Controls.ComboBox
        $this.viewModel.config.GetOUList() | ForEach-Object { $ouComboBox.Items.Add($_) }
        if ($ouComboBox.Items.Count -gt 0) { $ouComboBox.SelectedIndex = 0 }
        $capturedViewModelState = $this.viewModel.state
        $ouComboBox.add_SelectionChanged({ 
            if ($ouComboBox.SelectedItem) {
                $capturedViewModelState.SetSelectedOU($ouComboBox.SelectedItem.ToString())
            }
        }.GetNewClosure())
        $panel.Children.Add($ouComboBox)

        # Validate button
        $validateButton = New-Object System.Windows.Controls.Button
        $validateButton.Content = "Validate Details"
        $validateButton.Margin = "0,15,0,0"
        $validateButton.Height = 30
        $capturedViewModel = $this.viewModel
        $validateButton.add_Click({ $capturedViewModel.ValidateUserDetails() }.GetNewClosure())
        $panel.Children.Add($validateButton)

        return $panel
    }

    hidden [object] CreateWizardStep2() { # User Creation Configuration
        $panel = New-Object System.Windows.Controls.StackPanel
        $panel.Margin = 20

        # Title
        $titleLabel = New-Object System.Windows.Controls.Label
        $titleLabel.Content = "Step 3: Configure User Account"
        $titleLabel.FontSize = 16
        $titleLabel.FontWeight = 'Bold'
        $panel.Children.Add($titleLabel)

        # Generated Username
        $userLabel = New-Object System.Windows.Controls.Label
        $userLabel.Content = "Generated Username:"
        $userLabel.Margin = "0,10,0,0"
        $panel.Children.Add($userLabel)

        $userTextBlock = New-Object System.Windows.Controls.TextBlock
        $userTextBlock.Text = "Will be generated based on first/last name"
        $userTextBlock.FontStyle = 'Italic'
        $panel.Children.Add($userTextBlock)

        # Password Options
        $passLabel = New-Object System.Windows.Controls.Label
        $passLabel.Content = "Password Options:"
        $passLabel.Margin = "0,15,0,0"
        $panel.Children.Add($passLabel)

        $tempPassCheck = New-Object System.Windows.Controls.CheckBox
        $tempPassCheck.Content = "Generate temporary password"
        $tempPassCheck.IsChecked = $true
        $panel.Children.Add($tempPassCheck)

        $mustChangeCheck = New-Object System.Windows.Controls.CheckBox
        $mustChangeCheck.Content = "User must change password at next logon"
        $mustChangeCheck.IsChecked = $true
        $mustChangeCheck.Margin = "0,5,0,0"
        $panel.Children.Add($mustChangeCheck)

        # Groups
        $groupLabel = New-Object System.Windows.Controls.Label
        $groupLabel.Content = "Default Groups:"
        $groupLabel.Margin = "0,15,0,0"
        $panel.Children.Add($groupLabel)

        $groupTextBlock = New-Object System.Windows.Controls.TextBlock
        $groupTextBlock.Text = "Domain Users (automatic)"
        $groupTextBlock.FontStyle = 'Italic'
        $panel.Children.Add($groupTextBlock)

        # Create User button
        $createButton = New-Object System.Windows.Controls.Button
        $createButton.Content = "Create User Account"
        $createButton.Margin = "0,20,0,0"
        $createButton.Height = 35
        $createButton.Background = 'LightGreen'
        $capturedViewModel = $this.viewModel
        $createButton.add_Click({ $capturedViewModel.CreateUserAccount() }.GetNewClosure())
        $panel.Children.Add($createButton)

        return $panel
    }

    hidden [object] CreateWizardStep3() { # Summary and Completion
        $panel = New-Object System.Windows.Controls.StackPanel
        $panel.Margin = 20

        # Title
        $titleLabel = New-Object System.Windows.Controls.Label
        $titleLabel.Content = "Step 4: Summary and Completion"
        $titleLabel.FontSize = 16
        $titleLabel.FontWeight = 'Bold'
        $panel.Children.Add($titleLabel)

        # Results
        $resultsLabel = New-Object System.Windows.Controls.Label
        $resultsLabel.Content = "Operation Results:"
        $resultsLabel.Margin = "0,10,0,0"
        $panel.Children.Add($resultsLabel)

        $resultsTextBox = New-Object System.Windows.Controls.TextBox
        $resultsTextBox.IsReadOnly = $true
        $resultsTextBox.Height = 150
        $resultsTextBox.TextWrapping = 'Wrap'
        $resultsTextBox.VerticalScrollBarVisibility = 'Auto'
        $resultsTextBox.Text = "User creation results will appear here..."
        $panel.Children.Add($resultsTextBox)

        # Action buttons
        $buttonPanel = New-Object System.Windows.Controls.StackPanel
        $buttonPanel.Orientation = 'Horizontal'
        $buttonPanel.Margin = "0,15,0,0"

        $commentButton = New-Object System.Windows.Controls.Button
        $commentButton.Content = "Post Comment to Jira"
        $commentButton.Margin = "0,0,10,0"
        $commentButton.Height = 30
        $capturedViewModel = $this.viewModel
        $commentButton.add_Click({ $capturedViewModel.PostJiraComment() }.GetNewClosure())
        $buttonPanel.Children.Add($commentButton)

        $finishButton = New-Object System.Windows.Controls.Button
        $finishButton.Content = "Finish"
        $finishButton.Height = 30
        $capturedWindow = $this.window
        $finishButton.add_Click({ $capturedWindow.Close() }.GetNewClosure())
        $buttonPanel.Children.Add($finishButton)

        $panel.Children.Add($buttonPanel)

        return $panel
    }

    Show() {
        try {
            # Ensure WPF assemblies are loaded
            if (-not ([System.Management.Automation.PSTypeName]'System.Windows.Window').Type) {
                throw "WPF assemblies not properly loaded"
            }
            
            $this.window = New-Object System.Windows.Window
            $this.window.Title = $this.viewModel.config.GetWindowTitle()
            $this.window.Width = 700; $this.window.Height = 500
            $this.window.WindowStartupLocation = 'CenterScreen'

            # Main Layout - Use simple StackPanel
            $mainPanel = New-Object System.Windows.Controls.StackPanel
            $this.window.Content = $mainPanel

            # Content Area (fills most space)
            $contentArea = New-Object System.Windows.Controls.ContentControl
            $contentArea.Height = 380
            $mainPanel.Children.Add($contentArea)

            # Navigation buttons
            $navPanel = New-Object System.Windows.Controls.StackPanel
            $navPanel.Orientation = 'Horizontal'
            $navPanel.HorizontalAlignment = 'Right'
            $navPanel.Margin = 10
            $mainPanel.Children.Add($navPanel)

            # Status Bar
            $statusBar = New-Object System.Windows.Controls.Primitives.StatusBar
            $statusText = New-Object System.Windows.Controls.TextBlock
            $statusText.Text = $this.viewModel.state.StatusMessage
            
            # Manual binding for status message
            $this.viewModel.state.AddPropertyChangedHandler({
                param($propertyName)
                if ($propertyName -eq 'StatusMessage') {
                    try {
                        if ($this.window.Dispatcher) {
                            $this.window.Dispatcher.InvokeAsync({
                                $statusText.Text = $this.viewModel.state.StatusMessage
                            }.GetNewClosure())
                        }
                    } catch {
                        Write-Warning "Failed to update status: $($_.Exception.Message)"
                    }
                }
            }.GetNewClosure())
            
            $statusBar.Items.Add($statusText)
            $mainPanel.Children.Add($statusBar)

            $prevButton = New-Object System.Windows.Controls.Button
            $prevButton.Content = "Previous"
            $prevButton.Width = 80
            $prevButton.Height = 30
            $prevButton.Margin = "0,0,10,0"
            $capturedViewModel = $this.viewModel
            $prevButton.add_Click({ $capturedViewModel.GoToPreviousStep() }.GetNewClosure())
            $navPanel.Children.Add($prevButton)

            $nextButton = New-Object System.Windows.Controls.Button
            $nextButton.Content = "Next"
            $nextButton.Width = 80
            $nextButton.Height = 30
            $nextButton.add_Click({ $capturedViewModel.GoToNextStep() }.GetNewClosure())
            $navPanel.Children.Add($nextButton)

            # Logic to switch wizard steps based on state.CurrentStepIndex
            $this.viewModel.state.AddPropertyChangedHandler({
                param($propertyName)
                if ($propertyName -eq 'CurrentStepIndex') {
                    try {
                        $stepIndex = $this.viewModel.state.CurrentStepIndex
                        if ($this.window.Dispatcher) {
                            $this.window.Dispatcher.InvokeAsync({
                                switch ($stepIndex) {
                                    0 { $contentArea.Content = $this.CreateWizardStep0() }
                                    1 { $contentArea.Content = $this.CreateWizardStep1() }
                                    2 { $contentArea.Content = $this.CreateWizardStep2() }
                                    3 { $contentArea.Content = $this.CreateWizardStep3() }
                                }
                                
                                # Update navigation buttons
                                $prevButton.IsEnabled = ($stepIndex -gt 0)
                                $nextButton.Content = if ($stepIndex -eq 3) { "Finish" } else { "Next" }
                            }.GetNewClosure())
                        }
                    } catch {
                        Write-Warning "Failed to update step: $($_.Exception.Message)"
                    }
                }
            }.GetNewClosure())

            # Handle IsBusy state
            $this.viewModel.state.AddPropertyChangedHandler({
                param($propertyName)
                if ($propertyName -eq 'IsBusy') {
                    try {
                        if ($this.window.Dispatcher) {
                            $this.window.Dispatcher.InvokeAsync({
                                $mainPanel.IsEnabled = -not $this.viewModel.state.IsBusy
                            }.GetNewClosure())
                        }
                    } catch {
                        Write-Warning "Failed to update busy state: $($_.Exception.Message)"
                    }
                }
            }.GetNewClosure())

            # Initial content load
            $contentArea.Content = $this.CreateWizardStep0()
            $prevButton.IsEnabled = $false

            $this.window.ShowDialog() | Out-Null
        } catch {
            Write-Error "Failed to create or show window: $($_.Exception.Message)"
            throw
        }
    }
}

# SECTION 5: SCRIPT EXECUTION

try {
    # 1. Instantiate all services and state
    Write-Host "Initializing services..." -ForegroundColor Green
    $logService = [LoggingService]::new()
    $configService = [ConfigurationService]::new()
    $jiraService = [JiraService]::new($logService)
    $adService = [ActiveDirectoryService]::new($true, $logService) # Start in simulation mode
    $appState = [AppState]::new()

    # 2. Instantiate the ViewModel, injecting all dependencies
    Write-Host "Creating ViewModel..." -ForegroundColor Green
    $viewModel = [WizardViewModel]::new($logService, $configService, $jiraService, $adService, $appState)

    # 3. Instantiate the View, injecting the ViewModel
    Write-Host "Creating View..." -ForegroundColor Green
    $view = [WizardView]::new($viewModel)

    # 4. Link View back to ViewModel (for dispatcher access)
    Write-Host "Linking View and ViewModel..." -ForegroundColor Green
    $viewModel.RegisterView($view)

    # 5. Start the application
    Write-Host "Starting application..." -ForegroundColor Green
    $logService.Info("Application starting...")
    $view.Show()
    $logService.Info("Application closed.")
    
} catch {
    Write-Error "Application failed to start: $($_.Exception.Message)"
    Write-Error "Stack trace: $($_.ScriptStackTrace)"
    if ($logService) {
        $logService.Error("Application startup failed", $_.Exception)
    }
    Read-Host "Press Enter to exit"
}
