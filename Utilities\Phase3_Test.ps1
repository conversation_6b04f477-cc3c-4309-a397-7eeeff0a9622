# Phase 3 Test - Enhanced Error Handling
# Test that the enhanced error handling is working

Write-Host "=== Phase 3 Enhanced Error Handling Test ===" -ForegroundColor Green

# Test 1: Check ErrorHelper class exists
Write-Host "Test 1: Check ErrorHelper class" -ForegroundColor Cyan
$errorHelperLines = Select-String -Path "/mnt/c/Users/<USER>/OneDrive - JERA Global Markets/Azure Devops/it-endusersupport-automation/scripts/Utilities/OnboardingFromJiraGUI_v2.ps1" -Pattern "class ErrorHelper"
if ($errorHelperLines) {
    Write-Host "✅ ErrorHelper class found" -ForegroundColor Green
} else {
    Write-Host "❌ ErrorHelper class not found" -ForegroundColor Red
}

# Test 2: Check Success method
Write-Host "`nTest 2: Check ErrorHelper Success method" -ForegroundColor Cyan
$successMethodLines = Select-String -Path "/mnt/c/Users/<USER>/OneDrive - JERA Global Markets/Azure Devops/it-endusersupport-automation/scripts/Utilities/OnboardingFromJiraGUI_v2.ps1" -Pattern "static.*Success.*operation"
if ($successMethodLines) {
    Write-Host "✅ Success method with operation context found" -ForegroundColor Green
} else {
    Write-Host "❌ Success method not found" -ForegroundColor Red
}

# Test 3: Check Failure method
Write-Host "`nTest 3: Check ErrorHelper Failure method" -ForegroundColor Cyan
$failureMethodLines = Select-String -Path "/mnt/c/Users/<USER>/OneDrive - JERA Global Markets/Azure Devops/it-endusersupport-automation/scripts/Utilities/OnboardingFromJiraGUI_v2.ps1" -Pattern "static.*Failure.*operation"
if ($failureMethodLines) {
    Write-Host "✅ Failure method with operation context found" -ForegroundColor Green
} else {
    Write-Host "❌ Failure method not found" -ForegroundColor Red
}

# Test 4: Check SafeExecute method
Write-Host "`nTest 4: Check ErrorHelper SafeExecute method" -ForegroundColor Cyan
$safeExecuteLines = Select-String -Path "/mnt/c/Users/<USER>/OneDrive - JERA Global Markets/Azure Devops/it-endusersupport-automation/scripts/Utilities/OnboardingFromJiraGUI_v2.ps1" -Pattern "SafeExecute.*scriptblock.*operationName"
if ($safeExecuteLines) {
    Write-Host "✅ SafeExecute method found" -ForegroundColor Green
} else {
    Write-Host "❌ SafeExecute method not found" -ForegroundColor Red
}

# Test 5: Check if ActiveDirectoryService uses ErrorHelper
Write-Host "`nTest 5: Check ActiveDirectoryService uses ErrorHelper" -ForegroundColor Cyan
$adErrorHelperLines = Select-String -Path "/mnt/c/Users/<USER>/OneDrive - JERA Global Markets/Azure Devops/it-endusersupport-automation/scripts/Utilities/OnboardingFromJiraGUI_v2.ps1" -Pattern "ErrorHelper::SafeExecute" -Context 0,2 | Where-Object { $_.Line -match "ActiveDirectoryService" -or $_.Context.PostContext -match "UserCreation|UserExistenceCheck" }
if ($adErrorHelperLines) {
    Write-Host "✅ ActiveDirectoryService uses ErrorHelper" -ForegroundColor Green
} else {
    # Check for any ErrorHelper usage in AD context
    $anyAdErrorHelper = Select-String -Path "/mnt/c/Users/<USER>/OneDrive - JERA Global Markets/Azure Devops/it-endusersupport-automation/scripts/Utilities/OnboardingFromJiraGUI_v2.ps1" -Pattern "ErrorHelper" -Context 2,2 | Where-Object { $_.Context.PreContext -match "ActiveDirectory|CreateUser|DoesUserExist" -or $_.Context.PostContext -match "UserCreation|UserExistenceCheck" }
    if ($anyAdErrorHelper) {
        Write-Host "✅ ActiveDirectoryService uses ErrorHelper" -ForegroundColor Green
    } else {
        Write-Host "❌ ActiveDirectoryService not using ErrorHelper" -ForegroundColor Red
    }
}

# Test 6: Check if JiraService methods use ErrorHelper
Write-Host "`nTest 6: Check JiraService uses ErrorHelper" -ForegroundColor Cyan
$jiraErrorHelperLines = Select-String -Path "/mnt/c/Users/<USER>/OneDrive - JERA Global Markets/Azure Devops/it-endusersupport-automation/scripts/Utilities/OnboardingFromJiraGUI_v2.ps1" -Pattern "ErrorHelper::" -Context 1,1 | Where-Object { $_.Context.PreContext -match "Jira|Ticket" -or $_.Context.PostContext -match "TicketFetch|MockTicketFetch" }
if ($jiraErrorHelperLines) {
    Write-Host "✅ JiraService uses ErrorHelper" -ForegroundColor Green
    Write-Host "Found $($jiraErrorHelperLines.Count) usages" -ForegroundColor Yellow
} else {
    Write-Host "❌ JiraService not using ErrorHelper" -ForegroundColor Red
}

# Test 7: Check for operation context in error returns
Write-Host "`nTest 7: Check for operation context in error handling" -ForegroundColor Cyan
$operationContextLines = Select-String -Path "/mnt/c/Users/<USER>/OneDrive - JERA Global Markets/Azure Devops/it-endusersupport-automation/scripts/Utilities/OnboardingFromJiraGUI_v2.ps1" -Pattern "Operation.*=" | Where-Object { $_.Line -match "UserCreation|TicketFetch|MockTicketFetch|UserExistenceCheck" }
if ($operationContextLines) {
    Write-Host "✅ Operation context found in error handling" -ForegroundColor Green
    Write-Host "Found contexts: $($operationContextLines.Count)" -ForegroundColor Yellow
} else {
    Write-Host "❌ No operation context found" -ForegroundColor Red
}

# Test 8: Check debug statement improvements in CreateUserAccount
Write-Host "`nTest 8: Check CreateUserAccount debug improvements" -ForegroundColor Cyan
$improvedDebugLines = Select-String -Path "/mnt/c/Users/<USER>/OneDrive - JERA Global Markets/Azure Devops/it-endusersupport-automation/scripts/Utilities/OnboardingFromJiraGUI_v2.ps1" -Pattern "log\.Debug.*UserCreation"
if ($improvedDebugLines) {
    Write-Host "✅ CreateUserAccount uses improved debug logging" -ForegroundColor Green
    Write-Host "Found $($improvedDebugLines.Count) improved debug statements" -ForegroundColor Yellow
} else {
    Write-Host "❌ CreateUserAccount debug not improved" -ForegroundColor Red
}

Write-Host "`n=== Phase 3 Summary ===" -ForegroundColor Green
Write-Host "🔧 ErrorHelper class provides standardized error handling" -ForegroundColor Cyan
Write-Host "📊 Operation context added to error returns" -ForegroundColor Cyan
Write-Host "🛡️ SafeExecute wrapper for automatic error handling" -ForegroundColor Cyan
Write-Host "📝 Enhanced debug logging with operation context" -ForegroundColor Cyan
Write-Host "🎯 Error handling is now more robust and informative!" -ForegroundColor Yellow