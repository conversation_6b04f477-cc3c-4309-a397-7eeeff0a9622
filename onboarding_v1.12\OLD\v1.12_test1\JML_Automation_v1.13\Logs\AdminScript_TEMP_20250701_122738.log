2025-07-01 12:27:38.693 | akinje          | INFO     | JML Automation Script v1.13 starting...
2025-07-01 12:27:38.709 | akinje          | DEBUG    | Script path: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13
2025-07-01 12:27:38.743 | akinje          | DEBUG    | PowerShell version: 7.5.1
2025-07-01 12:27:39.900 | akinje          | DEBUG    | Execution policy: Bypass
2025-07-01 12:27:39.916 | akinje          | DEBUG    | User: akinje
2025-07-01 12:27:39.934 | akinje          | DEBUG    | Computer: JGM-CG7THR3
2025-07-01 12:28:18.853 | akinje          | INFO     | === JML ENVIRONMENT VALIDATION ===
2025-07-01 12:28:18.888 | akin<PERSON>          | INFO     | Verifying PowerShell Version...
2025-07-01 12:28:18.920 | akinje          | INFO     | === JML ENVIRONMENT VALIDATION ===
2025-07-01 12:28:18.944 | akinje          | INFO     | Verifying PowerShell Version...
2025-07-01 12:28:18.984 | akinje          | INFO     | PowerShell 7+ detected (version 7.5.1). Checking for updates...
2025-07-01 12:28:19.013 | akinje          | INFO     | PowerShell 7+ detected (version 7.5.1)
2025-07-01 12:28:19.071 | akinje          | INFO     | Checking for PowerShell updates online...
2025-07-01 12:28:19.109 | akinje          | DEBUG    | Checking for PowerShell updates online...
2025-07-01 12:28:19.561 | akinje          | INFO     | A newer version of PowerShell (7.5.2) is available.
2025-07-01 12:28:19.634 | akinje          | INFO     | Current version: 7.5.1
2025-07-01 12:28:19.669 | akinje          | INFO     | Download from: https://github.com/PowerShell/PowerShell/releases
2025-07-01 12:28:19.711 | akinje          | INFO     | === CHECKING REQUIRED MODULES ===
2025-07-01 12:28:19.749 | akinje          | INFO     | === CHECKING REQUIRED MODULES ===
2025-07-01 12:28:19.819 | akinje          | INFO     | Checking for required modules and available updates...
2025-07-01 12:28:48.056 | akinje          | INFO     | Checking module: Microsoft.PowerShell.SecretManagement...
2025-07-01 12:28:48.458 | akinje          | INFO     |   Found installed version 1.1.2
2025-07-01 12:28:49.724 | akinje          | INFO     |   Already up to date
2025-07-01 12:28:49.764 | akinje          | INFO     | Checking module: Microsoft.PowerShell.SecretStore...
2025-07-01 12:28:50.227 | akinje          | INFO     |   Found installed version 1.0.6
2025-07-01 12:28:50.729 | akinje          | INFO     |   Already up to date
2025-07-01 12:28:50.768 | akinje          | INFO     | Checking module: Microsoft.Graph...
2025-07-01 12:28:51.031 | akinje          | INFO     |   Found installed version 2.28.0
2025-07-01 12:28:51.725 | akinje          | INFO     |   Already up to date
2025-07-01 12:28:51.804 | akinje          | INFO     | Checking module: JiraPS...
2025-07-01 12:28:52.387 | akinje          | INFO     |   Found installed version 2.14.7
2025-07-01 12:28:53.337 | akinje          | INFO     |   Already up to date
2025-07-01 12:28:53.434 | akinje          | INFO     | Checking module: ImportExcel...
2025-07-01 12:28:53.893 | akinje          | INFO     |   Found installed version 7.8.10
2025-07-01 12:28:54.782 | akinje          | INFO     |   Already up to date
2025-07-01 12:28:54.834 | akinje          | INFO     | Checking module: PSWriteHTML...
2025-07-01 12:28:56.468 | akinje          | INFO     |   Found installed version 1.36.0
2025-07-01 12:28:57.374 | akinje          | INFO     |   Already up to date
2025-07-01 12:28:57.460 | akinje          | INFO     | All modules are up to date.
2025-07-01 12:28:57.497 | akinje          | INFO     | Verifying module availability...
2025-07-01 12:28:57.656 | akinje          | INFO     |   [VERIFIED] Microsoft.PowerShell.SecretManagement v1.1.2 is available.
2025-07-01 12:28:57.763 | akinje          | INFO     |   [VERIFIED] Microsoft.PowerShell.SecretStore v1.0.6 is available.
2025-07-01 12:28:57.882 | akinje          | INFO     |   [VERIFIED] Microsoft.Graph v2.28.0 is available.
2025-07-01 12:28:57.998 | akinje          | INFO     |   [VERIFIED] JiraPS v2.14.7 is available.
2025-07-01 12:28:58.151 | akinje          | INFO     |   [VERIFIED] ImportExcel v7.8.10 is available.
2025-07-01 12:28:58.330 | akinje          | INFO     |   [VERIFIED] PSWriteHTML v1.36.0 is available.
2025-07-01 12:28:58.376 | akinje          | INFO     | All required modules are successfully installed and available.
2025-07-01 12:28:58.409 | akinje          | INFO     | === JML ENVIRONMENT VALIDATION COMPLETE ===
2025-07-01 12:28:58.447 | akinje          | DEBUG    | [DEBUG] Starting JML script execution...
2025-07-01 12:28:58.488 | akinje          | DEBUG    | [DEBUG] Script directory: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13
2025-07-01 12:28:58.526 | akinje          | DEBUG    | [DEBUG] Checking ActiveDirectory module availability...
2025-07-01 12:28:58.648 | akinje          | INFO     | [INFO] ActiveDirectory module is not available - running in UAT mode
2025-07-01 12:28:58.686 | akinje          | INFO     | [INFO] This is normal for UAT/test environments
2025-07-01 12:28:58.716 | akinje          | DEBUG    | [DEBUG] Initializing JML paths...
2025-07-01 12:28:58.758 | akinje          | DEBUG    | [DEBUG] JML Paths initialized globally.
2025-07-01 12:28:58.793 | akinje          | DEBUG    | [DEBUG] Project Root: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13
2025-07-01 12:28:58.834 | akinje          | DEBUG    | [DEBUG] Modules Root: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Modules
2025-07-01 12:28:58.865 | akinje          | DEBUG    | [DEBUG] Config Dir: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Config
2025-07-01 12:28:58.895 | akinje          | DEBUG    | [DEBUG] Updated ConfigPath to use Config directory: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Config\AdminAccountConfig.psd1
2025-07-01 12:28:58.927 | akinje          | DEBUG    | [DEBUG] Starting enhanced module loading...
2025-07-01 12:28:58.975 | akinje          | INFO     | Loading JML modules from nested structure...
2025-07-01 12:28:59.007 | akinje          | INFO     | Modules Root: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Modules
2025-07-01 12:28:59.055 | akinje          | INFO     |   Loading: JML-Core/JML-Utilities [CRITICAL]
2025-07-01 12:28:59.091 | akinje          | INFO     |     Description: General utility functions
2025-07-01 12:28:59.130 | akinje          | INFO     |     Path: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Modules\JML-Core\JML-Utilities.psd1 (manifest)
2025-07-01 12:28:59.178 | akinje          | INFO     |     Attempting to import module using manifest...
2025-07-01 12:28:59.904 | akinje          | WARNING  | WARNING: JML-Security: JML-Configuration module functions not available. Some features may be limited.
2025-07-01 12:29:00.282 | akinje          | WARNING  | WARNING: JML-Logging: Missing dependencies: Get-ModuleConfiguration (JML-Configuration), Protect-SensitiveData (JML-Security). Some features may be limited.
2025-07-01 12:29:00.321 | akinje          | INFO     |     [SUCCESS] Module loaded: JML-Core/JML-Utilities
2025-07-01 12:29:00.369 | akinje          | INFO     |   Loading: JML-Core/JML-Configuration [CRITICAL]
2025-07-01 12:29:00.411 | akinje          | INFO     |     Description: Core configuration management
2025-07-01 12:29:00.443 | akinje          | INFO     |     Path: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Modules\JML-Core\JML-Configuration.psd1 (manifest)
2025-07-01 12:29:00.480 | akinje          | INFO     |     Attempting to import module using manifest...
2025-07-01 12:29:00.603 | akinje          | INFO     |     [SUCCESS] Module loaded: JML-Core/JML-Configuration
2025-07-01 12:29:00.646 | akinje          | INFO     |     [VERIFIED] Function Initialize-SmartConfiguration is available
2025-07-01 12:29:00.692 | akinje          | INFO     |     [VERIFIED] Function Get-ModuleConfiguration is available
2025-07-01 12:29:00.729 | akinje          | INFO     |   Loading: JML-Core/JML-Security [CRITICAL]
2025-07-01 12:29:00.775 | akinje          | INFO     |     Description: Security and credential management
2025-07-01 12:29:00.809 | akinje          | INFO     |     Path: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Modules\JML-Core\JML-Security.psd1 (manifest)
2025-07-01 12:29:00.861 | akinje          | INFO     |     Attempting to import module using manifest...
2025-07-01 12:29:00.938 | akinje          | INFO     |     [SUCCESS] Module loaded: JML-Core/JML-Security
2025-07-01 12:29:00.977 | akinje          | INFO     |     [VERIFIED] Function Get-SecureCredential is available
2025-07-01 12:29:01.011 | akinje          | INFO     |     [VERIFIED] Function Set-SecureCredential is available
2025-07-01 12:29:01.054 | akinje          | INFO     |     [VERIFIED] Function Get-StringHash is available
2025-07-01 12:29:01.089 | akinje          | INFO     |   Loading: JML-Core/JML-Logging [CRITICAL]
2025-07-01 12:29:01.122 | akinje          | INFO     |     Description: Secure logging functionality
2025-07-01 12:29:01.158 | akinje          | INFO     |     Path: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Modules\JML-Core\JML-Logging.psd1 (manifest)
2025-07-01 12:29:01.201 | akinje          | INFO     |     Attempting to import module using manifest...
2025-07-01 12:29:01.293 | akinje          | INFO     |     [SUCCESS] Module loaded: JML-Core/JML-Logging
2025-07-01 12:29:01.345 | akinje          | INFO     |     [VERIFIED] Function Write-SecureLog is available
2025-07-01 12:29:01.393 | akinje          | INFO     |     [VERIFIED] Function Initialize-SecureLogging is available
2025-07-01 12:29:01.442 | akinje          | INFO     |   Loading: JML-Features/JML-ActiveDirectory [CRITICAL]
2025-07-01 12:29:01.498 | akinje          | INFO     |     Description: Active Directory operations
2025-07-01 12:29:01.549 | akinje          | INFO     |     Path: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Modules\JML-Features\JML-ActiveDirectory.psd1 (manifest)
2025-07-01 12:29:01.597 | akinje          | INFO     |     Attempting to import module using manifest...
2025-07-01 12:29:01.834 | akinje          | INFO     |     [SUCCESS] Module loaded: JML-Features/JML-ActiveDirectory
2025-07-01 12:29:01.873 | akinje          | INFO     |   Loading: JML-Features/JML-Email [CRITICAL]
2025-07-01 12:29:01.912 | akinje          | INFO     |     Description: Email notification functionality
2025-07-01 12:29:01.963 | akinje          | INFO     |     Path: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Modules\JML-Features\JML-Email.psd1 (manifest)
2025-07-01 12:29:02.009 | akinje          | INFO     |     Attempting to import module using manifest...
2025-07-01 12:29:02.141 | akinje          | INFO     |     [SUCCESS] Module loaded: JML-Features/JML-Email
2025-07-01 12:29:02.195 | akinje          | INFO     |   Loading: JML-Features/JML-Jira [CRITICAL]
2025-07-01 12:29:02.230 | akinje          | INFO     |     Description: Jira integration
2025-07-01 12:29:02.270 | akinje          | INFO     |     Path: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Modules\JML-Features\JML-Jira.psd1 (manifest)
2025-07-01 12:29:02.303 | akinje          | INFO     |     Attempting to import module using manifest...
2025-07-01 12:29:02.409 | akinje          | INFO     |     [SUCCESS] Module loaded: JML-Features/JML-Jira
2025-07-01 12:29:02.464 | akinje          | INFO     |   Loading: JML-Infrastructure/JML-Setup [CRITICAL]
2025-07-01 12:29:02.516 | akinje          | INFO     |     Description: Setup and environment validation
2025-07-01 12:29:02.560 | akinje          | INFO     |     Path: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Modules\JML-Infrastructure\JML-Setup.psd1 (manifest)
2025-07-01 12:29:02.594 | akinje          | INFO     |     Attempting to import module using manifest...
2025-07-01 12:29:03.147 | akinje          | INFO     |     [SUCCESS] Module loaded: JML-Infrastructure/JML-Setup
2025-07-01 12:29:03.195 | akinje          | INFO     |   Loading: JML-Infrastructure/JML-Monitoring [CRITICAL]
2025-07-01 12:29:03.248 | akinje          | INFO     |     Description: System monitoring
2025-07-01 12:29:03.293 | akinje          | INFO     |     Path: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Modules\JML-Infrastructure\JML-Monitoring.psd1 (manifest)
2025-07-01 12:29:03.335 | akinje          | INFO     |     Attempting to import module using manifest...
2025-07-01 12:29:03.446 | akinje          | INFO     |     [SUCCESS] Module loaded: JML-Infrastructure/JML-Monitoring
2025-07-01 12:29:03.500 | akinje          | INFO     |   Loading: JML-Infrastructure/JML-UIManager [CRITICAL]
2025-07-01 12:29:03.552 | akinje          | INFO     |     Description: Modern hybrid dashboard UI with real-time status
2025-07-01 12:29:03.592 | akinje          | INFO     |     Path: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Modules\JML-Infrastructure\JML-UIManager.psd1 (manifest)
2025-07-01 12:29:03.630 | akinje          | INFO     |     Attempting to import module using manifest...
2025-07-01 12:29:04.079 | akinje          | INFO     |     [SUCCESS] Module loaded: JML-Infrastructure/JML-UIManager
2025-07-01 12:29:04.136 | akinje          | INFO     | Module Loading Summary:
2025-07-01 12:29:04.210 | akinje          | INFO     |   Successfully loaded: 10/10
2025-07-01 12:29:04.265 | akinje          | DEBUG    | [DEBUG] Enhanced module loading completed successfully
2025-07-01 12:29:04.310 | akinje          | DEBUG    | [DEBUG] Performing post-loading verification...
2025-07-01 12:29:04.351 | akinje          | DEBUG    | [DEBUG] ✓ Initialize-SmartConfiguration is available
2025-07-01 12:29:04.388 | akinje          | DEBUG    | [DEBUG] ✓ Get-ModuleConfiguration is available
2025-07-01 12:29:40.320 | akinje          | INFO     | Configuration loaded from: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Config\AdminAccountConfig.psd1
2025-07-01 12:29:40.343 | akinje          | INFO     | [SUCCESS] Configuration initialized successfully
2025-07-01 12:29:40.361 | akinje          | DEBUG    | [DEBUG] Initializing credential storage selection...
2025-07-01 12:29:40.442 | akinje          | INFO     | ================================================================================
2025-07-01 12:29:40.476 | akinje          | INFO     |                     JML Credential Storage Selection                      
2025-07-01 12:29:40.502 | akinje          | INFO     | ================================================================================
2025-07-01 12:29:40.528 | akinje          | INFO     | Please select your preferred credential storage method:
2025-07-01 12:29:40.548 | akinje          | INFO     |   1. Encrypted File (Recommended for Portability)
2025-07-01 12:29:40.568 | akinje          | INFO     |      - Stores credentials in an encrypted XML file
2025-07-01 12:29:40.589 | akinje          | INFO     |      - Portable across different machines
2025-07-01 12:29:40.609 | akinje          | INFO     |      - Uses Windows DPAPI for encryption
2025-07-01 12:29:40.630 | akinje          | INFO     |   2. PowerShell Secret Management / SecretStore
2025-07-01 12:29:40.649 | akinje          | INFO     |      - Uses Microsoft.PowerShell.SecretStore module
2025-07-01 12:29:40.672 | akinje          | INFO     |      - Requires vault password for access
2025-07-01 12:29:40.693 | akinje          | INFO     |      - More secure but requires setup
2025-07-01 12:29:40.715 | akinje          | INFO     |   3. Windows Credential Manager
2025-07-01 12:29:40.737 | akinje          | INFO     |      - Uses Windows built-in credential storage
2025-07-01 12:29:40.760 | akinje          | INFO     |      - Integrated with Windows security
2025-07-01 12:29:40.781 | akinje          | INFO     |      - Machine-specific storage
2025-07-01 12:29:40.806 | akinje          | INFO     | Enter your choice (1-3) [Default: 1 in 10 seconds]: 
2025-07-01 12:29:50.923 | akinje          | INFO     | 1 (default - Encrypted File)
2025-07-01 12:29:51.016 | akinje          | INFO     | Selected: Encrypted File storage
2025-07-01 12:29:51.039 | akinje          | INFO     | Credentials will be stored in an encrypted XML file for portability.
2025-07-01 12:29:51.083 | akinje          | INFO     | Credential storage method set for this session: EncryptedFile
2025-07-01 12:29:51.114 | akinje          | INFO     | Press any key to continue...
2025-07-01 12:31:59.436 | akinje          | DEBUG    | [DEBUG] Credential storage method selected: EncryptedFile
2025-07-01 12:31:59.463 | akinje          | DEBUG    | [DEBUG] Using credential method for status check: EncryptedFile
2025-07-01 12:31:59.488 | akinje          | DEBUG    | [DEBUG] Checking credential setup status for method: EncryptedFile
2025-07-01 12:31:59.511 | akinje          | DEBUG    | [DEBUG] Checking EncryptedFile credential storage
2025-07-01 12:31:59.537 | akinje          | DEBUG    | [DEBUG] Checking for encrypted file at: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Config\SecureCredentials.xml
2025-07-01 12:31:59.590 | akinje          | DEBUG    | [DEBUG] EncryptedFile credentials appear to be configured and accessible
2025-07-01 12:31:59.633 | akinje          | INFO     | === JML SERVICE-ORIENTED ARCHITECTURE INITIALIZATION ===
2025-07-01 12:31:59.656 | akinje          | INFO     | Creating JML context...
2025-07-01 12:31:59.720 | akinje          | INFO     | Registering core services...
2025-07-01 12:31:59.752 | akinje          | DEBUG    | Transitioned from early logging to full logging system
