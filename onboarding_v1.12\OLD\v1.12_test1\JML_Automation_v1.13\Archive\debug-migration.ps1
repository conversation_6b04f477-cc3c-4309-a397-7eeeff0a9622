# Debug Migration SystemImport-Module ".\Modules\JML-Configuration.psm1" -ForceWrite-Host "=== Debugging Migration System ===" -ForegroundColor Cyan# Test the migration functions directlyWrite-Host "`nTesting individual migration functions..." -ForegroundColor Yellow# Load current config$config = Import-PowerShellDataFile -Path ".\AdminAccountConfig.psd1"Write-Host "Original config loaded successfully" -ForegroundColor Green# Test migration 1.0 to 1.1Write-Host "`nTesting 1.0 -> 1.1 migration..." -ForegroundColor Yellow$config1_1 = Invoke-Migration_1_0_to_1_1 -Config $configif ($config1_1.Security.AuditTrail) {    Write-Host "1.0 -> 1.1 migration successful - AuditTrail added" -ForegroundColor Green} else {    Write-Host "1.0 -> 1.1 migration failed" -ForegroundColor Red}# Test migration 1.1 to 1.2Write-Host "`nTesting 1.1 -> 1.2 migration..." -ForegroundColor Yellow$config1_2 = Invoke-Migration_1_1_to_1_2 -Config $config1_1if ($config1_2.Security.Automation) {    Write-Host "1.1 -> 1.2 migration successful - Automation added" -ForegroundColor Green} else {    Write-Host "1.1 -> 1.2 migration failed" -ForegroundColor Red}# Test migration 1.2 to 1.12Write-Host "`nTesting 1.2 -> 1.12 migration..." -ForegroundColor Yellow$config1_12 = Invoke-Migration_1_2_to_1_12 -Config $config1_2if ($config1_12.ConfigVersion -eq "1.12.0") {    Write-Host "1.2 -> 1.12 migration successful - ConfigVersion set to $($config1_12.ConfigVersion)" -ForegroundColor Green} else {    Write-Host "1.2 -> 1.12 migration failed" -ForegroundColor Red}if ($config1_12.Monitoring) {    Write-Host "Monitoring section added successfully" -ForegroundColor Green} else {    Write-Host "Monitoring section missing" -ForegroundColor Red}if ($config1_12.FeatureFlags) {    Write-Host "FeatureFlags section added successfully" -ForegroundColor Green} else {    Write-Host "FeatureFlags section missing" -ForegroundColor Red}# Test the save function directlyWrite-Host "`nTesting save function..." -ForegroundColor Yellowtry {    # Create a test config file    $testConfig = @{        ConfigVersion = "1.12.0"        TestSection = @{            TestValue = "Hello World"        }    }        Save-ConfigurationFile -Config $testConfig -Path ".\test-config.psd1"    Write-Host "Save-ConfigurationFile succeeded" -ForegroundColor Green        # Try to load it back    $loadedTest = Import-PowerShellDataFile -Path ".\test-config.psd1"    if ($loadedTest.ConfigVersion -eq "1.12.0") {        Write-Host "Test config loaded successfully" -ForegroundColor Green    } else {        Write-Host "Test config load failed" -ForegroundColor Red    }        # Clean up    Remove-Item ".\test-config.psd1" -ErrorAction SilentlyContinue} catch {    Write-Host "Save-ConfigurationFile failed: $($_.Exception.Message)" -ForegroundColor Red}# Test the append save methodWrite-Host "`nTesting append save method..." -ForegroundColor Yellowtry {    # Create a backup first    Copy-Item ".\AdminAccountConfig.psd1" ".\AdminAccountConfig-debug-backup.psd1" -Force        # Test the append method    Save-MigratedConfiguration -Config $config1_12 -Path ".\AdminAccountConfig.psd1"    Write-Host "Save-MigratedConfiguration completed" -ForegroundColor Green        # Check if new sections were added    $content = Get-Content ".\AdminAccountConfig.psd1" -Raw    if ($content -match "ConfigVersion") {        Write-Host "ConfigVersion found in file" -ForegroundColor Green    } else {        Write-Host "ConfigVersion NOT found in file" -ForegroundColor Red    }        if ($content -match "Monitoring") {        Write-Host "Monitoring section found in file" -ForegroundColor Green    } else {        Write-Host "Monitoring section NOT found in file" -ForegroundColor Red    }        # Restore backup    Copy-Item ".\AdminAccountConfig-debug-backup.psd1" ".\AdminAccountConfig.psd1" -Force    Remove-Item ".\AdminAccountConfig-debug-backup.psd1" -ErrorAction SilentlyContinue    } catch {    Write-Host "Save-MigratedConfiguration failed: $($_.Exception.Message)" -ForegroundColor Red    # Restore backup if it exists    if (Test-Path ".\AdminAccountConfig-debug-backup.psd1") {        Copy-Item ".\AdminAccountConfig-debug-backup.psd1" ".\AdminAccountConfig.psd1" -Force        Remove-Item ".\AdminAccountConfig-debug-backup.psd1" -ErrorAction SilentlyContinue    }}Write-Host "`n=== Debug Complete ===" -ForegroundColor Cyan