# Fix MessageBox method calls
$scriptPath = "OnboardingFromJiraGUI.ps1"
$content = Get-Content $scriptPath -Raw

Write-Host "Fixing MessageBox method calls..." -ForegroundColor Yellow

$content = $content -replace '\(Invoke-Expression "\[System\.Windows\.MessageBox\]::(\w+)"\)\(([^)]+(?:\([^)]*\))*[^)]*)\)', 'Invoke-Expression "[System.Windows.MessageBox]::$1($2)"'

$content | Set-Content $scriptPath -Encoding UTF8

Write-Host "Fixed MessageBox method calls" -ForegroundColor Green