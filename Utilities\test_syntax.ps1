# Simple syntax test script
try {
    # Test class definitions
    Write-Host "Testing class definitions..."
    
    # Test if we can create instances without WPF
    $logService = [LoggingService]::new()
    Write-Host "LoggingService: OK"
    
    $configService = [ConfigurationService]::new()
    Write-Host "ConfigurationService: OK"
    
    $jiraService = [JiraService]::new($logService)
    Write-Host "JiraService: OK"
    
    $adService = [ActiveDirectoryService]::new($true, $logService)
    Write-Host "ActiveDirectoryService: OK"
    
    $appState = [AppState]::new()
    Write-Host "AppState: OK"
    
    $viewModel = [WizardViewModel]::new($logService, $configService, $jiraService, $adService, $appState)
    Write-Host "WizardViewModel: OK"
    
    Write-Host "All classes instantiated successfully!"
    
    # Test some basic operations
    $logService.Info("Test message")
    $configService.GetOUList()
    $appState.SetStatusMessage("Test status")
    
    Write-Host "Basic operations work!"
    
} catch {
    Write-Error "Syntax error: $($_.Exception.Message)"
    Write-Error "Line: $($_.InvocationInfo.ScriptLineNumber)"
    Write-Error "Position: $($_.InvocationInfo.OffsetInLine)"
}