# Quick Jira API test - Run this in PowerShell on Windows
# Replace YOUR_API_TOKEN with your actual token

$email = "<EMAIL>"
$apiToken = "ATATT3xFfGF0IoMQCNhFcJBABDwKqPqvK3-MSVJAUF-ocPi5lwuLkfx5JgZ_PH_8IE4fxvuczoti0QbKtpGDIuWLygo3gQXkGV7UHAsPq8OqltraOqOe2UNeq4HDQqV0zoM_S6LoYFXzLLqP-b_X3QNEs8Gt4o_gl6gbnZ8DVGjkn_28C6QamGA=8427942E"  # Replace this
$jiraBaseUrl = "https://jeragm.atlassian.net"
$testTicketId = "TESTIT-49342"

$headers = @{
    "Authorization" = "Basic " + [Convert]::ToBase64String([Text.Encoding]::ASCII.GetBytes("$email`:$apiToken"))
    "Accept" = "application/json"
}

try {
    Write-Host "Testing authentication..." -ForegroundColor Cyan
    $authTest = Invoke-RestMethod -Uri "$jiraBaseUrl/rest/api/3/myself" -Headers $headers
    Write-Host "✅ Auth successful: $($authTest.displayName)" -ForegroundColor Green
    
    Write-Host "Fetching ticket $testTicketId..." -ForegroundColor Cyan
    $issue = Invoke-RestMethod -Uri "$jiraBaseUrl/rest/api/3/issue/$testTicketId" -Headers $headers
    Write-Host "✅ Ticket fetched: $($issue.fields.summary)" -ForegroundColor Green
    
    # Check for our required custom fields
    $requiredFields = @{
        "customfield_10304" = "FirstName"
        "customfield_10305" = "LastName"
        "customfield_10238" = "JobTitle"
        "customfield_10120" = "Department"
        "customfield_10343" = "ModelAccount"
    }
    
    Write-Host "`nCustom fields found:" -ForegroundColor Yellow
    foreach ($fieldId in $requiredFields.Keys) {
        $value = $issue.fields.$fieldId
        if ($value) {
            $displayValue = if ($value.value) { $value.value } elseif ($value.displayName) { $value.displayName } else { $value }
            Write-Host "✅ $($requiredFields[$fieldId]): $displayValue" -ForegroundColor Green
        } else {
            Write-Host "❌ $($requiredFields[$fieldId]): NOT FOUND" -ForegroundColor Red
        }
    }
    
    Write-Host "`n🎉 Your API token works! The WPF app should work on Windows." -ForegroundColor Green
    
} catch {
    Write-Host "❌ Error: $($_.Exception.Message)" -ForegroundColor Red
}