2025-07-01 10:14:41.051 | JERAGM\akinje   | DEBUG    | Credential storage method auto-selected due to timeout | AUDIT: SelectionType=Automatic; Operation=CredentialStorageSelection; TimeoutSeconds=10; Method=EncryptedFile
2025-07-01 10:14:57.180 | JERAGM\akinje   | DEBUG    | JML Context initialized successfully | AUDIT: Operation=ContextInitialization; ScriptRoot=C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13; SessionId=17cd591c-2c63-45be-b0a5-b2bf29559508
2025-07-01 10:14:57.318 | JERAGM\akinje   | DEBUG    | Configuration loaded successfully | AUDIT: ConfigPath=C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Config\AdminAccountConfig.psd1; Operation=ConfigurationLoading; KeyCount=12; ConfigType=System.Collections.Hashtable
2025-07-01 10:14:57.357 | JERAGM\akinje   | INFO     | Setting up credential management...
2025-07-01 10:14:57.403 | JERAGM\akinje   | INFO     | Using credential storage method: EncryptedFile
2025-07-01 10:14:57.455 | JERAGM\akinje   | INFO     | Encrypted file credential manager registered
2025-07-01 10:14:57.491 | JERAGM\akinje   | INFO     | Validating service health...
2025-07-01 10:14:57.555 | JERAGM\akinje   | INFO     | All services are healthy and ready
2025-07-01 10:14:57.590 | JERAGM\akinje   | INFO     | JML service-oriented architecture initialized successfully!
2025-07-01 10:14:57.620 | JERAGM\akinje   | INFO     | Session ID: 17cd591c-2c63-45be-b0a5-b2bf29559508
2025-07-01 10:14:57.653 | JERAGM\akinje   | INFO     | Initializing UI Manager service...
2025-07-01 10:14:57.689 | JERAGM\akinje   | INFO     | UI Manager service initialized successfully
2025-07-01 10:14:57.735 | JERAGM\akinje   | INFO     | Verifying critical module availability...
2025-07-01 10:14:57.773 | JERAGM\akinje   | DEBUG    | [DEBUG] Checking credential setup status for method: EncryptedFile
2025-07-01 10:14:57.843 | JERAGM\akinje   | DEBUG    | [DEBUG] Checking EncryptedFile credential storage
2025-07-01 10:14:57.921 | JERAGM\akinje   | DEBUG    | [DEBUG] Checking for encrypted file at: [REDACTED_PATH]s\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Config\SecureCredentials.xml
2025-07-01 10:14:58.000 | JERAGM\akinje   | DEBUG    | [DEBUG] EncryptedFile credentials appear to be configured and accessible
2025-07-01 10:14:58.593 | JERAGM\akinje   | INFO     | Credential setup complete. Loading main menu...
2025-07-01 10:14:58.680 | JERAGM\akinje   | INFO     | [INFO] Initializing Jira session...
2025-07-01 10:14:58.811 | JERAGM\akinje   | DEBUG    | [DEBUG] Received credential types:
2025-07-01 10:14:58.888 | JERAGM\akinje   | DEBUG    | [DEBUG] - jiraUsername type: String
2025-07-01 10:14:58.999 | JERAGM\akinje   | DEBUG    | [DEBUG] - jiraApiToken type: SecureString
2025-07-01 10:14:59.080 | JERAGM\akinje   | DEBUG    | [DEBUG] Credential validation passed
2025-07-01 10:14:59.174 | JERAGM\akinje   | DEBUG    | [DEBUG] Username format: Valid email
2025-07-01 10:14:59.282 | JERAGM\akinje   | DEBUG    | [DEBUG] API token=[REDACTED] (length: 192)
2025-07-01 10:14:59.407 | JERAGM\akinje   | DEBUG    | [DEBUG] Final PSCredential object:
2025-07-01 10:14:59.515 | JERAGM\akinje   | DEBUG    | [DEBUG] - Username: emmanuel.akinjomo***@jeragm.com
2025-07-01 10:14:59.602 | JERAGM\akinje   | DEBUG    | [DEBUG] - Password type: SecureString
2025-07-01 10:14:59.705 | JERAGM\akinje   | INFO     | [INFO] Connecting to Jira server: [SERVER]
2025-07-01 10:14:59.788 | JERAGM\akinje   | INFO     | [INFO] Using username: emmanuel.akinjomo***@jeragm.com
2025-07-01 10:14:59.900 | JERAGM\akinje   | INFO     | Initializing Jira connection | AUDIT: Username=emmanuel.akinjomo***@jeragm.com; Operation=JiraConnectionInit; ServerUrl=[SERVER]
2025-07-01 10:14:59.996 | JERAGM\akinje   | INFO     | Loading JiraPS module
2025-07-01 10:15:02.638 | JERAGM\akinje   | INFO     | Setting Jira server configuration
2025-07-01 10:15:02.669 | JERAGM\akinje   | INFO     | Creating Jira session using direct authentication
2025-07-01 10:15:02.706 | JERAGM\akinje   | INFO     | Testing Jira connection using GDPR-compliant endpoint
2025-07-01 10:15:03.653 | JERAGM\akinje   | INFO     | Jira connection test successful | AUDIT: UserDisplayName=Emmanuel Akinjomo; Status=Success; Operation=JiraConnectionTest; UserAccountId=712020:************************e1dd4dd40efa
2025-07-01 10:15:03.695 | JERAGM\akinje   | INFO     | Jira connection established successfully.
2025-07-01 10:15:03.789 | JERAGM\akinje   | INFO     | Connected as: Emmanuel Akinjomo (emmanuel.akinjomo***@jeragm.com)
2025-07-01 10:15:03.861 | JERAGM\akinje   | INFO     | Jira connection initialized successfully using JiraPS module
2025-07-01 10:15:03.944 | JERAGM\akinje   | INFO     | [SUCCESS] Jira session initialized successfully
2025-07-01 10:15:04.071 | JERAGM\akinje   | INFO     | Jira authentication and connection successful!
2025-07-01 10:15:04.172 | JERAGM\akinje   | INFO     | JML System started in interactive mode | AUDIT: Operation=SystemStartup; Version=1.13; ComputerName=JGM-CG7THR3; SecretStoreConfigured=False; CredentialsAvailable=True; ExecutingUser=JERAGM\akinje; SkipJiraIntegration=False
2025-07-01 10:15:05.831 | JERAGM\akinje   | INFO     | +--------------------------------------------------------------------------------------------------+
2025-07-01 10:15:05.878 | JERAGM\akinje   | INFO     | |  JML v1.13 | STATUS: Warning | AD: Unavailable | JIRA: Available | CREDS: Ready | USER: akinje   |
2025-07-01 10:15:05.919 | JERAGM\akinje   | INFO     | +--------------------------------------------------------------------------------------------------+
2025-07-01 10:15:05.954 | JERAGM\akinje   | INFO     | Available Operations:
2025-07-01 10:15:05.991 | JERAGM\akinje   | INFO     |   [1] Create Admin Account
2025-07-01 10:15:06.020 | JERAGM\akinje   | INFO     |   [2] Delete Admin Account
2025-07-01 10:15:06.051 | JERAGM\akinje   | INFO     |   [3] Reset Admin Account Password
2025-07-01 10:15:06.085 | JERAGM\akinje   | INFO     |   [4] System Information
2025-07-01 10:15:06.117 | JERAGM\akinje   | INFO     |   [5] Run Setup / Repair Credentials
2025-07-01 10:15:06.148 | JERAGM\akinje   | INFO     |   [7] Exit
2025-07-01 10:15:06.183 | JERAGM\akinje   | INFO     | +-[ Real-time Log Viewer ]---------------------------------------------------------------------------+
2025-07-01 10:15:06.239 | JERAGM\akinje   | INFO     | | 10:14:57 [INFO] UIManager service initialized                                                    |
2025-07-01 10:15:06.286 | JERAGM\akinje   | INFO     | | 10:14:57 [INFO] UI Manager service started                                                       |
2025-07-01 10:15:06.321 | JERAGM\akinje   | INFO     | +--------------------------------------------------------------------------------------------------+
2025-07-01 10:15:06.363 | JERAGM\akinje   | INFO     | > 
2025-07-01 10:15:06.404 | JERAGM\akinje   | WARNING  | WARNING: UIManager: Slow screen draw detected: 2123.799ms
2025-07-01 10:15:12.262 | JERAGM\akinje   | INFO     | === CREATE ADMIN ACCOUNT ===
2025-07-01 10:15:41.586 | JERAGM\akinje   | INFO     | === ADMIN ACCOUNT CREATION ===
2025-07-01 10:15:41.609 | JERAGM\akinje   | INFO     | JML System v1.13
2025-07-01 10:15:41.634 | JERAGM\akinje   | INFO     | Processing Jira ticket: TESTIT-49342
2025-07-01 10:15:41.656 | JERAGM\akinje   | INFO     | [INFO] Initializing Jira session...
2025-07-01 10:15:41.681 | JERAGM\akinje   | DEBUG    | [DEBUG] Received credential types:
2025-07-01 10:15:41.719 | JERAGM\akinje   | DEBUG    | [DEBUG] - jiraUsername type: String
2025-07-01 10:15:41.744 | JERAGM\akinje   | DEBUG    | [DEBUG] - jiraApiToken type: SecureString
2025-07-01 10:15:41.768 | JERAGM\akinje   | DEBUG    | [DEBUG] Credential validation passed
2025-07-01 10:15:41.789 | JERAGM\akinje   | DEBUG    | [DEBUG] Username format: Valid email
2025-07-01 10:15:41.814 | JERAGM\akinje   | DEBUG    | [DEBUG] API token=[REDACTED] (length: 192)
2025-07-01 10:15:41.838 | JERAGM\akinje   | DEBUG    | [DEBUG] Final PSCredential object:
2025-07-01 10:15:41.861 | JERAGM\akinje   | DEBUG    | [DEBUG] - Username: emmanuel.akinjomo***@jeragm.com
2025-07-01 10:15:41.889 | JERAGM\akinje   | DEBUG    | [DEBUG] - Password type: SecureString
2025-07-01 10:15:41.934 | JERAGM\akinje   | INFO     | [INFO] Connecting to Jira server: [SERVER]
2025-07-01 10:15:42.011 | JERAGM\akinje   | INFO     | [INFO] Using username: emmanuel.akinjomo***@jeragm.com
2025-07-01 10:15:42.058 | JERAGM\akinje   | INFO     | Initializing Jira connection | AUDIT: Username=emmanuel.akinjomo***@jeragm.com; Operation=JiraConnectionInit; ServerUrl=[SERVER]
2025-07-01 10:15:42.092 | JERAGM\akinje   | INFO     | Setting Jira server configuration
2025-07-01 10:15:42.147 | JERAGM\akinje   | INFO     | Creating Jira session using direct authentication
2025-07-01 10:15:42.188 | JERAGM\akinje   | INFO     | Testing Jira connection using GDPR-compliant endpoint
2025-07-01 10:15:43.085 | JERAGM\akinje   | INFO     | Jira connection test successful | AUDIT: UserDisplayName=Emmanuel Akinjomo; Status=Success; Operation=JiraConnectionTest; UserAccountId=712020:************************e1dd4dd40efa
2025-07-01 10:15:43.118 | JERAGM\akinje   | INFO     | Jira connection established successfully.
2025-07-01 10:15:43.166 | JERAGM\akinje   | INFO     | Connected as: Emmanuel Akinjomo (emmanuel.akinjomo***@jeragm.com)
2025-07-01 10:15:43.206 | JERAGM\akinje   | INFO     | Jira connection initialized successfully using JiraPS module
2025-07-01 10:15:43.278 | JERAGM\akinje   | INFO     | [SUCCESS] Jira session initialized successfully
2025-07-01 10:15:43.384 | JERAGM\akinje   | INFO     | Validating Jira ticket | AUDIT: TicketKey=TESTIT-49342; Operation=JiraTicketValidation
2025-07-01 10:15:44.453 | JERAGM\akinje   | INFO     | Retrieved Jira ticket successfully | AUDIT: Operation=JiraTicketRetrieval; IssueTypeExists=False; TicketKey=TESTIT-49342; FieldsAvailable=; IssueTypeType=null; TicketSummary=
2025-07-01 10:15:44.503 | JERAGM\akinje   | DEBUG    | Full ticket structure for debugging | AUDIT: TicketKey=TESTIT-49342; Operation=FullTicketStructure; TicketStructure=null
2025-07-01 10:15:44.560 | JERAGM\akinje   | ERROR    | Admin account creation failed: Jira ticket validation failed: Jira ticket 'TESTIT-49342' does not exist or you do not have permission to view it.
2025-07-01 10:15:44.606 | JERAGM\akinje   | ERROR    | Admin account creation failed: Jira ticket validation failed: Jira ticket 'TESTIT-49342' does not exist or you do not have permission to view it. | AUDIT: ErrorType=RuntimeException; Operation=AdminAccountCreationError; Success=False
2025-07-01 10:15:44.648 | JERAGM\akinje   | INFO     | Adding comment to Jira ticket | AUDIT: UseADF=True; TicketKey=TESTIT-49342; Operation=JiraCommentAdd; IncludeTimestamp=True
2025-07-01 10:15:45.602 | JERAGM\akinje   | WARNING  | Failed to add comment using JiraPS: Issue does not exist or you do not have permission to see it.
2025-07-01 10:15:45.630 | JERAGM\akinje   | ERROR    | Failed to add Jira comment: Issue does not exist or you do not have permission to see it. | AUDIT: ErrorType=RuntimeException; TicketKey=TESTIT-49342; Operation=JiraCommentError
2025-07-01 10:15:45.677 | JERAGM\akinje   | WARNING  | WARNING: Failed to call Jira function 'Add-EnhancedJiraComment': Issue does not exist or you do not have permission to see it.
2025-07-01 10:15:45.724 | JERAGM\akinje   | WARNING  | WARNING: Failed to update Jira ticket with error: Jira function not available
2025-07-01 10:15:45.764 | JERAGM\akinje   | INFO     | Press any key to continue...
2025-07-01 10:17:19.223 | JERAGM\akinje   | INFO     | +--------------------------------------------------------------------------------------------------+
2025-07-01 10:17:19.254 | JERAGM\akinje   | INFO     | |  JML v1.13 | STATUS: Warning | AD: Unavailable | JIRA: Available | CREDS: Ready | USER: akinje   |
2025-07-01 10:17:19.278 | JERAGM\akinje   | INFO     | +--------------------------------------------------------------------------------------------------+
2025-07-01 10:17:19.324 | JERAGM\akinje   | INFO     | Available Operations:
2025-07-01 10:17:19.376 | JERAGM\akinje   | INFO     |   [1] Create Admin Account
2025-07-01 10:17:19.429 | JERAGM\akinje   | INFO     |   [2] Delete Admin Account
2025-07-01 10:17:19.459 | JERAGM\akinje   | INFO     |   [3] Reset Admin Account Password
2025-07-01 10:17:19.486 | JERAGM\akinje   | INFO     |   [4] System Information
2025-07-01 10:17:19.517 | JERAGM\akinje   | INFO     |   [5] Run Setup / Repair Credentials
2025-07-01 10:17:19.545 | JERAGM\akinje   | INFO     |   [7] Exit
2025-07-01 10:17:19.575 | JERAGM\akinje   | INFO     | +-[ Real-time Log Viewer ]---------------------------------------------------------------------------+
2025-07-01 10:17:19.605 | JERAGM\akinje   | INFO     | | 10:14:57 [INFO] UIManager service initialized                                                    |
2025-07-01 10:17:19.634 | JERAGM\akinje   | INFO     | | 10:14:57 [INFO] UI Manager service started                                                       |
2025-07-01 10:17:19.669 | JERAGM\akinje   | INFO     | +--------------------------------------------------------------------------------------------------+
2025-07-01 10:17:19.698 | JERAGM\akinje   | INFO     | > 
2025-07-01 10:17:19.725 | JERAGM\akinje   | WARNING  | WARNING: UIManager: Slow screen draw detected: 789.6063ms
2025-07-01 10:27:32.720 | JERAGM\akinje   | INFO     | === CREATE ADMIN ACCOUNT ===
2025-07-01 10:28:21.179 | JERAGM\akinje   | INFO     | === ADMIN ACCOUNT CREATION ===
2025-07-01 10:28:21.206 | JERAGM\akinje   | INFO     | JML System v1.13
2025-07-01 10:28:21.231 | JERAGM\akinje   | INFO     | Processing Jira ticket: IT-46455
2025-07-01 10:28:21.262 | JERAGM\akinje   | INFO     | [INFO] Initializing Jira session...
2025-07-01 10:28:21.289 | JERAGM\akinje   | DEBUG    | [DEBUG] Received credential types:
2025-07-01 10:28:21.315 | JERAGM\akinje   | DEBUG    | [DEBUG] - jiraUsername type: String
2025-07-01 10:28:21.360 | JERAGM\akinje   | DEBUG    | [DEBUG] - jiraApiToken type: SecureString
2025-07-01 10:28:21.384 | JERAGM\akinje   | DEBUG    | [DEBUG] Credential validation passed
2025-07-01 10:28:21.430 | JERAGM\akinje   | DEBUG    | [DEBUG] Username format: Valid email
2025-07-01 10:28:21.469 | JERAGM\akinje   | DEBUG    | [DEBUG] API token=[REDACTED] (length: 192)
2025-07-01 10:28:21.499 | JERAGM\akinje   | DEBUG    | [DEBUG] Final PSCredential object:
2025-07-01 10:28:21.549 | JERAGM\akinje   | DEBUG    | [DEBUG] - Username: emmanuel.akinjomo***@jeragm.com
2025-07-01 10:28:21.594 | JERAGM\akinje   | DEBUG    | [DEBUG] - Password type: SecureString
2025-07-01 10:28:21.624 | JERAGM\akinje   | INFO     | [INFO] Connecting to Jira server: [SERVER]
2025-07-01 10:28:21.662 | JERAGM\akinje   | INFO     | [INFO] Using username: emmanuel.akinjomo***@jeragm.com
2025-07-01 10:28:21.690 | JERAGM\akinje   | INFO     | Initializing Jira connection | AUDIT: Username=emmanuel.akinjomo***@jeragm.com; Operation=JiraConnectionInit; ServerUrl=[SERVER]
2025-07-01 10:28:21.725 | JERAGM\akinje   | INFO     | Setting Jira server configuration
2025-07-01 10:28:21.765 | JERAGM\akinje   | INFO     | Creating Jira session using direct authentication
2025-07-01 10:28:21.824 | JERAGM\akinje   | INFO     | Testing Jira connection using GDPR-compliant endpoint
2025-07-01 10:28:22.689 | JERAGM\akinje   | INFO     | Jira connection test successful | AUDIT: UserDisplayName=Emmanuel Akinjomo; Status=Success; Operation=JiraConnectionTest; UserAccountId=712020:************************e1dd4dd40efa
2025-07-01 10:28:22.767 | JERAGM\akinje   | INFO     | Jira connection established successfully.
2025-07-01 10:28:22.892 | JERAGM\akinje   | INFO     | Connected as: Emmanuel Akinjomo (emmanuel.akinjomo***@jeragm.com)
2025-07-01 10:28:22.954 | JERAGM\akinje   | INFO     | Jira connection initialized successfully using JiraPS module
2025-07-01 10:28:23.052 | JERAGM\akinje   | INFO     | [SUCCESS] Jira session initialized successfully
2025-07-01 10:28:23.106 | JERAGM\akinje   | INFO     | Validating Jira ticket | AUDIT: TicketKey=IT-46455; Operation=JiraTicketValidation
2025-07-01 10:28:23.543 | JERAGM\akinje   | INFO     | Retrieved Jira ticket successfully | AUDIT: Operation=JiraTicketRetrieval; IssueTypeExists=False; TicketKey=IT-46455; FieldsAvailable=; IssueTypeType=null; TicketSummary=
2025-07-01 10:28:23.581 | JERAGM\akinje   | DEBUG    | Full ticket structure for debugging | AUDIT: TicketKey=IT-46455; Operation=FullTicketStructure; TicketStructure=null
2025-07-01 10:28:23.612 | JERAGM\akinje   | ERROR    | Admin account creation failed: Jira ticket validation failed: Jira ticket 'IT-46455' does not exist or you do not have permission to view it.
2025-07-01 10:28:23.643 | JERAGM\akinje   | ERROR    | Admin account creation failed: Jira ticket validation failed: Jira ticket 'IT-46455' does not exist or you do not have permission to view it. | AUDIT: ErrorType=RuntimeException; Operation=AdminAccountCreationError; Success=False
2025-07-01 10:28:23.675 | JERAGM\akinje   | INFO     | Adding comment to Jira ticket | AUDIT: UseADF=True; TicketKey=IT-46455; Operation=JiraCommentAdd; IncludeTimestamp=True
2025-07-01 10:28:24.507 | JERAGM\akinje   | WARNING  | Failed to add comment using JiraPS: Issue does not exist or you do not have permission to see it.
2025-07-01 10:28:24.542 | JERAGM\akinje   | ERROR    | Failed to add Jira comment: Issue does not exist or you do not have permission to see it. | AUDIT: ErrorType=RuntimeException; TicketKey=IT-46455; Operation=JiraCommentError
2025-07-01 10:28:24.577 | JERAGM\akinje   | WARNING  | WARNING: Failed to call Jira function 'Add-EnhancedJiraComment': Issue does not exist or you do not have permission to see it.
2025-07-01 10:28:24.615 | JERAGM\akinje   | WARNING  | WARNING: Failed to update Jira ticket with error: Jira function not available
2025-07-01 10:28:24.659 | JERAGM\akinje   | INFO     | Press any key to continue...
2025-07-01 10:40:40.441 | JERAGM\akinje   | INFO     | +--------------------------------------------------------------------------------------------------+
2025-07-01 10:40:40.464 | JERAGM\akinje   | INFO     | |  JML v1.13 | STATUS: Warning | AD: Unavailable | JIRA: Available | CREDS: Ready | USER: akinje   |
2025-07-01 10:40:40.489 | JERAGM\akinje   | INFO     | +--------------------------------------------------------------------------------------------------+
2025-07-01 10:40:40.523 | JERAGM\akinje   | INFO     | Available Operations:
2025-07-01 10:40:40.550 | JERAGM\akinje   | INFO     |   [1] Create Admin Account
2025-07-01 10:40:40.573 | JERAGM\akinje   | INFO     |   [2] Delete Admin Account
2025-07-01 10:40:40.605 | JERAGM\akinje   | INFO     |   [3] Reset Admin Account Password
2025-07-01 10:40:40.632 | JERAGM\akinje   | INFO     |   [4] System Information
2025-07-01 10:40:40.655 | JERAGM\akinje   | INFO     |   [5] Run Setup / Repair Credentials
2025-07-01 10:40:40.678 | JERAGM\akinje   | INFO     |   [7] Exit
2025-07-01 10:40:40.703 | JERAGM\akinje   | INFO     | +-[ Real-time Log Viewer ]---------------------------------------------------------------------------+
2025-07-01 10:40:40.725 | JERAGM\akinje   | INFO     | | 10:14:57 [INFO] UIManager service initialized                                                    |
2025-07-01 10:40:40.751 | JERAGM\akinje   | INFO     | | 10:14:57 [INFO] UI Manager service started                                                       |
2025-07-01 10:40:40.774 | JERAGM\akinje   | INFO     | +--------------------------------------------------------------------------------------------------+
2025-07-01 10:40:40.799 | JERAGM\akinje   | INFO     | > 
2025-07-01 10:40:40.829 | JERAGM\akinje   | WARNING  | WARNING: UIManager: Slow screen draw detected: 676.1913ms
