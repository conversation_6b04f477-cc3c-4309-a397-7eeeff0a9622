# Simple and precise fix for WPF parse-time type resolution
$scriptPath = "OnboardingFromJiraGUI.ps1"
$content = Get-Content $scriptPath -Raw

# Only fix the specific problematic type references that cause parse errors
# Use string literal replacement to avoid regex complexity

# Fix System.Windows.Media.Brushes - replace with runtime type resolution
$content = $content.Replace('[System.Windows.Media.Brushes]::', '([System.Windows.Media.Brushes])::')