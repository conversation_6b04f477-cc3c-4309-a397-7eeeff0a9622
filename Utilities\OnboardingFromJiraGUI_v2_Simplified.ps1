#Requires -Version 5.1

# UAT-Safe version - No AD module required

# Load WPF Assemblies - MUST be at the top before any WPF types are referenced
Add-Type -AssemblyName PresentationFramework
Add-Type -AssemblyName PresentationCore
Add-Type -AssemblyName WindowsBase
Add-Type -AssemblyName System.Windows.Forms # Often needed for MessageBox, etc.

# OnboardingFromJiraGUI_v2_Simplified.ps1
# Simplified version with minimal dependencies

# SECTION 1: SERVICE CLASSES
class LoggingService {
    [string]$LogLevel = 'INFO'
    [System.Collections.Generic.List[string]]$LogHistory

    LoggingService() {
        $this.LogHistory = [System.Collections.Generic.List[string]]::new()
    }

    [void] Info([string]$Message) { $this.WriteLog('INFO', $Message) }
    [void] Warn([string]$Message) { $this.WriteLog('WARN', $Message) }
    [void] Error([string]$Message) { $this.WriteLog('ERROR', $Message) }
    
    hidden WriteLog([string]$level, [string]$message) {
        $logEntry = "[{0}] [{1}] {2}" -f (Get-Date -Format 'yyyy-MM-dd HH:mm:ss'), $level.ToUpper(), $message
        Write-Host $logEntry
        $this.LogHistory.Add($logEntry)
        
        # Keep only last 100 entries to prevent memory issues
        if ($this.LogHistory.Count -gt 100) {
            $this.LogHistory.RemoveAt(0)
        }
    }
}

class ConfigurationService {
    [string[]] GetOUList() {
        return @(
            "OU=Tokyo,OU=Users,OU=Accounts,OU=JeraGM,DC=JERAGM,DC=COM",
            "OU=Users,OU=London,DC=jeragm,DC=com",
            "OU=Users,OU=Singapore,DC=jeragm,DC=com",
            "OU=Users,OU=USA,DC=jeragm,DC=com"
        )
    }

    [string] GetWindowTitle() { return "Onboarding from Jira GUI v2.0 - Simplified" }
}

class JiraService {
    $log
    JiraService($loggingService) { $this.log = $loggingService }

    [hashtable] GetTicketDetails([string]$ticketId) {
        try {
            $this.log.Info("Fetching ticket details for $ticketId.")
            # MOCK IMPLEMENTATION
            Start-Sleep -Milliseconds 500
            $mockData = @{ 
                summary = "Onboard New Analyst: John Doe"; 
                FirstName = "John";
                LastName = "Doe";
                JobTitle = "Trading Analyst";
                Department = "Trading";
            }
            return @{ Success = $true; Data = $mockData }
        } catch {
            $this.log.Error("Failed to get ticket $ticketId")
            return @{ Success = $false; ErrorMessage = $_.Exception.Message }
        }
    }
}

class ActiveDirectoryService {
    [bool]$Simulate
    [bool]$ADAvailable
    $log

    ActiveDirectoryService([bool]$simulateMode, $loggingService) { 
        $this.Simulate = $simulateMode
        $this.log = $loggingService
        
        # Check if AD module is available
        $this.ADAvailable = $false
        try {
            Get-Module -Name ActiveDirectory -ListAvailable | Out-Null
            $this.ADAvailable = $true
            $this.log.Info("Active Directory module detected.")
        } catch {
            $this.log.Info("Active Directory module not available - using simulation mode.")
            $this.Simulate = $true
        }
    }

    [hashtable] CreateUser([hashtable]$userDetails) {
        if ($this.Simulate -or -not $this.ADAvailable) {
            $this.log.Info("SIMULATION: Creating user $($userDetails.SamAccountName).")
            Start-Sleep -Milliseconds 300
            
            # Create output file for UAT validation
            $outputPath = Join-Path $env:TEMP "UAT_UserCreation_$(Get-Date -Format 'yyyyMMdd_HHmmss').txt"
            $output = @"
UAT TEST - User Creation Simulation
Generated: $(Get-Date)
Username: $($userDetails.SamAccountName)
First Name: $($userDetails.GivenName)
Last Name: $($userDetails.Surname)
Full Name: $($userDetails.Name)
"@
            $output | Out-File -FilePath $outputPath -Encoding UTF8
            
            return @{ 
                Success = $true; 
                Message = "SIMULATION: User created successfully. Output saved to: $outputPath" 
            }
        }
        return @{ Success = $true; Message = "User would be created in real mode." }
    }
}

# SECTION 2: STATE MANAGEMENT CLASS
class AppState {
    [string]$StatusMessage = "Ready."
    [bool]$IsBusy = $false
    [int]$CurrentStepIndex = 0
    [string]$TicketIdInput = ""
    [hashtable]$CurrentOnboardingData = @{}
    [string]$SelectedOU = ""
    
    # Event handlers
    [System.Collections.Generic.List[scriptblock]]$PropertyChangedHandlers = [System.Collections.Generic.List[scriptblock]]::new()
    
    [void] AddPropertyChangedHandler([scriptblock]$handler) {
        $this.PropertyChangedHandlers.Add($handler)
    }
    
    [void] NotifyPropertyChanged([string]$propertyName) {
        foreach ($handler in $this.PropertyChangedHandlers) {
            try {
                & $handler $propertyName
            } catch {
                Write-Warning "Property change handler error: $($_.Exception.Message)"
            }
        }
    }
    
    [void] SetStatusMessage([string]$value) { 
        $this.StatusMessage = $value
        $this.NotifyPropertyChanged('StatusMessage')
    }
    
    [void] SetCurrentStepIndex([int]$value) { 
        $this.CurrentStepIndex = $value
        $this.NotifyPropertyChanged('CurrentStepIndex')
    }
}

# SECTION 3: VIEWMODEL CLASS
class WizardViewModel {
    $log
    $config
    $jira
    $ad
    $state
    $view

    WizardViewModel($loggingService, $configService, $jiraService, $adService, $appState) {
        $this.log = $loggingService
        $this.config = $configService
        $this.jira = $jiraService
        $this.ad = $adService
        $this.state = $appState
    }

    RegisterView($view) {
        $this.view = $view
    }

    FetchTicketDetails() {
        $this.log.Info("FetchTicketDetails command executed.")
        $this.state.SetStatusMessage("Fetching ticket $($this.state.TicketIdInput)...")
        $this.state.IsBusy = $true

        try {
            $result = $this.jira.GetTicketDetails($this.state.TicketIdInput)
            
            if ($result.Success) {
                $this.state.SetStatusMessage("Ticket fetched successfully.")
                $this.state.CurrentOnboardingData = $result.Data
            } else {
                $this.state.SetStatusMessage("Error: $($result.ErrorMessage)")
            }
        } catch {
            $this.state.SetStatusMessage("Error: $($_.Exception.Message)")
        } finally {
            $this.state.IsBusy = $false
        }
    }

    CreateUser() {
        $this.log.Info("CreateUser command executed.")
        $this.state.SetStatusMessage("Creating user account...")
        $this.state.IsBusy = $true

        try {
            $data = $this.state.CurrentOnboardingData
            $userDetails = @{
                SamAccountName = ($data.FirstName.Substring(0,1) + $data.LastName).ToLower()
                GivenName = $data.FirstName
                Surname = $data.LastName
            }
            
            $result = $this.ad.CreateUser($userDetails)
            
            if ($result.Success) {
                $this.state.SetStatusMessage("User created successfully!")
            } else {
                $this.state.SetStatusMessage("Error: $($result.ErrorMessage)")
            }
        } catch {
            $this.state.SetStatusMessage("Error: $($_.Exception.Message)")
        } finally {
            $this.state.IsBusy = $false
        }
    }

    GoToNextStep() {
        if ($this.state.CurrentStepIndex -lt 2) {
            $this.state.SetCurrentStepIndex($this.state.CurrentStepIndex + 1)
        }
    }

    GoToPreviousStep() {
        if ($this.state.CurrentStepIndex -gt 0) {
            $this.state.SetCurrentStepIndex($this.state.CurrentStepIndex - 1)
        }
    }
}

# SECTION 4: VIEW CLASS
class WizardView {
    $viewModel
    $window

    WizardView($wizardViewModel) {
        $this.viewModel = $wizardViewModel
    }

    [object] CreateStep0() {
        $panel = New-Object System.Windows.Controls.StackPanel
        $panel.Margin = 20

        $label = New-Object System.Windows.Controls.Label
        $label.Content = "Step 1: Enter Jira Ticket ID"
        $label.FontSize = 16
        $label.FontWeight = 'Bold'
        $panel.Children.Add($label)

        $ticketLabel = New-Object System.Windows.Controls.Label
        $ticketLabel.Content = "Ticket ID:"
        $ticketLabel.Margin = "0,20,0,5"
        $panel.Children.Add($ticketLabel)

        $ticketBox = New-Object System.Windows.Controls.TextBox
        $ticketBox.Text = $this.viewModel.state.TicketIdInput
        $ticketBox.add_TextChanged({ $this.viewModel.state.TicketIdInput = $ticketBox.Text })
        $panel.Children.Add($ticketBox)

        $fetchButton = New-Object System.Windows.Controls.Button
        $fetchButton.Content = "Fetch Ticket Details"
        $fetchButton.Margin = "0,15,0,0"
        $fetchButton.Height = 30
        $fetchButton.add_Click({ $this.viewModel.FetchTicketDetails() })
        $panel.Children.Add($fetchButton)

        return $panel
    }

    [object] CreateStep1() {
        $panel = New-Object System.Windows.Controls.StackPanel
        $panel.Margin = 20

        $label = New-Object System.Windows.Controls.Label
        $label.Content = "Step 2: Review User Details"
        $label.FontSize = 16
        $label.FontWeight = 'Bold'
        $panel.Children.Add($label)

        $data = $this.viewModel.state.CurrentOnboardingData
        if ($data -and $data.Count -gt 0) {
            foreach ($key in $data.Keys) {
                $itemLabel = New-Object System.Windows.Controls.Label
                $itemLabel.Content = "$key : $($data[$key])"
                $itemLabel.Margin = "0,5,0,0"
                $panel.Children.Add($itemLabel)
            }
        } else {
            $noDataLabel = New-Object System.Windows.Controls.Label
            $noDataLabel.Content = "No data available. Please fetch ticket details first."
            $noDataLabel.Foreground = 'Red'
            $panel.Children.Add($noDataLabel)
        }

        return $panel
    }

    [object] CreateStep2() {
        $panel = New-Object System.Windows.Controls.StackPanel
        $panel.Margin = 20

        $label = New-Object System.Windows.Controls.Label
        $label.Content = "Step 3: Create User Account"
        $label.FontSize = 16
        $label.FontWeight = 'Bold'
        $panel.Children.Add($label)

        $createButton = New-Object System.Windows.Controls.Button
        $createButton.Content = "Create User Account"
        $createButton.Margin = "0,20,0,0"
        $createButton.Height = 35
        $createButton.Background = 'LightGreen'
        $createButton.add_Click({ $this.viewModel.CreateUser() })
        $panel.Children.Add($createButton)

        return $panel
    }

    Show() {
        try {
            $this.window = New-Object System.Windows.Window
            $this.window.Title = $this.viewModel.config.GetWindowTitle()
            $this.window.Width = 600
            $this.window.Height = 450
            $this.window.WindowStartupLocation = 'CenterScreen'

            # Main Grid
            $mainGrid = New-Object System.Windows.Controls.Grid
            $mainGrid.RowDefinitions.Add((New-Object System.Windows.Controls.RowDefinition))
            $mainGrid.RowDefinitions.Add((New-Object System.Windows.Controls.RowDefinition -Property @{ Height = 'Auto' }))
            $mainGrid.RowDefinitions.Add((New-Object System.Windows.Controls.RowDefinition -Property @{ Height = 'Auto' }))
            $this.window.Content = $mainGrid

            # Content Area
            $contentArea = New-Object System.Windows.Controls.ContentControl
            [System.Windows.Controls.Grid]::SetRow($contentArea, 0)
            $mainGrid.Children.Add($contentArea)

            # Status Bar
            $statusBar = New-Object System.Windows.Controls.Primitives.StatusBar
            [System.Windows.Controls.Grid]::SetRow($statusBar, 1)
            $statusText = New-Object System.Windows.Controls.TextBlock
            $statusText.Text = $this.viewModel.state.StatusMessage
            $statusBar.Items.Add($statusText)
            $mainGrid.Children.Add($statusBar)

            # Navigation
            $navPanel = New-Object System.Windows.Controls.StackPanel
            $navPanel.Orientation = 'Horizontal'
            $navPanel.HorizontalAlignment = 'Right'
            $navPanel.Margin = 10
            [System.Windows.Controls.Grid]::SetRow($navPanel, 2)

            $prevButton = New-Object System.Windows.Controls.Button
            $prevButton.Content = "Previous"
            $prevButton.Width = 80
            $prevButton.Height = 30
            $prevButton.Margin = "0,0,10,0"
            $prevButton.IsEnabled = $false
            $prevButton.add_Click({ $this.viewModel.GoToPreviousStep() })
            $navPanel.Children.Add($prevButton)

            $nextButton = New-Object System.Windows.Controls.Button
            $nextButton.Content = "Next"
            $nextButton.Width = 80
            $nextButton.Height = 30
            $nextButton.add_Click({ $this.viewModel.GoToNextStep() })
            $navPanel.Children.Add($nextButton)

            $mainGrid.Children.Add($navPanel)

            # Handle step changes
            $this.viewModel.state.AddPropertyChangedHandler({
                param($propertyName)
                if ($propertyName -eq 'CurrentStepIndex') {
                    $stepIndex = $this.viewModel.state.CurrentStepIndex
                    $this.window.Dispatcher.Invoke({
                        switch ($stepIndex) {
                            0 { $contentArea.Content = $this.CreateStep0() }
                            1 { $contentArea.Content = $this.CreateStep1() }
                            2 { $contentArea.Content = $this.CreateStep2() }
                        }
                        $prevButton.IsEnabled = ($stepIndex -gt 0)
                        $nextButton.Content = if ($stepIndex -eq 2) { "Finish" } else { "Next" }
                        if ($stepIndex -eq 2 -and $nextButton.Content -eq "Finish") {
                            $nextButton.add_Click({ $this.window.Close() })
                        }
                    })
                }
                if ($propertyName -eq 'StatusMessage') {
                    $this.window.Dispatcher.Invoke({
                        $statusText.Text = $this.viewModel.state.StatusMessage
                    })
                }
            })

            # Initial content
            $contentArea.Content = $this.CreateStep0()

            $this.window.ShowDialog() | Out-Null
        } catch {
            Write-Error "Failed to create window: $($_.Exception.Message)"
            throw
        }
    }
}

# SECTION 5: SCRIPT EXECUTION

try {
    # Create all services
    $logService = [LoggingService]::new()
    $configService = [ConfigurationService]::new()
    $jiraService = [JiraService]::new($logService)
    $adService = [ActiveDirectoryService]::new($true, $logService)
    $appState = [AppState]::new()

    # Create ViewModel
    $viewModel = [WizardViewModel]::new($logService, $configService, $jiraService, $adService, $appState)

    # Create View
    $view = [WizardView]::new($viewModel)
    $viewModel.RegisterView($view)

    # Start the application
    $logService.Info("Application starting...")
    $view.Show()
    $logService.Info("Application closed.")
} catch {
    Write-Error "Application failed to start: $($_.Exception.Message)"
    Read-Host "Press Enter to exit"
}