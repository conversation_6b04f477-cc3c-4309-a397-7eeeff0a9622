# Test Script Loading
# This script tests if the main script can be loaded without errors

Write-Host "Testing OnboardingFromJiraGUI.ps1 loading..." -ForegroundColor Cyan

try {
    # Set a flag to prevent GUI from loading
    $global:TestMode = $true
    
    # Try to dot-source the script
    Write-Host "Loading script..." -ForegroundColor Yellow
    . ".\Utilities\OnboardingFromJiraGUI.ps1"
    
    Write-Host "✅ Script loaded successfully!" -ForegroundColor Green
    
    # Test if the SAM function exists and works
    if (Get-Command Get-SamAccountName -ErrorAction SilentlyContinue) {
        Write-Host "✅ Get-SamAccountName function found" -ForegroundColor Green
        
        $testSam = Get-SamAccountName -FirstName "Test1" -LastName "Test2"
        Write-Host "✅ SAM generation test: $testSam" -ForegroundColor Green
    } else {
        Write-Host "❌ Get-SamAccountName function not found" -ForegroundColor Red
    }
    
} catch {
    Write-Host "❌ Error loading script:" -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Red
    Write-Host "Line: $($_.InvocationInfo.ScriptLineNumber)" -ForegroundColor Red
}

Write-Host "`nScript loading test complete" -ForegroundColor Cyan
