# Ultimate WPF fix - replace ALL problematic type references with Invoke-Expression
$scriptPath = "OnboardingFromJiraGUI.ps1"
$content = Get-Content $scriptPath -Raw

Write-Host "Applying ultimate WPF fix with Invoke-Expression..." -ForegroundColor Yellow

# Replace function parameters
$content = $content -replace '\[System\.Windows\.Controls\.Grid\]\$', '[Object]$'
$content = $content -replace '\[System\.Windows\.Controls\.DockPanel\]\$', '[Object]$'

# Replace ALL type references with Invoke-Expression to delay resolution
$content = $content -replace '\[System\.Windows\.Media\.Brushes\]::(\w+)', '(Invoke-Expression "[System.Windows.Media.Brushes]::$1")'
$content = $content -replace '\[System\.Windows\.GridUnitType\]::(\w+)', '(Invoke-Expression "[System.Windows.GridUnitType]::$1")'
$content = $content -replace '\[System\.Windows\.Controls\.Grid\]::(\w+)', '(Invoke-Expression "[System.Windows.Controls.Grid]::$1")'
$content = $content -replace '\[System\.Windows\.Controls\.DockPanel\]::(\w+)', '(Invoke-Expression "[System.Windows.Controls.DockPanel]::$1")'

$content | Set-Content $scriptPath -Encoding UTF8

Write-Host "Applied ultimate WPF fix" -ForegroundColor Green