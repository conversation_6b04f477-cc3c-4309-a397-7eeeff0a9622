# Debug test to compare authentication methods
param(
    [Parameter(Mandatory=$true)]
    [string]$Email,
    
    [Parameter(Mandatory=$true)]
    [Security.SecureString]$ApiToken
)

Write-Host "=== Authentication Debug Test ===" -ForegroundColor Cyan
Write-Host "Testing different authentication approaches..." -ForegroundColor Yellow

# Convert secure string to plain text
$apiTokenPlain = [Runtime.InteropServices.Marshal]::PtrToStringAuto([Runtime.InteropServices.Marshal]::SecureStringToBSTR($ApiToken))

$jiraUrl = "https://jeraglobalmarkets.atlassian.net"

Write-Host "`n1. Testing Enhanced Jira Viewer method (JiraPS)..." -ForegroundColor Green

try {
    # Method 1: Enhanced Jira Viewer approach (JiraPS module)
    Import-Module JiraPS -ErrorAction Stop
    Set-JiraConfigServer -Server $jiraUrl
    $credential = New-Object System.Management.Automation.PSCredential($Email, $ApiToken)
    $serverInfo = Get-JiraServerInfo -Credential $credential
    Write-Host "✅ JiraPS method SUCCESS" -ForegroundColor Green
    Write-Host "   Server: $($serverInfo.serverTitle)" -ForegroundColor Gray
} catch {
    Write-Host "❌ JiraPS method FAILED: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n2. Testing Onboarding Script method (Direct REST)..." -ForegroundColor Green

try {
    # Method 2: Direct REST API approach (like onboarding script)
    $authString = $Email + ':' + $apiTokenPlain
    $base64AuthInfo = [Convert]::ToBase64String([Text.Encoding]::ASCII.GetBytes($authString))
    
    # Test with same headers as onboarding script
    $headers = @{
        "Authorization" = "Basic $base64AuthInfo"
        "Accept" = "application/json"
        "Content-Type" = "application/json"
    }
    
    $response = Invoke-RestMethod -Uri "$jiraUrl/rest/api/3/myself" -Headers $headers -Method Get -ErrorAction Stop
    Write-Host "✅ Direct REST method SUCCESS" -ForegroundColor Green
    Write-Host "   User: $($response.displayName) ($($response.emailAddress))" -ForegroundColor Gray
} catch {
    Write-Host "❌ Direct REST method FAILED: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        Write-Host "   Status: $($_.Exception.Response.StatusCode)" -ForegroundColor Red
    }
}

Write-Host "`n3. Testing Direct REST without Content-Type header..." -ForegroundColor Green

try {
    # Method 3: Direct REST without Content-Type (for GET requests)
    $authString = $Email + ':' + $apiTokenPlain
    $base64AuthInfo = [Convert]::ToBase64String([Text.Encoding]::ASCII.GetBytes($authString))
    
    $headers = @{
        "Authorization" = "Basic $base64AuthInfo"
        "Accept" = "application/json"
    }
    
    $response = Invoke-RestMethod -Uri "$jiraUrl/rest/api/3/myself" -Headers $headers -Method Get -ErrorAction Stop
    Write-Host "✅ Direct REST (no Content-Type) SUCCESS" -ForegroundColor Green
    Write-Host "   User: $($response.displayName) ($($response.emailAddress))" -ForegroundColor Gray
} catch {
    Write-Host "❌ Direct REST (no Content-Type) FAILED: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        Write-Host "   Status: $($_.Exception.Response.StatusCode)" -ForegroundColor Red
    }
}

Write-Host "`n4. Testing with UTF8 encoding instead of ASCII..." -ForegroundColor Green

try {
    # Method 4: UTF8 encoding instead of ASCII
    $authString = $Email + ':' + $apiTokenPlain
    $base64AuthInfo = [Convert]::ToBase64String([Text.Encoding]::UTF8.GetBytes($authString))
    
    $headers = @{
        "Authorization" = "Basic $base64AuthInfo"
        "Accept" = "application/json"
    }
    
    $response = Invoke-RestMethod -Uri "$jiraUrl/rest/api/3/myself" -Headers $headers -Method Get -ErrorAction Stop
    Write-Host "✅ UTF8 encoding SUCCESS" -ForegroundColor Green
    Write-Host "   User: $($response.displayName) ($($response.emailAddress))" -ForegroundColor Gray
} catch {
    Write-Host "❌ UTF8 encoding FAILED: $($_.Exception.Message)" -ForegroundColor Red
    if ($_.Exception.Response) {
        Write-Host "   Status: $($_.Exception.Response.StatusCode)" -ForegroundColor Red
    }
}

Write-Host "`n=== Debug Complete ===" -ForegroundColor Cyan