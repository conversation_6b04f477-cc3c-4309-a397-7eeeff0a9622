# Test SAM Generation Function
# This script tests the SAM generation function to ensure it works correctly

# Extract just the SAM generation function from the main script
function Get-SamAccountName {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory)]
        [string]$FirstName,
        [Parameter(Mandatory)]
        [string]$LastName
    )

    # Remove spaces from names for SAM account generation (matching bulk import logic)
    $FirstName = $FirstName -replace '\s+', ''
    $LastName = $LastName -replace '\s+', ''

    # Get first 5 letters of last name (or less if name is shorter) - SAME AS BULK IMPORT
    $lastPart = $LastName.Substring(0, [Math]::Min(5, $LastName.Length))

    # Get first letter of first name - SAME AS BULK IMPORT
    $firstPart = $FirstName.Substring(0, 1)

    # Combine to create base SAM account name - SAME AS BULK IMPORT
    $baseSam = ($lastPart + $firstPart).ToLower()

    # Ensure minimum 6 characters by padding with additional letters from first name if needed
    if ($baseSam.Length -lt 6 -and $FirstName.Length -gt 1) {
        $additionalChars = $FirstName.Substring(1, [Math]::Min($FirstName.Length - 1, 6 - $baseSam.Length))
        $baseSam += $additionalChars.ToLower()
    }

    # If still less than 6 characters, pad with 'x'
    while ($baseSam.Length -lt 6) {
        $baseSam += 'x'
    }

    # Ensure it doesn't exceed 20 characters (AD limit)
    if ($baseSam.Length -gt 20) {
        $baseSam = $baseSam.Substring(0, 20)
    }

    return $baseSam
}

Write-Host "Testing SAM Generation Function..." -ForegroundColor Cyan

# Test cases
$testCases = @(
    @{ FirstName = "Test1"; LastName = "Test2"; Expected = "test2t" }
    @{ FirstName = "John"; LastName = "Smith"; Expected = "smithj" }
    @{ FirstName = "Jane"; LastName = "Doe"; Expected = "doej" }
    @{ FirstName = "A"; LastName = "B"; Expected = "ba" }
    @{ FirstName = "Very Long First Name"; LastName = "Very Long Last Name"; Expected = "verylv" }
)

foreach ($test in $testCases) {
    try {
        Write-Host "`nTesting: $($test.FirstName) $($test.LastName)" -ForegroundColor Yellow
        
        $result = Get-SamAccountName -FirstName $test.FirstName -LastName $test.LastName
        
        Write-Host "Expected: $($test.Expected)" -ForegroundColor Green
        Write-Host "Got:      $result" -ForegroundColor $(if ($result -eq $test.Expected) { "Green" } else { "Red" })
        Write-Host "Result:   $(if ($result -eq $test.Expected) { "PASS" } else { "FAIL" })" -ForegroundColor $(if ($result -eq $test.Expected) { "Green" } else { "Red" })
        
        # Debug the result
        Write-Host "Result Type: $($result.GetType().Name)" -ForegroundColor Gray
        Write-Host "Result Length: $($result.Length)" -ForegroundColor Gray
        Write-Host "Result Bytes: $([System.Text.Encoding]::ASCII.GetBytes($result) -join ' ')" -ForegroundColor Gray
        
    } catch {
        Write-Host "ERROR: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "`nSAM Generation Test Complete" -ForegroundColor Cyan
