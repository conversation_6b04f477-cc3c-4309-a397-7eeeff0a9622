# Fix the GridLength constructor issue
$scriptPath = "OnboardingFromJiraGUI.ps1"
$content = Get-Content $scriptPath -Raw

Write-Host "Fixing GridLength constructor syntax..." -ForegroundColor Yellow

# Fix the specific issue where Invoke-Expression is inside New-Object parameters
$content = $content -replace 'New-Object System\.Windows\.GridLength\(([^,]+), Invoke-Expression "\[System\.Windows\.GridUnitType\]::(\w+)"\)', '$star = Invoke-Expression "[System.Windows.GridUnitType]::$2"; New-Object System.Windows.GridLength($1, $star)'

# Also fix any other similar patterns
$content = $content -replace 'Invoke-Expression "\[System\.Windows\.GridUnitType\]::(\w+)"', '([System.Windows.GridUnitType]::$1)'

# Write back
$content | Set-Content $scriptPath -Encoding UTF8

Write-Host "Fixed GridLength constructor syntax" -ForegroundColor Green