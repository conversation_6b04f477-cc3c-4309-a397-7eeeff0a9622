# Diagnostic version of the script with extensive logging
#Requires -Version 5.1

Write-Host "=== DIAGNOSTIC MODE ===" -ForegroundColor Yellow

# Load WPF Assemblies with detailed error reporting
try {
    Write-Host "Loading PresentationFramework..." -ForegroundColor Green
    Add-Type -AssemblyName PresentationFramework -ErrorAction Stop
    Write-Host "✓ PresentationFramework loaded" -ForegroundColor Green
    
    Write-Host "Loading PresentationCore..." -ForegroundColor Green
    Add-Type -AssemblyName PresentationCore -ErrorAction Stop  
    Write-Host "✓ PresentationCore loaded" -ForegroundColor Green
    
    Write-Host "Loading WindowsBase..." -ForegroundColor Green
    Add-Type -AssemblyName WindowsBase -ErrorAction Stop
    Write-Host "✓ WindowsBase loaded" -ForegroundColor Green
    
    Write-Host "Loading System.Windows.Forms..." -ForegroundColor Green
    Add-Type -AssemblyName System.Windows.Forms -ErrorAction Stop
    Write-Host "✓ System.Windows.Forms loaded" -ForegroundColor Green
} catch {
    Write-Error "Failed to load required WPF assemblies: $($_.Exception.Message)"
    exit 1
}

# Simple test class
class SimpleState {
    [string]$TestValue = "Hello"
    
    [void] SetTestValue([string]$value) {
        Write-Host "Setting value to: $value" -ForegroundColor Cyan
        $this.TestValue = $value
    }
}

Write-Host "Creating simple state..." -ForegroundColor Green
$simpleState = [SimpleState]::new()

Write-Host "Testing direct property access..." -ForegroundColor Green
$simpleState.TestValue = "Direct assignment works"
Write-Host "Direct assignment result: $($simpleState.TestValue)" -ForegroundColor Green

Write-Host "Testing setter method..." -ForegroundColor Green
$simpleState.SetTestValue("Setter method works")
Write-Host "Setter method result: $($simpleState.TestValue)" -ForegroundColor Green

# Create minimal window
try {
    Write-Host "Creating window..." -ForegroundColor Green
    $window = New-Object System.Windows.Window
    $window.Title = "Diagnostic Test"
    $window.Width = 300
    $window.Height = 200
    Write-Host "✓ Window created" -ForegroundColor Green
    
    Write-Host "Creating StackPanel..." -ForegroundColor Green
    $panel = New-Object System.Windows.Controls.StackPanel
    $panel.Margin = 10
    Write-Host "✓ StackPanel created" -ForegroundColor Green
    
    Write-Host "Creating Label..." -ForegroundColor Green
    $label = New-Object System.Windows.Controls.Label
    $label.Content = "Type here to test:"
    $panel.Children.Add($label)
    Write-Host "✓ Label created and added" -ForegroundColor Green
    
    Write-Host "Creating TextBox..." -ForegroundColor Green
    $textBox = New-Object System.Windows.Controls.TextBox
    $textBox.Text = $simpleState.TestValue
    Write-Host "✓ TextBox created" -ForegroundColor Green
    
    Write-Host "Setting up event handler..." -ForegroundColor Green
    $capturedState = $simpleState
    $capturedTextBox = $textBox
    
    $textChangedHandler = {
        try {
            $currentText = $capturedTextBox.Text
            Write-Host "TextChanged event fired with text: $currentText" -ForegroundColor Yellow
            $capturedState.SetTestValue($currentText)
        } catch {
            Write-Host "ERROR in TextChanged handler: $($_.Exception.Message)" -ForegroundColor Red
            Write-Host "Stack trace: $($_.ScriptStackTrace)" -ForegroundColor Red
        }
    }.GetNewClosure()
    
    $textBox.add_TextChanged($textChangedHandler)
    Write-Host "✓ Event handler attached" -ForegroundColor Green
    
    $panel.Children.Add($textBox)
    Write-Host "✓ TextBox added to panel" -ForegroundColor Green
    
    $window.Content = $panel
    Write-Host "✓ Panel set as window content" -ForegroundColor Green
    
    Write-Host "About to show window - this should open a dialog..." -ForegroundColor Yellow
    Write-Host "Type in the text box to test event handlers" -ForegroundColor Yellow
    Write-Host "Close the window when done testing" -ForegroundColor Yellow
    
    $result = $window.ShowDialog()
    Write-Host "Window closed with result: $result" -ForegroundColor Green
    
} catch {
    Write-Host "ERROR creating or showing window:" -ForegroundColor Red
    Write-Host "Message: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Stack: $($_.ScriptStackTrace)" -ForegroundColor Red
    Write-Host "Full error: $($_ | Out-String)" -ForegroundColor Red
}

Write-Host "=== DIAGNOSTIC COMPLETE ===" -ForegroundColor Yellow