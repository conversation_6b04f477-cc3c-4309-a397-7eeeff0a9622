#Requires -Version 5.1

<#
.SYNOPSIS
    Automates Active Directory user creation by fetching onboarding data from a Jira ticket via a professional WPF GUI.

.DESCRIPTION
    This script provides an enterprise-grade graphical user interface to streamline the user onboarding process. 
    It combines the robust features of an advanced Jira ticket viewer with a backend AD user creation engine.

    Key Features:
    - Professional WPF-based GUI with responsive controls and status indicators.
    - Securely connects to Jira with session management and timeouts.
    - Caches Jira ticket data to improve performance and reduce API calls.
    - Fetches user details from Jira custom fields, description, and comments with robust fallback logic.
    - Automatically generates sAMAccountName and UPN.
    - Allows for manual correction of fetched data before user creation.
    - Copies group memberships from a specified model user.
    - Performs pre-creation duplicate checks in Active Directory.
    - Posts detailed success or failure comments back to the Jira ticket.
    - Provides real-time, multi-level logging within the GUI.
    - **SIMULATION MODE**: When Active Directory module is not available, runs in a safe simulation mode.

.PARAMETER Domain
    The Active Directory domain name (e.g., "jeragm.com").

.PARAMETER OUList
    A comma-separated string of Distinguished Names for the OUs to be listed in the dropdown.

.EXAMPLE
    .\OnboardingFromJiraGUI.ps1 -Domain "jeragm.com" -OUList "OU=Users,OU=London,DC=jeragm,DC=com"

.NOTES
    Version: 5.0.0
    Author: Enhanced with Enterprise Features v5.0
    - Inspired by the 'Enhanced Jira Ticket Viewer with Enterprise Features'.
    - Run in a PowerShell session with permissions to create AD users and comment on Jira tickets.
    - PowerShell 5.1+ compatible with dynamic Unicode support
    - Enhanced error handling, caching, and real-time validation
#>
[CmdletBinding()]
param(
    [string]$Domain = "jeragm.com",
    [string[]]$OUList = @(
        "OU=Tokyo,OU=Users,OU=Accounts,OU=JeraGM,DC=JERAGM,DC=COM",
        "OU=Users,OU=London,DC=jeragm,DC=com",
        "OU=Users,OU=Singapore,DC=jeragm,DC=com",
        "OU=Users,OU=USA,DC=jeragm,DC=com"
    )
)

###############################################################################
#                           VERSION MANAGEMENT                                #
###############################################################################

#region Version Management & Compatibility

# Application Version Management
$script:AppVersion = @{
    Major = 5
    Minor = 0
    Patch = 0
    Build = (Get-Date -Format "yyyyMMdd")
    FullVersion = "5.0.0"
    ConfigVersion = "1.0"
    PowerShellMinVersion = "5.1"
}

Write-Host "OnboardingFromJiraGUI v$($script:AppVersion.FullVersion) - Build $($script:AppVersion.Build)" -ForegroundColor Cyan

# Configuration Compatibility Functions
function Test-ConfigurationCompatibility {
    param([hashtable]$Config)

    $issues = @()

    # Check if config version is compatible
    if ($Config.ConfigVersion -and $Config.ConfigVersion -ne $script:AppVersion.ConfigVersion) {
        $issues += "Configuration version mismatch. Expected: $($script:AppVersion.ConfigVersion), Found: $($Config.ConfigVersion)"
    }

    # Check required configuration sections
    $requiredSections = @('Security', 'Performance', 'Validation', 'UI')
    foreach ($section in $requiredSections) {
        if (-not $Config[$section]) {
            $issues += "Missing required configuration section: $section"
        }
    }

    return @{
        IsCompatible = $issues.Count -eq 0
        Issues = $issues
        Version = $Config.ConfigVersion
    }
}

function Update-ConfigurationToLatest {
    param([hashtable]$Config)

    # Add missing sections with defaults
    if (-not $Config.Security) {
        $Config.Security = @{
            MaxLoginAttempts = 3
            SessionTimeoutMinutes = 120
            RequireHttps = $true
            AuditLogging = $true
        }
    }

    if (-not $Config.Performance) {
        $Config.Performance = @{
            EnableCaching = $true
            EnableStatistics = $true
            CacheCleanupIntervalMinutes = 30
            MaxConcurrentOperations = 5
        }
    }

    if (-not $Config.Validation) {
        $Config.Validation = @{
            EnableRealTimeValidation = $true
            ShowSuggestions = $true
            HighlightErrors = $true
        }
    }

    if (-not $Config.UI) {
        $Config.UI = @{
            ShowProgressIndicators = $true
            EnableCancellation = $true
            AutoRefreshInterval = 30
            ThemeMode = "Auto"
        }
    }

    # Update version
    $Config.ConfigVersion = $script:AppVersion.ConfigVersion

    Write-AppLog "$($script:Icons.Success) Configuration updated to version $($script:AppVersion.ConfigVersion)" -Level 'INFO'
    return $Config
}

function Get-VersionInfo {
    return @{
        Application = $script:AppVersion
        PowerShell = @{
            Version = $PSVersionTable.PSVersion.ToString()
            Edition = $PSVersionTable.PSEdition
            Platform = $PSVersionTable.Platform
        }
        System = @{
            OS = [System.Environment]::OSVersion.ToString()
            MachineName = [System.Environment]::MachineName
            UserName = [System.Environment]::UserName
            CLRVersion = [System.Environment]::Version.ToString()
        }
        Modules = @{
            JiraPS = (Get-Module JiraPS -ListAvailable | Select-Object -First 1).Version.ToString()
        }
    }
}

###############################################################################
#                           PREREQUISITE CHECKS                               #
###############################################################################

#region Prerequisite Checks & Setup

# Enhanced PowerShell version and compatibility check
$script:PSVersion = $PSVersionTable.PSVersion.Major
if ($script:PSVersion -lt 5) {
    Write-Host "ERROR: This script requires PowerShell 5.1 or higher. Current version: $($PSVersionTable.PSVersion)" -ForegroundColor Red
    Write-Host "Please upgrade PowerShell or use PowerShell Core 6+" -ForegroundColor Yellow
    exit 1
}

# Enhanced dynamic icon selection based on PowerShell version
$script:UseUnicode = $script:PSVersion -ge 7

function Get-CompatibilityIcons {
    if ($script:UseUnicode) {
        # Unicode emojis for PowerShell 7+
        return @{
            Warning     = [char]0x26A0  # ⚠️ Warning sign
            Success     = [char]0x2705  # ✅ Check mark
            Error       = [char]0x274C  # ❌ Cross mark
            Info        = [char]0x2139  # ℹ️ Information
            Connection  = [char]0x1F510 # 🔐 Lock
            Login       = [char]0x1F511 # 🔑 Key
            Ticket      = [char]0x1F3AB # 🎫 Ticket
            User        = [char]0x1F464 # 👤 User
            Processing  = [char]0x23F3  # ⏳ Hourglass
            Cache       = [char]0x1F4E6 # 📦 Package
            Network     = [char]0x1F310 # 🌐 Globe
            Connected   = [char]0x2705  # ✅ Connected
            Refresh     = [char]0x1F504 # 🔄 Refresh
            Export      = [char]0x1F4BE # 💾 Save
            Copy        = [char]0x1F4CB # 📋 Clipboard
        }
    } else {
        # ASCII alternatives for PowerShell 5.x
        return @{
            Warning = "[!]"; Success = "[OK]"; Error = "[X]"; Info = "[i]";
            Connection = "[*]"; Login = "[+]"; Ticket = "[T]"; User = "[U]";
            Processing = "[...]"; Cache = "[CACHE]"; Network = "[NET]";
            Connected = "[OK]"; Refresh = "[R]"; Export = "[E]"; Copy = "[C]"
        }
    }
}

# Backward compatibility alias
function Get-Icons { return Get-CompatibilityIcons }

$script:Icons = Get-CompatibilityIcons

Write-Host "$($script:Icons.Success) PowerShell Version: $($PSVersionTable.PSVersion) - Compatible" -ForegroundColor Green
Write-Host "$($script:Icons.Info) Unicode Support: $(if ($script:UseUnicode) { 'Enabled (Emojis)' } else { 'Disabled (ASCII)' })" -ForegroundColor Cyan

# Enhanced Field Mapping Structure
$script:FieldMapping = @{
    # Core user fields with validation rules
    FirstName = @{
        CustomFieldId = "customfield_10304"
        DisplayName = "First Name"
        RegexPattern = "New Joiner Name:\s*([^`r`n]+)"
        ValidationRules = @("Required", "MinLength:1", "MaxLength:50", "NoSpecialChars")
        DefaultValue = ""
        Dependencies = @()
    }
    LastName = @{
        CustomFieldId = "customfield_10305"
        DisplayName = "Last Name"
        RegexPattern = "Last Name:\s*([^`r`n]+)"
        ValidationRules = @("Required", "MinLength:1", "MaxLength:50", "NoSpecialChars")
        DefaultValue = ""
        Dependencies = @()
    }
    JobTitle = @{
        CustomFieldId = "customfield_10238"
        DisplayName = "Job Title"
        RegexPattern = "Job Title:\s*([^`r`n]+)"
        ValidationRules = @("Required", "MinLength:2", "MaxLength:100")
        DefaultValue = ""
        Dependencies = @("Department")
        Suggestions = @{
            "Trading" = @("Trader", "Senior Trader", "Trading Assistant", "Risk Manager")
            "IT" = @("Software Developer", "System Administrator", "IT Support", "DevOps Engineer")
            "Finance" = @("Financial Analyst", "Accountant", "Controller", "CFO")
            "HR" = @("HR Manager", "Recruiter", "HR Assistant", "People Operations")
        }
    }
    Department = @{
        CustomFieldId = "customfield_10120"
        DisplayName = "Department"
        RegexPattern = "Department:\s*([^`r`n]+)"
        ValidationRules = @("Required", "ValidDepartment")
        DefaultValue = ""
        Dependencies = @()
        ValidValues = @("Trading", "IT", "Finance", "HR", "Operations", "Legal", "Compliance")
    }
    ModelAccount = @{
        CustomFieldId = "customfield_10343"
        DisplayName = "Model Account"
        RegexPattern = "Model Account:\s*([^`r`n]+)"
        ValidationRules = @("Required", "ValidSamAccountName")
        DefaultValue = ""
        Dependencies = @()
    }
    OfficeLocation = @{
        CustomFieldId = "customfield_10115"
        DisplayName = "Office Location"
        RegexPattern = "Office Location:\s*([^`r`n]+)"
        ValidationRules = @("Required", "ValidLocation")
        DefaultValue = ""
        Dependencies = @()
        ValidValues = @("London", "Singapore", "Tokyo", "New York")
    }
    EmployeeType = @{
        CustomFieldId = "customfield_10342"
        DisplayName = "Employee Type"
        RegexPattern = "Employee Type:\s*([^`r`n]+)"
        ValidationRules = @("Required")
        DefaultValue = "Employee"
        Dependencies = @()
        ValidValues = @("Employee", "Contractor", "Intern", "Consultant")
    }
    EffectiveDate = @{
        CustomFieldId = "customfield_10344"
        DisplayName = "Effective Date"
        RegexPattern = "Effective Date:\s*([^`r`n]+)"
        ValidationRules = @("Required", "ValidDate", "FutureDate")
        DefaultValue = ""
        Dependencies = @()
    }
}

###############################################################################
#                         ENHANCED ERROR HANDLING                             #
###############################################################################

#region Enhanced Error Handling Classes

# Error Level Enumeration
$script:ErrorLevel = @{
    DEBUG = 0
    INFO = 1
    WARN = 2
    ERROR = 3
    CRITICAL = 4
}

# Error Category Enumeration
$script:ErrorCategory = @{
    Authentication = "Authentication"
    Network = "Network"
    Validation = "Validation"
    ActiveDirectory = "ActiveDirectory"
    Jira = "Jira"
    Configuration = "Configuration"
    UserInput = "UserInput"
    System = "System"
}

# Enhanced Application Error Class
function New-AppError {
    param(
        [string]$Message,
        [string]$Category = $script:ErrorCategory.System,
        [int]$Level = $script:ErrorLevel.ERROR,
        [string]$Source = "",
        [object]$Exception = $null,
        [hashtable]$Context = @{},
        [string]$Suggestion = "",
        [bool]$IsRecoverable = $true
    )

    return @{
        Message = $Message
        Category = $Category
        Level = $Level
        Source = $Source
        Exception = $Exception
        Context = $Context
        Suggestion = $Suggestion
        IsRecoverable = $IsRecoverable
        Timestamp = Get-Date
        Id = [System.Guid]::NewGuid().ToString()
    }
}

# Error Recovery Strategies
$script:RecoveryStrategies = @{
    Authentication = @{
        MaxRetries = 3
        BackoffSeconds = @(1, 3, 5)
        Actions = @("ClearCredentials", "PromptReauth", "CheckNetwork")
    }
    Network = @{
        MaxRetries = 5
        BackoffSeconds = @(2, 4, 8, 16, 32)
        Actions = @("CheckConnection", "RetryRequest", "FallbackEndpoint")
    }
    Validation = @{
        MaxRetries = 1
        BackoffSeconds = @(0)
        Actions = @("ShowValidationErrors", "HighlightFields", "ProvideGuidance")
    }
    ActiveDirectory = @{
        MaxRetries = 3
        BackoffSeconds = @(1, 2, 4)
        Actions = @("CheckPermissions", "ValidateOU", "TestConnection")
    }
    Jira = @{
        MaxRetries = 3
        BackoffSeconds = @(2, 5, 10)
        Actions = @("CheckApiToken", "ValidateUrl", "TestPermissions")
    }
}

# User-Friendly Error Messages
$script:FriendlyErrorMessages = @{
    "Authentication failed" = @{
        Message = "Unable to connect to Jira. Please check your credentials."
        Suggestion = "Verify your API token and try again. Contact IT if the issue persists."
        Icon = "Error"
    }
    "Network timeout" = @{
        Message = "Connection timed out. Please check your network connection."
        Suggestion = "Ensure you're connected to the internet and try again."
        Icon = "Warning"
    }
    "Invalid ticket format" = @{
        Message = "The ticket number format is not valid."
        Suggestion = "Please enter a ticket number like 'PROJ-123' or 'IT-456'."
        Icon = "Info"
    }
    "User already exists" = @{
        Message = "A user with this information already exists in Active Directory."
        Suggestion = "Check the existing user or modify the details before creating."
        Icon = "Warning"
    }
    "Insufficient permissions" = @{
        Message = "You don't have permission to perform this action."
        Suggestion = "Contact your administrator to request the necessary permissions."
        Icon = "Error"
    }
}

# Error Recovery Function
function Invoke-ErrorRecovery {
    param(
        [object]$AppError,
        [int]$AttemptNumber = 1
    )

    $strategy = $script:RecoveryStrategies[$AppError.Category]
    if (-not $strategy -or $AttemptNumber -gt $strategy.MaxRetries) {
        return $false
    }

    $backoffTime = $strategy.BackoffSeconds[$AttemptNumber - 1]
    if ($backoffTime -gt 0) {
        Write-AppLog "Waiting $backoffTime seconds before retry attempt $AttemptNumber" -Level 'INFO'
        Start-Sleep -Seconds $backoffTime
    }

    foreach ($action in $strategy.Actions) {
        switch ($action) {
            "ClearCredentials" {
                $script:JiraCredential = $null
                Write-AppLog "Cleared cached credentials" -Level 'DEBUG'
            }
            "PromptReauth" {
                Update-Status "Authentication required. Please reconnect." "WARN"
            }
            "CheckNetwork" {
                Write-AppLog "Checking network connectivity..." -Level 'DEBUG'
            }
            "ShowValidationErrors" {
                if ($AppError.Context.ValidationErrors) {
                    foreach ($error in $AppError.Context.ValidationErrors) {
                        Update-Status $error "ERROR"
                    }
                }
            }
            "HighlightFields" {
                if ($AppError.Context.InvalidFields) {
                    # Highlight invalid fields in UI
                    Write-AppLog "Highlighting invalid fields: $($AppError.Context.InvalidFields -join ', ')" -Level 'DEBUG'
                }
            }
        }
    }

    return $true
}

# Enhanced Error Handling Function
function Handle-AppError {
    param(
        [object]$AppError,
        [bool]$ShowToUser = $true
    )

    # Log the error
    $logLevel = switch ($AppError.Level) {
        0 { 'DEBUG' }
        1 { 'INFO' }
        2 { 'WARN' }
        3 { 'ERROR' }
        4 { 'CRITICAL' }
        default { 'ERROR' }
    }

    Write-AppLog "[$($AppError.Category)] $($AppError.Message)" -Level $logLevel

    if ($AppError.Exception) {
        Write-AppLog "Exception: $($AppError.Exception.Message)" -Level 'DEBUG'
    }

    # Show user-friendly message
    if ($ShowToUser) {
        $friendlyError = $script:FriendlyErrorMessages[$AppError.Message]
        if ($friendlyError) {
            Update-Status $friendlyError.Message $logLevel
            if ($friendlyError.Suggestion) {
                Write-AppLog "Suggestion: $($friendlyError.Suggestion)" -Level 'INFO'
            }
        } else {
            Update-Status $AppError.Message $logLevel
            if ($AppError.Suggestion) {
                Write-AppLog "Suggestion: $($AppError.Suggestion)" -Level 'INFO'
            }
        }
    }

    return $AppError
}

###############################################################################
#                         USER-FRIENDLY ERROR MESSAGES                        #
###############################################################################

#region User-Friendly Error Messages

# Friendly Error Message Mapping
$script:FriendlyErrorMessages = @{
    # Authentication Errors
    "Authentication" = @{
        "InvalidCredentials" = @{
            Message = "The username or password you entered is incorrect."
            Suggestion = "Please check your credentials and try again. If you continue to have issues, contact your system administrator."
            Icon = $script:Icons.Error
        }
        "AccountLocked" = @{
            Message = "Your account has been temporarily locked due to multiple failed login attempts."
            Suggestion = "Please wait a few minutes before trying again, or contact your system administrator to unlock your account."
            Icon = $script:Icons.Warning
        }
        "SessionExpired" = @{
            Message = "Your session has expired for security reasons."
            Suggestion = "Please log in again to continue using the application."
            Icon = $script:Icons.Info
        }
    }

    # Network Errors
    "Network" = @{
        "ConnectionTimeout" = @{
            Message = "Unable to connect to the server. The connection timed out."
            Suggestion = "Please check your internet connection and try again. If the problem persists, the server may be temporarily unavailable."
            Icon = $script:Icons.Warning
        }
        "ServerUnavailable" = @{
            Message = "The server is currently unavailable."
            Suggestion = "Please try again in a few minutes. If the problem continues, contact your system administrator."
            Icon = $script:Icons.Error
        }
        "DNSResolutionFailed" = @{
            Message = "Unable to find the server. DNS resolution failed."
            Suggestion = "Please check the server address and your network connection. Contact your IT support if needed."
            Icon = $script:Icons.Error
        }
    }

    # Validation Errors
    "Validation" = @{
        "RequiredFieldEmpty" = @{
            Message = "This field is required and cannot be left empty."
            Suggestion = "Please enter a value for this field before continuing."
            Icon = $script:Icons.Warning
        }
        "InvalidFormat" = @{
            Message = "The format of the entered data is not valid."
            Suggestion = "Please check the format requirements and enter the data correctly."
            Icon = $script:Icons.Warning
        }
        "ValueTooLong" = @{
            Message = "The entered value is too long."
            Suggestion = "Please shorten the text to meet the maximum length requirement."
            Icon = $script:Icons.Warning
        }
        "ValueTooShort" = @{
            Message = "The entered value is too short."
            Suggestion = "Please enter a longer value to meet the minimum length requirement."
            Icon = $script:Icons.Warning
        }
    }

    # Active Directory Errors
    "ActiveDirectory" = @{
        "UserAlreadyExists" = @{
            Message = "A user with this account name already exists."
            Suggestion = "Please choose a different account name or check if this user has already been created."
            Icon = $script:Icons.Warning
        }
        "InsufficientPermissions" = @{
            Message = "You don't have sufficient permissions to perform this operation."
            Suggestion = "Please contact your system administrator to request the necessary permissions."
            Icon = $script:Icons.Error
        }
        "OUNotFound" = @{
            Message = "The specified organizational unit (OU) could not be found."
            Suggestion = "Please verify the OU path and ensure it exists in Active Directory."
            Icon = $script:Icons.Error
        }
    }

    # System Errors
    "System" = @{
        "OutOfMemory" = @{
            Message = "The system is running low on memory."
            Suggestion = "Please close other applications and try again. If the problem persists, restart the application."
            Icon = $script:Icons.Error
        }
        "FileNotFound" = @{
            Message = "A required file could not be found."
            Suggestion = "Please ensure all necessary files are present and try again. You may need to reinstall the application."
            Icon = $script:Icons.Error
        }
        "AccessDenied" = @{
            Message = "Access to the requested resource was denied."
            Suggestion = "Please check your permissions or contact your system administrator for assistance."
            Icon = $script:Icons.Error
        }
    }
}

# Get Friendly Error Message
function Get-FriendlyErrorMessage {
    param(
        [string]$Category,
        [string]$ErrorType,
        [string]$DefaultMessage = "An unexpected error occurred."
    )

    $friendlyError = $script:FriendlyErrorMessages[$Category][$ErrorType]

    if ($friendlyError) {
        return @{
            Message = $friendlyError.Message
            Suggestion = $friendlyError.Suggestion
            Icon = $friendlyError.Icon
            HasFriendlyMessage = $true
        }
    } else {
        return @{
            Message = $DefaultMessage
            Suggestion = "Please try again or contact support if the problem persists."
            Icon = $script:Icons.Error
            HasFriendlyMessage = $false
        }
    }
}

# Enhanced Error Display Function
function Show-FriendlyError {
    param(
        [object]$AppError,
        [bool]$ShowDialog = $false
    )

    $friendlyError = Get-FriendlyErrorMessage -Category $AppError.Category -ErrorType $AppError.Type -DefaultMessage $AppError.Message

    # Log the technical error details
    Write-AppLog "Technical Error: $($AppError.Message)" -Level 'DEBUG'
    if ($AppError.Exception) {
        Write-AppLog "Exception Details: $($AppError.Exception.ToString())" -Level 'DEBUG'
    }

    # Display friendly message to user
    $displayMessage = "$($friendlyError.Icon) $($friendlyError.Message)"
    if ($friendlyError.Suggestion) {
        $displayMessage += "`n`nSuggestion: $($friendlyError.Suggestion)"
    }

    Write-AppLog $displayMessage -Level 'INFO'

    # Update UI status
    if ($controls.StatusBar) {
        Update-Status $friendlyError.Message "ERROR"
    }

    # Show dialog if requested
    if ($ShowDialog) {
        [System.Windows.MessageBox]::Show($displayMessage, "Error", [System.Windows.MessageBoxButton]::OK, [System.Windows.MessageBoxImage]::Warning)
    }

    return $friendlyError
}

#endregion

###############################################################################
#                         ENHANCED CACHING SYSTEM                             #
###############################################################################

#region Enhanced Caching System

# Performance Cache Class
function New-PerformanceCache {
    param(
        [int]$MaxSize = 50,
        [int]$ExpiryMinutes = 60,
        [bool]$EnableStatistics = $true
    )

    return @{
        Data = @{}
        MaxSize = $MaxSize
        ExpiryMinutes = $ExpiryMinutes
        EnableStatistics = $EnableStatistics
        Statistics = @{
            Hits = 0
            Misses = 0
            Evictions = 0
            TotalRequests = 0
            LastCleanup = Get-Date
        }
        CreatedAt = Get-Date
    }
}

# Enhanced Cache Operations
function Get-CacheEntry {
    param(
        [object]$Cache,
        [string]$Key
    )

    $Cache.Statistics.TotalRequests++

    if ($Cache.Data.ContainsKey($Key)) {
        $entry = $Cache.Data[$Key]
        $age = ((Get-Date) - $entry.Timestamp).TotalMinutes

        if ($age -lt $Cache.ExpiryMinutes) {
            $Cache.Statistics.Hits++
            Write-AppLog "$($script:Icons.Cache) Cache hit for key: $Key (age: $([math]::Round($age, 1))m)" -Level 'DEBUG'
            return $entry.Data
        } else {
            # Expired entry
            $Cache.Data.Remove($Key)
            Write-AppLog "$($script:Icons.Refresh) Cache expired for key: $Key (age: $([math]::Round($age, 1))m)" -Level 'DEBUG'
        }
    }

    $Cache.Statistics.Misses++
    Write-AppLog "$($script:Icons.Warning) Cache miss for key: $Key" -Level 'DEBUG'
    return $null
}

function Set-CacheEntry {
    param(
        [object]$Cache,
        [string]$Key,
        [object]$Data
    )

    # Check if cache is full and evict oldest entry
    if ($Cache.Data.Count -ge $Cache.MaxSize) {
        $oldestKey = ($Cache.Data.GetEnumerator() | Sort-Object { $_.Value.Timestamp } | Select-Object -First 1).Key
        $Cache.Data.Remove($oldestKey)
        $Cache.Statistics.Evictions++
        Write-AppLog "$($script:Icons.Export) Cache evicted oldest entry: $oldestKey" -Level 'DEBUG'
    }

    $Cache.Data[$Key] = @{
        Data = $Data
        Timestamp = Get-Date
        AccessCount = 1
    }

    Write-AppLog "$($script:Icons.Cache) Cached entry: $Key" -Level 'DEBUG'

    # Update UI cache statistics if available
    if ($controls.CacheStats) {
        $hitRate = if ($Cache.Statistics.TotalRequests -gt 0) {
            [math]::Round(($Cache.Statistics.Hits / $Cache.Statistics.TotalRequests) * 100, 1)
        } else { 0 }
        $controls.CacheStats.Text = "Cache: $($Cache.Data.Count)/$($Cache.MaxSize) items (Hit Rate: $hitRate%)"
    }
}

function Clear-CacheExpired {
    param([object]$Cache)

    $expiredKeys = @()
    $now = Get-Date

    foreach ($key in $Cache.Data.Keys) {
        $age = ($now - $Cache.Data[$key].Timestamp).TotalMinutes
        if ($age -gt $Cache.ExpiryMinutes) {
            $expiredKeys += $key
        }
    }

    foreach ($key in $expiredKeys) {
        $Cache.Data.Remove($key)
    }

    if ($expiredKeys.Count -gt 0) {
        Write-AppLog "$($script:Icons.Refresh) Cleaned up $($expiredKeys.Count) expired cache entries" -Level 'DEBUG'
    }

    $Cache.Statistics.LastCleanup = $now
}

function Get-CacheStatistics {
    param([object]$Cache)

    $hitRate = if ($Cache.Statistics.TotalRequests -gt 0) {
        [math]::Round(($Cache.Statistics.Hits / $Cache.Statistics.TotalRequests) * 100, 2)
    } else { 0 }

    return @{
        TotalEntries = $Cache.Data.Count
        MaxSize = $Cache.MaxSize
        HitRate = $hitRate
        TotalRequests = $Cache.Statistics.TotalRequests
        Hits = $Cache.Statistics.Hits
        Misses = $Cache.Statistics.Misses
        Evictions = $Cache.Statistics.Evictions
        LastCleanup = $Cache.Statistics.LastCleanup
        CacheAge = ((Get-Date) - $Cache.CreatedAt).TotalMinutes
    }
}

###############################################################################
#                         ENHANCED VALIDATION FRAMEWORK                       #
###############################################################################

#region Enhanced Validation Framework

# Field Validator Class
function New-FieldValidator {
    return @{
        Rules = @{}
        Dependencies = @{}
        ValidationResults = @{}
    }
}

# Validation Rule Functions
function Test-Required {
    param([string]$Value)
    return -not [string]::IsNullOrWhiteSpace($Value)
}

function Test-MinLength {
    param([string]$Value, [int]$MinLength)
    return $Value.Length -ge $MinLength
}

function Test-MaxLength {
    param([string]$Value, [int]$MaxLength)
    return $Value.Length -le $MaxLength
}

function Test-NoSpecialChars {
    param([string]$Value)
    return $Value -match '^[a-zA-Z\s\-\.]+$'
}

function Test-ValidSamAccountName {
    param([string]$Value)
    return $Value -match '^[a-zA-Z0-9\-\.]+$' -and $Value.Length -le 20
}

function Test-ValidDate {
    param([string]$Value)
    try {
        [DateTime]::Parse($Value) | Out-Null
        return $true
    } catch {
        return $false
    }
}

function Test-FutureDate {
    param([string]$Value)
    try {
        $date = [DateTime]::Parse($Value)
        return $date -gt (Get-Date)
    } catch {
        return $false
    }
}

function Test-ValidDepartment {
    param([string]$Value)
    $validDepartments = $script:FieldMapping.Department.ValidValues
    return $validDepartments -contains $Value
}

function Test-ValidLocation {
    param([string]$Value)
    $validLocations = $script:FieldMapping.OfficeLocation.ValidValues
    return $validLocations -contains $Value
}

# Enhanced Field Validation Function
function Invoke-FieldValidation {
    param(
        [string]$FieldName,
        [string]$Value,
        [object]$FieldMapping
    )

    $results = @{
        IsValid = $true
        Errors = @()
        Warnings = @()
        Suggestions = @()
    }

    $fieldConfig = $FieldMapping[$FieldName]
    if (-not $fieldConfig) {
        $results.IsValid = $false
        $results.Errors += "Unknown field: $FieldName"
        return $results
    }

    # Apply validation rules
    foreach ($rule in $fieldConfig.ValidationRules) {
        $ruleParts = $rule -split ':'
        $ruleName = $ruleParts[0]
        $ruleParam = if ($ruleParts.Length -gt 1) { $ruleParts[1] } else { $null }

        $isValid = switch ($ruleName) {
            "Required" { Test-Required -Value $Value }
            "MinLength" { Test-MinLength -Value $Value -MinLength ([int]$ruleParam) }
            "MaxLength" { Test-MaxLength -Value $Value -MaxLength ([int]$ruleParam) }
            "NoSpecialChars" { Test-NoSpecialChars -Value $Value }
            "ValidSamAccountName" { Test-ValidSamAccountName -Value $Value }
            "ValidDate" { Test-ValidDate -Value $Value }
            "FutureDate" { Test-FutureDate -Value $Value }
            "ValidDepartment" { Test-ValidDepartment -Value $Value }
            "ValidLocation" { Test-ValidLocation -Value $Value }
            default { $true }
        }

        if (-not $isValid) {
            $results.IsValid = $false
            $errorMessage = switch ($ruleName) {
                "Required" { "$($fieldConfig.DisplayName) is required" }
                "MinLength" { "$($fieldConfig.DisplayName) must be at least $ruleParam characters" }
                "MaxLength" { "$($fieldConfig.DisplayName) must be no more than $ruleParam characters" }
                "NoSpecialChars" { "$($fieldConfig.DisplayName) can only contain letters, spaces, hyphens, and periods" }
                "ValidSamAccountName" { "$($fieldConfig.DisplayName) must be a valid account name (alphanumeric, hyphens, periods, max 20 chars)" }
                "ValidDate" { "$($fieldConfig.DisplayName) must be a valid date" }
                "FutureDate" { "$($fieldConfig.DisplayName) must be a future date" }
                "ValidDepartment" { "$($fieldConfig.DisplayName) must be one of: $($fieldConfig.ValidValues -join ', ')" }
                "ValidLocation" { "$($fieldConfig.DisplayName) must be one of: $($fieldConfig.ValidValues -join ', ')" }
                default { "$($fieldConfig.DisplayName) failed validation rule: $ruleName" }
            }
            $results.Errors += $errorMessage
        }
    }

    # Add suggestions based on field type and dependencies
    if ($fieldConfig.Suggestions -and $FieldName -eq "JobTitle") {
        $department = $script:CurrentFormData.Department
        if ($department -and $fieldConfig.Suggestions[$department]) {
            $results.Suggestions += $fieldConfig.Suggestions[$department]
        }
    }

    return $results
}

# Validate All Fields Function
function Invoke-FormValidation {
    param([hashtable]$FormData)

    $overallResults = @{
        IsValid = $true
        FieldResults = @{}
        ErrorCount = 0
        WarningCount = 0
    }

    foreach ($fieldName in $script:FieldMapping.Keys) {
        $value = $FormData[$fieldName]
        $fieldResult = Invoke-FieldValidation -FieldName $fieldName -Value $value -FieldMapping $script:FieldMapping

        $overallResults.FieldResults[$fieldName] = $fieldResult

        if (-not $fieldResult.IsValid) {
            $overallResults.IsValid = $false
            $overallResults.ErrorCount += $fieldResult.Errors.Count
        }

        $overallResults.WarningCount += $fieldResult.Warnings.Count
    }

    return $overallResults
}

###############################################################################
#                         REAL-TIME VALIDATION & UI                           #
###############################################################################

#region Real-time Validation and UI Feedback

# Real-time Field Validation Function
function Add-RealTimeValidation {
    param(
        [object]$Control,
        [string]$FieldName
    )

    if (-not $Control) { return }

    # Add TextChanged event for real-time validation
    $Control.Add_TextChanged({
        $fieldValue = $this.Text
        $script:CurrentFormData[$FieldName] = $fieldValue

        # Perform validation
        $validationResult = Invoke-FieldValidation -FieldName $FieldName -Value $fieldValue -FieldMapping $script:FieldMapping

        # Update UI based on validation result
        Update-FieldValidationUI -Control $this -ValidationResult $validationResult -FieldName $FieldName

        # Update suggestions if available
        if ($validationResult.Suggestions.Count -gt 0 -and $script:Config.Validation.ShowSuggestions) {
            Show-FieldSuggestions -Control $this -Suggestions $validationResult.Suggestions
        }
    })

    # Add LostFocus event for final validation
    $Control.Add_LostFocus({
        $fieldValue = $this.Text
        $validationResult = Invoke-FieldValidation -FieldName $FieldName -Value $fieldValue -FieldMapping $script:FieldMapping

        if (-not $validationResult.IsValid) {
            Show-ValidationTooltip -Control $this -Errors $validationResult.Errors
        }
    })
}

# Update Field Validation UI
function Update-FieldValidationUI {
    param(
        [object]$Control,
        [object]$ValidationResult,
        [string]$FieldName
    )

    if (-not $Control -or -not $script:Config.Validation.HighlightErrors) { return }

    try {
        if ($ValidationResult.IsValid) {
            # Valid field - green border
            $Control.BorderBrush = [System.Windows.Media.Brushes]::LightGreen
            $Control.BorderThickness = "1"
            $Control.ToolTip = $null
        } else {
            # Invalid field - red border
            $Control.BorderBrush = [System.Windows.Media.Brushes]::Red
            $Control.BorderThickness = "2"

            # Set tooltip with error messages
            $errorText = $ValidationResult.Errors -join "`n"
            $Control.ToolTip = $errorText
        }
    } catch {
        Write-AppLog "Error updating field validation UI: $($_.Exception.Message)" -Level 'DEBUG'
    }
}

# Show Field Suggestions
function Show-FieldSuggestions {
    param(
        [object]$Control,
        [array]$Suggestions
    )

    if (-not $Control -or $Suggestions.Count -eq 0) { return }

    try {
        # Create or update suggestion tooltip
        $suggestionText = "Suggestions:`n" + ($Suggestions -join "`n")

        # Only show if control doesn't already have an error tooltip
        if (-not $Control.ToolTip -or $Control.BorderBrush -ne [System.Windows.Media.Brushes]::Red) {
            $Control.ToolTip = $suggestionText
        }
    } catch {
        Write-AppLog "Error showing field suggestions: $($_.Exception.Message)" -Level 'DEBUG'
    }
}

# Show Validation Tooltip
function Show-ValidationTooltip {
    param(
        [object]$Control,
        [array]$Errors
    )

    if (-not $Control -or $Errors.Count -eq 0) { return }

    try {
        $errorText = $Errors -join "`n"
        $Control.ToolTip = $errorText

        # Optionally show a popup or status message
        if ($script:Config.Validation.ShowSuggestions) {
            Update-Status "Validation Error: $($Errors[0])" "ERROR"
        }
    } catch {
        Write-AppLog "Error showing validation tooltip: $($_.Exception.Message)" -Level 'DEBUG'
    }
}

# Validate Form on Submit
function Invoke-FormSubmitValidation {
    param([hashtable]$FormData)

    $validationResults = Invoke-FormValidation -FormData $FormData

    if (-not $validationResults.IsValid) {
        $errorCount = $validationResults.ErrorCount
        $warningCount = $validationResults.WarningCount

        $message = "Form validation failed with $errorCount error(s)"
        if ($warningCount -gt 0) {
            $message += " and $warningCount warning(s)"
        }

        Update-Status $message "ERROR"

        # Highlight first invalid field
        foreach ($fieldName in $validationResults.FieldResults.Keys) {
            $fieldResult = $validationResults.FieldResults[$fieldName]
            if (-not $fieldResult.IsValid) {
                # Focus on first invalid field
                $controlName = $fieldName + "Box"
                if ($controls[$controlName]) {
                    $controls[$controlName].Focus()
                }
                break
            }
        }

        return $false
    }

    Update-Status "Form validation passed successfully" "INFO"
    return $true
}

###############################################################################
#                         PROGRESS INDICATORS & CANCELLATION                  #
###############################################################################

#region Progress Indicators and Operation Cancellation

# Progress Dialog Class
function New-ProgressDialog {
    param(
        [string]$Title = "Operation in Progress",
        [string]$Message = "Please wait...",
        [bool]$ShowCancelButton = $true
    )

    $xaml = @"
<Window xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="$Title" Height="200" Width="400"
        WindowStartupLocation="CenterOwner" ResizeMode="NoResize"
        WindowStyle="ToolWindow" Topmost="True">
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="20"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="20"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <TextBlock Name="MessageText" Grid.Row="0" Text="$Message"
                   TextWrapping="Wrap" HorizontalAlignment="Center"/>

        <ProgressBar Name="ProgressBar" Grid.Row="2" Height="20"
                     IsIndeterminate="True"/>

        <StackPanel Grid.Row="4" Orientation="Horizontal"
                    HorizontalAlignment="Center">
            <Button Name="CancelButton" Content="Cancel" Width="80" Height="25"
                    Visibility="$(if ($ShowCancelButton) { 'Visible' } else { 'Collapsed' })"/>
        </StackPanel>
    </Grid>
</Window>
"@

    try {
        $reader = [System.IO.StringReader]::new($xaml)
        $xmlReader = [System.Xml.XmlReader]::Create($reader)
        $dialog = [Windows.Markup.XamlReader]::Load($xmlReader)

        $controls = @{}
        $controls.MessageText = $dialog.FindName("MessageText")
        $controls.ProgressBar = $dialog.FindName("ProgressBar")
        $controls.CancelButton = $dialog.FindName("CancelButton")

        return @{
            Dialog = $dialog
            Controls = $controls
            IsCancelled = $false
        }
    } catch {
        Write-AppLog "Error creating progress dialog: $($_.Exception.Message)" -Level 'ERROR'
        return $null
    }
}

# Show Progress Dialog
function Show-ProgressDialog {
    param(
        [string]$Title = "Operation in Progress",
        [string]$Message = "Please wait...",
        [bool]$ShowCancelButton = $true,
        [object]$Owner = $null
    )

    $progressDialog = New-ProgressDialog -Title $Title -Message $Message -ShowCancelButton $ShowCancelButton
    if (-not $progressDialog) { return $null }

    # Set owner if provided
    if ($Owner) {
        $progressDialog.Dialog.Owner = $Owner
    }

    # Add cancel button handler
    if ($ShowCancelButton -and $progressDialog.Controls.CancelButton) {
        $progressDialog.Controls.CancelButton.Add_Click({
            $progressDialog.IsCancelled = $true
            $progressDialog.Dialog.DialogResult = $false
            $progressDialog.Dialog.Close()
        })
    }

    return $progressDialog
}

# Update Progress Dialog
function Update-ProgressDialog {
    param(
        [object]$ProgressDialog,
        [string]$Message = $null,
        [int]$PercentComplete = -1
    )

    if (-not $ProgressDialog -or -not $ProgressDialog.Dialog) { return }

    try {
        $ProgressDialog.Dialog.Dispatcher.Invoke([Action]{
            if ($Message -and $ProgressDialog.Controls.MessageText) {
                $ProgressDialog.Controls.MessageText.Text = $Message
            }

            if ($PercentComplete -ge 0 -and $ProgressDialog.Controls.ProgressBar) {
                $ProgressDialog.Controls.ProgressBar.IsIndeterminate = $false
                $ProgressDialog.Controls.ProgressBar.Value = $PercentComplete
            }
        })
    } catch {
        Write-AppLog "Error updating progress dialog: $($_.Exception.Message)" -Level 'DEBUG'
    }
}

# Close Progress Dialog
function Close-ProgressDialog {
    param([object]$ProgressDialog)

    if (-not $ProgressDialog -or -not $ProgressDialog.Dialog) { return }

    try {
        $ProgressDialog.Dialog.Dispatcher.Invoke([Action]{
            $ProgressDialog.Dialog.Close()
        })
    } catch {
        Write-AppLog "Error closing progress dialog: $($_.Exception.Message)" -Level 'DEBUG'
    }
}

# Async Operation with Progress
function Invoke-AsyncOperation {
    param(
        [scriptblock]$Operation,
        [string]$OperationName = "Operation",
        [bool]$ShowProgress = $true,
        [bool]$AllowCancel = $true,
        [object]$Owner = $null
    )

    $operationId = [System.Guid]::NewGuid().ToString()
    $script:CurrentOperations[$operationId] = @{
        Name = $OperationName
        StartTime = Get-Date
        IsCancelled = $false
        Progress = 0
    }

    $progressDialog = $null
    if ($ShowProgress) {
        $progressDialog = Show-ProgressDialog -Title $OperationName -Message "Starting $OperationName..." -ShowCancelButton $AllowCancel -Owner $Owner
    }

    try {
        # Create a runspace for the async operation
        $runspace = [runspacefactory]::CreateRunspace()
        $runspace.Open()

        # Create PowerShell instance
        $powershell = [powershell]::Create()
        $powershell.Runspace = $runspace

        # Add the operation script
        $powershell.AddScript($Operation) | Out-Null

        # Start the async operation
        $asyncResult = $powershell.BeginInvoke()

        # Monitor progress and cancellation
        while (-not $asyncResult.IsCompleted) {
            Start-Sleep -Milliseconds 100

            # Check for cancellation
            if ($progressDialog -and $progressDialog.IsCancelled) {
                $script:CurrentOperations[$operationId].IsCancelled = $true
                $powershell.Stop()
                break
            }

            # Update progress if available
            if ($progressDialog) {
                $elapsed = ((Get-Date) - $script:CurrentOperations[$operationId].StartTime).TotalSeconds
                Update-ProgressDialog -ProgressDialog $progressDialog -Message "$OperationName... ($([math]::Round($elapsed, 1))s elapsed)"
            }
        }

        # Get results
        $result = $null
        if (-not $script:CurrentOperations[$operationId].IsCancelled) {
            $result = $powershell.EndInvoke($asyncResult)
        }

        return @{
            Success = -not $script:CurrentOperations[$operationId].IsCancelled
            Result = $result
            IsCancelled = $script:CurrentOperations[$operationId].IsCancelled
            Duration = ((Get-Date) - $script:CurrentOperations[$operationId].StartTime).TotalSeconds
        }

    } catch {
        $appError = New-AppError -Message "Async operation failed: $($_.Exception.Message)" -Category $script:ErrorCategory.System -Exception $_.Exception
        Handle-AppError -AppError $appError

        return @{
            Success = $false
            Result = $null
            Error = $appError
            IsCancelled = $false
            Duration = ((Get-Date) - $script:CurrentOperations[$operationId].StartTime).TotalSeconds
        }
    } finally {
        # Cleanup
        if ($progressDialog) {
            Close-ProgressDialog -ProgressDialog $progressDialog
        }

        $script:CurrentOperations.Remove($operationId)

        if ($powershell) {
            $powershell.Dispose()
        }
        if ($runspace) {
            $runspace.Close()
            $runspace.Dispose()
        }
    }
}

###############################################################################
#                         SECURITY & COMPLIANCE                               #
###############################################################################

#region Security and Compliance Enhancements

# Security Manager Class
function New-SecurityManager {
    return @{
        AuditLog = @()
        CurrentUser = [System.Security.Principal.WindowsIdentity]::GetCurrent().Name
        SessionId = [System.Guid]::NewGuid().ToString()
        StartTime = Get-Date
        Permissions = @{
            CanCreateUsers = $false
            CanModifyUsers = $false
            CanViewAuditLog = $false
            CanAccessAllOUs = $false
        }
    }
}

# Initialize Security Manager
$script:SecurityManager = New-SecurityManager

# Audit Logging Function
function Write-AuditLog {
    param(
        [string]$Action,
        [string]$Target = "",
        [string]$Details = "",
        [string]$Result = "Success",
        [hashtable]$AdditionalData = @{}
    )

    if (-not $script:Config.Security.AuditLogging) { return }

    $auditEntry = @{
        Timestamp = Get-Date
        SessionId = $script:SecurityManager.SessionId
        User = $script:SecurityManager.CurrentUser
        Action = $Action
        Target = $Target
        Details = $Details
        Result = $Result
        AdditionalData = $AdditionalData
        Id = [System.Guid]::NewGuid().ToString()
    }

    $script:SecurityManager.AuditLog += $auditEntry

    # Log to file if configured
    $logMessage = "[$($auditEntry.Timestamp.ToString('yyyy-MM-dd HH:mm:ss'))] [$($auditEntry.User)] [$($auditEntry.Action)] Target: $Target | Result: $Result | Details: $Details"
    Write-AppLog $logMessage -Level 'INFO'

    # Optional: Write to Windows Event Log
    try {
        if ($script:Config.Security.WriteToEventLog) {
            Write-EventLog -LogName Application -Source "OnboardingFromJiraGUI" -EventId 1001 -EntryType Information -Message $logMessage
        }
    } catch {
        Write-AppLog "Could not write to Event Log: $($_.Exception.Message)" -Level 'DEBUG'
    }
}

# Permission Checking
function Test-Permission {
    param(
        [string]$Permission,
        [bool]$ThrowOnDenied = $false
    )

    $hasPermission = $script:SecurityManager.Permissions[$Permission]

    if (-not $hasPermission) {
        $message = "Access denied: User $($script:SecurityManager.CurrentUser) does not have permission: $Permission"
        Write-AuditLog -Action "PermissionCheck" -Target $Permission -Result "Denied" -Details $message

        if ($ThrowOnDenied) {
            $appError = New-AppError -Message $message -Category $script:ErrorCategory.Authentication -Level $script:ErrorLevel.ERROR
            throw $appError
        }
    } else {
        Write-AuditLog -Action "PermissionCheck" -Target $Permission -Result "Granted"
    }

    return $hasPermission
}

# Initialize User Permissions
function Initialize-UserPermissions {
    try {
        $currentUser = [System.Security.Principal.WindowsIdentity]::GetCurrent()
        $principal = [System.Security.Principal.WindowsPrincipal]::new($currentUser)

        # Check if user is in specific groups or has specific rights
        # This is a simplified example - in production, you'd check against AD groups

        # Check for admin privileges
        $isAdmin = $principal.IsInRole([System.Security.Principal.WindowsBuiltInRole]::Administrator)

        if ($isAdmin) {
            $script:SecurityManager.Permissions.CanCreateUsers = $true
            $script:SecurityManager.Permissions.CanModifyUsers = $true
            $script:SecurityManager.Permissions.CanViewAuditLog = $true
            $script:SecurityManager.Permissions.CanAccessAllOUs = $true
            Write-AuditLog -Action "PermissionInit" -Result "Success" -Details "Admin privileges detected"
        } else {
            # Check for specific group memberships (example)
            # In production, replace with actual AD group checks
            $script:SecurityManager.Permissions.CanCreateUsers = $true  # Default for this app
            Write-AuditLog -Action "PermissionInit" -Result "Success" -Details "Standard user privileges"
        }

        Write-AppLog "$($script:Icons.Success) User permissions initialized for $($script:SecurityManager.CurrentUser)" -Level 'INFO'

    } catch {
        $appError = New-AppError -Message "Failed to initialize user permissions: $($_.Exception.Message)" -Category $script:ErrorCategory.Authentication -Exception $_.Exception
        Handle-AppError -AppError $appError
    }
}

# Secure Data Handling
function Protect-SensitiveData {
    param(
        [string]$Data,
        [string]$Purpose = "General"
    )

    try {
        # Simple obfuscation for logging purposes
        if ($Data.Length -le 4) {
            return "***"
        } else {
            return $Data.Substring(0, 2) + "*".PadRight($Data.Length - 4, '*') + $Data.Substring($Data.Length - 2)
        }
    } catch {
        return "***"
    }
}

# Session Security Validation
function Test-SessionSecurity {
    $issues = @()

    # Check session age
    $sessionAge = ((Get-Date) - $script:SecurityManager.StartTime).TotalMinutes
    if ($sessionAge -gt $script:Config.Security.SessionTimeoutMinutes) {
        $issues += "Session has expired"
    }

    # Check for suspicious activity (example)
    $recentFailures = $script:SecurityManager.AuditLog | Where-Object {
        $_.Result -eq "Failed" -and
        ((Get-Date) - $_.Timestamp).TotalMinutes -lt 5
    }

    if ($recentFailures.Count -gt 3) {
        $issues += "Multiple recent failures detected"
    }

    return @{
        IsSecure = $issues.Count -eq 0
        Issues = $issues
        SessionAge = $sessionAge
        RecentFailures = $recentFailures.Count
    }
}

# Data Sanitization
function Invoke-DataSanitization {
    param(
        [hashtable]$Data
    )

    $sanitized = @{}

    foreach ($key in $Data.Keys) {
        $value = $Data[$key]

        if ($value -is [string]) {
            # Remove potentially dangerous characters
            $sanitized[$key] = $value -replace '[<>"\''&]', ''

            # Trim whitespace
            $sanitized[$key] = $sanitized[$key].Trim()

            # Limit length
            if ($sanitized[$key].Length -gt 255) {
                $sanitized[$key] = $sanitized[$key].Substring(0, 255)
            }
        } else {
            $sanitized[$key] = $value
        }
    }

    return $sanitized
}

###############################################################################
#                         MONITORING & ALERTING                               #
###############################################################################

#region Monitoring and Alerting System

# Monitoring Manager Class
function New-MonitoringManager {
    return @{
        Metrics = @{
            OperationsCompleted = 0
            OperationsFailed = 0
            AverageOperationTime = 0
            CacheHitRate = 0
            ValidationErrors = 0
            SecurityEvents = 0
        }
        Alerts = @()
        Thresholds = @{
            MaxFailureRate = 0.1  # 10%
            MaxAverageTime = 30   # 30 seconds
            MinCacheHitRate = 0.7 # 70%
            MaxValidationErrors = 5
        }
        LastCheck = Get-Date
    }
}

# Initialize Monitoring Manager
$script:MonitoringManager = New-MonitoringManager

# Record Operation Metric
function Record-OperationMetric {
    param(
        [string]$Operation,
        [bool]$Success,
        [double]$Duration,
        [hashtable]$AdditionalMetrics = @{}
    )

    if ($Success) {
        $script:MonitoringManager.Metrics.OperationsCompleted++
    } else {
        $script:MonitoringManager.Metrics.OperationsFailed++
    }

    # Update average operation time
    $totalOps = $script:MonitoringManager.Metrics.OperationsCompleted + $script:MonitoringManager.Metrics.OperationsFailed
    if ($totalOps -gt 0) {
        $currentAvg = $script:MonitoringManager.Metrics.AverageOperationTime
        $script:MonitoringManager.Metrics.AverageOperationTime = (($currentAvg * ($totalOps - 1)) + $Duration) / $totalOps
    }

    # Check thresholds and generate alerts
    Invoke-ThresholdCheck

    Write-AppLog "$($script:Icons.Chart) Operation metric recorded: $Operation (Success: $Success, Duration: $([math]::Round($Duration, 2))s)" -Level 'DEBUG'
}

# Update Cache Metrics
function Update-CacheMetrics {
    if ($script:PerformanceCache) {
        $stats = Get-CacheStatistics -Cache $script:PerformanceCache
        $script:MonitoringManager.Metrics.CacheHitRate = $stats.HitRate / 100
    }
}

# Record Validation Error
function Record-ValidationError {
    param([string]$FieldName, [string]$Error)

    $script:MonitoringManager.Metrics.ValidationErrors++
    Write-AppLog "$($script:Icons.Warning) Validation error recorded: $FieldName - $Error" -Level 'DEBUG'

    Invoke-ThresholdCheck
}

# Record Security Event
function Record-SecurityEvent {
    param([string]$EventType, [string]$Details)

    $script:MonitoringManager.Metrics.SecurityEvents++
    Write-AuditLog -Action "SecurityEvent" -Target $EventType -Details $Details

    # Security events always trigger immediate threshold check
    Invoke-ThresholdCheck
}

# Threshold Checking
function Invoke-ThresholdCheck {
    $alerts = @()
    $metrics = $script:MonitoringManager.Metrics
    $thresholds = $script:MonitoringManager.Thresholds

    # Check failure rate
    $totalOps = $metrics.OperationsCompleted + $metrics.OperationsFailed
    if ($totalOps -gt 0) {
        $failureRate = $metrics.OperationsFailed / $totalOps
        if ($failureRate -gt $thresholds.MaxFailureRate) {
            $alerts += New-Alert -Type "HighFailureRate" -Message "Operation failure rate is $([math]::Round($failureRate * 100, 1))%" -Severity "Warning"
        }
    }

    # Check average operation time
    if ($metrics.AverageOperationTime -gt $thresholds.MaxAverageTime) {
        $alerts += New-Alert -Type "SlowOperations" -Message "Average operation time is $([math]::Round($metrics.AverageOperationTime, 1))s" -Severity "Warning"
    }

    # Check cache hit rate
    if ($metrics.CacheHitRate -lt $thresholds.MinCacheHitRate -and $totalOps -gt 5) {
        $alerts += New-Alert -Type "LowCacheHitRate" -Message "Cache hit rate is $([math]::Round($metrics.CacheHitRate * 100, 1))%" -Severity "Info"
    }

    # Check validation errors
    if ($metrics.ValidationErrors -gt $thresholds.MaxValidationErrors) {
        $alerts += New-Alert -Type "HighValidationErrors" -Message "Validation errors: $($metrics.ValidationErrors)" -Severity "Warning"
    }

    # Process new alerts
    foreach ($alert in $alerts) {
        if (-not ($script:MonitoringManager.Alerts | Where-Object { $_.Type -eq $alert.Type -and ((Get-Date) - $_.Timestamp).TotalMinutes -lt 5 })) {
            $script:MonitoringManager.Alerts += $alert
            Show-Alert -Alert $alert
        }
    }
}

# Create Alert
function New-Alert {
    param(
        [string]$Type,
        [string]$Message,
        [string]$Severity = "Info"
    )

    return @{
        Type = $Type
        Message = $Message
        Severity = $Severity
        Timestamp = Get-Date
        Id = [System.Guid]::NewGuid().ToString()
    }
}

# Show Alert
function Show-Alert {
    param([object]$Alert)

    $icon = switch ($Alert.Severity) {
        "Critical" { $script:Icons.Error }
        "Warning" { $script:Icons.Warning }
        "Info" { $script:Icons.Info }
        default { $script:Icons.Info }
    }

    $message = "$icon Alert: $($Alert.Message)"
    Write-AppLog $message -Level $Alert.Severity.ToUpper()

    # Update UI if available
    if ($controls.StatusBar) {
        Update-Status $Alert.Message $Alert.Severity.ToUpper()
    }

    # Optional: Show popup for critical alerts
    if ($Alert.Severity -eq "Critical" -and $script:Config.UI.ShowCriticalAlerts) {
        [System.Windows.MessageBox]::Show($Alert.Message, "Critical Alert", [System.Windows.MessageBoxButton]::OK, [System.Windows.MessageBoxImage]::Warning)
    }
}

# Get Monitoring Report
function Get-MonitoringReport {
    $metrics = $script:MonitoringManager.Metrics
    $totalOps = $metrics.OperationsCompleted + $metrics.OperationsFailed
    $failureRate = if ($totalOps -gt 0) { ($metrics.OperationsFailed / $totalOps) * 100 } else { 0 }

    return @{
        TotalOperations = $totalOps
        SuccessfulOperations = $metrics.OperationsCompleted
        FailedOperations = $metrics.OperationsFailed
        FailureRate = [math]::Round($failureRate, 2)
        AverageOperationTime = [math]::Round($metrics.AverageOperationTime, 2)
        CacheHitRate = [math]::Round($metrics.CacheHitRate * 100, 2)
        ValidationErrors = $metrics.ValidationErrors
        SecurityEvents = $metrics.SecurityEvents
        ActiveAlerts = ($script:MonitoringManager.Alerts | Where-Object { ((Get-Date) - $_.Timestamp).TotalHours -lt 1 }).Count
        LastCheck = $script:MonitoringManager.LastCheck
    }
}

# Initialize monitoring
function Initialize-Monitoring {
    Write-AppLog "$($script:Icons.Success) Monitoring system initialized" -Level 'INFO'

    # Initialize user permissions
    Initialize-UserPermissions

    # Start periodic cache cleanup if configured
    if ($script:Config.Performance.CacheCleanupIntervalMinutes -gt 0) {
        # Note: In a real implementation, you'd set up a timer here
        Write-AppLog "$($script:Icons.Timer) Cache cleanup scheduled every $($script:Config.Performance.CacheCleanupIntervalMinutes) minutes" -Level 'DEBUG'
    }
}

###############################################################################
#                         ENHANCED ASYNC OPERATIONS                           #
###############################################################################

#region Enhanced Asynchronous Operations

# Async Jira Operations
function Invoke-AsyncJiraOperation {
    param(
        [scriptblock]$JiraOperation,
        [string]$OperationName = "Jira Operation",
        [hashtable]$Parameters = @{},
        [object]$Owner = $null
    )

    $asyncOperation = {
        param($Operation, $Params)

        try {
            # Execute the Jira operation with parameters
            $result = & $Operation @Params
            return @{
                Success = $true
                Data = $result
                Error = $null
            }
        } catch {
            return @{
                Success = $false
                Data = $null
                Error = $_.Exception.Message
            }
        }
    }

    return Invoke-AsyncOperation -Operation { & $asyncOperation $JiraOperation $Parameters } -OperationName $OperationName -Owner $Owner
}

# Async Active Directory Operations
function Invoke-AsyncADOperation {
    param(
        [scriptblock]$ADOperation,
        [string]$OperationName = "Active Directory Operation",
        [hashtable]$Parameters = @{},
        [object]$Owner = $null
    )

    # Check permissions first
    if (-not (Test-Permission -Permission "CanCreateUsers")) {
        return @{
            Success = $false
            Result = $null
            Error = "Insufficient permissions for Active Directory operations"
            IsCancelled = $false
            Duration = 0
        }
    }

    $asyncOperation = {
        param($Operation, $Params)

        try {
            # Record security event
            Write-AuditLog -Action "ADOperation" -Target $Params.SamAccountName -Details "Starting AD operation"

            # Execute the AD operation with parameters
            $result = & $Operation @Params

            Write-AuditLog -Action "ADOperation" -Target $Params.SamAccountName -Details "AD operation completed successfully" -Result "Success"

            return @{
                Success = $true
                Data = $result
                Error = $null
            }
        } catch {
            Write-AuditLog -Action "ADOperation" -Target $Params.SamAccountName -Details "AD operation failed: $($_.Exception.Message)" -Result "Failed"

            return @{
                Success = $false
                Data = $null
                Error = $_.Exception.Message
            }
        }
    }

    return Invoke-AsyncOperation -Operation { & $asyncOperation $ADOperation $Parameters } -OperationName $OperationName -Owner $Owner
}

# Async Form Validation
function Invoke-AsyncFormValidation {
    param(
        [hashtable]$FormData,
        [object]$Owner = $null
    )

    $validationOperation = {
        param($Data)

        try {
            # Perform comprehensive validation
            $validationResults = Invoke-FormValidation -FormData $Data

            # Additional async validations (e.g., checking AD for existing users)
            if ($Data.SamAccountName) {
                try {
                    $existingUser = Get-ADUser -Filter "SamAccountName -eq '$($Data.SamAccountName)'" -ErrorAction SilentlyContinue
                    if ($existingUser) {
                        $validationResults.FieldResults.SamAccountName.IsValid = $false
                        $validationResults.FieldResults.SamAccountName.Errors += "User with this SAM account name already exists"
                        $validationResults.IsValid = $false
                        $validationResults.ErrorCount++
                    }
                } catch {
                    # If AD check fails, log but don't fail validation
                    Write-AppLog "Could not check AD for existing user: $($_.Exception.Message)" -Level 'DEBUG'
                }
            }

            return @{
                Success = $true
                Data = $validationResults
                Error = $null
            }
        } catch {
            return @{
                Success = $false
                Data = $null
                Error = $_.Exception.Message
            }
        }
    }

    return Invoke-AsyncOperation -Operation { & $validationOperation $FormData } -OperationName "Form Validation" -ShowProgress $false -Owner $Owner
}

# Async Cache Operations
function Invoke-AsyncCacheCleanup {
    param([object]$Owner = $null)

    $cleanupOperation = {
        try {
            # Clean up performance cache
            if ($script:PerformanceCache) {
                Clear-CacheExpired -Cache $script:PerformanceCache
            }

            # Clean up legacy cache
            $expiredKeys = @()
            $now = Get-Date

            foreach ($key in $script:Cache.Keys) {
                if ($script:Cache[$key].Timestamp) {
                    $age = ($now - $script:Cache[$key].Timestamp).TotalMinutes
                    if ($age -gt $script:Config.CacheExpiryMinutes) {
                        $expiredKeys += $key
                    }
                }
            }

            foreach ($key in $expiredKeys) {
                if ($script:Cache -is [System.Collections.Concurrent.ConcurrentDictionary[string, object]]) {
                    $script:Cache.TryRemove($key, [ref]$null) | Out-Null
                } else {
                    $script:Cache.Remove($key)
                }
            }

            return @{
                Success = $true
                Data = @{
                    ExpiredKeysRemoved = $expiredKeys.Count
                    CacheSize = $script:Cache.Count
                }
                Error = $null
            }
        } catch {
            return @{
                Success = $false
                Data = $null
                Error = $_.Exception.Message
            }
        }
    }

    return Invoke-AsyncOperation -Operation $cleanupOperation -OperationName "Cache Cleanup" -ShowProgress $false -Owner $Owner
}

# Async Operation Queue Manager
function New-AsyncOperationQueue {
    return @{
        Operations = @()
        MaxConcurrent = $script:Config.Performance.MaxConcurrentOperations
        Running = @()
        Completed = @()
        Failed = @()
    }
}

function Add-AsyncOperationToQueue {
    param(
        [object]$Queue,
        [scriptblock]$Operation,
        [string]$OperationName,
        [hashtable]$Parameters = @{},
        [int]$Priority = 5
    )

    $operationItem = @{
        Id = [System.Guid]::NewGuid().ToString()
        Operation = $Operation
        Name = $OperationName
        Parameters = $Parameters
        Priority = $Priority
        QueuedAt = Get-Date
        Status = "Queued"
    }

    $Queue.Operations += $operationItem

    # Sort by priority (lower number = higher priority)
    $Queue.Operations = $Queue.Operations | Sort-Object Priority

    return $operationItem.Id
}

function Start-AsyncOperationQueue {
    param([object]$Queue)

    while ($Queue.Operations.Count -gt 0 -or $Queue.Running.Count -gt 0) {
        # Start new operations if we have capacity
        while ($Queue.Running.Count -lt $Queue.MaxConcurrent -and $Queue.Operations.Count -gt 0) {
            $nextOperation = $Queue.Operations[0]
            $Queue.Operations = $Queue.Operations[1..($Queue.Operations.Count - 1)]

            $nextOperation.Status = "Running"
            $nextOperation.StartedAt = Get-Date
            $Queue.Running += $nextOperation

            # Start the operation asynchronously
            $asyncResult = Invoke-AsyncOperation -Operation $nextOperation.Operation -OperationName $nextOperation.Name -ShowProgress $false
            $nextOperation.AsyncResult = $asyncResult
        }

        # Check for completed operations
        $completedOperations = @()
        foreach ($runningOp in $Queue.Running) {
            if ($runningOp.AsyncResult -and $runningOp.AsyncResult.IsCompleted) {
                $runningOp.Status = if ($runningOp.AsyncResult.Success) { "Completed" } else { "Failed" }
                $runningOp.CompletedAt = Get-Date
                $runningOp.Result = $runningOp.AsyncResult

                $completedOperations += $runningOp

                if ($runningOp.AsyncResult.Success) {
                    $Queue.Completed += $runningOp
                } else {
                    $Queue.Failed += $runningOp
                }
            }
        }

        # Remove completed operations from running list
        foreach ($completedOp in $completedOperations) {
            $Queue.Running = $Queue.Running | Where-Object { $_.Id -ne $completedOp.Id }
        }

        Start-Sleep -Milliseconds 100
    }
}

#endregion

# Load required assemblies for WPF with PowerShell 5.x compatibility
try {
    Add-Type -AssemblyName PresentationCore
    Add-Type -AssemblyName PresentationFramework  
    Add-Type -AssemblyName System.Windows.Forms
    Add-Type -AssemblyName WindowsBase
    Write-Host "Successfully loaded WPF assemblies" -ForegroundColor Green
} catch {
    Write-Host "Failed to load WPF assemblies: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Please ensure .NET Framework 4.5+ is installed" -ForegroundColor Yellow
    exit 1
}

# Check for Active Directory module and set simulation mode
$script:SimulationMode = $false
if (-not (Get-Module -ListAvailable -Name ActiveDirectory)) {
    $script:SimulationMode = $true
    Write-Host "Active Directory module not found. Running in SIMULATION MODE." -ForegroundColor Yellow
}

# Function to ensure NuGet provider is installed
function Install-NuGetProviderSilently {
    try {
        Write-Host "Checking NuGet provider..." -ForegroundColor Cyan
        $nugetProvider = Get-PackageProvider -Name NuGet -ErrorAction SilentlyContinue
        
        if (-not $nugetProvider -or $nugetProvider.Version -lt [Version]"*********") {
            Write-Host "Installing NuGet provider silently..." -ForegroundColor Yellow
            
            # Set TLS 1.2 for secure downloads
            [Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12
            
            # Install NuGet provider without prompts
            Install-PackageProvider -Name NuGet -MinimumVersion ********* -Force -Scope CurrentUser -Confirm:$false | Out-Null
            Write-Host "NuGet provider installed successfully" -ForegroundColor Green
        } else {
            Write-Host "NuGet provider already available" -ForegroundColor Green
        }
    } catch {
        Write-Host "Warning: Could not install NuGet provider automatically: $($_.Exception.Message)" -ForegroundColor Yellow
        Write-Host "Continuing with module installation..." -ForegroundColor Yellow
    }
}

# Function to set PowerShell Gallery as trusted repository
function Set-PSGalleryTrusted {
    try {
        $psGallery = Get-PSRepository -Name PSGallery -ErrorAction SilentlyContinue
        if ($psGallery -and $psGallery.InstallationPolicy -ne 'Trusted') {
            Write-Host "Setting PowerShell Gallery as trusted repository..." -ForegroundColor Cyan
            Set-PSRepository -Name PSGallery -InstallationPolicy Trusted -Confirm:$false
            Write-Host "PowerShell Gallery set as trusted" -ForegroundColor Green
        }
    } catch {
        Write-Host "Warning: Could not set PSGallery as trusted: $($_.Exception.Message)" -ForegroundColor Yellow
    }
}

# Ensure prerequisites are in place
Install-NuGetProviderSilently
Set-PSGalleryTrusted

# Import required modules with error handling and automatic updates
$requiredModules = @('JiraPS')
foreach ($module in $requiredModules) {
    try {
        Write-Host "Checking module: $module" -ForegroundColor Cyan
        
        if (-not (Get-Module -ListAvailable -Name $module)) {
            Write-Host "Installing missing module: $module. This may take a moment..." -ForegroundColor Yellow
            
            # Set TLS 1.2 for secure downloads
            [Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12
            
            try {
                # Install module with all prompts suppressed
                Install-Module -Name $module -Scope CurrentUser -Force -AllowClobber -Confirm:$false -SkipPublisherCheck -AcceptLicense -ErrorAction Stop
                Write-Host "Module $module installed successfully" -ForegroundColor Green
            } catch {
                Write-Host "First attempt failed. Trying alternative installation method..." -ForegroundColor Yellow
                
                # Alternative method: Install without publisher check and license acceptance
                try {
                    Install-Module -Name $module -Scope CurrentUser -Force -AllowClobber -Confirm:$false -ErrorAction Stop
                    Write-Host "Module $module installed successfully (alternative method)" -ForegroundColor Green
                } catch {
                    # Final fallback: Try with minimal parameters
                    Install-Module -Name $module -Force -ErrorAction Stop
                    Write-Host "Module $module installed successfully (minimal method)" -ForegroundColor Green
                }
            }
        } else {
            Write-Host "Module $module found. Checking for updates..." -ForegroundColor Yellow
            try {
                Update-Module -Name $module -Force -Confirm:$false -AcceptLicense -ErrorAction SilentlyContinue
                Write-Host "Module $module updated successfully" -ForegroundColor Green
            } catch {
                Write-Host "Update check failed, continuing with existing version" -ForegroundColor Yellow
            }
        }
        
        Import-Module $module -Force -ErrorAction Stop
        Write-Host "Successfully loaded module: $module" -ForegroundColor Green
    }
    catch {
        $errorMsg = "Failed to load or update required module '$module': $($_.Exception.Message). Please ensure you have an internet connection."
        Write-Host $errorMsg -ForegroundColor Red
        
        # Try one more time with basic installation
        try {
            Write-Host "Attempting final fallback installation..." -ForegroundColor Yellow
            Install-Module -Name $module -Confirm:$false -Force
            Import-Module $module -Force
            Write-Host "Successfully loaded module: $module (fallback method)" -ForegroundColor Green
        } catch {
            Write-Host "All installation methods failed for module: $module" -ForegroundColor Red
            try {
                [System.Windows.MessageBox]::Show($errorMsg, "Module Error", "OK", "Error")
            } catch {
                # MessageBox might not be available yet
                Write-Host "Press any key to exit..." -ForegroundColor Red
                $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
            }
            exit 1
        }
    }
}

#endregion

###############################################################################
#                         CONFIGURATION & STATE                               #
###############################################################################

#region Configuration

$script:Config = @{
    MaxCacheSize = 50
    CacheExpiryMinutes = 60
    LogLevel = 'DEBUG' # Set to 'INFO' for less verbose logging
    Security = @{
        MaxLoginAttempts = 3
        SessionTimeoutMinutes = 120
        RequireHttps = $true
        AuditLogging = $true
    }
    Performance = @{
        EnableCaching = $true
        EnableStatistics = $true
        CacheCleanupIntervalMinutes = 30
        MaxConcurrentOperations = 5
    }
    Validation = @{
        EnableRealTimeValidation = $true
        ShowSuggestions = $true
        HighlightErrors = $true
    }
    UI = @{
        ShowProgressIndicators = $true
        EnableCancellation = $true
        AutoRefreshInterval = 30
        ThemeMode = "Auto" # Light, Dark, Auto
    }
}

# Enhanced State Variables - Initialize with new caching system
$script:PerformanceCache = New-PerformanceCache -MaxSize $script:Config.MaxCacheSize -ExpiryMinutes $script:Config.CacheExpiryMinutes -EnableStatistics $script:Config.Performance.EnableStatistics

# Legacy cache for backward compatibility
try {
    $script:Cache = New-Object 'System.Collections.Concurrent.ConcurrentDictionary[string, object]'
    Write-AppLog "$($script:Icons.Success) Using ConcurrentDictionary for legacy cache" -Level 'DEBUG'
} catch {
    Write-AppLog "$($script:Icons.Warning) ConcurrentDictionary not available, using Hashtable for legacy cache" -Level 'WARN'
    $script:Cache = @{}
}
$script:JiraCredential = $null
$script:LoginAttempts = 0
$script:SessionStartTime = $null

# Enhanced form data tracking for validation
$script:CurrentFormData = @{
    FirstName = ""
    LastName = ""
    JobTitle = ""
    Department = ""
    ModelAccount = ""
    OfficeLocation = ""
    EmployeeType = ""
    EffectiveDate = ""
}

# Validation state tracking
$script:ValidationState = New-FieldValidator

# Operation tracking for progress and cancellation
$script:CurrentOperations = @{}

# Enhanced UI State Variables
$script:WorkflowSteps = @{
    Connection = @{ Index = 1; Name = "Connect to Jira"; Status = "Pending" }
    FetchData = @{ Index = 2; Name = "Fetch Ticket Data"; Status = "Pending" }
    Validation = @{ Index = 3; Name = "Validate Information"; Status = "Pending" }
    CreateUser = @{ Index = 4; Name = "Create AD User"; Status = "Pending" }
}

$script:SessionStats = @{
    UsersCreated = 0
    ErrorsOccurred = 0
    TicketsFetched = 0
    ValidationErrors = 0
    StartTime = Get-Date
}

$script:ThemeColors = @{
    Primary = "#FF0078D4"
    Secondary = "#FF106EBE"
    Success = "#FF107C10"
    Warning = "#FFFF8C00"
    Error = "#FFD13438"
    Background = "#FFF3F2F1"
    Surface = "#FFFFFFFF"
    OnSurface = "#FF323130"
    Border = "#FFE1DFDD"
}

$script:LogEntries = New-Object System.Collections.ArrayList
$script:LogFilters = @{
    Level = "All"
    SearchText = ""
    ShowDebug = $true
}

#endregion

###############################################################################
#                                GUI (XAML)                                   #
###############################################################################

#region XAML Definition

$windowTitle = "OnboardingFromJiraGUI v3.0"
if ($script:SimulationMode) { $windowTitle += " - SIMULATION MODE" }

$warningVisibility = if ($script:SimulationMode) { 'Visible' } else { 'Collapsed' }
$warningText = "$($script:Icons.Warning) SIMULATION MODE ACTIVE"

$script:xaml = @"
<Window xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="$windowTitle" Height="800" Width="1000" MinHeight="700" MinWidth="800" WindowStartupLocation="CenterScreen" Background="#FFF3F2F1">
    <Window.Resources>
        <Style x:Key="ModernButton" TargetType="Button">
            <Setter Property="Background" Value="#FF0078D4"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="12,8"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Cursor" Value="Hand"/>
        </Style>
        <Style x:Key="ToolbarButton" TargetType="Button">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="BorderBrush" Value="#FFE1DFDD"/>
            <Setter Property="Padding" Value="8,4"/>
            <Setter Property="Margin" Value="2"/>
            <Setter Property="Cursor" Value="Hand"/>
        </Style>
    </Window.Resources>
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- Progress Indicator -->
        <Border Grid.Row="0" Background="#FFFFFFFF" BorderBrush="#FFE1DFDD" BorderThickness="0,0,0,1" Padding="10,8">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Ellipse Name="Step1Indicator" Width="24" Height="24" Fill="#FFCCCCCC" Margin="5"/>
                <Rectangle Width="40" Height="2" Fill="#FFCCCCCC" VerticalAlignment="Center"/>
                <Ellipse Name="Step2Indicator" Width="24" Height="24" Fill="#FFCCCCCC" Margin="5"/>
                <Rectangle Width="40" Height="2" Fill="#FFCCCCCC" VerticalAlignment="Center"/>
                <Ellipse Name="Step3Indicator" Width="24" Height="24" Fill="#FFCCCCCC" Margin="5"/>
                <Rectangle Width="40" Height="2" Fill="#FFCCCCCC" VerticalAlignment="Center"/>
                <Ellipse Name="Step4Indicator" Width="24" Height="24" Fill="#FFCCCCCC" Margin="5"/>
            </StackPanel>
        </Border>
        
        <!-- Quick Actions Toolbar -->
        <Border Grid.Row="1" Background="#FFF8F8F8" BorderBrush="#FFE1DFDD" BorderThickness="0,0,0,1" Padding="5">
            <StackPanel Orientation="Horizontal">
                <Button Name="RefreshButton" Content="Refresh" Style="{StaticResource ToolbarButton}" ToolTip="Refresh Jira connection"/>
                <Button Name="ClearFormButton" Content="Clear Form" Style="{StaticResource ToolbarButton}" ToolTip="Clear all fields"/>
                <Rectangle Width="1" Height="20" Fill="#FFE1DFDD" Margin="5,0"/>
                <Button Name="ValidateButton" Content="Validate" Style="{StaticResource ToolbarButton}" ToolTip="Validate current data"/>
                <Button Name="PreviewButton" Content="Preview" Style="{StaticResource ToolbarButton}" ToolTip="Preview user creation"/>
                <Rectangle Width="1" Height="20" Fill="#FFE1DFDD" Margin="5,0"/>
                <Button Name="HelpButton" Content="Help" Style="{StaticResource ToolbarButton}" ToolTip="Show help information"/>
            </StackPanel>
        </Border>
        
        <TabControl Grid.Row="2">
            <TabItem Header="Onboarding">
                <Grid Margin="10">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/><RowDefinition Height="Auto"/><RowDefinition Height="Auto"/><RowDefinition Height="Auto"/><RowDefinition Height="Auto"/><RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <Border Grid.Row="0" Background="#FFFFE4B5" BorderBrush="#FFFF8C00" BorderThickness="2" Padding="8" Margin="0,0,0,10" Visibility="$warningVisibility">
                        <TextBlock Text="$warningText - Active Directory module not found. All AD operations will be simulated." TextWrapping="Wrap" HorizontalAlignment="Center"/>
                    </Border>

                    <GroupBox Header="$($script:Icons.Connection) Jira Connection" Grid.Row="1" FontWeight="Bold" Margin="0,0,0,5">
                        <Grid Margin="5">
                            <Grid.ColumnDefinitions><ColumnDefinition Width="150"/><ColumnDefinition Width="*"/><ColumnDefinition Width="Auto"/></Grid.ColumnDefinitions>
                            <Grid.RowDefinitions><RowDefinition/><RowDefinition/><RowDefinition/></Grid.RowDefinitions>
                            <Label Content="Jira URL:" Grid.Row="0" Grid.Column="0" VerticalAlignment="Center"/>
                            <TextBox Name="JiraUrlBox" Grid.Row="0" Grid.Column="1" VerticalAlignment="Center" Text="https://jeragm.atlassian.net"/>
                            <Label Content="Username:" Grid.Row="1" Grid.Column="0" VerticalAlignment="Center"/>
                            <TextBox Name="JiraUserBox" Grid.Row="1" Grid.Column="1" VerticalAlignment="Center"/>
                            <Label Content="API Token:" Grid.Row="2" Grid.Column="0" VerticalAlignment="Center"/>
                            <PasswordBox Name="JiraTokenBox" Grid.Row="2" Grid.Column="1" VerticalAlignment="Center"/>
                            <StackPanel Grid.Row="0" Grid.Column="2" Grid.RowSpan="3" Margin="10,0,0,0">
                                <Button Name="ConnectButton" Content="$($script:Icons.Login) Connect" Padding="10,5"/>
                            </StackPanel>
                        </Grid>
                    </GroupBox>

                    <GroupBox Header="$($script:Icons.Ticket) Onboarding Ticket" Grid.Row="2" FontWeight="Bold" Margin="0,5,0,5">
                         <Grid Margin="5">
                            <Grid.ColumnDefinitions><ColumnDefinition Width="150"/><ColumnDefinition Width="*"/><ColumnDefinition Width="Auto"/></Grid.ColumnDefinitions>
                            <Label Content="Jira Ticket ID:" VerticalAlignment="Center"/>
                            <TextBox Name="TicketIdBox" Grid.Column="1" IsEnabled="False" VerticalAlignment="Center"/>
                            <Button Name="FetchButton" Content="Fetch Data" Grid.Column="2" IsEnabled="False" Padding="10,5" Margin="10,0,0,0"/>
                        </Grid>
                    </GroupBox>

                    <GroupBox Header="$($script:Icons.User) User Details (Editable)" Grid.Row="3" FontWeight="Bold" Margin="0,5,0,5">
                        <Grid Margin="5">
                            <Grid.ColumnDefinitions><ColumnDefinition Width="150"/><ColumnDefinition Width="*"/></Grid.ColumnDefinitions>
                            <Grid.RowDefinitions><RowDefinition/><RowDefinition/><RowDefinition/><RowDefinition/><RowDefinition/><RowDefinition/><RowDefinition/></Grid.RowDefinitions>
                            <Label Content="First Name:" Grid.Row="0" Grid.Column="0"/><TextBox Name="FirstNameBox" Grid.Row="0" Grid.Column="1" IsEnabled="False"/>
                            <Label Content="Last Name:" Grid.Row="1" Grid.Column="0"/><TextBox Name="LastNameBox" Grid.Row="1" Grid.Column="1" IsEnabled="False"/>
                            <Label Content="Email Address:" Grid.Row="2" Grid.Column="0"/><TextBox Name="EmailBox" Grid.Row="2" Grid.Column="1" IsEnabled="False"/>
                            <Label Content="UPN:" Grid.Row="3" Grid.Column="0"/><TextBox Name="UpnBox" Grid.Row="3" Grid.Column="1" IsEnabled="False"/>
                            <Label Content="SAM:" Grid.Row="4" Grid.Column="0"/><TextBox Name="SamBox" Grid.Row="4" Grid.Column="1" IsEnabled="False"/>
                            <Label Content="Job Title:" Grid.Row="5" Grid.Column="0"/><TextBox Name="JobTitleBox" Grid.Row="5" Grid.Column="1" IsEnabled="False"/>
                            <Label Content="Department:" Grid.Row="6" Grid.Column="0"/><TextBox Name="DepartmentBox" Grid.Row="6" Grid.Column="1" IsEnabled="False"/>
                        </Grid>
                    </GroupBox>

                    <GroupBox Header="Active Directory Settings" Grid.Row="4" FontWeight="Bold" Margin="0,5,0,5">
                         <Grid Margin="5">
                            <Grid.ColumnDefinitions><ColumnDefinition Width="150"/><ColumnDefinition Width="*"/></Grid.ColumnDefinitions>
                            <Grid.RowDefinitions><RowDefinition/><RowDefinition/><RowDefinition/></Grid.RowDefinitions>
                            <Label Content="OU Location:" Grid.Row="0" Grid.Column="0"/><ComboBox Name="OuComboBox" Grid.Row="0" Grid.Column="1" IsEnabled="False"/>
                            <Label Content="Model Account:" Grid.Row="1" Grid.Column="0"/><TextBox Name="ModelAccountBox" Grid.Row="1" Grid.Column="1" IsEnabled="False"/>
                            <Label Content="Copy Model Groups?: " Grid.Row="2" Grid.Column="0"/>
                            <ComboBox Name="CopyGroupsComboBox" Grid.Row="2" Grid.Column="1" IsEnabled="False">
                                <ComboBoxItem Content="Yes"/><ComboBoxItem Content="No" IsSelected="True"/>
                            </ComboBox>
                        </Grid>
                    </GroupBox>

                    <Button Name="CreateUserButton" Content="Create AD User" Grid.Row="5" HorizontalAlignment="Right" Padding="15,8" FontWeight="Bold" IsEnabled="False" Margin="0,10,0,0"/>
                </Grid>
            </TabItem>
            <TabItem Header="Log">
                <Grid Margin="5">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>
                    
                    <!-- Log controls -->
                    <Border Grid.Row="0" Background="#FFF8F8F8" BorderBrush="#FFE1DFDD" BorderThickness="1" Padding="8" Margin="0,0,0,5">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="Level:" VerticalAlignment="Center" Margin="0,0,5,0" FontWeight="SemiBold"/>
                            <ComboBox Name="LogLevelFilter" Width="80" SelectedIndex="0">
                                <ComboBoxItem Content="All"/>
                                <ComboBoxItem Content="INFO"/>
                                <ComboBoxItem Content="WARN"/>
                                <ComboBoxItem Content="ERROR"/>
                                <ComboBoxItem Content="DEBUG"/>
                            </ComboBox>
                            
                            <TextBlock Text="Search:" VerticalAlignment="Center" Margin="15,0,5,0" FontWeight="SemiBold"/>
                            <TextBox Name="LogSearchBox" Width="150" VerticalAlignment="Center"/>
                            
                            <Button Name="ClearLogButton" Content="Clear Log" Margin="15,0,0,0" Padding="8,4" Style="{StaticResource ToolbarButton}"/>
                            <Button Name="ExportLogButton" Content="Export" Margin="5,0,0,0" Padding="8,4" Style="{StaticResource ToolbarButton}"/>
                        </StackPanel>
                    </Border>
                    
                    <!-- Enhanced log display -->
                    <TextBox Name="LogBox" Grid.Row="1" IsReadOnly="True" VerticalScrollBarVisibility="Auto" 
                             TextWrapping="Wrap" FontFamily="Consolas" Background="#FFFAFAFA" BorderBrush="#FFE1DFDD"/>
                </Grid>
            </TabItem>
        </TabControl>

        <!-- Statistics Dashboard -->
        <Border Grid.Row="3" Background="#FFF8F8F8" BorderBrush="#FFE1DFDD" BorderThickness="0,1,0,0" Padding="10,8">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <TextBlock Text="Users Created: " FontWeight="SemiBold" VerticalAlignment="Center"/>
                    <TextBlock Name="UsersCreatedCount" Text="0" Foreground="#FF107C10" FontWeight="Bold" FontSize="14" VerticalAlignment="Center"/>
                </StackPanel>
                
                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <TextBlock Text="Errors: " FontWeight="SemiBold" VerticalAlignment="Center"/>
                    <TextBlock Name="ErrorsCount" Text="0" Foreground="#FFD13438" FontWeight="Bold" FontSize="14" VerticalAlignment="Center"/>
                </StackPanel>
                
                <StackPanel Grid.Column="2" Orientation="Horizontal">
                    <TextBlock Text="Tickets: " FontWeight="SemiBold" VerticalAlignment="Center"/>
                    <TextBlock Name="TicketsCount" Text="0" Foreground="#FF0078D4" FontWeight="Bold" FontSize="14" VerticalAlignment="Center"/>
                </StackPanel>
                
                <StackPanel Grid.Column="3" Orientation="Horizontal">
                    <TextBlock Text="Runtime: " FontWeight="SemiBold" VerticalAlignment="Center"/>
                    <TextBlock Name="RuntimeDisplay" Text="00:00:00" Foreground="#FF323130" FontWeight="Bold" FontSize="14" VerticalAlignment="Center"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Status Bar -->
        <Border Grid.Row="4" Padding="8,5" Background="#FF2D2D30">
            <Grid>
                <TextBlock Name="StatusText" Text="Ready" VerticalAlignment="Center" Foreground="White"/>
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                    <TextBlock Name="SessionInfo" Text="Not Connected" VerticalAlignment="Center" Foreground="#FFAAAAAA" Margin="0,0,20,0"/>
                    <TextBlock Name="CacheStats" Text="Cache: 0 items" VerticalAlignment="Center" Foreground="#FFAAAAAA"/>
                </StackPanel>
            </Grid>
        </Border>
    </Grid>
</Window>
"@

$stringReader = New-Object System.IO.StringReader($script:xaml)
$xmlReader = [System.Xml.XmlReader]::Create($stringReader)
$script:window = [Windows.Markup.XamlReader]::Load($xmlReader)
$xmlReader.Close(); $stringReader.Close()

#endregion

###############################################################################
#                             GUI CONTROL MAPPING                             #
###############################################################################

#region Control Mapping

$controls = @{
    # Original controls
    JiraUrlBox       = $window.FindName("JiraUrlBox")
    JiraUserBox      = $window.FindName("JiraUserBox")
    JiraTokenBox     = $window.FindName("JiraTokenBox")
    ConnectButton    = $window.FindName("ConnectButton")
    TicketIdBox      = $window.FindName("TicketIdBox")
    FetchButton      = $window.FindName("FetchButton")
    FirstNameBox     = $window.FindName("FirstNameBox")
    LastNameBox      = $window.FindName("LastNameBox")
    EmailBox         = $window.FindName("EmailBox")
    UpnBox           = $window.FindName("UpnBox")
    SamBox           = $window.FindName("SamBox")
    JobTitleBox      = $window.FindName("JobTitleBox")
    DepartmentBox    = $window.FindName("DepartmentBox")
    OuComboBox       = $window.FindName("OuComboBox")
    ModelAccountBox  = $window.FindName("ModelAccountBox")
    CopyGroupsComboBox = $window.FindName("CopyGroupsComboBox")
    CreateUserButton = $window.FindName("CreateUserButton")
    LogBox           = $window.FindName("LogBox")
    StatusText       = $window.FindName("StatusText")
    SessionInfo      = $window.FindName("SessionInfo")
    CacheStats       = $window.FindName("CacheStats")
    
    # Progress indicators
    Step1Indicator   = $window.FindName("Step1Indicator")
    Step2Indicator   = $window.FindName("Step2Indicator")
    Step3Indicator   = $window.FindName("Step3Indicator")
    Step4Indicator   = $window.FindName("Step4Indicator")
    
    # Toolbar buttons
    RefreshButton    = $window.FindName("RefreshButton")
    ClearFormButton  = $window.FindName("ClearFormButton")
    ValidateButton   = $window.FindName("ValidateButton")
    PreviewButton    = $window.FindName("PreviewButton")
    HelpButton       = $window.FindName("HelpButton")
    
    # Log controls
    LogLevelFilter   = $window.FindName("LogLevelFilter")
    LogSearchBox     = $window.FindName("LogSearchBox")
    ClearLogButton   = $window.FindName("ClearLogButton")
    ExportLogButton  = $window.FindName("ExportLogButton")
    
    # Statistics
    UsersCreatedCount = $window.FindName("UsersCreatedCount")
    ErrorsCount      = $window.FindName("ErrorsCount")
    TicketsCount     = $window.FindName("TicketsCount")
    RuntimeDisplay   = $window.FindName("RuntimeDisplay")
}

#endregion

###############################################################################
#                             HELPER FUNCTIONS                                #
###############################################################################

#region Helper Functions

# Enhanced logging function with PowerShell 5.x compatibility
function Write-AppLog {
    param([string]$Message, [string]$Level = 'INFO')
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss.fff"
    $prefix = if ($script:SimulationMode) { "[SIM]" } else { "[LIVE]" }
    $logEntry = "[$timestamp] $prefix [$Level] $Message"
    
    switch ($Level) {
        'ERROR' { Write-Host $logEntry -ForegroundColor Red }
        'WARN'  { Write-Host $logEntry -ForegroundColor Yellow }
        'INFO'  { Write-Host $logEntry -ForegroundColor Green }
        'DEBUG' { if ($script:Config.LogLevel -eq 'DEBUG') { Write-Host $logEntry -ForegroundColor Gray } }
    }
}

# Legacy Write-Log function for backward compatibility
function Write-Log {
    param([string]$Message, [string]$Level = "INFO")
    if ($script:Config.LogLevel -eq 'DEBUG' -or $Level -ne 'DEBUG') {
        $timestamp = Get-Date -Format "HH:mm:ss"
        $prefix = if ($script:SimulationMode) { "[SIM]" } else { "[LIVE]" }
        $logEntry = "[$timestamp] $prefix [$Level] $Message`r`n"
        if ($controls.LogBox -ne $null) {
            $controls.LogBox.AppendText($logEntry)
            $controls.LogBox.ScrollToEnd()
        }
    }
    # Also call enhanced logging
    Write-AppLog $Message $Level
}

# Helper for PowerShell 5.1 compatibility
function Get-ValueOrDefault {
    param($Value, $Default)
    if ($null -ne $Value -and $Value -isnot [System.DBNull]) { return $Value }
    return $Default
}

function Update-Status {
    param([string]$Message, [string]$Level = "INFO")
    try {
        if ($controls.StatusText) {
            $controls.StatusText.Text = "[$(Get-Date -Format "HH:mm:ss")] $Message"
            $color = switch ($Level) {
                'ERROR'   { '#FFFF8A8A' }
                'WARN'    { '#FFFFE08A' }
                'SUCCESS' { '#FFB9FFB9' }
                default   { 'White' }
            }
            $controls.StatusText.Foreground = $color
        }
        Write-Log $Message -Level $Level
    } catch {
        Write-AppLog "Error updating status: $($_.Exception.Message)" -Level 'ERROR'
    }
}

function Set-UiLock {
    param([bool]$locked)
    try {
        if ($controls.FetchButton) { $controls.FetchButton.IsEnabled = !$locked }
        if ($controls.CreateUserButton) { $controls.CreateUserButton.IsEnabled = !$locked }
        if ($controls.ConnectButton) { $controls.ConnectButton.IsEnabled = !$locked }
    } catch {
        Write-AppLog "Error setting UI lock state: $($_.Exception.Message)" -Level 'ERROR'
    }
}

$script:LocationToOuMap = @{
    "United Kingdom" = "OU=Users,OU=London,DC=jeragm,DC=com"
    "Singapore" = "OU=Users,OU=Singapore,DC=jeragm,DC=com"
    "USA" = "OU=Users,OU=USA,DC=jeragm,DC=com"
    "Japan" = "OU=Tokyo,OU=Users,OU=Accounts,OU=JeraGM,DC=JERAGM,DC=COM"
}

#endregion

###############################################################################
#                         ENHANCED UI FUNCTIONS                               #
###############################################################################

#region Enhanced UI Functions

function Test-PowerShellCompatibility {
    $version = $PSVersionTable.PSVersion.Major
    
    if ($version -lt 5) {
        Write-Host "ERROR: PowerShell 5.0 or higher required. Current: $($PSVersionTable.PSVersion)" -ForegroundColor Red
        return $false
    }
    
    # Test WPF availability
    try {
        Add-Type -AssemblyName PresentationFramework -ErrorAction Stop
        return $true
    }
    catch {
        Write-Host "ERROR: WPF not available: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

function Initialize-ModernTheme {
    param([System.Windows.Window]$Window)
    
    try {
        # Apply theme to existing controls
        Apply-ThemeToControls -Window $Window
        Write-Log "Modern theme applied successfully" "DEBUG"
    }
    catch {
        Write-Log "Error applying theme: $($_.Exception.Message)" "WARN"
    }
}

function Apply-ThemeToControls {
    param([System.Windows.Window]$Window)
    
    try {
        # Apply modern styling to main buttons
        $mainButtons = @($controls.ConnectButton, $controls.FetchButton, $controls.CreateUserButton)
        foreach ($button in $mainButtons) {
            if ($button -ne $null) {
                $button.Background = [System.Windows.Media.SolidColorBrush]::new([System.Windows.Media.ColorConverter]::ConvertFromString($script:ThemeColors.Primary))
                $button.Foreground = [System.Windows.Media.Brushes]::White
                $button.BorderThickness = [System.Windows.Thickness]::new(0)
                $button.Padding = [System.Windows.Thickness]::new(12, 8, 12, 8)
            }
        }
        
        Write-Log "Theme applied to controls" "DEBUG"
    }
    catch {
        Write-Log "Error applying theme to controls: $($_.Exception.Message)" "WARN"
    }
}

function Update-WorkflowProgress {
    param(
        [string]$StepName,
        [ValidateSet("Pending", "InProgress", "Completed", "Error")]
        [string]$Status
    )
    
    if ($script:WorkflowSteps.ContainsKey($StepName)) {
        $script:WorkflowSteps[$StepName].Status = $Status
        Update-ProgressDisplay
        Write-Log "Workflow step '$StepName' status: $Status" "DEBUG"
    }
}

function Update-ProgressDisplay {
    try {
        # Update progress indicators
        $stepIndicators = @($controls.Step1Indicator, $controls.Step2Indicator, $controls.Step3Indicator, $controls.Step4Indicator)
        
        $i = 0
        foreach ($step in $script:WorkflowSteps.Values | Sort-Object Index) {
            if ($i -lt $stepIndicators.Count -and $stepIndicators[$i] -ne $null) {
                $indicator = $stepIndicators[$i]
                
                switch ($step.Status) {
                    "Pending" { 
                        $indicator.Fill = [System.Windows.Media.SolidColorBrush]::new([System.Windows.Media.ColorConverter]::ConvertFromString("#FFCCCCCC"))
                    }
                    "InProgress" { 
                        $indicator.Fill = [System.Windows.Media.SolidColorBrush]::new([System.Windows.Media.ColorConverter]::ConvertFromString($script:ThemeColors.Warning))
                    }
                    "Completed" { 
                        $indicator.Fill = [System.Windows.Media.SolidColorBrush]::new([System.Windows.Media.ColorConverter]::ConvertFromString($script:ThemeColors.Success))
                    }
                    "Error" { 
                        $indicator.Fill = [System.Windows.Media.SolidColorBrush]::new([System.Windows.Media.ColorConverter]::ConvertFromString($script:ThemeColors.Error))
                    }
                }
            }
            $i++
        }
        
        # Update status text with current step
        $currentStep = $script:WorkflowSteps.Values | Where-Object { $_.Status -eq "InProgress" } | Select-Object -First 1
        if ($currentStep -ne $null) {
            Update-Status "Step $($currentStep.Index): $($currentStep.Name)" "INFO"
        }
        
        # Update progress in session info
        $completedSteps = ($script:WorkflowSteps.Values | Where-Object { $_.Status -eq "Completed" }).Count
        $totalSteps = $script:WorkflowSteps.Count
        $progressPercent = if ($totalSteps -gt 0) { [math]::Round(($completedSteps / $totalSteps) * 100) } else { 0 }
        
        $controls.SessionInfo.Text = "Progress: $completedSteps/$totalSteps ($progressPercent%)"
    }
    catch {
        Write-Log "Error updating progress display: $($_.Exception.Message)" "WARN"
    }
}

function Update-SessionStatistics {
    param(
        [ValidateSet("UserCreated", "ErrorOccurred", "TicketFetched", "ValidationError")]
        [string]$Event
    )
    
    switch ($Event) {
        "UserCreated" { $script:SessionStats.UsersCreated++ }
        "ErrorOccurred" { $script:SessionStats.ErrorsOccurred++ }
        "TicketFetched" { $script:SessionStats.TicketsFetched++ }
        "ValidationError" { $script:SessionStats.ValidationErrors++ }
    }
    
    Update-StatisticsDisplay
}

function Update-StatisticsDisplay {
    try {
        # Update statistics counters
        if ($controls.UsersCreatedCount -ne $null) {
            $controls.UsersCreatedCount.Text = $script:SessionStats.UsersCreated.ToString()
        }
        if ($controls.ErrorsCount -ne $null) {
            $controls.ErrorsCount.Text = $script:SessionStats.ErrorsOccurred.ToString()
        }
        if ($controls.TicketsCount -ne $null) {
            $controls.TicketsCount.Text = $script:SessionStats.TicketsFetched.ToString()
        }
        
        # Update runtime
        if ($controls.RuntimeDisplay -ne $null) {
            $runtime = (Get-Date) - $script:SessionStats.StartTime
            $runtimeText = "{0:hh\:mm\:ss}" -f $runtime
            $controls.RuntimeDisplay.Text = $runtimeText
        }
    }
    catch {
        Write-Log "Error updating statistics display: $($_.Exception.Message)" "WARN"
    }
}

function Write-EnhancedLog {
    param(
        [string]$Message, 
        [string]$Level = "INFO",
        [string]$Category = "General"
    )
    
    try {
        $timestamp = Get-Date
        $logEntry = [PSCustomObject]@{
            Timestamp = $timestamp
            Level = $Level
            Category = $Category
            Message = $Message
            DisplayText = "[$($timestamp.ToString("HH:mm:ss"))] [$Level] $Message"
        }
        
        # Add to collection
        $null = $script:LogEntries.Add($logEntry)
        
        # Update display
        Update-LogDisplay
        
        # Keep original logging for file
        Write-Log $Message $Level
    }
    catch {
        # Fallback to original logging
        Write-Log $Message $Level
    }
}

function Update-LogDisplay {
    try {
        $filteredLogs = $script:LogEntries
        
        # Apply level filter
        if ($controls.LogLevelFilter -ne $null -and $controls.LogLevelFilter.SelectedItem -ne $null) {
            $selectedLevel = $controls.LogLevelFilter.SelectedItem.Content
            if ($selectedLevel -ne "All") {
                $filteredLogs = $filteredLogs | Where-Object { $_.Level -eq $selectedLevel }
            }
        }
        
        # Apply search filter
        if ($controls.LogSearchBox -ne $null -and -not [string]::IsNullOrWhiteSpace($controls.LogSearchBox.Text)) {
            $searchText = $controls.LogSearchBox.Text
            $filteredLogs = $filteredLogs | Where-Object { $_.Message -like "*$searchText*" }
        }
        
        # Update log display
        if ($controls.LogBox -ne $null) {
            $logText = ($filteredLogs | Select-Object -Last 100 | ForEach-Object { $_.DisplayText }) -join "`r`n"
            $controls.LogBox.Text = $logText
            $controls.LogBox.ScrollToEnd()
        }
    }
    catch {
        Write-Log "Error updating log display: $($_.Exception.Message)" "WARN"
    }
}

function Initialize-FieldValidation {
    try {
        # Add validation to text boxes with individual handlers to avoid closure issues
        if ($controls.FirstNameBox -ne $null) {
            $controls.FirstNameBox.Add_TextChanged({
                param($sender, $e)
                $rules = @{ Required = $true; MinLength = 1; Pattern = "^[a-zA-Z\s\-'\.]+$" }
                Validate-Field -Field $sender -Rules $rules
            })
        }
        
        if ($controls.LastNameBox -ne $null) {
            $controls.LastNameBox.Add_TextChanged({
                param($sender, $e)
                $rules = @{ Required = $true; MinLength = 1; Pattern = "^[a-zA-Z\s\-'\.]+$" }
                Validate-Field -Field $sender -Rules $rules
            })
        }
        
        if ($controls.EmailBox -ne $null) {
            $controls.EmailBox.Add_TextChanged({
                param($sender, $e)
                $rules = @{ Required = $false; Pattern = "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$" }
                Validate-Field -Field $sender -Rules $rules
            })
        }
        
        if ($controls.SamBox -ne $null) {
            $controls.SamBox.Add_TextChanged({
                param($sender, $e)
                $rules = @{ Required = $false; MinLength = 6; Pattern = "^[a-zA-Z0-9]+$" }
                Validate-Field -Field $sender -Rules $rules
            })
        }
        
        Write-Log "Field validation initialized" "DEBUG"
    }
    catch {
        Write-Log "Error initializing field validation: $($_.Exception.Message)" "WARN"
    }
}

function Validate-Field {
    param(
        [System.Windows.Controls.TextBox]$Field,
        [hashtable]$Rules
    )
    
    try {
        # Null checks
        if ($Field -eq $null) {
            Write-Log "Validate-Field called with null field" "WARN"
            return $true
        }
        
        if ($Rules -eq $null) {
            Write-Log "Validate-Field called with null rules" "WARN"
            return $true
        }
        
        $isValid = $true
        $errorMessage = ""
        $value = if ($Field.Text -ne $null) { $Field.Text } else { "" }
        
        # Required field check
        if ($Rules.ContainsKey("Required") -and $Rules.Required -and [string]::IsNullOrWhiteSpace($value)) {
            $isValid = $false
            $errorMessage = "This field is required"
        }
        
        # Minimum length check
        if ($isValid -and $Rules.ContainsKey("MinLength") -and $Rules.MinLength -and $value.Length -lt $Rules.MinLength) {
            $isValid = $false
            $errorMessage = "Minimum length is $($Rules.MinLength) characters"
        }
        
        # Pattern check
        if ($isValid -and $Rules.ContainsKey("Pattern") -and $Rules.Pattern -and -not [string]::IsNullOrWhiteSpace($value) -and $value -notmatch $Rules.Pattern) {
            $isValid = $false
            $errorMessage = "Invalid format"
        }
        
        # Apply visual feedback
        try {
            if ($isValid) {
                $Field.BorderBrush = [System.Windows.Media.SolidColorBrush]::new([System.Windows.Media.ColorConverter]::ConvertFromString($script:ThemeColors.Success))
                $Field.ToolTip = $null
            } else {
                $Field.BorderBrush = [System.Windows.Media.SolidColorBrush]::new([System.Windows.Media.ColorConverter]::ConvertFromString($script:ThemeColors.Error))
                $Field.ToolTip = $errorMessage
                Update-SessionStatistics -Event "ValidationError"
            }
        }
        catch {
            Write-Log "Error applying visual feedback to field: $($_.Exception.Message)" "DEBUG"
        }
        
        return $isValid
    }
    catch {
        Write-Log "Error validating field: $($_.Exception.Message)" "WARN"
        return $true
    }
}

function Clear-AllFields {
    try {
        # Clear text boxes individually with null checks
        $textBoxNames = @("FirstNameBox", "LastNameBox", "EmailBox", "UpnBox", "SamBox", "JobTitleBox", "DepartmentBox")
        
        foreach ($boxName in $textBoxNames) {
            try {
                $textBox = $controls[$boxName]
                if ($textBox -ne $null) {
                    $textBox.Text = ""
                    $textBox.BorderBrush = [System.Windows.Media.SystemColors]::ControlBorderBrush
                    $textBox.ToolTip = $null
                }
            }
            catch {
                Write-Log "Error clearing $boxName : $($_.Exception.Message)" "DEBUG"
            }
        }
        
        # Reset combo boxes
        try {
            if ($controls.OuComboBox -ne $null -and $controls.OuComboBox.Items.Count -gt 0) {
                $controls.OuComboBox.SelectedIndex = 0
            }
            if ($controls.CopyGroupsComboBox -ne $null) {
                $controls.CopyGroupsComboBox.SelectedIndex = 1  # Default to "No"
            }
        }
        catch {
            Write-Log "Error resetting combo boxes: $($_.Exception.Message)" "DEBUG"
        }
        
        # Reset workflow progress
        try {
            foreach ($stepName in $script:WorkflowSteps.Keys) {
                Update-WorkflowProgress -StepName $stepName -Status "Pending"
            }
        }
        catch {
            Write-Log "Error resetting workflow progress: $($_.Exception.Message)" "DEBUG"
        }
        
        Write-EnhancedLog "Form cleared" "INFO"
    }
    catch {
        Write-Log "Error clearing fields: $($_.Exception.Message)" "WARN"
    }
}

function Validate-AllFields {
    try {
        $allValid = $true
        $validationErrors = @()
        
        # Validate each field individually and collect errors
        if ($controls.FirstNameBox -ne $null) {
            $rules = @{ Required = $true; MinLength = 1; Pattern = "^[a-zA-Z\s\-'\.]+$" }
            $isValid = Validate-Field -Field $controls.FirstNameBox -Rules $rules
            if (-not $isValid) {
                $validationErrors += "First Name: $($controls.FirstNameBox.ToolTip)"
            }
            $allValid = $allValid -and $isValid
        }
        
        if ($controls.LastNameBox -ne $null) {
            $rules = @{ Required = $true; MinLength = 1; Pattern = "^[a-zA-Z\s\-'\.]+$" }
            $isValid = Validate-Field -Field $controls.LastNameBox -Rules $rules
            if (-not $isValid) {
                $validationErrors += "Last Name: $($controls.LastNameBox.ToolTip)"
            }
            $allValid = $allValid -and $isValid
        }
        
        if ($controls.EmailBox -ne $null -and -not [string]::IsNullOrWhiteSpace($controls.EmailBox.Text)) {
            $rules = @{ Required = $false; Pattern = "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$" }
            $isValid = Validate-Field -Field $controls.EmailBox -Rules $rules
            if (-not $isValid) {
                $validationErrors += "Email: $($controls.EmailBox.ToolTip)"
            }
            $allValid = $allValid -and $isValid
        }
        
        if ($controls.SamBox -ne $null -and -not [string]::IsNullOrWhiteSpace($controls.SamBox.Text)) {
            $rules = @{ Required = $false; MinLength = 6; Pattern = "^[a-zA-Z0-9]+$" }
            $isValid = Validate-Field -Field $controls.SamBox -Rules $rules
            if (-not $isValid) {
                $validationErrors += "SAM Account: $($controls.SamBox.ToolTip)"
            }
            $allValid = $allValid -and $isValid
        }
        
        # Log specific validation errors
        if ($validationErrors.Count -gt 0) {
            $errorDetails = $validationErrors -join "; "
            Write-EnhancedLog "Field validation errors found: $errorDetails" "WARN" "Validation"
        }
        
        return $allValid
    }
    catch {
        Write-Log "Error validating all fields: $($_.Exception.Message)" "WARN"
        return $false
    }
}

function Initialize-ResponsiveLayout {
    param([System.Windows.Window]$Window)
    
    try {
        # Set responsive sizing
        $Window.MinWidth = 800
        $Window.MinHeight = 700
        
        # Add window state changed handler
        $Window.Add_SizeChanged({
            param($sender, $e)
            Adjust-LayoutForSize -Width $sender.ActualWidth -Height $sender.ActualHeight
        })
        
        Write-Log "Responsive layout initialized" "DEBUG"
    }
    catch {
        Write-Log "Error initializing responsive layout: $($_.Exception.Message)" "WARN"
    }
}

function Adjust-LayoutForSize {
    param([double]$Width, [double]$Height)
    
    try {
        # Adjust layout based on window size
        if ($controls.LogBox -ne $null) {
            if ($Width -lt 900) {
                # Compact layout
                $controls.LogBox.FontSize = 10
            } else {
                # Normal layout
                $controls.LogBox.FontSize = 12
            }
        }
    }
    catch {
        Write-Log "Error adjusting layout: $($_.Exception.Message)" "WARN"
    }
}

#endregion

###############################################################################
#                    SESSION, CACHE & SIMULATION FUNCTIONS                    #
###############################################################################

#region Core Logic Functions

function Test-SessionValid {
    return ($null -ne $script:SessionStartTime) -and (((Get-Date) - $script:SessionStartTime).TotalMinutes -lt $script:Config.Security.SessionTimeoutMinutes)
}

function Test-JiraConnection {
    try {
        if ($script:JiraCredential -eq $null) {
            return $false
        }
        
        # Try a simple API call to test the connection
        Get-JiraServerInfo -Credential $script:JiraCredential -ErrorAction Stop | Out-Null
        return $true
    }
    catch {
        Write-EnhancedLog "Jira connection test failed: $($_.Exception.Message)" "DEBUG" "Jira"
        return $false
    }
}

function Reset-Session {
    $script:JiraCredential = $null
    $script:SessionStartTime = $null
    $script:LoginAttempts = 0
    Update-Status "Session reset." "INFO"
    Write-AppLog "Session reset" -Level 'INFO'
    
    # Reset workflow progress
    foreach ($stepName in $script:WorkflowSteps.Keys) {
        Update-WorkflowProgress -StepName $stepName -Status "Pending"
    }
}

# Enhanced input validation functions
function Test-JiraUrl {
    param([string]$Url)
    if ([string]::IsNullOrWhiteSpace($Url)) { 
        return @{ IsValid = $false; Error = "URL cannot be empty" } 
    }
    if ($script:Config.Security.RequireHttps -and -not $Url.StartsWith('https://')) { 
        return @{ IsValid = $false; Error = "HTTPS is required for security" } 
    }
    try { 
        if ($PSVersionTable.PSVersion.Major -ge 6) {
            [System.Uri]::new($Url) | Out-Null
        } else {
            New-Object System.Uri($Url) | Out-Null
        }
        return @{ IsValid = $true } 
    } catch { 
        return @{ IsValid = $false; Error = "Invalid URL format" } 
    }
}

function Test-TicketKey {
    param([string]$TicketKey)
    if ([string]::IsNullOrWhiteSpace($TicketKey)) { 
        return @{ IsValid = $false; Error = "Ticket key cannot be empty" } 
    }
    if ($TicketKey -notmatch '^[A-Z][A-Z0-9]+-\d+$') {
        return @{ IsValid = $false; Error = "Invalid ticket key format (e.g., PROJECT-123)" }
    }
    return @{ IsValid = $true }
}

function Get-CachedTicket {
    param([string]$TicketKey)
    if ($script:Cache.ContainsKey($TicketKey)) {
        $cacheEntry = $script:Cache[$TicketKey]
        if (((Get-Date) - $cacheEntry.Timestamp).TotalMinutes -lt $script:Config.CacheExpiryMinutes) {
            Write-AppLog "Cache hit for ticket: $TicketKey" -Level 'DEBUG'
            return $cacheEntry.Data
        }
        Write-AppLog "Cache expired for ticket: $TicketKey" -Level 'DEBUG'
        # Handle both ConcurrentDictionary and Hashtable
        if ($script:Cache -is [System.Collections.Concurrent.ConcurrentDictionary[string, object]]) {
            $script:Cache.TryRemove($TicketKey, [ref]$null) | Out-Null
        } else {
            $script:Cache.Remove($TicketKey)
        }
    }
    return $null
}

function Set-CachedTicket {
    param([string]$TicketKey, [object]$TicketData)
    if ($script:Cache.Count -ge $script:Config.MaxCacheSize) {
        $oldestKey = ($script:Cache.GetEnumerator() | Sort-Object { $_.Value.Timestamp } | Select-Object -First 1).Key
        # Handle both ConcurrentDictionary and Hashtable
        if ($script:Cache -is [System.Collections.Concurrent.ConcurrentDictionary[string, object]]) {
            $script:Cache.TryRemove($oldestKey, [ref]$null) | Out-Null
        } else {
            $script:Cache.Remove($oldestKey)
        }
        Write-AppLog "Cache evicted oldest entry: $oldestKey" -Level 'DEBUG'
    }
    $script:Cache[$TicketKey] = @{ Data = $TicketData; Timestamp = Get-Date }
    if ($controls.CacheStats) {
        $controls.CacheStats.Text = "Cache: $($script:Cache.Count) items"
    }
    Write-AppLog "Cached ticket: $TicketKey" -Level 'DEBUG'
}

function Get-ADUser-Simulated { param($Filter, $Identity, $Properties, $ErrorAction="Continue")
    if ($script:SimulationMode) {
        if ($Filter -match "SamAccountName -eq '([^']+)'") {
            if (@("smithj", "johnd") -contains $Matches[1]) { return @{ SamAccountName = $Matches[1] } }
            if ($ErrorAction -eq "Stop") { throw "User not found (simulated)" } else { return $null }
        }
        if ($Identity) { return @{ MemberOf = @("CN=Group1,DC=jeragm,DC=com", "CN=Group2,DC=jeragm,DC=com") } }
    } else { return Get-ADUser @PSBoundParameters }
}
function New-ADUser-Simulated {
    param($UserParams)
    if ($script:SimulationMode) {
        Write-AppLog "SIMULATION: New-ADUser with params:`n$($UserParams | Out-String)" -Level 'INFO'
    } else {
        New-ADUser @UserParams
    }
}

function Add-ADGroupMember-Simulated {
    param($Identity, $Members)
    if ($script:SimulationMode) {
        Write-AppLog "SIMULATION: Add-ADGroupMember: Adding $Members to $Identity" -Level 'INFO'
    } else {
        Add-ADGroupMember @PSBoundParameters
    }
}

#endregion

###############################################################################
#                          ACTIVE DIRECTORY FUNCTIONS                         #
###############################################################################

#region Active Directory Functions

function Get-SamAccountName {
    param([string]$FirstName, [string]$LastName)
    
    # Remove spaces from names for SAM account generation
    $originalFirstName = $FirstName
    $originalLastName = $LastName
    $FirstName = $FirstName -replace '\s+', ''
    $LastName = $LastName -replace '\s+', ''
    
    if ($FirstName -ne $originalFirstName -or $LastName -ne $originalLastName) {
        Write-Log "Removed spaces from names: '$originalFirstName $originalLastName' -> '$FirstName $LastName'" "DEBUG"
    }
    
    # Get first 5 letters of last name (or less if name is shorter)
    $lastPart = $LastName.Substring(0, [Math]::Min(5, $LastName.Length))

    # Start with just first letter of first name
    $firstNameIndex = 1
    $isUnique = $false
    $minLengthSatisfied = $false

    Write-Log "Generating SAM account name for $FirstName $LastName" "DEBUG"

    while (-not $isUnique -and $firstNameIndex -le $FirstName.Length) {
        # Get increasing portions of the first name
        $firstPart = $FirstName.Substring(0, $firstNameIndex)
        $proposedSam = ("{0}{1}" -f $lastPart, $firstPart).ToLower()
        
        # Check if the proposed SAM meets the minimum length requirement of 6 characters
        if ($proposedSam.Length -lt 6) {
            # If the proposed SAM is too short, add more characters from the first name if possible
            if ($firstNameIndex -lt $FirstName.Length) {
                $firstNameIndex++
                Write-Log "SAM account name '$proposedSam' is less than 6 characters, adding more letters from first name" "WARN"
                continue
            }
            else {
                # If we've used all first name characters and still under 6, flag it but continue
                Write-Log "Warning: SAM account name '$proposedSam' is less than 6 characters but using all available name parts" "WARN"
                $minLengthSatisfied = $false
            }
        }
        else {
            $minLengthSatisfied = $true
        }
        
        try {
            Write-Log "Checking if SAM account name '$proposedSam' exists" "DEBUG"
            
            # Check if this SAM account exists
            $existingSam = Get-ADUser-Simulated -Filter "SamAccountName -eq '$proposedSam'" -ErrorAction Stop
            
            if (-not $existingSam) {
                # No user found with this SAM - we can use it
                $isUnique = $true
                
                if (-not $minLengthSatisfied) {
                    Write-Log "Generated unique SAM account name: $proposedSam (Warning: less than 6 characters)" "WARN"
                }
                else {
                    Write-Log "Generated unique SAM account name: $proposedSam" "INFO"
                }
            }
            else {
                # SAM exists - try next letter of first name
                $firstNameIndex++
                Write-Log "SAM account name '$proposedSam' already exists, trying with more letters from first name" "WARN"
            }
        }
        catch {
            # If there's an error checking AD, assume it doesn't exist (for simulation mode)
            $isUnique = $true
            Write-Log "Generated SAM account name: $proposedSam (could not verify uniqueness)" "INFO"
        }
    }

    if (-not $isUnique) {
        $errorMsg = "Could not generate unique SAM account name for $FirstName $LastName after trying all combinations"
        Write-Log $errorMsg "ERROR"
        throw $errorMsg
    }

    return $proposedSam
}

function Get-UserLogonName {
    param([string]$Upn)
    if (-not $Upn) { throw "UPN value is required for Get-UserLogonName" }
    return $Upn.ToLower()
}

function Get-EmailAddress {
    param([string]$Upn, [string]$Domain = "jeragm.com")
    if (-not $Upn) {
        throw "UPN value is required for Get-EmailAddress"
    }
    return ("{0}@{1}" -f $Upn.ToLower(), $Domain).ToLower()
}

function Get-UpnFromNames {
    param([string]$FirstName, [string]$LastName)
    if (-not $FirstName -or -not $LastName) {
        throw "Both FirstName and LastName are required for Get-UpnFromNames"
    }
    
    # Remove spaces from names for UPN generation
    $originalFirstName = $FirstName
    $originalLastName = $LastName
    $FirstName = $FirstName -replace '\s+', ''
    $LastName = $LastName -replace '\s+', ''
    
    if ($FirstName -ne $originalFirstName -or $LastName -ne $originalLastName) {
        Write-Log "Removed spaces from names for UPN: '$originalFirstName $originalLastName' -> '$FirstName $LastName'" "DEBUG"
    }
    
    # Create UPN in firstname.lastname format
    return ("{0}.{1}" -f $FirstName.ToLower(), $LastName.ToLower())
}

function New-SecurePassword {
    $charSet = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*()-_=+[]{}'.ToCharArray()
    $password = -join (1..24 | ForEach-Object { $charSet | Get-Random })
    return ConvertTo-SecureString -String $password -AsPlainText -Force
}

#endregion

###############################################################################
#                             EVENT HANDLERS                                  #
###############################################################################

#region Event Handlers

$controls.ConnectButton.Add_Click({
    Update-WorkflowProgress -StepName "Connection" -Status "InProgress"
    Set-UiLock -locked $true
    try {
        if ($script:LoginAttempts -ge $script:Config.Security.MaxLoginAttempts) { throw "Maximum login attempts exceeded." }
        if ([string]::IsNullOrWhiteSpace($controls.JiraTokenBox.Password)) { throw "API Token must be provided." }
        
        Update-Status "Connecting to $($controls.JiraUrlBox.Text)..."
        Set-JiraConfigServer -Server $controls.JiraUrlBox.Text
        $secureToken = ConvertTo-SecureString $controls.JiraTokenBox.Password -AsPlainText -Force
        $script:JiraCredential = New-Object System.Management.Automation.PSCredential($controls.JiraUserBox.Text, $secureToken)

        Get-JiraServerInfo -Credential $script:JiraCredential | Out-Null
        
        $script:SessionStartTime = Get-Date
        $script:LoginAttempts = 0
        Update-Status "Successfully connected to Jira." "SUCCESS"
        $controls.TicketIdBox.IsEnabled = $true
        $controls.FetchButton.IsEnabled = $true
        $controls.ConnectButton.IsEnabled = $false
        
        Update-WorkflowProgress -StepName "Connection" -Status "Completed"
        Write-EnhancedLog "Successfully connected to Jira" "INFO" "Connection"
    } catch {
        $script:LoginAttempts++
        Update-Status "Jira connection failed: $($_.Exception.Message)" "ERROR"
        Update-WorkflowProgress -StepName "Connection" -Status "Error"
        Update-SessionStatistics -Event "ErrorOccurred"
        Write-EnhancedLog "Jira connection failed: $($_.Exception.Message)" "ERROR" "Connection"
    } finally {
        Set-UiLock -locked $false
    }
})

$controls.FetchButton.Add_Click({
    Update-WorkflowProgress -StepName "FetchData" -Status "InProgress"
    Set-UiLock -locked $true
    try {
        if (-not (Test-SessionValid)) { Reset-Session; throw "Session expired. Please reconnect." }
        $ticketId = $controls.TicketIdBox.Text
        if ([string]::IsNullOrWhiteSpace($ticketId)) { throw "Ticket ID cannot be empty." }

        Update-Status "Fetching data for ticket $ticketId..."
        $issue = Get-CachedTicket -TicketKey $ticketId
        if (-not $issue) {
            $issue = Get-JiraIssue -Key $ticketId -Credential $script:JiraCredential -ErrorAction Stop
            Set-CachedTicket -TicketKey $ticketId -TicketData $issue
            Update-SessionStatistics -Event "TicketFetched"
        }
        
        # Validate that we have a valid issue object
        if (-not $issue) {
            throw "Failed to retrieve ticket data for $ticketId"
        }
        
        # Debug: Log the structure of the issue object
        Write-Log "Issue object properties: $($issue.PSObject.Properties.Name -join ', ')" "DEBUG"
        if ($issue.PSObject.Properties['CustomFields']) {
            Write-Log "Found CustomFields property with $($issue.CustomFields.Count) items" "DEBUG"
        }
        if ($issue.PSObject.Properties['Comment']) {
            Write-Log "Found Comment property with $($issue.Comment.Count) comments" "DEBUG"
        }
        
        # Extract data from Jira issue object
        Write-Log "Starting data extraction from ticket $ticketId" "INFO"
        
        function Get-CustomFieldValue {
            param($customFieldId, $customFieldName)
            try {
                # Look for custom field by ID in the issue
                if ($issue.PSObject.Properties['CustomFields']) {
                    $customField = $issue.CustomFields | Where-Object { $_.Id -eq $customFieldId -or $_.Name -eq $customFieldName }
                    if ($customField -and $customField.Value) {
                        return $customField.Value
                    }
                }
                return $null
            }
            catch {
                Write-Log "Error extracting custom field '$customFieldName': $($_.Exception.Message)" "DEBUG"
                return $null
            }
        }

        function Get-ValueFromComments {
            param($regexPattern)
            try {
                if ($issue.PSObject.Properties['Comment'] -and $issue.Comment) {
                    foreach ($comment in $issue.Comment) {
                        if ($comment.Body -and $comment.Body -match $regexPattern) {
                            return $Matches[1].Trim()
                        }
                    }
                }
                return $null
            }
            catch {
                Write-Log "Error extracting from comments: $($_.Exception.Message)" "DEBUG"
                return $null
            }
        }

        function Get-Value {
            param($customFieldId, $customFieldName, $regexInComments)
            try {
                # First try to get from custom fields
                $val = Get-CustomFieldValue -customFieldId $customFieldId -customFieldName $customFieldName
                if (-not [string]::IsNullOrWhiteSpace($val)) { 
                    return $val 
                }
                
                # Fallback to parsing from comments
                if ($regexInComments) {
                    $val = Get-ValueFromComments -regexPattern $regexInComments
                    if (-not [string]::IsNullOrWhiteSpace($val)) { 
                        return $val 
                    }
                }
                
                # Final fallback to description
                if ($issue.Description -and $issue.Description -match $regexInComments) { 
                    return $Matches[1].Trim() 
                }
                
                return $null
            }
            catch {
                Write-Log "Error extracting field '$customFieldName': $($_.Exception.Message)" "WARN"
                return $null
            }
        }

        # Extract field values from the ticket
        try {
            # Based on your XML, the data is in comments, so we'll extract from there
            $newJoinerName = Get-Value -customFieldId "customfield_10304" -customFieldName "First Name" -regexInComments "New Joiner Name:\s*([^\r\n]+)"
            $jobTitle = Get-Value -customFieldId "customfield_10238" -customFieldName "Job Title" -regexInComments "Job Title:\s*([^\r\n]+)"
            $department = Get-Value -customFieldId "customfield_10120" -customFieldName "Department" -regexInComments "Department:\s*([^\r\n]+)"
            $modelAccount = Get-Value -customFieldId "customfield_10343" -customFieldName "Model Account" -regexInComments "Model Account:\s*([^\r\n]+)"
            
            # Try to get office location from custom field
            $officeLocation = Get-CustomFieldValue -customFieldId "customfield_10115" -customFieldName "Office Location"
            
            # Also try to extract first and last names directly from custom fields
            $firstNameFromField = Get-CustomFieldValue -customFieldId "customfield_10304" -customFieldName "First Name"
            $lastNameFromField = Get-CustomFieldValue -customFieldId "customfield_10305" -customFieldName "Last Name"

            # Parse name with better error handling
            $firstName = "MISSING"
            $lastName = "MISSING"
            
            # Prefer individual name fields if available
            if (-not [string]::IsNullOrWhiteSpace($firstNameFromField)) {
                $firstName = $firstNameFromField
            }
            if (-not [string]::IsNullOrWhiteSpace($lastNameFromField)) {
                $lastName = $lastNameFromField
            }
            
            # Fallback to parsing from "New Joiner Name" if individual fields are empty
            if (($firstName -eq "MISSING" -or $lastName -eq "MISSING") -and -not [string]::IsNullOrWhiteSpace($newJoinerName)) {
                if ($newJoinerName -match "(\S+)\s+(.*)") { 
                    if ($firstName -eq "MISSING") { $firstName = $Matches[1] }
                    if ($lastName -eq "MISSING") { $lastName = $Matches[2] }
                } else {
                    # If no space found, treat entire string as first name
                    if ($firstName -eq "MISSING") { $firstName = $newJoinerName }
                }
            }
            
            Write-Log "Extracted data - Name: '$firstName $lastName', Job: '$jobTitle', Dept: '$department', Model: '$modelAccount', Location: '$officeLocation'" "DEBUG"
        }
        catch {
            Write-Log "Error parsing ticket fields: $($_.Exception.Message)" "ERROR"
            $firstName = "MISSING"
            $lastName = "MISSING"
            $jobTitle = $null
            $department = $null
            $modelAccount = $null
            $officeLocation = $null
        }

        # Safely populate UI fields with null checking
        $controls.FirstNameBox.Text = if ($firstName) { $firstName } else { "MISSING" }
        $controls.LastNameBox.Text = if ($lastName) { $lastName } else { "MISSING" }
        $controls.JobTitleBox.Text = if ($jobTitle) { $jobTitle } else { "" }
        $controls.DepartmentBox.Text = if ($department) { $department } else { "" }
        $controls.ModelAccountBox.Text = if ($modelAccount) { $modelAccount } else { "" }
        
        # Generate SAM account name, UPN, and Email if we have valid names
        if ($firstName -ne "MISSING" -and $lastName -ne "MISSING") {
            try {
                # Generate SAM account name using the same logic as bulk import
                $generatedSam = Get-SamAccountName -FirstName $firstName -LastName $lastName
                $controls.SamBox.Text = $generatedSam
                
                # Generate UPN in firstname.lastname format using names from Jira ticket
                # This matches the bulk import logic where UPN comes from CSV in firstname.lastname format
                $baseUpn = Get-UpnFromNames -FirstName $firstName -LastName $lastName
                $emailAddress = Get-EmailAddress -Upn $baseUpn
                
                $controls.UpnBox.Text = $emailAddress  # This will be {firstname.lastname}@{domain}
                $controls.EmailBox.Text = $emailAddress
                
                Write-Log "Generated from Jira ticket - SAM: $generatedSam, UPN: $emailAddress (based on $firstName.$lastName), Email: $emailAddress" "DEBUG"
            }
            catch {
                Write-Log "Error generating account details: $($_.Exception.Message)" "WARN"
                $controls.SamBox.Text = ""
                $controls.UpnBox.Text = ""
                $controls.EmailBox.Text = ""
            }
        } else {
            $controls.SamBox.Text = ""
            $controls.UpnBox.Text = ""
            $controls.EmailBox.Text = ""
        }
        
        # Enable UI controls
        @($controls.FirstNameBox, $controls.LastNameBox, $controls.EmailBox, $controls.UpnBox, $controls.SamBox, $controls.JobTitleBox, $controls.DepartmentBox, $controls.OuComboBox, $controls.ModelAccountBox, $controls.CopyGroupsComboBox, $controls.CreateUserButton) | ForEach-Object { 
            if ($_) { $_.IsEnabled = $true }
        }

        # Safely set OU selection
        try {
            if ($officeLocation -and $script:LocationToOuMap.ContainsKey($officeLocation)) { 
                $targetOU = $script:LocationToOuMap[$officeLocation]
                # Find the matching item in the ComboBox
                for ($i = 0; $i -lt $controls.OuComboBox.Items.Count; $i++) {
                    if ($controls.OuComboBox.Items[$i] -eq $targetOU) {
                        $controls.OuComboBox.SelectedIndex = $i
                        break
                    }
                }
            }
        }
        catch {
            Write-Log "Error setting OU selection: $($_.Exception.Message)" "WARN"
        }

        Update-Status "Data extraction completed. Please verify the populated fields." "INFO"
        Update-WorkflowProgress -StepName "FetchData" -Status "Completed"
        Update-WorkflowProgress -StepName "Validation" -Status "InProgress"
        Write-EnhancedLog "Successfully fetched and populated data from ticket $ticketId" "INFO" "DataFetch"
    } catch {
        Update-Status "Failed to fetch ticket data: $($_.Exception.Message)" "ERROR"
        Update-WorkflowProgress -StepName "FetchData" -Status "Error"
        Update-SessionStatistics -Event "ErrorOccurred"
        Write-EnhancedLog "Failed to fetch ticket data: $($_.Exception.Message)" "ERROR" "DataFetch"
    } finally {
        Set-UiLock -locked $false
    }
})

# Toolbar Event Handlers
$controls.RefreshButton.Add_Click({
    try {
        Reset-Session
        Update-Status "Session refreshed" "INFO"
        Write-EnhancedLog "Session refreshed by user" "INFO" "Session"
    }
    catch {
        Write-EnhancedLog "Error refreshing session: $($_.Exception.Message)" "ERROR" "Session"
    }
})

$controls.ClearFormButton.Add_Click({
    try {
        Clear-AllFields
        Update-Status "Form cleared" "INFO"
    }
    catch {
        Write-EnhancedLog "Error clearing form: $($_.Exception.Message)" "ERROR" "UI"
    }
})

$controls.ValidateButton.Add_Click({
    try {
        $isValid = Validate-AllFields
        if ($isValid) {
            Update-Status "All fields are valid" "SUCCESS"
            Update-WorkflowProgress -StepName "Validation" -Status "Completed"
            Write-EnhancedLog "Field validation completed successfully" "INFO" "Validation"
            
            # Show success message
            [System.Windows.MessageBox]::Show("All fields passed validation!", "Validation Success", "OK", "Information")
        } else {
            Update-Status "Some fields have validation errors" "WARN"
            Write-EnhancedLog "Field validation found errors" "WARN" "Validation"
            
            # Show detailed validation errors
            Show-ValidationErrors
        }
    }
    catch {
        Write-EnhancedLog "Error during validation: $($_.Exception.Message)" "ERROR" "Validation"
    }
})

$controls.PreviewButton.Add_Click({
    try {
        if ([string]::IsNullOrWhiteSpace($controls.FirstNameBox.Text) -or [string]::IsNullOrWhiteSpace($controls.LastNameBox.Text)) {
            Update-Status "First Name and Last Name are required for preview" "WARN"
            return
        }
        
        $previewText = "User Preview:`n"
        $previewText += "Name: $($controls.FirstNameBox.Text) $($controls.LastNameBox.Text)`n"
        $previewText += "Email: $($controls.EmailBox.Text)`n"
        $previewText += "UPN: $($controls.UpnBox.Text)`n"
        $previewText += "SAM: $($controls.SamBox.Text)`n"
        $previewText += "Job Title: $($controls.JobTitleBox.Text)`n"
        $previewText += "Department: $($controls.DepartmentBox.Text)`n"
        $previewText += "OU: $($controls.OuComboBox.SelectedItem)"
        
        [System.Windows.MessageBox]::Show($previewText, "User Creation Preview", "OK", "Information")
        Write-EnhancedLog "User preview displayed" "INFO" "Preview"
    }
    catch {
        Write-EnhancedLog "Error showing preview: $($_.Exception.Message)" "ERROR" "Preview"
    }
})

$controls.HelpButton.Add_Click({
    try {
        $helpText = "OnboardingFromJiraGUI Help:`n`n"
        $helpText += "1. Connect to Jira using your credentials`n"
        $helpText += "2. Enter a Jira ticket ID and fetch data`n"
        $helpText += "3. Verify and edit the populated fields`n"
        $helpText += "4. Select the appropriate OU location`n"
        $helpText += "5. Click 'Create AD User' to complete onboarding`n`n"
        $helpText += "Toolbar Functions:`n"
        $helpText += "- Refresh: Reset the Jira connection`n"
        $helpText += "- Clear Form: Clear all input fields`n"
        $helpText += "- Validate: Check field validation`n"
        $helpText += "- Preview: Show user creation preview`n`n"
        $helpText += "Progress Indicators show workflow status`n"
        $helpText += "Statistics panel shows session metrics"
        
        [System.Windows.MessageBox]::Show($helpText, "Help - OnboardingFromJiraGUI", "OK", "Information")
        Write-EnhancedLog "Help dialog displayed" "INFO" "Help"
    }
    catch {
        Write-EnhancedLog "Error showing help: $($_.Exception.Message)" "ERROR" "Help"
    }
})

# Log Control Event Handlers
$controls.LogLevelFilter.Add_SelectionChanged({
    try {
        Update-LogDisplay
    }
    catch {
        Write-Log "Error updating log filter: $($_.Exception.Message)" "WARN"
    }
})

$controls.LogSearchBox.Add_TextChanged({
    try {
        Update-LogDisplay
    }
    catch {
        Write-Log "Error updating log search: $($_.Exception.Message)" "WARN"
    }
})

$controls.ClearLogButton.Add_Click({
    try {
        $script:LogEntries.Clear()
        $controls.LogBox.Text = ""
        Write-EnhancedLog "Log cleared by user" "INFO" "Log"
    }
    catch {
        Write-Log "Error clearing log: $($_.Exception.Message)" "WARN"
    }
})

$controls.ExportLogButton.Add_Click({
    try {
        $timestamp = Get-Date -Format "yyyyMMdd-HHmmss"
        $exportPath = "C:\Temp\OnboardingLog-Export-$timestamp.txt"
        
        # Ensure directory exists
        $exportDir = Split-Path $exportPath -Parent
        if (-not (Test-Path $exportDir)) {
            New-Item -ItemType Directory -Path $exportDir -Force | Out-Null
        }
        
        $logContent = ($script:LogEntries | ForEach-Object { $_.DisplayText }) -join "`r`n"
        Set-Content -Path $exportPath -Value $logContent -Encoding UTF8
        
        Update-Status "Log exported to: $exportPath" "SUCCESS"
        Write-EnhancedLog "Log exported to $exportPath" "INFO" "Export"
    }
    catch {
        Update-Status "Error exporting log: $($_.Exception.Message)" "ERROR"
        Write-Log "Error exporting log: $($_.Exception.Message)" "ERROR"
    }
})

# Add event handlers for automatic UPN and Email generation when SAM changes
# Note: UPN should remain in firstname.lastname format, not follow SAM changes
$controls.SamBox.Add_TextChanged({
    # SAM changes don't affect UPN - UPN should stay in firstname.lastname format
    # This event handler is kept for potential future use but doesn't modify UPN/Email
})

# Add event handlers for automatic SAM, UPN and Email generation when names change
$controls.FirstNameBox.Add_TextChanged({
    if ($controls.FirstNameBox.IsEnabled -and $controls.LastNameBox.IsEnabled -and 
        -not [string]::IsNullOrWhiteSpace($controls.FirstNameBox.Text) -and 
        -not [string]::IsNullOrWhiteSpace($controls.LastNameBox.Text)) {
        try {
            $firstName = $controls.FirstNameBox.Text.Trim()
            $lastName = $controls.LastNameBox.Text.Trim()
            
            $generatedSam = Get-SamAccountName -FirstName $firstName -LastName $lastName
            $controls.SamBox.Text = $generatedSam
            
            # Generate UPN in firstname.lastname format from current name fields
            $baseUpn = Get-UpnFromNames -FirstName $firstName -LastName $lastName
            $emailAddress = Get-EmailAddress -Upn $baseUpn
            $controls.UpnBox.Text = $emailAddress
            $controls.EmailBox.Text = $emailAddress
        }
        catch {
            Write-Log "Error auto-generating account details from name change: $($_.Exception.Message)" "DEBUG"
        }
    }
})

$controls.LastNameBox.Add_TextChanged({
    if ($controls.FirstNameBox.IsEnabled -and $controls.LastNameBox.IsEnabled -and 
        -not [string]::IsNullOrWhiteSpace($controls.FirstNameBox.Text) -and 
        -not [string]::IsNullOrWhiteSpace($controls.LastNameBox.Text)) {
        try {
            $firstName = $controls.FirstNameBox.Text.Trim()
            $lastName = $controls.LastNameBox.Text.Trim()
            
            $generatedSam = Get-SamAccountName -FirstName $firstName -LastName $lastName
            $controls.SamBox.Text = $generatedSam
            
            # Generate UPN in firstname.lastname format from current name fields
            $baseUpn = Get-UpnFromNames -FirstName $firstName -LastName $lastName
            $emailAddress = Get-EmailAddress -Upn $baseUpn
            $controls.UpnBox.Text = $emailAddress
            $controls.EmailBox.Text = $emailAddress
        }
        catch {
            Write-Log "Error auto-generating account details from name change: $($_.Exception.Message)" "DEBUG"
        }
    }
})

$controls.CreateUserButton.Add_Click({
    Update-WorkflowProgress -StepName "CreateUser" -Status "InProgress"
    Set-UiLock -locked $true
    try {
        if ($controls.FirstNameBox.Text -like "MISSING*" -or [string]::IsNullOrWhiteSpace($controls.FirstNameBox.Text) -or [string]::IsNullOrWhiteSpace($controls.LastNameBox.Text) -or !$controls.OuComboBox.SelectedItem) { throw "First Name, Last Name, and OU are required." }

        $modeText = if ($script:SimulationMode) { "SIMULATE" } else { "CREATE" }
        if (([System.Windows.MessageBox]::Show("Confirm user details?", "Confirm $modeText", "OKCancel", "Question")) -ne "OK") { throw "User creation cancelled." }
        
        Update-Status "Starting user $($modeText.ToLower()) process..."
        Write-EnhancedLog "Starting user creation process for $($controls.FirstNameBox.Text) $($controls.LastNameBox.Text)" "INFO" "UserCreation"
        
        # Use the SAM account name from the UI (user can edit it if needed)
        $samAccountName = $controls.SamBox.Text
        if ([string]::IsNullOrWhiteSpace($samAccountName)) {
            $samAccountName = Get-SamAccountName -FirstName $controls.FirstNameBox.Text -LastName $controls.LastNameBox.Text
            $controls.SamBox.Text = $samAccountName
        }
        
        # Use UPN and Email from UI fields, generate if empty
        $upn = $controls.UpnBox.Text
        if ([string]::IsNullOrWhiteSpace($upn)) {
            # Generate UPN in firstname.lastname format
            $baseUpn = Get-UpnFromNames -FirstName $controls.FirstNameBox.Text -LastName $controls.LastNameBox.Text
            $upn = Get-EmailAddress -Upn $baseUpn
            $controls.UpnBox.Text = $upn
        }
        
        $email = $controls.EmailBox.Text
        if ([string]::IsNullOrWhiteSpace($email)) {
            # Generate Email in firstname.lastname format
            $baseUpn = Get-UpnFromNames -FirstName $controls.FirstNameBox.Text -LastName $controls.LastNameBox.Text
            $email = Get-EmailAddress -Upn $baseUpn
            $controls.EmailBox.Text = $email
        }
        
        $userParams = @{
            Name = "$($controls.FirstNameBox.Text) $($controls.LastNameBox.Text)"
            GivenName = $controls.FirstNameBox.Text
            Surname = $controls.LastNameBox.Text
            DisplayName = "$($controls.FirstNameBox.Text) $($controls.LastNameBox.Text)"
            UserPrincipalName = $upn
            SamAccountName = $samAccountName
            EmailAddress = $email
            AccountPassword = (New-SecurePassword)
            Path = $controls.OuComboBox.SelectedItem
            Enabled = $true
            Department = $controls.DepartmentBox.Text
            Title = $controls.JobTitleBox.Text
            ErrorAction = 'Stop'
        }
        
        New-ADUser-Simulated -UserParams $userParams
        Update-Status "User '$($userParams.Name)' processed." "SUCCESS"
        Update-SessionStatistics -Event "UserCreated"
        
        if ($controls.CopyGroupsComboBox.Text -eq "Yes" -and -not [string]::IsNullOrWhiteSpace($controls.ModelAccountBox.Text)) {
            $modelUserGroups = (Get-ADUser-Simulated -Identity $controls.ModelAccountBox.Text -Properties MemberOf).MemberOf
            $modelUserGroups | ForEach-Object { Add-ADGroupMember-Simulated -Identity $_ -Members $samAccountName }
            Update-Status "Copied groups from $($controls.ModelAccountBox.Text)." "INFO"
            Write-EnhancedLog "Copied groups from model account: $($controls.ModelAccountBox.Text)" "INFO" "UserCreation"
        }
        
        # Try to add comment to Jira ticket
        try {
            $automationTag = if ($script:SimulationMode) { "[AUTOMATION - SIMULATION]" } else { "[AUTOMATION]" }
            $jiraComment = "$automationTag AD user processed. Username: $samAccountName"
            
            # Validate session and connection before commenting
            if (-not (Test-SessionValid) -or -not (Test-JiraConnection)) {
                Write-EnhancedLog "Jira session expired or connection lost, cannot add comment to ticket" "WARN" "Jira"
                Update-Status "User created successfully, but could not comment on Jira ticket (connection issue)" "WARN"
            } else {
                # Try to add comment with better error handling
                $ticketId = $controls.TicketIdBox.Text
                Write-EnhancedLog "Attempting to add comment to ticket: $ticketId" "DEBUG" "Jira"
                
                # Validate ticket ID format
                if ([string]::IsNullOrWhiteSpace($ticketId)) {
                    Write-EnhancedLog "Ticket ID is empty, cannot add comment" "WARN" "Jira"
                    Update-Status "User created successfully, but no ticket ID available for comment" "WARN"
                } else {
                    Add-JiraIssueComment -Issue $ticketId -Comment $jiraComment -Credential $script:JiraCredential -ErrorAction Stop
                    Update-Status "Successfully commented on Jira ticket." "SUCCESS"
                    Write-EnhancedLog "Successfully added comment to Jira ticket $ticketId" "INFO" "Jira"
                }
            }
        }
        catch {
            Write-EnhancedLog "Failed to add comment to Jira ticket: $($_.Exception.Message)" "WARN" "Jira"
            Update-Status "User created successfully, but could not comment on Jira ticket" "WARN"
        }
        
        Update-WorkflowProgress -StepName "CreateUser" -Status "Completed"
        Write-EnhancedLog "User creation completed successfully for $($userParams.Name)" "INFO" "UserCreation"

    } catch {
        Update-Status "Operation failed: $($_.Exception.Message)" "ERROR"
        Update-WorkflowProgress -StepName "CreateUser" -Status "Error"
        Update-SessionStatistics -Event "ErrorOccurred"
        Write-EnhancedLog "User creation failed: $($_.Exception.Message)" "ERROR" "UserCreation"
        
        # Try to add error comment to Jira ticket
        try {
            if (Test-SessionValid -and Test-JiraConnection) {
                $ticketId = $controls.TicketIdBox.Text
                if (-not [string]::IsNullOrWhiteSpace($ticketId)) {
                    $errorComment = "[AUTOMATION] ERROR: $($_.Exception.Message)"
                    Add-JiraIssueComment -Issue $ticketId -Comment $errorComment -Credential $script:JiraCredential -ErrorAction Stop
                    Write-EnhancedLog "Added error comment to Jira ticket $ticketId" "INFO" "Jira"
                } else {
                    Write-EnhancedLog "Cannot add error comment - no ticket ID available" "DEBUG" "Jira"
                }
            } else {
                Write-EnhancedLog "Cannot add error comment to Jira ticket - session expired or connection lost" "WARN" "Jira"
            }
        } 
        catch {
            Write-EnhancedLog "Failed to add error comment to Jira ticket: $($_.Exception.Message)" "DEBUG" "Jira"
        }
    } finally {
        Set-UiLock -locked $false
    }
})

#endregion

###############################################################################
#                             INITIALIZE AND RUN                              #
###############################################################################

#region Initialization

function Initialize-EnhancedUI {
    try {
        # Check PowerShell compatibility first
        if (-not (Test-PowerShellCompatibility)) {
            exit 1
        }
        
        Write-Log "Initializing enhanced UI components..." "INFO"
        
        # Initialize all enhanced components
        Initialize-ModernTheme -Window $script:window
        Initialize-FieldValidation
        Initialize-ResponsiveLayout -Window $script:window
        
        # Setup statistics timer
        $script:statsTimer = New-Object System.Windows.Threading.DispatcherTimer
        $script:statsTimer.Interval = [TimeSpan]::FromSeconds(1)
        $script:statsTimer.Add_Tick({ 
            Update-StatisticsDisplay 
        })
        $script:statsTimer.Start()
        
        # Initialize workflow
        Update-WorkflowProgress -StepName "Connection" -Status "Pending"
        
        Write-EnhancedLog "Enhanced UI initialization completed" "INFO" "System"
    }
    catch {
        Write-Log "Error initializing enhanced UI: $($_.Exception.Message)" "ERROR"
    }
}

# Populate OU ComboBox
$OUList | ForEach-Object { $controls.OuComboBox.Items.Add($_) }
if ($controls.OuComboBox.Items.Count -gt 0) { $controls.OuComboBox.SelectedIndex = 0 }

# Update button text based on simulation mode
if ($script:SimulationMode) { $controls.CreateUserButton.Content = "Simulate AD User Creation" }

# Initialize enhanced UI
Initialize-EnhancedUI

# Session timer
$sessionTimer = New-Object System.Windows.Threading.DispatcherTimer
$sessionTimer.Interval = [TimeSpan]::FromSeconds(30)
$sessionTimer.Add_Tick({
    if ($script:JiraCredential -and (Test-SessionValid)) {
        $remaining = $script:Config.Security.SessionTimeoutMinutes - ((Get-Date) - $script:SessionStartTime).TotalMinutes
        $controls.SessionInfo.Text = "Session valid ($([math]::Round($remaining, 0)) minutes left)"
    } else {
        $controls.SessionInfo.Text = "Not Connected"
        $controls.ConnectButton.IsEnabled = $true
    }
})
$sessionTimer.Start()

Update-Status "Welcome! Please connect to Jira to begin." "INFO"
Write-EnhancedLog "OnboardingFromJiraGUI started successfully" "INFO" "System"

# Initialize enhanced systems
# Check configuration compatibility
$configCheck = Test-ConfigurationCompatibility -Config $script:Config
if (-not $configCheck.IsCompatible) {
    Write-AppLog "$($script:Icons.Warning) Configuration compatibility issues detected. Updating configuration..." -Level 'WARN'
    $script:Config = Update-ConfigurationToLatest -Config $script:Config
}

Initialize-Monitoring

$null = $window.ShowDialog()

# Cleanup
$sessionTimer.Stop()
if ($script:statsTimer -ne $null) {
    $script:statsTimer.Stop()
}

#endregion


<!--  
RSS generated by JIRA (1001.0.0-SNAPSHOT#100286-rev:7c76895fd78a7be0c0256e674ee8e2d573bbbdc2) at Tue Jul 01 17:36:54 UTC 2025

It is possible to restrict the fields that are returned in this document by specifying the 'field' parameter in your request.
For example, to request only the issue key and summary add field=key&field=summary to the URL of your request.
 -->
<rss version="0.92">
<channel>
<title>Jira</title>
<link>https://jeragm.atlassian.net</link>
<description>This file is an XML representation of an issue</description>
<language>en-us</language>
<build-info>
<version>1001.0.0-SNAPSHOT</version>
<build-number>100286</build-number>
<build-date>30-06-2025</build-date>
</build-info>
<item>
<title>[IT-46441] Onboarding Request - Tom Bell - IT Accounts Creation</title>
<link>https://jeragm.atlassian.net/browse/IT-46441</link>
<project id="10045" key="IT">IT Support</project>
<description><p>Please complete the following activities to close this task:</p> <ul class="alternate" type="square"> <li>Create AD account and update the form with new joiner’s username &amp; email address.</li> <li>Add the following AAD groups:</li> <li>Atlassian Suite Users</li> <li>MetaCompliance Users</li> <li>EXT_JERAGM_MIMECAST_TO_DARKTRACE_MIGRATION</li> <li>Pass-through Auth</li> <li>ASG_U_Keeper_SSO_Users</li> <li>ASG_U_BeyondTrust_Workstyle_StandardUser</li> </ul> <ul class="alternate" type="square"> <li>Add new joiner to Allegro Notification distribution list (if user has Allegro access)</li> <li>Add Teams Voice Number</li> </ul> <p>Please see link below for detailed instructions: <a href="https://jeragm.atlassian.net/wiki/spaces/IO/pages/**********/Employee+Onboarding" class="external-link" rel="nofollow noreferrer">https://jeragm.atlassian.net/wiki/spaces/IO/pages/**********/Employee+Onboarding</a></p></description>
<environment/>
<key id="110978">IT-46441</key>
<summary>Onboarding Request - Tom Bell - IT Accounts Creation</summary>
<type id="10002" iconUrl="https://jeragm.atlassian.net/rest/api/2/universal_avatar/view/type/issuetype/avatar/10316?size=medium">Sub-task</type>
<parent id="110836">IT-46396</parent>
<priority id="4" iconUrl="https://jeragm.atlassian.net/images/icons/priorities/low.svg">Low</priority>
<status id="10001" iconUrl="https://jeragm.atlassian.net/images/icons/status_generic.gif" description="">Done</status>
<statusCategory id="3" key="done" colorName="green"/>
<resolution id="10000">Done</resolution>
<assignee accountid="5fa90a7d048052006b41a362">Raymond Mukada</assignee>
<reporter accountid="557058:f58131cb-b67d-43c7-b30d-6b58d40bd077">Automation for Jira</reporter>
<labels> </labels>
<created>Wed, 11 Jun 2025 08:13:28 +0100</created>
<updated>Thu, 19 Jun 2025 12:30:57 +0100</updated>
<resolved>Thu, 19 Jun 2025 12:30:56 +0100</resolved>
<component>Onboarding</component>
<due/>
<watches>2</watches>
<comments>
<comment id="205178" author="5fa90a7d048052006b41a362" created="Thu, 19 Jun 2025 12:26:16 +0100"><p>User AD account created and groups added</p></comment>
<comment id="205179" author="5fa90a7d048052006b41a362" created="Thu, 19 Jun 2025 12:30:56 +0100"><p>Teams voice recording is now enabled +44 **********</p></comment>
</comments>
<attachments> </attachments>
<subtasks> </subtasks>
<customfields>
<customfield id="customfield_10381" key="com.atlassian.jira.plugin.system.customfieldtypes:textfield">
<customfieldname>Allegro-Email</customfieldname>
<customfieldvalues>
<customfieldvalue><a href='mailto:<EMAIL>'><EMAIL></a></customfieldvalue>
</customfieldvalues>
</customfield>
<customfield id="customfield_10380" key="com.atlassian.jira.plugin.system.customfieldtypes:textfield">
<customfieldname>Allegro-Username</customfieldname>
<customfieldvalues>
<customfieldvalue>JERAGM\bellto</customfieldvalue>
</customfieldvalues>
</customfield>
<customfield id="customfield_10042" key="com.atlassian.servicedesk.approvals-plugin:sd-approvals">
<customfieldname>Approvals</customfieldname>
<customfieldvalues> </customfieldvalues>
</customfield>
<customfield id="customfield_10249" key="com.atlassian.plugins.atlassian-connect-plugin:com.herocoders.plugins.jira.issuechecklist-free__issue-checklist-progress-field">
<customfieldname>Checklist Progress</customfieldname>
<customfieldvalues>
<customfieldvalue/>
</customfieldvalues>
</customfield>
<customfield id="customfield_10250" key="com.atlassian.plugins.atlassian-connect-plugin:com.herocoders.plugins.jira.issuechecklist-free__issue-checklist-progress-percent-field">
<customfieldname>Checklist Progress %</customfieldname>
<customfieldvalues>
<customfieldvalue>0.0</customfieldvalue>
</customfieldvalues>
</customfield>
<customfield id="customfield_10072" key="com.atlassian.jira.plugin.system.customfieldtypes:select">
<customfieldname>Classification</customfieldname>
<customfieldvalues>
<customfieldvalue key="10130">
<![CDATA[ Under Investigation ]]>
</customfieldvalue>
</customfieldvalues>
</customfield>
<customfield id="customfield_10783" key="com.atlassian.jira.plugin.system.customfieldtypes:select">
<customfieldname>Complexity</customfieldname>
<customfieldvalues>
<customfieldvalue key="11864">
<![CDATA[ Low ]]>
</customfieldvalue>
</customfieldvalues>
</customfield>
<customfield id="customfield_10626" key="com.atlassian.jira.plugin.system.customfieldtypes:select">
<customfieldname>Current Impact</customfieldname>
<customfieldvalues>
<customfieldvalue key="11278">
<![CDATA[ TBD ]]>
</customfieldvalue>
</customfieldvalues>
</customfield>
<customfield id="customfield_10631" key="com.atlassian.jira.plugin.system.customfieldtypes:select">
<customfieldname>Current Probability</customfieldname>
<customfieldvalues>
<customfieldvalue key="11282">
<![CDATA[ TBD ]]>
</customfieldvalue>
</customfieldvalues>
</customfield>
<customfield id="customfield_10684" key="com.atlassian.jira.plugin.system.customfieldtypes:select">
<customfieldname>Current Risk Score</customfieldname>
<customfieldvalues>
<customfieldvalue key="11712">
<![CDATA[ TBD ]]>
</customfieldvalue>
</customfieldvalues>
</customfield>
<customfield id="customfield_10120" key="com.atlassian.jira.plugin.system.customfieldtypes:select">
<customfieldname>Department</customfieldname>
<customfieldvalues>
<customfieldvalue key="10214">
<![CDATA[ Coal ]]>
</customfieldvalue>
</customfieldvalues>
</customfield>
<customfield id="customfield_10000" key="com.atlassian.jira.plugins.jira-development-integration-plugin:devsummarycf">
<customfieldname>Development</customfieldname>
<customfieldvalues> </customfieldvalues>
</customfield>
<customfield id="customfield_10344" key="com.atlassian.jira.plugin.system.customfieldtypes:datepicker">
<customfieldname>Effective Date</customfieldname>
<customfieldvalues>
<customfieldvalue>Mon, 23 Jun 2025 00:00:00 +0000</customfieldvalue>
</customfieldvalues>
</customfield>
<customfield id="customfield_10342" key="com.atlassian.jira.plugin.system.customfieldtypes:select">
<customfieldname>Employee Type</customfieldname>
<customfieldvalues>
<customfieldvalue key="10659">
<![CDATA[ JERAGM Employee ]]>
</customfieldvalue>
</customfieldvalues>
</customfield>
<customfield id="customfield_10304" key="com.atlassian.jira.plugin.system.customfieldtypes:textfield">
<customfieldname>First Name</customfieldname>
<customfieldvalues>
<customfieldvalue>Tom</customfieldvalue>
</customfieldvalues>
</customfield>
<customfield id="customfield_10238" key="com.atlassian.jira.plugin.system.customfieldtypes:textfield">
<customfieldname>Job Title</customfieldname>
<customfieldvalues>
<customfieldvalue>Head of Met Coal Origination</customfieldvalue>
</customfieldvalues>
</customfield>
<customfield id="customfield_10305" key="com.atlassian.jira.plugin.system.customfieldtypes:textfield">
<customfieldname>Last Name</customfieldname>
<customfieldvalues>
<customfieldvalue>Bell</customfieldvalue>
</customfieldvalues>
</customfield>
<customfield id="customfield_10226" key="com.atlassian.jira.plugins.proforma-managed-fields:forms-locked-field-cftype">
<customfieldname>Locked forms</customfieldname>
<customfieldvalues> </customfieldvalues>
</customfield>
<customfield id="customfield_10343" key="com.atlassian.jira.plugin.system.customfieldtypes:textfield">
<customfieldname>Model Account</customfieldname>
<customfieldvalues>
<customfieldvalue>Dominic Lim</customfieldvalue>
</customfieldvalues>
</customfield>
<customfield id="customfield_10115" key="com.atlassian.jira.plugin.system.customfieldtypes:select">
<customfieldname>Office Location</customfieldname>
<customfieldvalues>
<customfieldvalue key="10183">
<![CDATA[ United Kingdom ]]>
</customfieldvalue>
</customfieldvalues>
</customfield>
<customfield id="customfield_10224" key="com.atlassian.jira.plugins.proforma-managed-fields:forms-open-field-cftype">
<customfieldname>Open forms</customfieldname>
<customfieldvalues> </customfieldvalues>
</customfield>
<customfield id="customfield_10004" key="com.atlassian.servicedesk:sd-customer-organizations">
<customfieldname>Organizations</customfieldname>
<customfieldvalues> </customfieldvalues>
</customfield>
<customfield id="customfield_10011" key="com.pyxis.greenhopper.jira:gh-lexo-rank">
<customfieldname>Rank</customfieldname>
<customfieldvalues>
<customfieldvalue>1|i037af:</customfieldvalue>
</customfieldvalues>
</customfield>
<customfield id="customfield_10064" key="com.atlassian.servicedesk.servicedesk-************************:sd-request-language">
<customfieldname>Request language</customfieldname>
<customfieldvalues> </customfieldvalues>
</customfield>
<customfield id="customfield_10029" key="com.atlassian.servicedesk:sd-request-participants">
<customfieldname>Request participants</customfieldname>
<customfieldvalues> </customfieldvalues>
</customfield>
<customfield id="customfield_10628" key="com.atlassian.jira.plugin.system.customfieldtypes:select">
<customfieldname>Residual Impact</customfieldname>
<customfieldvalues>
<customfieldvalue key="11283">
<![CDATA[ TBD ]]>
</customfieldvalue>
</customfieldvalues>
</customfield>
<customfield id="customfield_10629" key="com.atlassian.jira.plugin.system.customfieldtypes:select">
<customfieldname>Residual Probability</customfieldname>
<customfieldvalues>
<customfieldvalue key="11284">
<![CDATA[ TBD ]]>
</customfieldvalue>
</customfieldvalues>
</customfield>
<customfield id="customfield_10686" key="com.atlassian.jira.plugin.system.customfieldtypes:select">
<customfieldname>Residual Risk Score</customfieldname>
<customfieldvalues>
<customfieldvalue key="11714">
<![CDATA[ TBD ]]>
</customfieldvalue>
</customfieldvalues>
</customfield>
<customfield id="customfield_10181" key="com.atlassian.jira.plugin.system.customfieldtypes:multiselect">
<customfieldname>Resolution Workstream</customfieldname>
<customfieldvalues>
<customfieldvalue key="10591">
<![CDATA[ End User Support (IT Ops) ]]>
</customfieldvalue>
</customfieldvalues>
</customfield>
<customfield id="customfield_10651" key="com.atlassian.jira.plugin.system.customfieldtypes:userpicker">
<customfieldname>Risk Assessment Approver</customfieldname>
<customfieldvalues>
<customfieldvalue>712020:4546b468-9348-49fd-8ad5-95059dd6c6b2</customfieldvalue>
</customfieldvalues>
</customfield>
<customfield id="customfield_10010" key="com.pyxis.greenhopper.jira:gh-sprint">
<customfieldname>Sprint</customfieldname>
<customfieldvalues> </customfieldvalues>
</customfield>
<customfield id="customfield_10225" key="com.atlassian.jira.plugins.proforma-managed-fields:forms-submitted-field-cftype">
<customfieldname>Submitted forms</customfieldname>
<customfieldvalues> </customfieldvalues>
</customfield>
<customfield id="customfield_10627" key="com.atlassian.jira.plugin.system.customfieldtypes:select">
<customfieldname>Target Impact</customfieldname>
<customfieldvalues>
<customfieldvalue key="11281">
<![CDATA[ TBD ]]>
</customfieldvalue>
</customfieldvalues>
</customfield>
<customfield id="customfield_10630" key="com.atlassian.jira.plugin.system.customfieldtypes:select">
<customfieldname>Target Probability</customfieldname>
<customfieldvalues>
<customfieldvalue key="11280">
<![CDATA[ TBD ]]>
</customfieldvalue>
</customfieldvalues>
</customfield>
<customfield id="customfield_10685" key="com.atlassian.jira.plugin.system.customfieldtypes:select">
<customfieldname>Target Risk Score</customfieldname>
<customfieldvalues>
<customfieldvalue key="11713">
<![CDATA[ TBD ]]>
</customfieldvalue>
</customfieldvalues>
</customfield>
<customfield id="customfield_10063" key="com.atlassian.servicedesk:sd-sla-field">
<customfieldname>Time to approve normal change</customfieldname>
<customfieldvalues> </customfieldvalues>
</customfield>
<customfield id="customfield_10062" key="com.atlassian.servicedesk:sd-sla-field">
<customfieldname>Time to close after resolution</customfieldname>
<customfieldvalues> </customfieldvalues>
</customfield>
<customfield id="customfield_10061" key="com.atlassian.servicedesk:sd-sla-field">
<customfieldname>Time to first response</customfieldname>
<customfieldvalues> </customfieldvalues>
</customfield>
<customfield id="customfield_10060" key="com.atlassian.servicedesk:sd-sla-field">
<customfieldname>Time to resolution</customfieldname>
<customfieldvalues> </customfieldvalues>
</customfield>
<customfield id="customfield_10227" key="com.atlassian.jira.plugins.proforma-managed-fields:forms-total-field-cftype">
<customfieldname>Total forms</customfieldname>
<customfieldvalues> </customfieldvalues>
</customfield>
<customfield id="customfield_10081" key="com.atlassian.jira.plugin.system.customfieldtypes:select">
<customfieldname>UAT Required</customfieldname>
<customfieldvalues>
<customfieldvalue key="10139">
<![CDATA[ TBC ]]>
</customfieldvalue>
</customfieldvalues>
</customfield>
<customfield id="customfield_10012" key="com.atlassian.jira.ext.charting:firstresponsedate">
<customfieldname>[CHART] Date of First Response</customfieldname>
<customfieldvalues>
<customfieldvalue>Thu, 19 Jun 2025 11:26:16 +0000</customfieldvalue>
</customfieldvalues>
</customfield>
<customfield id="customfield_10013" key="com.atlassian.jira.ext.charting:timeinstatus">
<customfieldname>[CHART] Time in Status</customfieldname>
<customfieldvalues> </customfieldvalues>
</customfield>
</customfields>
</item>
</channel>
</rss>

Ticket 3 :

This XML file does not appear to have any style information associated with it. The document tree is shown below.
<!--  
RSS generated by JIRA (1001.0.0-SNAPSHOT#100286-rev:7c76895fd78a7be0c0256e674ee8e2d573bbbdc2) at Tue Jul 01 18:38:00 UTC 2025

It is possible to restrict the fields that are returned in this document by specifying the 'field' parameter in your request.
For example, to request only the issue key and summary add field=key&field=summary to the URL of your request.
 -->
<rss version="0.92">
<channel>
<title>Jira</title>
<link>https://jeragm.atlassian.net</link>
<description>This file is an XML representation of an issue</description>
<language>en-us</language>
<build-info>
<version>1001.0.0-SNAPSHOT</version>
<build-number>100286</build-number>
<build-date>30-06-2025</build-date>
</build-info>
<item>
<title>[IT-46396] Onboarding Request - Tom Bell</title>
<link>https://jeragm.atlassian.net/browse/IT-46396</link>
<project id="10045" key="IT">IT Support</project>
<description><p>New Joiner Name: Tom Bell<br/> Job Title: Head of Met Coal Origination<br/> Department: Coal<br/> Line Manager: Alex Baileff<br/> Reporter: Susan Scanlan</p> <p>Applications Requested: <br/> Model Account: Dominic Lim</p></description>
<environment/>
<key id="110836">IT-46396</key>
<summary>Onboarding Request - Tom Bell</summary>
<type id="10042" iconUrl="https://jeragm.atlassian.net/rest/api/2/universal_avatar/view/type/issuetype/avatar/10533?size=medium">Service Request with Approvals</type>
<priority id="4" iconUrl="https://jeragm.atlassian.net/images/icons/priorities/low.svg">Low</priority>
<status id="3" iconUrl="https://jeragm.atlassian.net/images/icons/statuses/inprogress.png" description="This work item is being actively worked on at the moment by the assignee.">In Progress</status>
<statusCategory id="4" key="indeterminate" colorName="yellow"/>
<resolution id="-1">Unresolved</resolution>
<assignee accountid="5fa90a7d048052006b41a362">Raymond Mukada</assignee>
<reporter accountid="712020:59a9fab6-65cd-4aa6-93df-1b32897a040a">Susan Scanlan</reporter>
<labels> </labels>
<created>Tue, 10 Jun 2025 17:15:28 +0100</created>
<updated>Wed, 25 Jun 2025 16:49:17 +0100</updated>
<component>Onboarding</component>
<due/>
<watches>1</watches>
<comments>
<comment id="205444" author="712020:59a9fab6-65cd-4aa6-93df-1b32897a040a" created="Mon, 23 Jun 2025 09:50:53 +0100"><p>Hi Support Team, </p> <p>Tom Bell’s join date has been updated to Thursday 3rd July if you could kindly update your records and for your awareness for onboarding. </p> <p>Many thanks, </p> <p>Susan. </p> <p> <b><font color="#8096A3"> <b>Susan Scanlan, HR &amp; Admin Generalist</b></font></b><font color="#8096A3"></font> </p> <p><font color="#8096A3">JERA Global Markets UK Ltd</font> </p> <p><font color="#8096A3">M: +44 7514 800 596</font></p></comment>
<comment id="205448" author="5d2323782c5a110c1d47374c" created="Mon, 23 Jun 2025 10:38:59 +0100"><p>This issue has been waiting for support for 5 days.<br/> Please have a look at this issue or change its status if you are waiting for more information.</p></comment>
</comments>
<attachments> </attachments>
<subtasks>
<subtask id="110969">IT-46438</subtask>
<subtask id="110972">IT-46439</subtask>
<subtask id="110975">IT-46440</subtask>
<subtask id="110978">IT-46441</subtask>
<subtask id="110981">IT-46442</subtask>
</subtasks>
<customfields>
<customfield id="customfield_10042" key="com.atlassian.servicedesk.approvals-plugin:sd-approvals">
<customfieldname>Approvals</customfieldname>
<customfieldvalues> </customfieldvalues>
</customfield>
<customfield id="customfield_10043" key="com.atlassian.jira.plugin.system.customfieldtypes:multiuserpicker">
<customfieldname>Approvers</customfieldname>
<customfieldvalues>
<customfieldvalue>
<![CDATA[ Alex.Baileff ]]>
</customfieldvalue>
</customfieldvalues>
</customfield>
<customfield id="customfield_10354" key="com.atlassian.jira.plugin.system.customfieldtypes:textfield">
<customfieldname>Binding Transactions - Products</customfieldname>
<customfieldvalues>
<customfieldvalue>Met and Thermal Physical Coal products</customfieldvalue>
</customfieldvalues>
</customfield>
<customfield id="customfield_10347" key="com.atlassian.jira.plugin.system.customfieldtypes:select">
<customfieldname>Call Recording Required?</customfieldname>
<customfieldvalues>
<customfieldvalue key="10668">
<![CDATA[ Yes ]]>
</customfieldvalue>
</customfieldvalues>
</customfield>
<customfield id="customfield_10249" key="com.atlassian.plugins.atlassian-connect-plugin:com.herocoders.plugins.jira.issuechecklist-free__issue-checklist-progress-field">
<customfieldname>Checklist Progress</customfieldname>
<customfieldvalues>
<customfieldvalue/>
</customfieldvalues>
</customfield>
<customfield id="customfield_10250" key="com.atlassian.plugins.atlassian-connect-plugin:com.herocoders.plugins.jira.issuechecklist-free__issue-checklist-progress-percent-field">
<customfieldname>Checklist Progress %</customfieldname>
<customfieldvalues>
<customfieldvalue>0.0</customfieldvalue>
</customfieldvalues>
</customfield>
<customfield id="customfield_10072" key="com.atlassian.jira.plugin.system.customfieldtypes:select">
<customfieldname>Classification</customfieldname>
<customfieldvalues>
<customfieldvalue key="10130">
<![CDATA[ Under Investigation ]]>
</customfieldvalue>
</customfieldvalues>
</customfield>
<customfield id="customfield_10352" key="com.atlassian.jira.plugin.system.customfieldtypes:select">
<customfieldname>Commercial Decisions</customfieldname>
<customfieldvalues>
<customfieldvalue key="10694">
<![CDATA[ Yes ]]>
</customfieldvalue>
</customfieldvalues>
</customfield>
<customfield id="customfield_10783" key="com.atlassian.jira.plugin.system.customfieldtypes:select">
<customfieldname>Complexity</customfieldname>
<customfieldvalues>
<customfieldvalue key="11864">
<![CDATA[ Low ]]>
</customfieldvalue>
</customfieldvalues>
</customfield>
<customfield id="customfield_10348" key="com.atlassian.jira.plugin.system.customfieldtypes:select">
<customfieldname>Corporate Mobile Phone?</customfieldname>
<customfieldvalues>
<customfieldvalue key="10670">
<![CDATA[ Yes ]]>
</customfieldvalue>
</customfieldvalues>
</customfield>
<customfield id="customfield_10626" key="com.atlassian.jira.plugin.system.customfieldtypes:select">
<customfieldname>Current Impact</customfieldname>
<customfieldvalues>
<customfieldvalue key="11278">
<![CDATA[ TBD ]]>
</customfieldvalue>
</customfieldvalues>
</customfield>
<customfield id="customfield_10631" key="com.atlassian.jira.plugin.system.customfieldtypes:select">
<customfieldname>Current Probability</customfieldname>
<customfieldvalues>
<customfieldvalue key="11282">
<![CDATA[ TBD ]]>
</customfieldvalue>
</customfieldvalues>
</customfield>
<customfield id="customfield_10684" key="com.atlassian.jira.plugin.system.customfieldtypes:select">
<customfieldname>Current Risk Score</customfieldname>
<customfieldvalues>
<customfieldvalue key="11712">
<![CDATA[ TBD ]]>
</customfieldvalue>
</customfieldvalues>
</customfield>
<customfield id="customfield_10120" key="com.atlassian.jira.plugin.system.customfieldtypes:select">
<customfieldname>Department</customfieldname>
<customfieldvalues>
<customfieldvalue key="10214">
<![CDATA[ Coal ]]>
</customfieldvalue>
</customfieldvalues>
</customfield>
<customfield id="customfield_10000" key="com.atlassian.jira.plugins.jira-development-integration-plugin:devsummarycf">
<customfieldname>Development</customfieldname>
<customfieldvalues> </customfieldvalues>
</customfield>
<customfield id="customfield_10344" key="com.atlassian.jira.plugin.system.customfieldtypes:datepicker">
<customfieldname>Effective Date</customfieldname>
<customfieldvalues>
<customfieldvalue>Fri, 4 Jul 2025 00:00:00 +0000</customfieldvalue>
</customfieldvalues>
</customfield>
<customfield id="customfield_10342" key="com.atlassian.jira.plugin.system.customfieldtypes:select">
<customfieldname>Employee Type</customfieldname>
<customfieldvalues>
<customfieldvalue key="10659">
<![CDATA[ JERAGM Employee ]]>
</customfieldvalue>
</customfieldvalues>
</customfield>
<customfield id="customfield_10393" key="com.atlassian.jira.plugin.system.customfieldtypes:select">
<customfieldname>Financial or Physical Trading?</customfieldname>
<customfieldvalues>
<customfieldvalue key="10789">
<![CDATA[ Physical Trading ]]>
</customfieldvalue>
</customfieldvalues>
</customfield>
<customfield id="customfield_10304" key="com.atlassian.jira.plugin.system.customfieldtypes:textfield">
<customfieldname>First Name</customfieldname>
<customfieldvalues>
<customfieldvalue>Tom</customfieldvalue>
</customfieldvalues>
</customfield>
<customfield id="customfield_10238" key="com.atlassian.jira.plugin.system.customfieldtypes:textfield">
<customfieldname>Job Title</customfieldname>
<customfieldvalues>
<customfieldvalue>Head of Met Coal Origination</customfieldvalue>
</customfieldvalues>
</customfield>
<customfield id="customfield_10305" key="com.atlassian.jira.plugin.system.customfieldtypes:textfield">
<customfieldname>Last Name</customfieldname>
<customfieldvalues>
<customfieldvalue>Bell</customfieldvalue>
</customfieldvalues>
</customfield>
<customfield id="customfield_10226" key="com.atlassian.jira.plugins.proforma-managed-fields:forms-locked-field-cftype">
<customfieldname>Locked forms</customfieldname>
<customfieldvalues> </customfieldvalues>
</customfield>
<customfield id="customfield_10343" key="com.atlassian.jira.plugin.system.customfieldtypes:textfield">
<customfieldname>Model Account</customfieldname>
<customfieldvalues>
<customfieldvalue>Dominic Lim</customfieldvalue>
</customfieldvalues>
</customfield>
<customfield id="customfield_10115" key="com.atlassian.jira.plugin.system.customfieldtypes:select">
<customfieldname>Office Location</customfieldname>
<customfieldvalues>
<customfieldvalue key="10183">
<![CDATA[ United Kingdom ]]>
</customfieldvalue>
</customfieldvalues>
</customfield>
<customfield id="customfield_10224" key="com.atlassian.jira.plugins.proforma-managed-fields:forms-open-field-cftype">
<customfieldname>Open forms</customfieldname>
<customfieldvalues> </customfieldvalues>
</customfield>
<customfield id="customfield_10004" key="com.atlassian.servicedesk:sd-customer-organizations">
<customfieldname>Organizations</customfieldname>
<customfieldvalues>
<customfieldvalue>
<![CDATA[ HR & Admin ]]>
</customfieldvalue>
</customfieldvalues>
</customfield>
<customfield id="customfield_10448" key="com.atlassian.jira.plugin.system.customfieldtypes:select">
<customfieldname>People Manager</customfieldname>
<customfieldvalues>
<customfieldvalue key="11035">
<![CDATA[ Yes ]]>
</customfieldvalue>
</customfieldvalues>
</customfield>
<customfield id="customfield_10011" key="com.pyxis.greenhopper.jira:gh-lexo-rank">
<customfieldname>Rank</customfieldname>
<customfieldvalues>
<customfieldvalue>1|i036y7:</customfieldvalue>
</customfieldvalues>
</customfield>
<customfield id="customfield_10028" key="com.atlassian.servicedesk:vp-origin">
<customfieldname>Request Type</customfieldname>
<customfieldvalues> </customfieldvalues>
</customfield>
<customfield id="customfield_10064" key="com.atlassian.servicedesk.servicedesk-************************:sd-request-language">
<customfieldname>Request language</customfieldname>
<customfieldvalues> </customfieldvalues>
</customfield>
<customfield id="customfield_10029" key="com.atlassian.servicedesk:sd-request-participants">
<customfieldname>Request participants</customfieldname>
<customfieldvalues>
<customfieldvalue>
<![CDATA[ ug:565d0dac-000e-4f67-ac9f-b888525923e8 ]]>
</customfieldvalue>
<customfieldvalue>
<![CDATA[ aa23e789-d498-4100-b19d-c9e7ae16f9c7 ]]>
</customfieldvalue>
<customfieldvalue>
<![CDATA[ support ]]>
</customfieldvalue>
<customfieldvalue>
<![CDATA[ ug:15b6149f-3e70-4d79-9a72-da19d9d5ba4c ]]>
</customfieldvalue>
<customfieldvalue>
<![CDATA[ 712020:59a9fab6-65cd-4aa6-93df-1b32897a040a ]]>
</customfieldvalue>
<customfieldvalue>
<![CDATA[ ug:58265eec-23ef-4544-a9e2-ada717a3f5aa ]]>
</customfieldvalue>
<customfieldvalue>
<![CDATA[ Alex.Baileff ]]>
</customfieldvalue>
</customfieldvalues>
</customfield>
<customfield id="customfield_10628" key="com.atlassian.jira.plugin.system.customfieldtypes:select">
<customfieldname>Residual Impact</customfieldname>
<customfieldvalues>
<customfieldvalue key="11283">
<![CDATA[ TBD ]]>
</customfieldvalue>
</customfieldvalues>
</customfield>
<customfield id="customfield_10629" key="com.atlassian.jira.plugin.system.customfieldtypes:select">
<customfieldname>Residual Probability</customfieldname>
<customfieldvalues>
<customfieldvalue key="11284">
<![CDATA[ TBD ]]>
</customfieldvalue>
</customfieldvalues>
</customfield>
<customfield id="customfield_10686" key="com.atlassian.jira.plugin.system.customfieldtypes:select">
<customfieldname>Residual Risk Score</customfieldname>
<customfieldvalues>
<customfieldvalue key="11714">
<![CDATA[ TBD ]]>
</customfieldvalue>
</customfieldvalues>
</customfield>
<customfield id="customfield_10181" key="com.atlassian.jira.plugin.system.customfieldtypes:multiselect">
<customfieldname>Resolution Workstream</customfieldname>
<customfieldvalues>
<customfieldvalue key="10591">
<![CDATA[ End User Support (IT Ops) ]]>
</customfieldvalue>
</customfieldvalues>
</customfield>
<customfield id="customfield_10651" key="com.atlassian.jira.plugin.system.customfieldtypes:userpicker">
<customfieldname>Risk Assessment Approver</customfieldname>
<customfieldvalues>
<customfieldvalue>712020:4546b468-9348-49fd-8ad5-95059dd6c6b2</customfieldvalue>
</customfieldvalues>
</customfield>
<customfield id="customfield_10010" key="com.pyxis.greenhopper.jira:gh-sprint">
<customfieldname>Sprint</customfieldname>
<customfieldvalues> </customfieldvalues>
</customfield>
<customfield id="customfield_10225" key="com.atlassian.jira.plugins.proforma-managed-fields:forms-submitted-field-cftype">
<customfieldname>Submitted forms</customfieldname>
<customfieldvalues> </customfieldvalues>
</customfield>
<customfield id="customfield_10627" key="com.atlassian.jira.plugin.system.customfieldtypes:select">
<customfieldname>Target Impact</customfieldname>
<customfieldvalues>
<customfieldvalue key="11281">
<![CDATA[ TBD ]]>
</customfieldvalue>
</customfieldvalues>
</customfield>
<customfield id="customfield_10630" key="com.atlassian.jira.plugin.system.customfieldtypes:select">
<customfieldname>Target Probability</customfieldname>
<customfieldvalues>
<customfieldvalue key="11280">
<![CDATA[ TBD ]]>
</customfieldvalue>
</customfieldvalues>
</customfield>
<customfield id="customfield_10685" key="com.atlassian.jira.plugin.system.customfieldtypes:select">
<customfieldname>Target Risk Score</customfieldname>
<customfieldvalues>
<customfieldvalue key="11713">
<![CDATA[ TBD ]]>
</customfieldvalue>
</customfieldvalues>
</customfield>
<customfield id="customfield_10063" key="com.atlassian.servicedesk:sd-sla-field">
<customfieldname>Time to approve normal change</customfieldname>
<customfieldvalues> </customfieldvalues>
</customfield>
<customfield id="customfield_10062" key="com.atlassian.servicedesk:sd-sla-field">
<customfieldname>Time to close after resolution</customfieldname>
<customfieldvalues> </customfieldvalues>
</customfield>
<customfield id="customfield_10061" key="com.atlassian.servicedesk:sd-sla-field">
<customfieldname>Time to first response</customfieldname>
<customfieldvalues> </customfieldvalues>
</customfield>
<customfield id="customfield_10060" key="com.atlassian.servicedesk:sd-sla-field">
<customfieldname>Time to resolution</customfieldname>
<customfieldvalues> </customfieldvalues>
</customfield>
<customfield id="customfield_10227" key="com.atlassian.jira.plugins.proforma-managed-fields:forms-total-field-cftype">
<customfieldname>Total forms</customfieldname>
<customfieldvalues> </customfieldvalues>
</customfield>
<customfield id="customfield_10081" key="com.atlassian.jira.plugin.system.customfieldtypes:select">
<customfieldname>UAT Required</customfieldname>
<customfieldvalues>
<customfieldvalue key="10139">
<![CDATA[ TBC ]]>
</customfieldvalue>
</customfieldvalues>
</customfield>
<customfield id="customfield_10012" key="com.atlassian.jira.ext.charting:firstresponsedate">
<customfieldname>[CHART] Date of First Response</customfieldname>
<customfieldvalues>
<customfieldvalue>Mon, 23 Jun 2025 09:38:59 +0000</customfieldvalue>
</customfieldvalues>
</customfield>
</customfields>
</item>
</channel>
</rss>

#>