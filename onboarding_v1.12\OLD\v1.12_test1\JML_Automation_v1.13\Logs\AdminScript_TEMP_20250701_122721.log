﻿2025-07-01 12:27:21.298 | akinje          | INFO     | JML Automation Script v1.13 starting...
2025-07-01 12:27:21.309 | akinje          | DEBUG    | Script path: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13
2025-07-01 12:27:21.319 | akinje          | DEBUG    | PowerShell version: 5.1.19041.5848
2025-07-01 12:27:21.322 | akinje          | DEBUG    | Execution policy: Bypass
2025-07-01 12:27:21.325 | akinje          | DEBUG    | User: akinje
2025-07-01 12:27:21.328 | akinje          | DEBUG    | Computer: JGM-CG7THR3
2025-07-01 12:27:21.424 | akinje          | INFO     | === JML ENVIRONMENT VALIDATION ===
2025-07-01 12:27:21.430 | akin<PERSON>          | INFO     | Verifying PowerShell Version...
2025-07-01 12:27:21.434 | akinje          | INFO     | === JML ENVIRONMENT VALIDATION ===
2025-07-01 12:27:21.438 | akinje          | INFO     | Verifying PowerShell Version...
2025-07-01 12:27:21.452 | akinje          | WARNING  | WARNING: PowerShell 7 or higher is recommended for optimal performance and security.
2025-07-01 12:27:21.457 | akinje          | INFO     | Current version: PowerShell 5.1.19041.5848
2025-07-01 12:27:21.463 | akinje          | WARNING  | PowerShell 7+ recommended. Current: 5.1.19041.5848
2025-07-01 12:27:21.472 | akinje          | INFO     | Searching for PowerShell 7 installation...
2025-07-01 12:27:21.486 | akinje          | INFO     |   Checking: C:\Program Files\PowerShell\7\pwsh.exe
2025-07-01 12:27:22.125 | akinje          | INFO     |   Found PowerShell 7.5.1 at: C:\Program Files\PowerShell\7\pwsh.exe
2025-07-01 12:27:22.132 | akinje          | INFO     | PowerShell 7 is installed at: C:\Program Files\PowerShell\7\pwsh.exe
2025-07-01 12:27:22.139 | akinje          | INFO     | Options:
2025-07-01 12:27:22.145 | akinje          | INFO     |   1. Continue with current PowerShell 5.x
2025-07-01 12:27:22.153 | akinje          | INFO     |   2. Switch to PowerShell 7 (recommended)
2025-07-01 12:27:22.160 | akinje          | INFO     |   3. Exit and manually switch
2025-07-01 12:27:22.175 | akinje          | INFO     | Select option (1-3) [Default: 2 in 10 seconds]: 
2025-07-01 12:27:32.267 | akinje          | INFO     | 2 (default)
2025-07-01 12:27:32.277 | akinje          | INFO     | === POWERSHELL 7 EXECUTION OPTIONS ===
2025-07-01 12:27:32.282 | akinje          | INFO     | PowerShell 7 is available at: C:\Program Files\PowerShell\7\pwsh.exe
2025-07-01 12:27:32.286 | akinje          | INFO     | How would you like to run the script in PowerShell 7?
2025-07-01 12:27:32.290 | akinje          | INFO     |   [N] New Window - Launch in a new PowerShell 7 window
2025-07-01 12:27:32.292 | akinje          | INFO     |   [C] Current Window - Run PowerShell 7 in this window (default)
2025-07-01 12:27:32.298 | akinje          | INFO     | Choose option (N/C) - Waiting 5 seconds (default: Current Window)...
2025-07-01 12:27:37.312 | akinje          | INFO     | Selected: Current Window (default)
2025-07-01 12:27:37.329 | akinje          | INFO     | Starting PowerShell 7 in current window...
2025-07-01 12:27:37.334 | akinje          | INFO     | Script will continue in PowerShell 7...
