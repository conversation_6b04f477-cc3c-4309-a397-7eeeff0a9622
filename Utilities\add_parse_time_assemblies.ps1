# Add WPF assemblies to the Requires directive
$scriptPath = "OnboardingFromJiraGUI.ps1"
$content = Get-Content $scriptPath -Raw

Write-Host "Adding WPF assemblies to Requires directive..." -ForegroundColor Yellow

# Add assembly requirements at the top
$newRequires = @"
#Requires -Version 5.1

# Load WPF assemblies at parse time
Add-Type -AssemblyName PresentationCore
Add-Type -AssemblyName PresentationFramework  
Add-Type -AssemblyName WindowsBase
Add-Type -AssemblyName System.Windows.Forms
"@

$content = $content -replace '#Requires -Version 5\.1', $newRequires

$content | Set-Content $scriptPath -Encoding UTF8

Write-Host "Added WPF assemblies to parse-time loading" -ForegroundColor Green