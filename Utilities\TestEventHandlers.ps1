# Test script to verify event handler closure logic
Add-Type -AssemblyName PresentationFramework -ErrorAction SilentlyContinue

class TestState {
    [string]$TestProperty = "Initial"
    
    [void] SetTestProperty([string]$value) {
        $this.TestProperty = $value
        Write-Host "Property updated to: $value"
    }
}

class TestView {
    $state
    $textBox
    $window
    
    TestView($testState) {
        $this.state = $testState
    }
    
    Show() {
        try {
            if (-not ([System.Management.Automation.PSTypeName]'System.Windows.Window').Type) {
                Write-Host "WPF not available, testing closure logic only"
                $this.TestClosure()
                return
            }
            
            $this.window = New-Object System.Windows.Window
            $this.window.Title = "Test Event Handlers"
            $this.window.Width = 400; $this.window.Height = 200
            
            $panel = New-Object System.Windows.Controls.StackPanel
            $panel.Margin = 20
            
            $label = New-Object System.Windows.Controls.Label
            $label.Content = "Type something:"
            $panel.Children.Add($label)
            
            $this.textBox = New-Object System.Windows.Controls.TextBox
            $this.textBox.Text = $this.state.TestProperty
            
            # Test the closure pattern
            $capturedState = $this.state
            $capturedTextBox = $this.textBox
            $this.textBox.add_TextChanged({
                try {
                    Write-Host "TextChanged event fired"
                    $capturedState.SetTestProperty($capturedTextBox.Text)
                } catch {
                    Write-Host "Error in TextChanged: $($_.Exception.Message)"
                }
            }.GetNewClosure())
            
            $panel.Children.Add($this.textBox)
            $this.window.Content = $panel
            
            Write-Host "Showing window..."
            $this.window.ShowDialog()
            
        } catch {
            Write-Host "Error creating window: $($_.Exception.Message)"
            Write-Host "Stack: $($_.ScriptStackTrace)"
        }
    }
    
    TestClosure() {
        Write-Host "Testing closure logic without WPF..."
        
        $capturedState = $this.state
        $testClosure = {
            try {
                $capturedState.SetTestProperty("Test Value from Closure")
                Write-Host "Closure test successful!"
            } catch {
                Write-Host "Closure test failed: $($_.Exception.Message)"
            }
        }.GetNewClosure()
        
        & $testClosure
    }
}

# Test the logic
Write-Host "Creating test state..."
$testState = [TestState]::new()

Write-Host "Creating test view..."
$testView = [TestView]::new($testState)

Write-Host "Showing test view..."
$testView.Show()

Write-Host "Test completed."