# Final comprehensive fix for all remaining WPF issues
$scriptPath = "OnboardingFromJiraGUI.ps1"
$content = Get-Content $scriptPath -Raw

Write-Host "Applying final comprehensive WPF fixes..." -ForegroundColor Yellow

# Fix comparisons that use Invoke-Expression - these need to be pre-calculated
$content = $content -replace 'if \(-not \$Control\.ToolTip -or \$Control\.BorderBrush -ne Invoke-Expression "\[System\.Windows\.Media\.Brushes\]::Red"\)', 'if (-not $Control.ToolTip -or $Control.BorderBrush -ne [System.Windows.Media.Brushes]::Red)'

# Fix any remaining Invoke-Expression calls that are problematic in certain contexts
$content = $content -replace 'Invoke-Expression "\[System\.Windows\.Media\.Brushes\]::(\w+)"', '[System.Windows.Media.Brushes]::$1'

# Use a runtime execution block for WPF-dependent code
$wpfBlock = @'
# WPF Types - Execute at runtime after assemblies are loaded
if (-not $global:WPFTypesLoaded) {
    try {
        Add-Type -AssemblyName PresentationCore -ErrorAction Stop
        Add-Type -AssemblyName PresentationFramework -ErrorAction Stop
        Add-Type -AssemblyName WindowsBase -ErrorAction Stop
        $global:WPFTypesLoaded = $true
    } catch {
        Write-Error "Failed to load WPF assemblies: $_"
        return
    }
}
'@

# Add the WPF block near the top after the existing assembly loading
$content = $content -replace '(Write-Host "WPF assemblies loaded successfully" -ForegroundColor Green)', "`$1`n`n$wpfBlock"

$content | Set-Content $scriptPath -Encoding UTF8

Write-Host "Applied final comprehensive WPF fixes" -ForegroundColor Green