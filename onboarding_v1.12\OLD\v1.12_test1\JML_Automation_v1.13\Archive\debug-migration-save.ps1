# Debug Migration Save Issue
Write-Host "=== Debug Migration Save Issue ===" -ForegroundColor Cyan

# Load modules
Import-Module ".\Modules\JML-Configuration.psm1" -Force

# Create test copy
Copy-Item ".\AdminAccountConfig.psd1" ".\debug-test-config.psd1" -Force
Write-Host "Created test config copy" -ForegroundColor Green

# Run migration
Write-Host "`nRunning migration..." -ForegroundColor Yellow
$result = Invoke-ConfigurationMigration -ConfigPath ".\debug-test-config.psd1"

Write-Host "Migration result: $($result.Success)" -ForegroundColor $(if ($result.Success) { "Green" } else { "Red" })

# Check what's in the file
Write-Host "`nChecking file contents..." -ForegroundColor Yellow
if (Test-Path ".\debug-test-config.psd1") {
    $fileSize = (Get-Item ".\debug-test-config.psd1").Length
    Write-Host "File exists, size: $fileSize bytes" -ForegroundColor Green
    
    # Try to read first few lines
    $firstLines = Get-Content ".\debug-test-config.psd1" -TotalCount 10
    Write-Host "First 10 lines:" -ForegroundColor Gray
    $firstLines | ForEach-Object { Write-Host "  $_" -ForegroundColor Gray }
    
    # Try to read last few lines
    $lastLines = Get-Content ".\debug-test-config.psd1" | Select-Object -Last 10
    Write-Host "Last 10 lines:" -ForegroundColor Gray
    $lastLines | ForEach-Object { Write-Host "  $_" -ForegroundColor Gray }
    
    # Check for specific content
    $content = Get-Content ".\debug-test-config.psd1" -Raw
    if ($content -match "ConfigVersion") {
        Write-Host "ConfigVersion found in file" -ForegroundColor Green
    } else {
        Write-Host "ConfigVersion NOT found in file" -ForegroundColor Red
    }
    
    # Try to parse it
    try {
        $testConfig = Import-PowerShellDataFile -Path ".\debug-test-config.psd1"
        Write-Host "File parses successfully" -ForegroundColor Green
        Write-Host "ConfigVersion in parsed data: $($testConfig.ConfigVersion)" -ForegroundColor Green
    } catch {
        Write-Host "File parse error: $($_.Exception.Message)" -ForegroundColor Red
        
        # Check for common syntax issues
        if ($content -match '(?m)^\s*[^#\s].*[^}]\s*$') {
            Write-Host "Possible syntax issue: lines not properly terminated" -ForegroundColor Yellow
        }
        if ($content -match '(?m)^\s*@\{.*[^}]\s*$') {
            Write-Host "Possible syntax issue: hashtable not properly closed" -ForegroundColor Yellow
        }
    }
} else {
    Write-Host "File does not exist!" -ForegroundColor Red
}

Write-Host "`n=== Debug Complete ===" -ForegroundColor Cyan
Write-Host "Test file preserved as: debug-test-config.psd1" -ForegroundColor Gray
