# Configuration Manager Module for Enterprise Onboarding Suite
# Version: 4.0.0

# Module-level variables
$script:EnterpriseConfig = $null
$script:ConfigPath = $null
$script:ConfigWatcher = $null

Function Import-EnterpriseConfig {
    <#
    .SYNOPSIS
        Loads the enterprise configuration from file with environment-specific overrides.
    
    .DESCRIPTION
        Imports the main configuration file and applies environment-specific overrides if available.
        Validates the configuration and sets up file watching for automatic reloading.
    
    .PARAMETER ConfigPath
        Path to the main configuration file. Defaults to Config\EnterpriseConfig.psd1
    
    .PARAMETER Environment
        Environment name for loading environment-specific overrides (Development, Testing, Production)
    
    .PARAMETER EnableFileWatcher
        Enable automatic reloading when configuration file changes
    
    .EXAMPLE
        Import-EnterpriseConfig -Environment "Development"
    #>
    [CmdletBinding()]
    param(
        [string]$ConfigPath = "Config\EnterpriseConfig.psd1",
        [string]$Environment = $null,
        [switch]$EnableFileWatcher
    )
    
    try {
        # Resolve full path
        if (-not [System.IO.Path]::IsPathRooted($ConfigPath)) {
            $script:ConfigPath = Join-Path $PSScriptRoot "..\$ConfigPath"
        } else {
            $script:ConfigPath = $ConfigPath
        }
        
        if (-not (Test-Path $script:ConfigPath)) {
            throw "Configuration file not found: $script:ConfigPath"
        }
        
        Write-Verbose "Loading configuration from: $script:ConfigPath"
        
        # Load base configuration
        $config = Import-PowerShellDataFile -Path $script:ConfigPath
        
        # Load environment-specific overrides if specified
        if ($Environment) {
            $envConfigPath = Join-Path (Split-Path $script:ConfigPath) "EnterpriseConfig.$Environment.psd1"
            if (Test-Path $envConfigPath) {
                Write-Verbose "Loading environment overrides from: $envConfigPath"
                $envConfig = Import-PowerShellDataFile -Path $envConfigPath
                $config = Merge-Configuration -BaseConfig $config -OverrideConfig $envConfig
            } else {
                Write-Warning "Environment configuration file not found: $envConfigPath"
            }
        }
        
        # Validate configuration
        Test-ConfigurationValidity -Config $config
        
        # Set global configuration
        $script:EnterpriseConfig = $config
        
        # Set up file watcher if requested
        if ($EnableFileWatcher) {
            Start-ConfigurationWatcher
        }
        
        Write-Verbose "Configuration loaded successfully. Version: $($config.Version)"
        
        return $config
    }
    catch {
        Write-Error "Failed to load configuration: $($_.Exception.Message)"
        throw
    }
}

Function Get-ConfigValue {
    <#
    .SYNOPSIS
        Retrieves a configuration value using dot notation path.
    
    .DESCRIPTION
        Gets a configuration value from the loaded configuration using a dot-separated path.
        Returns the default value if the path doesn't exist.
    
    .PARAMETER Path
        Dot-separated path to the configuration value (e.g., "Jira.DefaultServer")
    
    .PARAMETER DefaultValue
        Value to return if the path doesn't exist
    
    .EXAMPLE
        $server = Get-ConfigValue -Path "Jira.DefaultServer" -DefaultValue "https://default.atlassian.net"
    #>
    [CmdletBinding()]
    param(
        [Parameter(Mandatory)]
        [string]$Path,
        [object]$DefaultValue = $null
    )
    
    if (-not $script:EnterpriseConfig) {
        throw "Configuration not loaded. Call Import-EnterpriseConfig first."
    }
    
    $pathParts = $Path -split '\.'
    $current = $script:EnterpriseConfig
    
    foreach ($part in $pathParts) {
        if ($current -is [hashtable] -and $current.ContainsKey($part)) {
            $current = $current[$part]
        } else {
            Write-Verbose "Configuration path not found: $Path, returning default value"
            return $DefaultValue
        }
    }
    
    return $current
}

Function Set-ConfigValue {
    <#
    .SYNOPSIS
        Sets a configuration value using dot notation path.
    
    .DESCRIPTION
        Sets a configuration value in the loaded configuration using a dot-separated path.
        Can optionally save the change to file.
    
    .PARAMETER Path
        Dot-separated path to the configuration value
    
    .PARAMETER Value
        Value to set
    
    .PARAMETER Temporary
        If specified, the change is not saved to file
    
    .EXAMPLE
        Set-ConfigValue -Path "UI.Theme" -Value "Dark"
    #>
    [CmdletBinding()]
    param(
        [Parameter(Mandatory)]
        [string]$Path,
        [Parameter(Mandatory)]
        [object]$Value,
        [switch]$Temporary
    )
    
    if (-not $script:EnterpriseConfig) {
        throw "Configuration not loaded. Call Import-EnterpriseConfig first."
    }
    
    $pathParts = $Path -split '\.'
    $current = $script:EnterpriseConfig
    
    # Navigate to parent
    for ($i = 0; $i -lt ($pathParts.Length - 1); $i++) {
        $part = $pathParts[$i]
        if (-not $current.ContainsKey($part)) {
            $current[$part] = @{}
        }
        $current = $current[$part]
    }
    
    # Set the value
    $lastPart = $pathParts[-1]
    $oldValue = if ($current.ContainsKey($lastPart)) { $current[$lastPart] } else { $null }
    $current[$lastPart] = $Value
    
    Write-Verbose "Configuration value changed: $Path = $Value (was: $oldValue)"
    
    # Save to file if not temporary
    if (-not $Temporary -and $script:ConfigPath) {
        try {
            Export-EnterpriseConfig -Path $script:ConfigPath
            Write-Verbose "Configuration saved to file"
        }
        catch {
            Write-Warning "Failed to save configuration to file: $($_.Exception.Message)"
        }
    }
}

Function Test-ConfigurationValidity {
    <#
    .SYNOPSIS
        Validates the configuration structure and required values.
    
    .DESCRIPTION
        Performs comprehensive validation of the configuration to ensure all required
        sections and values are present and valid.
    
    .PARAMETER Config
        Configuration hashtable to validate
    
    .EXAMPLE
        Test-ConfigurationValidity -Config $config
    #>
    [CmdletBinding()]
    param(
        [Parameter(Mandatory)]
        [hashtable]$Config
    )
    
    $validationErrors = @()
    
    # Required sections
    $requiredSections = @("Application", "Jira", "ActiveDirectory", "UI", "Security")
    foreach ($section in $requiredSections) {
        if (-not $Config.ContainsKey($section)) {
            $validationErrors += "Missing required configuration section: $section"
        }
    }
    
    # Validate Application configuration
    if ($Config.Application) {
        if (-not $Config.Application.Version) {
            $validationErrors += "Application.Version is required"
        }
        if ($Config.Application.SessionTimeoutMinutes -and $Config.Application.SessionTimeoutMinutes -lt 5) {
            $validationErrors += "Application.SessionTimeoutMinutes must be at least 5 minutes"
        }
    }
    
    # Validate Jira configuration
    if ($Config.Jira) {
        if (-not $Config.Jira.DefaultServer) {
            $validationErrors += "Jira.DefaultServer is required"
        }
        if ($Config.Jira.DefaultServer -and -not $Config.Jira.DefaultServer.StartsWith("https://")) {
            $validationErrors += "Jira.DefaultServer must use HTTPS"
        }
        if ($Config.Jira.MaxCacheSize -and $Config.Jira.MaxCacheSize -lt 1) {
            $validationErrors += "Jira.MaxCacheSize must be at least 1"
        }
    }
    
    # Validate Active Directory configuration
    if ($Config.ActiveDirectory) {
        if (-not $Config.ActiveDirectory.DefaultDomain) {
            $validationErrors += "ActiveDirectory.DefaultDomain is required"
        }
        if (-not $Config.ActiveDirectory.DefaultOUs -or $Config.ActiveDirectory.DefaultOUs.Count -eq 0) {
            $validationErrors += "ActiveDirectory.DefaultOUs must contain at least one OU"
        }
        
        # Validate OU paths
        if ($Config.ActiveDirectory.DefaultOUs) {
            foreach ($location in $Config.ActiveDirectory.DefaultOUs.Keys) {
                $ouPath = $Config.ActiveDirectory.DefaultOUs[$location]
                if (-not $ouPath.StartsWith("OU=") -or -not $ouPath.Contains("DC=")) {
                    $validationErrors += "Invalid OU path for location '$location': $ouPath"
                }
            }
        }
        
        # Validate password settings
        if ($Config.ActiveDirectory.PasswordSettings) {
            $pwdSettings = $Config.ActiveDirectory.PasswordSettings
            if ($pwdSettings.Length -and $pwdSettings.Length -lt 8) {
                $validationErrors += "ActiveDirectory.PasswordSettings.Length must be at least 8"
            }
        }
    }
    
    # Validate UI configuration
    if ($Config.UI) {
        $validThemes = @("Light", "Dark", "Auto")
        if ($Config.UI.Theme -and $Config.UI.Theme -notin $validThemes) {
            $validationErrors += "UI.Theme must be one of: $($validThemes -join ', ')"
        }
        
        if ($Config.UI.Breakpoints) {
            $breakpoints = $Config.UI.Breakpoints
            if ($breakpoints.Small -ge $breakpoints.Medium -or 
                $breakpoints.Medium -ge $breakpoints.Large -or 
                $breakpoints.Large -ge $breakpoints.ExtraLarge) {
                $validationErrors += "UI.Breakpoints must be in ascending order"
            }
        }
    }
    
    # Validate Security configuration
    if ($Config.Security) {
        if ($Config.Security.MFA -and $Config.Security.MFA.Enabled) {
            $validProviders = @("TOTP", "SMS", "Email")
            if ($Config.Security.MFA.Provider -notin $validProviders) {
                $validationErrors += "Security.MFA.Provider must be one of: $($validProviders -join ', ')"
            }
        }
        
        if ($Config.Security.Audit) {
            $validLevels = @("Basic", "Detailed", "Verbose")
            if ($Config.Security.Audit.Level -notin $validLevels) {
                $validationErrors += "Security.Audit.Level must be one of: $($validLevels -join ', ')"
            }
        }
    }
    
    # Validate field mappings
    if ($Config.Jira.CustomFieldMapping) {
        foreach ($fieldName in $Config.Jira.CustomFieldMapping.Keys) {
            $mapping = $Config.Jira.CustomFieldMapping[$fieldName]
            if (-not $mapping.ADAttributes -or $mapping.ADAttributes.Count -eq 0) {
                $validationErrors += "Custom field mapping '$fieldName' must have at least one AD attribute"
            }
            if (-not $mapping.Parser) {
                $validationErrors += "Custom field mapping '$fieldName' must specify a parser"
            }
        }
    }
    
    if ($validationErrors.Count -gt 0) {
        $errorMessage = "Configuration validation failed:`n$($validationErrors -join "`n")"
        throw $errorMessage
    }
    
    Write-Verbose "Configuration validation passed"
}

Function Merge-Configuration {
    <#
    .SYNOPSIS
        Merges two configuration hashtables with override support.
    
    .DESCRIPTION
        Recursively merges configuration hashtables, with override values taking precedence.
        Handles nested hashtables properly.
    
    .PARAMETER BaseConfig
        Base configuration hashtable
    
    .PARAMETER OverrideConfig
        Override configuration hashtable
    
    .EXAMPLE
        $merged = Merge-Configuration -BaseConfig $base -OverrideConfig $override
    #>
    [CmdletBinding()]
    param(
        [Parameter(Mandatory)]
        [hashtable]$BaseConfig,
        [Parameter(Mandatory)]
        [hashtable]$OverrideConfig
    )
    
    $merged = $BaseConfig.Clone()
    
    foreach ($key in $OverrideConfig.Keys) {
        if ($merged.ContainsKey($key) -and 
            $merged[$key] -is [hashtable] -and 
            $OverrideConfig[$key] -is [hashtable]) {
            # Recursively merge nested hashtables
            $merged[$key] = Merge-Configuration -BaseConfig $merged[$key] -OverrideConfig $OverrideConfig[$key]
        } else {
            # Override the value
            $merged[$key] = $OverrideConfig[$key]
        }
    }
    
    return $merged
}

Function Export-EnterpriseConfig {
    <#
    .SYNOPSIS
        Exports the current configuration to a file.
    
    .DESCRIPTION
        Saves the current in-memory configuration to a PowerShell data file.
    
    .PARAMETER Path
        Path to save the configuration file
    
    .EXAMPLE
        Export-EnterpriseConfig -Path "Config\EnterpriseConfig.psd1"
    #>
    [CmdletBinding()]
    param(
        [string]$Path = $script:ConfigPath
    )
    
    if (-not $script:EnterpriseConfig) {
        throw "No configuration loaded to export"
    }
    
    if (-not $Path) {
        throw "No path specified for export"
    }
    
    try {
        # Ensure directory exists
        $directory = Split-Path $Path -Parent
        if (-not (Test-Path $directory)) {
            New-Item -ItemType Directory -Path $directory -Force | Out-Null
        }
        
        # Convert hashtable to PowerShell data file format
        $configText = ConvertTo-PowerShellDataFile -InputObject $script:EnterpriseConfig
        
        # Write to file
        Set-Content -Path $Path -Value $configText -Encoding UTF8
        
        Write-Verbose "Configuration exported to: $Path"
    }
    catch {
        throw "Failed to export configuration: $($_.Exception.Message)"
    }
}

Function ConvertTo-PowerShellDataFile {
    <#
    .SYNOPSIS
        Converts a hashtable to PowerShell data file format.
    
    .DESCRIPTION
        Recursively converts a hashtable to a properly formatted PowerShell data file string.
    
    .PARAMETER InputObject
        Hashtable to convert
    
    .PARAMETER IndentLevel
        Current indentation level for formatting
    #>
    [CmdletBinding()]
    param(
        [Parameter(Mandatory)]
        [object]$InputObject,
        [int]$IndentLevel = 0
    )
    
    $indent = "    " * $IndentLevel
    $childIndent = "    " * ($IndentLevel + 1)
    
    if ($InputObject -is [hashtable]) {
        $lines = @("@{")
        
        foreach ($key in $InputObject.Keys | Sort-Object) {
            $value = $InputObject[$key]
            $valueString = ConvertTo-PowerShellDataFile -InputObject $value -IndentLevel ($IndentLevel + 1)
            $lines += "$childIndent$key = $valueString"
        }
        
        $lines += "$indent}"
        return $lines -join "`n"
    }
    elseif ($InputObject -is [array]) {
        if ($InputObject.Count -eq 0) {
            return "@()"
        }
        
        $lines = @("@(")
        foreach ($item in $InputObject) {
            $itemString = ConvertTo-PowerShellDataFile -InputObject $item -IndentLevel ($IndentLevel + 1)
            $lines += "$childIndent$itemString"
        }
        $lines += "$indent)"
        return $lines -join "`n"
    }
    elseif ($InputObject -is [string]) {
        # Escape quotes and special characters
        $escaped = $InputObject -replace '"', '""'
        return "`"$escaped`""
    }
    elseif ($InputObject -is [bool]) {
        return if ($InputObject) { '$true' } else { '$false' }
    }
    elseif ($InputObject -eq $null) {
        return '$null'
    }
    else {
        return $InputObject.ToString()
    }
}

Function Start-ConfigurationWatcher {
    <#
    .SYNOPSIS
        Starts watching the configuration file for changes.
    
    .DESCRIPTION
        Sets up a file system watcher to automatically reload configuration when the file changes.
    #>
    [CmdletBinding()]
    param()
    
    if (-not $script:ConfigPath) {
        Write-Warning "No configuration path set, cannot start file watcher"
        return
    }
    
    try {
        $directory = Split-Path $script:ConfigPath -Parent
        $filename = Split-Path $script:ConfigPath -Leaf
        
        $script:ConfigWatcher = New-Object System.IO.FileSystemWatcher
        $script:ConfigWatcher.Path = $directory
        $script:ConfigWatcher.Filter = $filename
        $script:ConfigWatcher.NotifyFilter = [System.IO.NotifyFilters]::LastWrite
        $script:ConfigWatcher.EnableRaisingEvents = $true
        
        # Register event handler
        Register-ObjectEvent -InputObject $script:ConfigWatcher -EventName "Changed" -Action {
            Start-Sleep -Milliseconds 500  # Debounce file changes
            try {
                Write-Host "Configuration file changed, reloading..." -ForegroundColor Yellow
                Import-EnterpriseConfig -ConfigPath $script:ConfigPath
                Write-Host "Configuration reloaded successfully" -ForegroundColor Green
            }
            catch {
                Write-Warning "Failed to reload configuration: $($_.Exception.Message)"
            }
        } | Out-Null
        
        Write-Verbose "Configuration file watcher started for: $script:ConfigPath"
    }
    catch {
        Write-Warning "Failed to start configuration file watcher: $($_.Exception.Message)"
    }
}

Function Stop-ConfigurationWatcher {
    <#
    .SYNOPSIS
        Stops the configuration file watcher.
    #>
    [CmdletBinding()]
    param()
    
    if ($script:ConfigWatcher) {
        $script:ConfigWatcher.EnableRaisingEvents = $false
        $script:ConfigWatcher.Dispose()
        $script:ConfigWatcher = $null
        
        # Unregister event handlers
        Get-EventSubscriber | Where-Object { $_.SourceObject -is [System.IO.FileSystemWatcher] } | Unregister-Event
        
        Write-Verbose "Configuration file watcher stopped"
    }
}

Function Get-ConfigurationInfo {
    <#
    .SYNOPSIS
        Gets information about the current configuration.
    
    .DESCRIPTION
        Returns metadata about the loaded configuration including version, load time, and source.
    
    .EXAMPLE
        Get-ConfigurationInfo
    #>
    [CmdletBinding()]
    param()
    
    if (-not $script:EnterpriseConfig) {
        return @{
            Loaded = $false
            Message = "No configuration loaded"
        }
    }
    
    return @{
        Loaded = $true
        Version = $script:EnterpriseConfig.Version
        SourcePath = $script:ConfigPath
        Environment = $script:EnterpriseConfig.Application.Environment
        LoadTime = Get-Date
        WatcherEnabled = $null -ne $script:ConfigWatcher
        SectionCount = $script:EnterpriseConfig.Keys.Count
    }
}

# Export module functions
Export-ModuleMember -Function @(
    'Import-EnterpriseConfig',
    'Get-ConfigValue',
    'Set-ConfigValue',
    'Test-ConfigurationValidity',
    'Merge-Configuration',
    'Export-EnterpriseConfig',
    'Start-ConfigurationWatcher',
    'Stop-ConfigurationWatcher',
    'Get-ConfigurationInfo'
)