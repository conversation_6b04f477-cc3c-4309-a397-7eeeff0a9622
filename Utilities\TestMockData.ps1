# Quick test of the updated mock data structure
$mockData = @{ 
    summary = "Onboarding Request - Test User (TESTIT-49342)"
    description = "New Joiner Name: Test User`nJob Title: Test Analyst`nDepartment: IT`nModel Account: testmodel"
    # Core onboarding fields (these are the exact IDs from your Jira)
    customfield_10304 = "Test"           # First Name
    customfield_10305 = "User"           # Last Name  
    customfield_10238 = "Test Analyst"   # Job Title
    customfield_10120 = "IT"             # Department
    customfield_10343 = "testmodel"      # Model Account
    customfield_10115 = "Singapore"      # Office Location
    customfield_10342 = "JERAGM Employee" # Employee Type
    customfield_10344 = "$(Get-Date -Format 'ddd, dd MMM yyyy HH:mm:ss +0000')" # Effective Date
}

Write-Host "Mock data structure:" -ForegroundColor Yellow
Write-Host "Keys: $($mockData.Keys -join ', ')" -ForegroundColor Green

# Test field mappings
$mappings = @{
    FirstName    = @{ CustomFieldId = "customfield_10304"; DisplayName = "First Name" }
    LastName     = @{ CustomFieldId = "customfield_10305"; DisplayName = "Last Name" }
    JobTitle     = @{ CustomFieldId = "customfield_10238"; DisplayName = "Job Title" }
    Department   = @{ CustomFieldId = "customfield_10120"; DisplayName = "Department" }
    ModelAccount = @{ CustomFieldId = "customfield_10343"; DisplayName = "Model Account" }
    OfficeLocation = @{ CustomFieldId = "customfield_10115"; DisplayName = "Office Location" }
}

Write-Host "`nParsing test:" -ForegroundColor Yellow
$parsedData = @{}
foreach ($key in $mappings.Keys) {
    $fieldId = $mappings[$key].CustomFieldId
    if ($mockData.ContainsKey($fieldId)) {
        $parsedData[$key] = $mockData[$fieldId]
        Write-Host "✓ Found $key = '$($mockData[$fieldId])'" -ForegroundColor Green
    } else {
        Write-Host "✗ Missing $key (field $fieldId)" -ForegroundColor Red
    }
}

Write-Host "`nParsed data keys: $($parsedData.Keys -join ', ')" -ForegroundColor Cyan
Write-Host "Required fields found: $($parsedData.Count)/4" -ForegroundColor $(if ($parsedData.Count -eq 4) { "Green" } else { "Red" })

# Test username generation
if ($parsedData.ContainsKey('FirstName') -and $parsedData.ContainsKey('LastName')) {
    $cleanFirstName = $parsedData.FirstName -replace '[^a-zA-Z]', ''
    $cleanLastName = $parsedData.LastName -replace '[^a-zA-Z]', ''
    $username = ($cleanFirstName.Substring(0,1) + $cleanLastName).ToLower()
    Write-Host "`nGenerated username: $username" -ForegroundColor Cyan
}

Write-Host "`n✅ Mock data structure test PASSED" -ForegroundColor Green