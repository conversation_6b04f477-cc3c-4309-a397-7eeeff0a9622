2025-07-01 12:24:28.064 | akinje          | INFO     | JML Automation Script v1.13 starting...
2025-07-01 12:24:28.066 | akinje          | DEBUG    | Script path: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13
2025-07-01 12:24:28.080 | akinje          | DEBUG    | PowerShell version: 7.4.10
2025-07-01 12:24:29.330 | akinje          | WARNING  | WARNING: Early logging initialization failed: The 'Get-ExecutionPolicy' command was found in the module 'Microsoft.PowerShell.Security', but the module could not be loaded due to the following error: [The following error occurred while loading the extended type data file: 
, C:\program files\powershell\7\Modules\Microsoft.PowerShell.Security\Security.types.ps1xml(19) : Error in type "System.Security.AccessControl.ObjectSecurity": The "Type" node must have "Members", "TypeConverters", or "TypeAdapters".
, C:\program files\powershell\7\Modules\Microsoft.PowerShell.Security\Security.types.ps1xml(52) : Error: CodeProperty should use a getter or setter method.
, C:\program files\powershell\7\Modules\Microsoft.PowerShell.Security\Security.types.ps1xml(53) : Error: Unable to find type [Microsoft.PowerShell.Commands.SecurityDescriptorCommandsBase].
, C:\program files\powershell\7\Modules\Microsoft.PowerShell.Security\Security.types.ps1xml(45) : Error: CodeProperty should use a getter or setter method.
, C:\program files\powershell\7\Modules\Microsoft.PowerShell.Security\Security.types.ps1xml(46) : Error: Unable to find type [Microsoft.PowerShell.Commands.SecurityDescriptorCommandsBase].
, C:\program files\powershell\7\Modules\Microsoft.PowerShell.Security\Security.types.ps1xml(38) : Error: CodeProperty should use a getter or setter method.
, C:\program files\powershell\7\Modules\Microsoft.PowerShell.Security\Security.types.ps1xml(39) : Error: Unable to find type [Microsoft.PowerShell.Commands.SecurityDescriptorCommandsBase].
, C:\program files\powershell\7\Modules\Microsoft.PowerShell.Security\Security.types.ps1xml(31) : Error: CodeProperty should use a getter or setter method.
, C:\program files\powershell\7\Modules\Microsoft.PowerShell.Security\Security.types.ps1xml(32) : Error: Unable to find type [Microsoft.PowerShell.Commands.SecurityDescriptorCommandsBase].
, C:\program files\powershell\7\Modules\Microsoft.PowerShell.Security\Security.types.ps1xml(24) : Error: CodeProperty should use a getter or setter method.
, C:\program files\powershell\7\Modules\Microsoft.PowerShell.Security\Security.types.ps1xml(25) : Error: Unable to find type [Microsoft.PowerShell.Commands.SecurityDescriptorCommandsBase].
]
For more information, run 'Import-Module Microsoft.PowerShell.Security'.
2025-07-01 12:24:29.402 | akinje          | INFO     | === JML ENVIRONMENT VALIDATION ===
2025-07-01 12:24:29.463 | akinje          | INFO     | Verifying PowerShell Version...
2025-07-01 12:24:29.503 | akinje          | INFO     | === JML ENVIRONMENT VALIDATION ===
2025-07-01 12:24:29.538 | akinje          | INFO     | Verifying PowerShell Version...
2025-07-01 12:24:29.584 | akinje          | INFO     | PowerShell 7+ detected (version 7.4.10). Checking for updates...
2025-07-01 12:24:29.645 | akinje          | INFO     | PowerShell 7+ detected (version 7.4.10)
2025-07-01 12:24:29.703 | akinje          | INFO     | Checking for PowerShell updates online...
2025-07-01 12:24:29.777 | akinje          | DEBUG    | Checking for PowerShell updates online...
2025-07-01 12:24:30.074 | akinje          | INFO     | A newer version of PowerShell (7.5.2) is available.
2025-07-01 12:24:30.120 | akinje          | INFO     | Current version: 7.4.10
2025-07-01 12:24:30.170 | akinje          | INFO     | Download from: https://github.com/PowerShell/PowerShell/releases
2025-07-01 12:24:30.219 | akinje          | INFO     | === CHECKING REQUIRED MODULES ===
2025-07-01 12:24:30.259 | akinje          | INFO     | === CHECKING REQUIRED MODULES ===
2025-07-01 12:24:30.314 | akinje          | INFO     | Checking for required modules and available updates...
2025-07-01 12:24:30.439 | akinje          | INFO     | Installing NuGet provider...
2025-07-01 12:24:33.513 | akinje          | WARNING  | WARNING: Failed to install NuGet provider: Collection was modified; enumeration operation may not execute.
2025-07-01 12:24:33.533 | akinje          | INFO     | Checking module: Microsoft.PowerShell.SecretManagement...
2025-07-01 12:24:33.632 | akinje          | INFO     |   Found installed version 1.1.2
2025-07-01 12:24:33.663 | akinje          | INFO     |   Could not check for updates: Unable to find type [Net.ServicePointManager].
2025-07-01 12:24:33.688 | akinje          | INFO     | Checking module: Microsoft.PowerShell.SecretStore...
2025-07-01 12:24:33.824 | akinje          | INFO     |   Found installed version 1.0.6
2025-07-01 12:24:33.871 | akinje          | INFO     |   Could not check for updates: Unable to find type [Net.ServicePointManager].
2025-07-01 12:24:33.907 | akinje          | INFO     | Checking module: Microsoft.Graph...
2025-07-01 12:24:34.195 | akinje          | INFO     |   Found installed version 2.28.0
2025-07-01 12:24:34.282 | akinje          | INFO     |   Could not check for updates: Unable to find type [Net.ServicePointManager].
2025-07-01 12:24:34.350 | akinje          | INFO     | Checking module: JiraPS...
2025-07-01 12:24:34.571 | akinje          | INFO     |   Found installed version 2.14.7
2025-07-01 12:24:34.614 | akinje          | INFO     |   Could not check for updates: Unable to find type [Net.ServicePointManager].
2025-07-01 12:24:34.654 | akinje          | INFO     | Checking module: ImportExcel...
2025-07-01 12:24:34.908 | akinje          | INFO     |   Found installed version 7.8.10
2025-07-01 12:24:35.030 | akinje          | INFO     |   Could not check for updates: Unable to find type [Net.ServicePointManager].
2025-07-01 12:24:35.115 | akinje          | INFO     | Checking module: PSWriteHTML...
2025-07-01 12:24:35.549 | akinje          | INFO     |   Found installed version 1.36.0
2025-07-01 12:24:35.603 | akinje          | INFO     |   Could not check for updates: Unable to find type [Net.ServicePointManager].
2025-07-01 12:24:35.687 | akinje          | INFO     | All modules are up to date.
2025-07-01 12:24:35.732 | akinje          | INFO     | Verifying module availability...
2025-07-01 12:24:35.930 | akinje          | INFO     |   [VERIFIED] Microsoft.PowerShell.SecretManagement v1.1.2 is available.
2025-07-01 12:24:36.132 | akinje          | INFO     |   [VERIFIED] Microsoft.PowerShell.SecretStore v1.0.6 is available.
2025-07-01 12:24:36.271 | akinje          | INFO     |   [VERIFIED] Microsoft.Graph v2.28.0 is available.
2025-07-01 12:24:36.355 | akinje          | INFO     |   [VERIFIED] JiraPS v2.14.7 is available.
2025-07-01 12:24:36.433 | akinje          | INFO     |   [VERIFIED] ImportExcel v7.8.10 is available.
2025-07-01 12:24:36.528 | akinje          | INFO     |   [VERIFIED] PSWriteHTML v1.36.0 is available.
2025-07-01 12:24:36.571 | akinje          | INFO     | All required modules are successfully installed and available.
2025-07-01 12:24:36.604 | akinje          | INFO     | === JML ENVIRONMENT VALIDATION COMPLETE ===
2025-07-01 12:24:36.640 | akinje          | DEBUG    | [DEBUG] Starting JML script execution...
2025-07-01 12:24:36.679 | akinje          | DEBUG    | [DEBUG] Script directory: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13
2025-07-01 12:24:36.717 | akinje          | DEBUG    | [DEBUG] Checking ActiveDirectory module availability...
2025-07-01 12:24:36.778 | akinje          | INFO     | [INFO] ActiveDirectory module is not available - running in UAT mode
2025-07-01 12:24:36.804 | akinje          | INFO     | [INFO] This is normal for UAT/test environments
2025-07-01 12:24:36.829 | akinje          | DEBUG    | [DEBUG] Initializing JML paths...
2025-07-01 12:24:36.863 | akinje          | DEBUG    | [DEBUG] JML Paths initialized globally.
2025-07-01 12:24:36.889 | akinje          | DEBUG    | [DEBUG] Project Root: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13
2025-07-01 12:24:36.919 | akinje          | DEBUG    | [DEBUG] Modules Root: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Modules
2025-07-01 12:24:36.949 | akinje          | DEBUG    | [DEBUG] Config Dir: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Config
2025-07-01 12:24:36.976 | akinje          | DEBUG    | [DEBUG] Updated ConfigPath to use Config directory: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Config\AdminAccountConfig.psd1
2025-07-01 12:24:37.001 | akinje          | DEBUG    | [DEBUG] Starting enhanced module loading...
2025-07-01 12:24:37.031 | akinje          | INFO     | Loading JML modules from nested structure...
2025-07-01 12:24:37.056 | akinje          | INFO     | Modules Root: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Modules
2025-07-01 12:24:37.089 | akinje          | INFO     |   Loading: JML-Core/JML-Utilities [CRITICAL]
2025-07-01 12:24:37.121 | akinje          | INFO     |     Description: General utility functions
2025-07-01 12:24:37.152 | akinje          | INFO     |     Path: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Modules\JML-Core\JML-Utilities.psd1 (manifest)
2025-07-01 12:24:37.181 | akinje          | INFO     |     Attempting to import module using manifest...
2025-07-01 12:24:37.490 | akinje          | WARNING  | WARNING: JML-Security: JML-Configuration module functions not available. Some features may be limited.
2025-07-01 12:24:37.635 | akinje          | WARNING  | WARNING: JML-Logging: Missing dependencies: Get-ModuleConfiguration (JML-Configuration), Protect-SensitiveData (JML-Security). Some features may be limited.
2025-07-01 12:24:37.661 | akinje          | INFO     |     [SUCCESS] Module loaded: JML-Core/JML-Utilities
2025-07-01 12:24:37.690 | akinje          | INFO     |   Loading: JML-Core/JML-Configuration [CRITICAL]
2025-07-01 12:24:37.717 | akinje          | INFO     |     Description: Core configuration management
2025-07-01 12:24:37.741 | akinje          | INFO     |     Path: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Modules\JML-Core\JML-Configuration.psd1 (manifest)
2025-07-01 12:24:37.767 | akinje          | INFO     |     Attempting to import module using manifest...
2025-07-01 12:24:37.827 | akinje          | INFO     |     [SUCCESS] Module loaded: JML-Core/JML-Configuration
2025-07-01 12:24:37.850 | akinje          | INFO     |     [VERIFIED] Function Initialize-SmartConfiguration is available
2025-07-01 12:24:37.876 | akinje          | INFO     |     [VERIFIED] Function Get-ModuleConfiguration is available
2025-07-01 12:24:37.903 | akinje          | INFO     |   Loading: JML-Core/JML-Security [CRITICAL]
2025-07-01 12:24:37.931 | akinje          | INFO     |     Description: Security and credential management
2025-07-01 12:24:37.956 | akinje          | INFO     |     Path: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Modules\JML-Core\JML-Security.psd1 (manifest)
2025-07-01 12:24:37.986 | akinje          | INFO     |     Attempting to import module using manifest...
2025-07-01 12:24:38.056 | akinje          | INFO     |     [SUCCESS] Module loaded: JML-Core/JML-Security
2025-07-01 12:24:38.083 | akinje          | INFO     |     [VERIFIED] Function Get-SecureCredential is available
2025-07-01 12:24:38.110 | akinje          | INFO     |     [VERIFIED] Function Set-SecureCredential is available
2025-07-01 12:24:38.138 | akinje          | INFO     |     [VERIFIED] Function Get-StringHash is available
2025-07-01 12:24:38.168 | akinje          | INFO     |   Loading: JML-Core/JML-Logging [CRITICAL]
2025-07-01 12:24:38.192 | akinje          | INFO     |     Description: Secure logging functionality
2025-07-01 12:24:38.223 | akinje          | INFO     |     Path: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Modules\JML-Core\JML-Logging.psd1 (manifest)
2025-07-01 12:24:38.255 | akinje          | INFO     |     Attempting to import module using manifest...
2025-07-01 12:24:38.316 | akinje          | INFO     |     [SUCCESS] Module loaded: JML-Core/JML-Logging
2025-07-01 12:24:38.342 | akinje          | INFO     |     [VERIFIED] Function Write-SecureLog is available
2025-07-01 12:24:38.369 | akinje          | INFO     |     [VERIFIED] Function Initialize-SecureLogging is available
2025-07-01 12:24:38.401 | akinje          | INFO     |   Loading: JML-Features/JML-ActiveDirectory [CRITICAL]
2025-07-01 12:24:38.429 | akinje          | INFO     |     Description: Active Directory operations
2025-07-01 12:24:38.461 | akinje          | INFO     |     Path: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Modules\JML-Features\JML-ActiveDirectory.psd1 (manifest)
2025-07-01 12:24:38.486 | akinje          | INFO     |     Attempting to import module using manifest...
2025-07-01 12:24:38.632 | akinje          | INFO     |     [SUCCESS] Module loaded: JML-Features/JML-ActiveDirectory
2025-07-01 12:24:38.659 | akinje          | INFO     |   Loading: JML-Features/JML-Email [CRITICAL]
2025-07-01 12:24:38.682 | akinje          | INFO     |     Description: Email notification functionality
2025-07-01 12:24:38.708 | akinje          | INFO     |     Path: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Modules\JML-Features\JML-Email.psd1 (manifest)
2025-07-01 12:24:38.733 | akinje          | INFO     |     Attempting to import module using manifest...
2025-07-01 12:24:38.809 | akinje          | INFO     |     [SUCCESS] Module loaded: JML-Features/JML-Email
2025-07-01 12:24:38.834 | akinje          | INFO     |   Loading: JML-Features/JML-Jira [CRITICAL]
2025-07-01 12:24:38.857 | akinje          | INFO     |     Description: Jira integration
2025-07-01 12:24:38.887 | akinje          | INFO     |     Path: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Modules\JML-Features\JML-Jira.psd1 (manifest)
2025-07-01 12:24:38.914 | akinje          | INFO     |     Attempting to import module using manifest...
2025-07-01 12:24:39.021 | akinje          | INFO     |     [SUCCESS] Module loaded: JML-Features/JML-Jira
2025-07-01 12:24:39.047 | akinje          | INFO     |   Loading: JML-Infrastructure/JML-Setup [CRITICAL]
2025-07-01 12:24:39.072 | akinje          | INFO     |     Description: Setup and environment validation
2025-07-01 12:24:39.101 | akinje          | INFO     |     Path: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Modules\JML-Infrastructure\JML-Setup.psd1 (manifest)
2025-07-01 12:24:39.127 | akinje          | INFO     |     Attempting to import module using manifest...
2025-07-01 12:24:39.205 | akinje          | INFO     |     [SUCCESS] Module loaded: JML-Infrastructure/JML-Setup
2025-07-01 12:24:39.234 | akinje          | INFO     |   Loading: JML-Infrastructure/JML-Monitoring [CRITICAL]
2025-07-01 12:24:39.259 | akinje          | INFO     |     Description: System monitoring
2025-07-01 12:24:39.286 | akinje          | INFO     |     Path: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Modules\JML-Infrastructure\JML-Monitoring.psd1 (manifest)
2025-07-01 12:24:39.312 | akinje          | INFO     |     Attempting to import module using manifest...
2025-07-01 12:24:39.388 | akinje          | INFO     |     [SUCCESS] Module loaded: JML-Infrastructure/JML-Monitoring
2025-07-01 12:24:39.414 | akinje          | INFO     |   Loading: JML-Infrastructure/JML-UIManager [CRITICAL]
2025-07-01 12:24:39.437 | akinje          | INFO     |     Description: Modern hybrid dashboard UI with real-time status
2025-07-01 12:24:39.461 | akinje          | INFO     |     Path: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Modules\JML-Infrastructure\JML-UIManager.psd1 (manifest)
2025-07-01 12:24:39.484 | akinje          | INFO     |     Attempting to import module using manifest...
2025-07-01 12:24:39.799 | akinje          | INFO     |     [SUCCESS] Module loaded: JML-Infrastructure/JML-UIManager
2025-07-01 12:24:39.821 | akinje          | INFO     | Module Loading Summary:
2025-07-01 12:24:39.846 | akinje          | INFO     |   Successfully loaded: 10/10
2025-07-01 12:24:39.869 | akinje          | DEBUG    | [DEBUG] Enhanced module loading completed successfully
2025-07-01 12:24:39.894 | akinje          | DEBUG    | [DEBUG] Performing post-loading verification...
2025-07-01 12:24:39.923 | akinje          | DEBUG    | [DEBUG] ✓ Initialize-SmartConfiguration is available
2025-07-01 12:24:39.946 | akinje          | DEBUG    | [DEBUG] ✓ Get-ModuleConfiguration is available
2025-07-01 12:25:15.805 | akinje          | INFO     | Configuration loaded from: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Config\AdminAccountConfig.psd1
2025-07-01 12:25:15.835 | akinje          | INFO     | [SUCCESS] Configuration initialized successfully
2025-07-01 12:25:15.868 | akinje          | DEBUG    | [DEBUG] Initializing credential storage selection...
2025-07-01 12:25:15.914 | akinje          | INFO     | ================================================================================
2025-07-01 12:25:15.949 | akinje          | INFO     |                     JML Credential Storage Selection                      
2025-07-01 12:25:15.978 | akinje          | INFO     | ================================================================================
2025-07-01 12:25:16.006 | akinje          | INFO     | Please select your preferred credential storage method:
2025-07-01 12:25:16.037 | akinje          | INFO     |   1. Encrypted File (Recommended for Portability)
2025-07-01 12:25:16.068 | akinje          | INFO     |      - Stores credentials in an encrypted XML file
2025-07-01 12:25:16.099 | akinje          | INFO     |      - Portable across different machines
2025-07-01 12:25:16.127 | akinje          | INFO     |      - Uses Windows DPAPI for encryption
2025-07-01 12:25:16.154 | akinje          | INFO     |   2. PowerShell Secret Management / SecretStore
2025-07-01 12:25:16.187 | akinje          | INFO     |      - Uses Microsoft.PowerShell.SecretStore module
2025-07-01 12:25:16.222 | akinje          | INFO     |      - Requires vault password for access
2025-07-01 12:25:16.255 | akinje          | INFO     |      - More secure but requires setup
2025-07-01 12:25:16.306 | akinje          | INFO     |   3. Windows Credential Manager
2025-07-01 12:25:16.336 | akinje          | INFO     |      - Uses Windows built-in credential storage
2025-07-01 12:25:16.374 | akinje          | INFO     |      - Integrated with Windows security
2025-07-01 12:25:16.447 | akinje          | INFO     |      - Machine-specific storage
2025-07-01 12:25:16.499 | akinje          | INFO     | Enter your choice (1-3) [Default: 1 in 10 seconds]: 
2025-07-01 12:25:26.584 | akinje          | INFO     | 1 (default - Encrypted File)
2025-07-01 12:25:26.651 | akinje          | INFO     | Selected: Encrypted File storage
2025-07-01 12:25:26.673 | akinje          | INFO     | Credentials will be stored in an encrypted XML file for portability.
2025-07-01 12:25:26.700 | akinje          | INFO     | Credential storage method set for this session: EncryptedFile
2025-07-01 12:25:26.722 | akinje          | INFO     | Press any key to continue...
