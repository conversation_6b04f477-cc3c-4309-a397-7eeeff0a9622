# Replace ampersands with 'and' to avoid syntax issues
$scriptPath = "OnboardingFromJiraGUI.ps1"
$content = Get-Content $scriptPath -Raw

Write-Host "Replacing ampersands with 'and'..." -ForegroundColor Yellow

$content = $content -replace 'Security & Compliance', 'Security and Compliance'
$content = $content -replace 'Progress Indicators & Cancellation', 'Progress Indicators and Cancellation'  
$content = $content -replace 'Logging & Monitoring', 'Logging and Monitoring'
$content = $content -replace 'Version Management & Updates', 'Version Management and Updates'
$content = $content -replace 'Documentation & Help', 'Documentation and Help'

$content | Set-Content $scriptPath -Encoding UTF8

Write-Host "Replaced ampersands with 'and'" -ForegroundColor Green