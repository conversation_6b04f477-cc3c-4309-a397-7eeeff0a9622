# Simple fix for ampersand issues only
$scriptPath = "OnboardingFromJiraGUI.ps1"
$content = Get-Content $scriptPath -Raw

Write-Host "Fixing ampersand issues in comments..." -ForegroundColor Yellow

# Replace & in comments with "&" but be very specific
$content = $content -replace '(#.*?Security) & (Compliance)', '$1 "&" $2'
$content = $content -replace '(#.*?Progress Indicators) & (Cancellation)', '$1 "&" $2'
$content = $content -replace '(#.*?Logging) & (Monitoring)', '$1 "&" $2'
$content = $content -replace '(#.*?Version Management) & (Updates)', '$1 "&" $2'
$content = $content -replace '(#.*?Documentation) & (Help)', '$1 "&" $2'

$content | Set-Content $scriptPath -Encoding UTF8

Write-Host "Fixed ampersand issues" -ForegroundColor Green