﻿{
    "Duration":  6.9461621,
    "StartTime":  {
                      "value":  "\/Date(1751482813048)\/",
                      "DisplayHint":  2,
                      "DateTime":  "Wednesday, July 2, 2025 8:00:13 PM"
                  },
    "SkippedTests":  0,
    "FailedTests":  5,
    "Errors":  [
                   "PowerShell Parser: Found 3 syntax errors: Cannot assign property, use \u0027$this.VersionInfo\u0027.; Variable is not assigned in the method.; Variable is not assigned in the method.",
                   "Script Loading: Script loading failed: FAILED: At C:\\Users\\<USER>\\OneDrive - JERA Global Markets\\Azure Devops\\it-endusersupport-automation\\scripts\\Utilities\\OnboardingFromJiraGUI.ps1:7047 char:17 +                 $versionInfo = $this.CompatibilityMatrix.PowerShell[$ ... +                 ~~~~~~~~~~~~ Cannot assign property, use \u0027$this.VersionInfo\u0027.  At C:\\Users\\<USER>\\OneDrive - JERA Global Markets\\Azure Devops\\it-endusersupport-automation\\scripts\\Utilities\\OnboardingFromJiraGUI.ps1:6432 char:25 +             ProcessId = $PID +                         ~~~~ Variable is not assigned in the method.  At C:\\Users\\<USER>\\OneDrive - JERA Global Markets\\Azure Devops\\it-endusersupport-automation\\scripts\\Utilities\\OnboardingFromJiraGUI.ps1:7035 char:22 +         $psVersion = $PSVersionTable.PSVersion.ToString() +                      ~~~~~~~~~~~~~~~ Variable is not assigned in the method.",
                   "Phase 1 Integration: Write-ErrorLog: Feature missing",
                   "Region Structure: Unbalanced regions: 31 regions, 30 endregions",
                   "Documentation Coverage: Low documentation: 7.03% comment lines"
               ],
    "PassedTests":  22,
    "TestDetails":  [
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751482813881)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Wednesday, July 2, 2025 8:00:13 PM"
                                          },
                            "Name":  "File Existence",
                            "Message":  "Script file found",
                            "Details":  null
                        },
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751482814078)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Wednesday, July 2, 2025 8:00:14 PM"
                                          },
                            "Name":  "File Size",
                            "Message":  "Script size: 0.35 MB (367893 bytes)",
                            "Details":  null
                        },
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751482814454)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Wednesday, July 2, 2025 8:00:14 PM"
                                          },
                            "Name":  "Line Count",
                            "Message":  "Total lines: 9856",
                            "Details":  null
                        },
                        {
                            "Status":  "FAILED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751482814991)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Wednesday, July 2, 2025 8:00:14 PM"
                                          },
                            "Name":  "PowerShell Parser",
                            "Message":  "Found 3 syntax errors: Cannot assign property, use \u0027$this.VersionInfo\u0027.; Variable is not assigned in the method.; Variable is not assigned in the method.",
                            "Details":  null
                        },
                        {
                            "Status":  "FAILED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751482817640)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Wednesday, July 2, 2025 8:00:17 PM"
                                          },
                            "Name":  "Script Loading",
                            "Message":  "Script loading failed: FAILED: At C:\\Users\\<USER>\\OneDrive - JERA Global Markets\\Azure Devops\\it-endusersupport-automation\\scripts\\Utilities\\OnboardingFromJiraGUI.ps1:7047 char:17 +                 $versionInfo = $this.CompatibilityMatrix.PowerShell[$ ... +                 ~~~~~~~~~~~~ Cannot assign property, use \u0027$this.VersionInfo\u0027.  At C:\\Users\\<USER>\\OneDrive - JERA Global Markets\\Azure Devops\\it-endusersupport-automation\\scripts\\Utilities\\OnboardingFromJiraGUI.ps1:6432 char:25 +             ProcessId = $PID +                         ~~~~ Variable is not assigned in the method.  At C:\\Users\\<USER>\\OneDrive - JERA Global Markets\\Azure Devops\\it-endusersupport-automation\\scripts\\Utilities\\OnboardingFromJiraGUI.ps1:7035 char:22 +         $psVersion = $PSVersionTable.PSVersion.ToString() +                      ~~~~~~~~~~~~~~~ Variable is not assigned in the method.",
                            "Details":  null
                        },
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751482817698)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Wednesday, July 2, 2025 8:00:17 PM"
                                          },
                            "Name":  "Class Definition: AdvancedLoggingManager",
                            "Message":  "Class definition found",
                            "Details":  null
                        },
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751482817740)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Wednesday, July 2, 2025 8:00:17 PM"
                                          },
                            "Name":  "Class Definition: VersionManager",
                            "Message":  "Class definition found",
                            "Details":  null
                        },
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751482817781)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Wednesday, July 2, 2025 8:00:17 PM"
                                          },
                            "Name":  "Class Definition: TestingFramework",
                            "Message":  "Class definition found",
                            "Details":  null
                        },
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751482817823)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Wednesday, July 2, 2025 8:00:17 PM"
                                          },
                            "Name":  "Class Definition: DocumentationGenerator",
                            "Message":  "Class definition found",
                            "Details":  null
                        },
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751482817844)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Wednesday, July 2, 2025 8:00:17 PM"
                                          },
                            "Name":  "Function Definition: Write-StructuredLog",
                            "Message":  "Function definition found",
                            "Details":  null
                        },
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751482817879)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Wednesday, July 2, 2025 8:00:17 PM"
                                          },
                            "Name":  "Function Definition: Get-ScriptVersion",
                            "Message":  "Function definition found",
                            "Details":  null
                        },
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751482817904)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Wednesday, July 2, 2025 8:00:17 PM"
                                          },
                            "Name":  "Function Definition: Register-Test",
                            "Message":  "Function definition found",
                            "Details":  null
                        },
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751482817932)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Wednesday, July 2, 2025 8:00:17 PM"
                                          },
                            "Name":  "Function Definition: New-Documentation",
                            "Message":  "Function definition found",
                            "Details":  null
                        },
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751482817958)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Wednesday, July 2, 2025 8:00:17 PM"
                                          },
                            "Name":  "Function Definition: Show-ScriptHelp",
                            "Message":  "Function definition found",
                            "Details":  null
                        },
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751482818015)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Wednesday, July 2, 2025 8:00:18 PM"
                                          },
                            "Name":  "Phase 1 Integration: ConfigurationManager",
                            "Message":  "Feature preserved",
                            "Details":  null
                        },
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751482818046)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Wednesday, July 2, 2025 8:00:18 PM"
                                          },
                            "Name":  "Phase 1 Integration: Get-ConfigurationValue",
                            "Message":  "Feature preserved",
                            "Details":  null
                        },
                        {
                            "Status":  "FAILED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751482818099)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Wednesday, July 2, 2025 8:00:18 PM"
                                          },
                            "Name":  "Phase 1 Integration: Write-ErrorLog",
                            "Message":  "Feature missing",
                            "Details":  null
                        },
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751482818123)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Wednesday, July 2, 2025 8:00:18 PM"
                                          },
                            "Name":  "Phase 2 Integration: AutoCompletionHistory",
                            "Message":  "Feature preserved",
                            "Details":  null
                        },
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751482818166)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Wednesday, July 2, 2025 8:00:18 PM"
                                          },
                            "Name":  "Phase 2 Integration: Apply-ModernUIIntegration",
                            "Message":  "Feature preserved",
                            "Details":  null
                        },
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751482818187)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Wednesday, July 2, 2025 8:00:18 PM"
                                          },
                            "Name":  "Phase 2 Integration: Show-ProgressDialog",
                            "Message":  "Feature preserved",
                            "Details":  null
                        },
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751482818213)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Wednesday, July 2, 2025 8:00:18 PM"
                                          },
                            "Name":  "Phase 3 Integration: OrganizationalDataManager",
                            "Message":  "Feature preserved",
                            "Details":  null
                        },
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751482818249)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Wednesday, July 2, 2025 8:00:18 PM"
                                          },
                            "Name":  "Phase 3 Integration: BatchOperationManager",
                            "Message":  "Feature preserved",
                            "Details":  null
                        },
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751482818274)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Wednesday, July 2, 2025 8:00:18 PM"
                                          },
                            "Name":  "Phase 3 Integration: EnhancedJiraManager",
                            "Message":  "Feature preserved",
                            "Details":  null
                        },
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751482818298)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Wednesday, July 2, 2025 8:00:18 PM"
                                          },
                            "Name":  "Phase 3 Integration: AdvancedADGroupManager",
                            "Message":  "Feature preserved",
                            "Details":  null
                        },
                        {
                            "Status":  "FAILED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751482818525)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Wednesday, July 2, 2025 8:00:18 PM"
                                          },
                            "Name":  "Region Structure",
                            "Message":  "Unbalanced regions: 31 regions, 30 endregions",
                            "Details":  null
                        },
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751482818552)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Wednesday, July 2, 2025 8:00:18 PM"
                                          },
                            "Name":  "Error Handling",
                            "Message":  "Good error handling coverage: 127 try blocks",
                            "Details":  null
                        },
                        {
                            "Status":  "FAILED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751482819991)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Wednesday, July 2, 2025 8:00:19 PM"
                                          },
                            "Name":  "Documentation Coverage",
                            "Message":  "Low documentation: 7.03% comment lines",
                            "Details":  null
                        }
                    ],
    "EndTime":  {
                    "value":  "\/Date(1751482819994)\/",
                    "DisplayHint":  2,
                    "DateTime":  "Wednesday, July 2, 2025 8:00:19 PM"
                },
    "TotalTests":  27
}
