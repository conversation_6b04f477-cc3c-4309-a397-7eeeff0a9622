# WPF Fix Verification Script
param(
    [string]$ScriptPath = "OnboardingFromJiraGUI.ps1",
    [switch]$TestParse,
    [switch]$TestLoad
)

Write-Host "=== WPF Fix Verification ===" -ForegroundColor Cyan

if ($TestParse) {
    Write-Host "Testing script parse..." -ForegroundColor Yellow
    try {
        $null = [System.Management.Automation.PSParser]::Tokenize((Get-Content $ScriptPath -Raw), [ref]$null)
        Write-Host "✓ Script parses successfully" -ForegroundColor Green
        return $true
    } catch {
        Write-Host "✗ Parse error: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

if ($TestLoad) {
    Write-Host "Testing script load..." -ForegroundColor Yellow
    try {
        . ".\$ScriptPath"
        Write-Host "✓ Script loads successfully" -ForegroundColor Green
        return $true
    } catch {
        Write-Host "✗ Load error: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Count remaining WPF type issues
$content = Get-Content $ScriptPath -Raw
$issues = @()

$issues += [regex]::Matches($content, '\[System\.Windows\.Media\.Brushes\]::') | ForEach-Object { "Brushes: Line $($_.Index)" }
$issues += [regex]::Matches($content, '\[System\.Windows\.GridUnitType\]::') | ForEach-Object { "GridUnitType: Line $($_.Index)" }
$issues += [regex]::Matches($content, '\[System\.Windows\.Controls\.Grid\]::') | ForEach-Object { "Grid static: Line $($_.Index)" }
$issues += [regex]::Matches($content, '\[System\.Windows\.Controls\.Grid\]\$') | ForEach-Object { "Grid param: Line $($_.Index)" }

Write-Host "Remaining WPF type issues: $($issues.Count)" -ForegroundColor $(if($issues.Count -eq 0) { 'Green' } else { 'Yellow' })

return $issues.Count -eq 0