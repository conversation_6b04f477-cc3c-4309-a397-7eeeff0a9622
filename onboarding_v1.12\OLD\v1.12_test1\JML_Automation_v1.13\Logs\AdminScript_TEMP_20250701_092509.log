2025-07-01 09:25:09.888 | akinje          | INFO     | JML Automation Script v1.13 starting...
2025-07-01 09:25:09.890 | akinje          | DEBUG    | Script path: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13
2025-07-01 09:25:09.903 | akinje          | DEBUG    | PowerShell version: 7.5.1
2025-07-01 09:25:09.918 | akinje          | DEBUG    | Execution policy: RemoteSigned
2025-07-01 09:25:09.929 | akinje          | DEBUG    | User: akinje
2025-07-01 09:25:09.943 | akinje          | DEBUG    | Computer: JGM-CG7THR3
2025-07-01 09:25:09.957 | akinje          | INFO     | === JML ENVIRONMENT VALIDATION ===
2025-07-01 09:25:09.972 | akin<PERSON>          | INFO     | Verifying PowerShell Version...
2025-07-01 09:25:09.990 | akinje          | INFO     | === JML ENVIRONMENT VALIDATION ===
2025-07-01 09:25:10.005 | akinje          | INFO     | Verifying PowerShell Version...
2025-07-01 09:25:10.023 | akinje          | INFO     | PowerShell 7+ detected (version 7.5.1). Checking for updates...
2025-07-01 09:25:10.037 | akinje          | INFO     | PowerShell 7+ detected (version 7.5.1)
2025-07-01 09:25:10.054 | akinje          | INFO     | Checking for PowerShell updates online...
2025-07-01 09:25:10.067 | akinje          | DEBUG    | Checking for PowerShell updates online...
2025-07-01 09:25:10.277 | akinje          | INFO     | A newer version of PowerShell (7.5.2) is available.
2025-07-01 09:25:10.291 | akinje          | INFO     | Current version: 7.5.1
2025-07-01 09:25:10.307 | akinje          | INFO     | Download from: https://github.com/PowerShell/PowerShell/releases
2025-07-01 09:25:10.328 | akinje          | INFO     | === CHECKING REQUIRED MODULES ===
2025-07-01 09:25:10.344 | akinje          | INFO     | === CHECKING REQUIRED MODULES ===
2025-07-01 09:25:10.362 | akinje          | INFO     | Checking for required modules and available updates...
2025-07-01 09:25:11.313 | akinje          | INFO     | Checking module: Microsoft.PowerShell.SecretManagement...
2025-07-01 09:25:11.356 | akinje          | INFO     |   Found installed version 1.1.2
2025-07-01 09:25:11.843 | akinje          | INFO     |   Already up to date
2025-07-01 09:25:11.899 | akinje          | INFO     | Checking module: Microsoft.PowerShell.SecretStore...
2025-07-01 09:25:12.027 | akinje          | INFO     |   Found installed version 1.0.6
2025-07-01 09:25:12.471 | akinje          | INFO     |   Already up to date
2025-07-01 09:25:12.488 | akinje          | INFO     | Checking module: Microsoft.Graph...
2025-07-01 09:25:12.537 | akinje          | INFO     |   Found installed version 2.28.0
2025-07-01 09:25:15.236 | akinje          | INFO     |   Already up to date
2025-07-01 09:25:15.253 | akinje          | INFO     | Checking module: JiraPS...
2025-07-01 09:25:15.305 | akinje          | INFO     |   Found installed version 2.14.7
2025-07-01 09:25:16.029 | akinje          | INFO     |   Already up to date
2025-07-01 09:25:16.074 | akinje          | INFO     | Checking module: ImportExcel...
2025-07-01 09:25:16.234 | akinje          | INFO     |   Found installed version 7.8.10
2025-07-01 09:25:16.982 | akinje          | INFO     |   Already up to date
2025-07-01 09:25:17.017 | akinje          | INFO     | Checking module: PSWriteHTML...
2025-07-01 09:25:17.157 | akinje          | INFO     |   Found installed version 1.36.0
2025-07-01 09:25:18.403 | akinje          | INFO     |   Already up to date
2025-07-01 09:25:18.423 | akinje          | INFO     | All modules are up to date.
2025-07-01 09:25:18.444 | akinje          | INFO     | Verifying module availability...
2025-07-01 09:25:18.489 | akinje          | INFO     |   [VERIFIED] Microsoft.PowerShell.SecretManagement v1.1.2 is available.
2025-07-01 09:25:18.536 | akinje          | INFO     |   [VERIFIED] Microsoft.PowerShell.SecretStore v1.0.6 is available.
2025-07-01 09:25:18.582 | akinje          | INFO     |   [VERIFIED] Microsoft.Graph v2.28.0 is available.
2025-07-01 09:25:18.630 | akinje          | INFO     |   [VERIFIED] JiraPS v2.14.7 is available.
2025-07-01 09:25:18.679 | akinje          | INFO     |   [VERIFIED] ImportExcel v7.8.10 is available.
2025-07-01 09:25:18.734 | akinje          | INFO     |   [VERIFIED] PSWriteHTML v1.36.0 is available.
2025-07-01 09:25:18.756 | akinje          | INFO     | All required modules are successfully installed and available.
2025-07-01 09:25:18.775 | akinje          | INFO     | === JML ENVIRONMENT VALIDATION COMPLETE ===
2025-07-01 09:25:18.799 | akinje          | DEBUG    | [DEBUG] Starting JML script execution...
2025-07-01 09:25:18.822 | akinje          | DEBUG    | [DEBUG] Script directory: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13
2025-07-01 09:25:18.845 | akinje          | DEBUG    | [DEBUG] Checking ActiveDirectory module availability...
2025-07-01 09:25:18.891 | akinje          | INFO     | [INFO] ActiveDirectory module is not available - running in UAT mode
2025-07-01 09:25:18.909 | akinje          | INFO     | [INFO] This is normal for UAT/test environments
2025-07-01 09:25:18.927 | akinje          | DEBUG    | [DEBUG] Initializing JML paths...
2025-07-01 09:25:18.950 | akinje          | DEBUG    | [DEBUG] JML Paths initialized globally.
2025-07-01 09:25:18.970 | akinje          | DEBUG    | [DEBUG] Project Root: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13
2025-07-01 09:25:18.990 | akinje          | DEBUG    | [DEBUG] Modules Root: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Modules
2025-07-01 09:25:19.008 | akinje          | DEBUG    | [DEBUG] Config Dir: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Config
2025-07-01 09:25:19.026 | akinje          | DEBUG    | [DEBUG] Updated ConfigPath to use Config directory: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Config\AdminAccountConfig.psd1
2025-07-01 09:25:19.044 | akinje          | DEBUG    | [DEBUG] Starting enhanced module loading...
2025-07-01 09:25:19.062 | akinje          | INFO     | Loading JML modules from nested structure...
2025-07-01 09:25:19.080 | akinje          | INFO     | Modules Root: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Modules
2025-07-01 09:25:19.099 | akinje          | INFO     |   Loading: JML-Core/JML-Utilities [CRITICAL]
2025-07-01 09:25:19.118 | akinje          | INFO     |     Description: General utility functions
2025-07-01 09:25:19.138 | akinje          | INFO     |     Path: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Modules\JML-Core\JML-Utilities.psd1 (manifest)
2025-07-01 09:25:19.157 | akinje          | INFO     |     Attempting to import module using manifest...
2025-07-01 09:25:19.242 | akinje          | WARNING  | WARNING: JML-Security: JML-Configuration module functions not available. Some features may be limited.
2025-07-01 09:25:19.351 | akinje          | WARNING  | WARNING: JML-Logging: Missing dependencies: Get-ModuleConfiguration (JML-Configuration), Protect-SensitiveData (JML-Security). Some features may be limited.
2025-07-01 09:25:19.368 | akinje          | INFO     |     [SUCCESS] Module loaded: JML-Core/JML-Utilities
2025-07-01 09:25:19.427 | akinje          | INFO     |     [WARNING] Critical function Get-StringHash not available after import
2025-07-01 09:25:19.446 | akinje          | INFO     |     [VERIFIED] Function Test-ModuleAvailability is available
2025-07-01 09:25:19.467 | akinje          | INFO     |   Loading: JML-Core/JML-Configuration [CRITICAL]
2025-07-01 09:25:19.492 | akinje          | INFO     |     Description: Core configuration management
2025-07-01 09:25:19.512 | akinje          | INFO     |     Path: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Modules\JML-Core\JML-Configuration.psd1 (manifest)
2025-07-01 09:25:19.532 | akinje          | INFO     |     Attempting to import module using manifest...
2025-07-01 09:25:19.561 | akinje          | INFO     |     [SUCCESS] Module loaded: JML-Core/JML-Configuration
2025-07-01 09:25:19.580 | akinje          | INFO     |     [VERIFIED] Function Initialize-SmartConfiguration is available
2025-07-01 09:25:19.601 | akinje          | INFO     |     [VERIFIED] Function Get-ModuleConfiguration is available
2025-07-01 09:25:19.625 | akinje          | INFO     |   Loading: JML-Core/JML-Security [CRITICAL]
2025-07-01 09:25:19.646 | akinje          | INFO     |     Description: Security and credential management
2025-07-01 09:25:19.666 | akinje          | INFO     |     Path: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Modules\JML-Core\JML-Security.psd1 (manifest)
2025-07-01 09:25:19.688 | akinje          | INFO     |     Attempting to import module using manifest...
2025-07-01 09:25:19.716 | akinje          | INFO     |     [SUCCESS] Module loaded: JML-Core/JML-Security
2025-07-01 09:25:19.740 | akinje          | INFO     |     [VERIFIED] Function Get-SecureCredential is available
2025-07-01 09:25:19.820 | akinje          | INFO     |     [WARNING] Critical function Set-SecureCredential not available after import
2025-07-01 09:25:19.850 | akinje          | INFO     |   Loading: JML-Core/JML-Logging [CRITICAL]
2025-07-01 09:25:19.881 | akinje          | INFO     |     Description: Secure logging functionality
2025-07-01 09:25:19.914 | akinje          | INFO     |     Path: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Modules\JML-Core\JML-Logging.psd1 (manifest)
2025-07-01 09:25:19.945 | akinje          | INFO     |     Attempting to import module using manifest...
2025-07-01 09:25:20.002 | akinje          | INFO     |     [SUCCESS] Module loaded: JML-Core/JML-Logging
2025-07-01 09:25:20.039 | akinje          | INFO     |     [VERIFIED] Function Write-SecureLog is available
2025-07-01 09:25:20.179 | akinje          | INFO     |     [WARNING] Critical function Initialize-LoggingSystem not available after import
2025-07-01 09:25:20.228 | akinje          | INFO     |   Loading: JML-Features/JML-ActiveDirectory [OPTIONAL]
2025-07-01 09:25:20.271 | akinje          | INFO     |     Description: Active Directory operations
2025-07-01 09:25:20.306 | akinje          | INFO     |     Path: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Modules\JML-Features\JML-ActiveDirectory.psd1 (manifest)
2025-07-01 09:25:20.337 | akinje          | INFO     |     Attempting to import module using manifest...
2025-07-01 09:25:20.420 | akinje          | INFO     |     [SUCCESS] Module loaded: JML-Features/JML-ActiveDirectory
2025-07-01 09:25:20.449 | akinje          | INFO     |   Loading: JML-Features/JML-Email [OPTIONAL]
2025-07-01 09:25:20.482 | akinje          | INFO     |     Description: Email notification functionality
2025-07-01 09:25:20.516 | akinje          | INFO     |     Path: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Modules\JML-Features\JML-Email.psd1 (manifest)
2025-07-01 09:25:20.556 | akinje          | INFO     |     Attempting to import module using manifest...
2025-07-01 09:25:20.620 | akinje          | INFO     |     [SUCCESS] Module loaded: JML-Features/JML-Email
2025-07-01 09:25:20.651 | akinje          | INFO     |   Loading: JML-Features/JML-Jira [OPTIONAL]
2025-07-01 09:25:20.679 | akinje          | INFO     |     Description: Jira integration
2025-07-01 09:25:20.705 | akinje          | INFO     |     Path: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Modules\JML-Features\JML-Jira.psd1 (manifest)
2025-07-01 09:25:20.730 | akinje          | INFO     |     Attempting to import module using manifest...
2025-07-01 09:25:20.764 | akinje          | INFO     |     [SUCCESS] Module loaded: JML-Features/JML-Jira
2025-07-01 09:25:20.792 | akinje          | INFO     |   Loading: JML-Infrastructure/JML-Setup [OPTIONAL]
2025-07-01 09:25:20.817 | akinje          | INFO     |     Description: Setup and environment validation
2025-07-01 09:25:20.848 | akinje          | INFO     |     Path: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Modules\JML-Infrastructure\JML-Setup.psd1 (manifest)
2025-07-01 09:25:20.875 | akinje          | INFO     |     Attempting to import module using manifest...
2025-07-01 09:25:20.915 | akinje          | INFO     |     [SUCCESS] Module loaded: JML-Infrastructure/JML-Setup
2025-07-01 09:25:20.940 | akinje          | INFO     |   Loading: JML-Infrastructure/JML-Monitoring [OPTIONAL]
2025-07-01 09:25:20.966 | akinje          | INFO     |     Description: System monitoring
2025-07-01 09:25:20.991 | akinje          | INFO     |     Path: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Modules\JML-Infrastructure\JML-Monitoring.psd1 (manifest)
2025-07-01 09:25:21.024 | akinje          | INFO     |     Attempting to import module using manifest...
2025-07-01 09:25:21.072 | akinje          | INFO     |     [SUCCESS] Module loaded: JML-Infrastructure/JML-Monitoring
2025-07-01 09:25:21.102 | akinje          | INFO     |   Loading: JML-Infrastructure/JML-UIManager [OPTIONAL]
2025-07-01 09:25:21.143 | akinje          | INFO     |     Description: Modern hybrid dashboard UI with real-time status
2025-07-01 09:25:21.170 | akinje          | INFO     |     Path: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Modules\JML-Infrastructure\JML-UIManager.psd1 (manifest)
2025-07-01 09:25:21.201 | akinje          | INFO     |     Attempting to import module using manifest...
2025-07-01 09:25:21.248 | akinje          | INFO     |     [SUCCESS] Module loaded: JML-Infrastructure/JML-UIManager
2025-07-01 09:25:21.276 | akinje          | INFO     | Module Loading Summary:
2025-07-01 09:25:21.301 | akinje          | INFO     |   Successfully loaded: 10/10
2025-07-01 09:25:21.328 | akinje          | DEBUG    | [DEBUG] Enhanced module loading completed successfully
2025-07-01 09:25:21.357 | akinje          | DEBUG    | [DEBUG] Performing post-loading verification...
2025-07-01 09:25:21.382 | akinje          | DEBUG    | [DEBUG] ✓ Initialize-SmartConfiguration is available
2025-07-01 09:25:21.408 | akinje          | DEBUG    | [DEBUG] ✓ Get-ModuleConfiguration is available
2025-07-01 09:25:57.276 | akinje          | INFO     | Configuration loaded from: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Config\AdminAccountConfig.psd1
2025-07-01 09:25:57.296 | akinje          | INFO     | [SUCCESS] Configuration initialized successfully
2025-07-01 09:25:57.316 | akinje          | DEBUG    | [DEBUG] Initializing credential storage selection...
2025-07-01 09:25:57.396 | akinje          | INFO     | ================================================================================
2025-07-01 09:25:57.418 | akinje          | INFO     |                     JML Credential Storage Selection                      
2025-07-01 09:25:57.439 | akinje          | INFO     | ================================================================================
2025-07-01 09:25:57.460 | akinje          | INFO     | Please select your preferred credential storage method:
2025-07-01 09:25:57.484 | akinje          | INFO     |   1. Encrypted File (Recommended for Portability)
2025-07-01 09:25:57.506 | akinje          | INFO     |      - Stores credentials in an encrypted XML file
2025-07-01 09:25:57.530 | akinje          | INFO     |      - Portable across different machines
2025-07-01 09:25:57.555 | akinje          | INFO     |      - Uses Windows DPAPI for encryption
2025-07-01 09:25:57.578 | akinje          | INFO     |   2. PowerShell Secret Management / SecretStore
2025-07-01 09:25:57.600 | akinje          | INFO     |      - Uses Microsoft.PowerShell.SecretStore module
2025-07-01 09:25:57.622 | akinje          | INFO     |      - Requires vault password for access
2025-07-01 09:25:57.644 | akinje          | INFO     |      - More secure but requires setup
2025-07-01 09:25:57.668 | akinje          | INFO     |   3. Windows Credential Manager
2025-07-01 09:25:57.689 | akinje          | INFO     |      - Uses Windows built-in credential storage
2025-07-01 09:25:57.710 | akinje          | INFO     |      - Integrated with Windows security
2025-07-01 09:25:57.733 | akinje          | INFO     |      - Machine-specific storage
2025-07-01 09:25:57.757 | akinje          | INFO     | Enter your choice (1-3) [Default: 1 in 10 seconds]: 
2025-07-01 09:26:04.296 | akinje          | INFO     | 1
2025-07-01 09:26:04.315 | akinje          | INFO     | Selected: Encrypted File storage
2025-07-01 09:26:04.332 | akinje          | INFO     | Credentials will be stored in an encrypted XML file for portability.
2025-07-01 09:26:04.352 | akinje          | INFO     | Credential storage method set for this session: EncryptedFile
2025-07-01 09:26:04.372 | akinje          | INFO     | Press any key to continue...
2025-07-01 09:26:07.203 | akinje          | DEBUG    | [DEBUG] Credential storage method selected: EncryptedFile
2025-07-01 09:26:07.226 | akinje          | DEBUG    | [DEBUG] Using credential method for status check: EncryptedFile
2025-07-01 09:26:07.246 | akinje          | DEBUG    | [DEBUG] Checking credential setup status for method: EncryptedFile
2025-07-01 09:26:07.264 | akinje          | DEBUG    | [DEBUG] Checking EncryptedFile credential storage
2025-07-01 09:26:07.281 | akinje          | DEBUG    | [DEBUG] Checking for encrypted file at: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Config\SecureCredentials.xml
2025-07-01 09:26:07.314 | akinje          | DEBUG    | [DEBUG] EncryptedFile credentials appear to be configured and accessible
2025-07-01 09:26:07.332 | akinje          | INFO     | === JML SERVICE-ORIENTED ARCHITECTURE INITIALIZATION ===
2025-07-01 09:26:07.351 | akinje          | INFO     | Creating JML context...
2025-07-01 09:26:07.401 | akinje          | INFO     | Registering core services...
2025-07-01 09:26:07.417 | akinje          | DEBUG    | Transitioned from early logging to full logging system
