﻿2025-07-01 09:32:46.688 | akinje          | INFO     | JML Automation Script v1.13 starting...
2025-07-01 09:32:46.705 | akinje          | DEBUG    | Script path: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13
2025-07-01 09:32:46.716 | akinje          | DEBUG    | PowerShell version: 5.1.19041.5848
2025-07-01 09:32:46.788 | akinje          | DEBUG    | Execution policy: Bypass
2025-07-01 09:32:46.795 | akinje          | DEBUG    | User: akinje
2025-07-01 09:32:46.795 | akinje          | DEBUG    | Computer: JGM-CG7THR3
2025-07-01 09:32:46.898 | akinje          | INFO     | === JML ENVIRONMENT VALIDATION ===
2025-07-01 09:32:46.905 | akin<PERSON>          | INFO     | Verifying PowerShell Version...
2025-07-01 09:32:46.906 | akinje          | INFO     | === JML ENVIRONMENT VALIDATION ===
2025-07-01 09:32:46.906 | akinje          | INFO     | Verifying PowerShell Version...
2025-07-01 09:32:46.935 | akinje          | WARNING  | WARNING: PowerShell 7 or higher is recommended for optimal performance and security.
2025-07-01 09:32:46.938 | akinje          | INFO     | Current version: PowerShell 5.1.19041.5848
2025-07-01 09:32:46.948 | akinje          | WARNING  | PowerShell 7+ recommended. Current: 5.1.19041.5848
2025-07-01 09:32:46.965 | akinje          | INFO     | Searching for PowerShell 7 installation...
2025-07-01 09:32:46.982 | akinje          | INFO     |   Checking: C:\Program Files\PowerShell\7\pwsh.exe
2025-07-01 09:32:47.279 | akinje          | INFO     |   Found PowerShell 7.5.1 at: C:\Program Files\PowerShell\7\pwsh.exe
2025-07-01 09:32:47.288 | akinje          | INFO     | PowerShell 7 is installed at: C:\Program Files\PowerShell\7\pwsh.exe
2025-07-01 09:32:47.375 | akinje          | INFO     | Options:
2025-07-01 09:32:47.386 | akinje          | INFO     |   1. Continue with current PowerShell 5.x
2025-07-01 09:32:47.388 | akinje          | INFO     |   2. Switch to PowerShell 7 (recommended)
2025-07-01 09:32:47.403 | akinje          | INFO     |   3. Exit and manually switch
2025-07-01 09:32:47.421 | akinje          | INFO     | Select option (1-3) [Default: 2 in 10 seconds]: 
2025-07-01 09:32:57.530 | akinje          | INFO     | 2 (default)
2025-07-01 09:32:57.535 | akinje          | INFO     | === POWERSHELL 7 EXECUTION OPTIONS ===
2025-07-01 09:32:57.546 | akinje          | INFO     | PowerShell 7 is available at: C:\Program Files\PowerShell\7\pwsh.exe
2025-07-01 09:32:57.560 | akinje          | INFO     | How would you like to run the script in PowerShell 7?
2025-07-01 09:32:57.575 | akinje          | INFO     |   [N] New Window - Launch in a new PowerShell 7 window
2025-07-01 09:32:57.630 | akinje          | INFO     |   [C] Current Window - Run PowerShell 7 in this window (default)
2025-07-01 09:32:57.641 | akinje          | INFO     | Choose option (N/C) - Waiting 5 seconds (default: Current Window)...
2025-07-01 09:33:02.748 | akinje          | INFO     | Selected: Current Window (default)
2025-07-01 09:33:02.759 | akinje          | INFO     | Starting PowerShell 7 in current window...
2025-07-01 09:33:02.766 | akinje          | INFO     | Script will continue in PowerShell 7...
