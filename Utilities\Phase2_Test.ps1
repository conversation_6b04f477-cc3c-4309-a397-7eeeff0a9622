# Phase 2 Test - Centralized Debug System
# Test that the debug system is working

# Load the LoggingService class from the main file
$serviceLines = Get-Content "/mnt/c/Users/<USER>/OneDrive - JERA Global Markets/Azure Devops/it-endusersupport-automation/scripts/Utilities/OnboardingFromJiraGUI_v2.ps1" | Select-Object -Skip 293 -First 70
$serviceScript = $serviceLines -join "`n"

# Execute the service class
Invoke-Expression $serviceScript

Write-Host "=== Phase 2 Debug System Test ===" -ForegroundColor Green

# Test 1: Create LoggingService and check debug mode
Write-Host "Test 1: LoggingService with debug mode" -ForegroundColor Cyan
$logger = [LoggingService]::new()
if ($logger.DebugMode -eq $true) {
    Write-Host "✅ Debug mode is ON by default" -ForegroundColor Green
} else {
    Write-Host "❌ Debug mode should be ON by default" -ForegroundColor Red
}

# Test 2: Test debug method with operation context
Write-Host "`nTest 2: Debug method with operation context" -ForegroundColor Cyan
$logger.Debug("TestOperation", "This is a test debug message")
Write-Host "✅ Debug method called successfully" -ForegroundColor Green

# Test 3: Test debug mode toggle
Write-Host "`nTest 3: Debug mode toggle" -ForegroundColor Cyan
Write-Host "Turning debug OFF..." -ForegroundColor Yellow
$logger.SetDebugMode($false)
if ($logger.GetDebugMode() -eq $false) {
    Write-Host "✅ Debug mode turned OFF" -ForegroundColor Green
} else {
    Write-Host "❌ Debug mode toggle failed" -ForegroundColor Red
}

# Test 4: Verify debug messages are suppressed when OFF
Write-Host "`nTest 4: Debug suppression when OFF" -ForegroundColor Cyan
Write-Host "Calling debug method while OFF (should not show DEBUG log):" -ForegroundColor Yellow
$logger.Debug("SuppressedOperation", "This should not appear in logs")
Write-Host "✅ Debug suppression test completed" -ForegroundColor Green

# Test 5: Turn debug back ON
Write-Host "`nTest 5: Turn debug back ON" -ForegroundColor Cyan
$logger.SetDebugMode($true)
Write-Host "Calling debug method while ON (should show DEBUG log):" -ForegroundColor Yellow
$logger.Debug("EnabledOperation", "This should appear in logs")
if ($logger.GetDebugMode() -eq $true) {
    Write-Host "✅ Debug mode turned back ON" -ForegroundColor Green
} else {
    Write-Host "❌ Debug mode re-enable failed" -ForegroundColor Red
}

# Test 6: Test other log levels still work
Write-Host "`nTest 6: Other log levels still work" -ForegroundColor Cyan
$logger.Info("Test info message")
$logger.Warn("Test warning message")
Write-Host "✅ Other log levels working" -ForegroundColor Green

Write-Host "`n=== Phase 2 Test Complete ===" -ForegroundColor Green
Write-Host "Debug system is ready for UI integration!" -ForegroundColor Yellow