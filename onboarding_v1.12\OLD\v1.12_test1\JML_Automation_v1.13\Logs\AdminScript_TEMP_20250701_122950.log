2025-07-01 12:29:50.946 | JERAGM\akinje   | DEBUG    | Credential storage method auto-selected due to timeout | AUDIT: Method=EncryptedFile; TimeoutSeconds=10; Operation=CredentialStorageSelection; SelectionType=Automatic
2025-07-01 12:31:59.695 | JERAGM\akinje   | DEBUG    | JML Context initialized successfully | AUDIT: ScriptRoot=C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13; SessionId=b75ac41c-6d2b-4832-965b-36f55c02d1be; Operation=ContextInitialization
2025-07-01 12:31:59.787 | JERAGM\akinje   | DEBUG    | Configuration loaded successfully | AUDIT: ConfigType=System.Collections.Hashtable; KeyCount=12; Operation=ConfigurationLoading; ConfigPath=C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Config\AdminAccountConfig.psd1
2025-07-01 12:31:59.812 | JERAGM\akinje   | INFO     | Setting up credential management...
2025-07-01 12:31:59.842 | JERAGM\akinje   | INFO     | Using credential storage method: EncryptedFile
2025-07-01 12:31:59.873 | JERAGM\akinje   | INFO     | Encrypted file credential manager registered
2025-07-01 12:31:59.897 | JERAGM\akinje   | INFO     | Validating service health...
2025-07-01 12:31:59.937 | JERAGM\akinje   | INFO     | All services are healthy and ready
2025-07-01 12:31:59.962 | JERAGM\akinje   | INFO     | JML service-oriented architecture initialized successfully!
2025-07-01 12:31:59.983 | JERAGM\akinje   | INFO     | Session ID: b75ac41c-6d2b-4832-965b-36f55c02d1be
2025-07-01 12:32:00.008 | JERAGM\akinje   | INFO     | Initializing UI Manager service...
2025-07-01 12:32:00.289 | JERAGM\akinje   | WARNING  | WARNING: UIManager initialization failed: Exception calling "UpdateTerminalSize" with "0" argument(s): "Method invocation failed because [System.Management.Automation.PSCustomObject] does not contain a method named 'CalculateDynamicWidths'."
2025-07-01 12:32:00.562 | JERAGM\akinje   | ERROR    | ERROR: Failed to create UIManager service: UIManager service initialization failed
2025-07-01 12:32:00.589 | JERAGM\akinje   | ERROR    | UI Manager service initialization failed - using fallback UI
2025-07-01 12:32:00.613 | JERAGM\akinje   | INFO     | Verifying critical module availability...
2025-07-01 12:32:00.642 | JERAGM\akinje   | DEBUG    | [DEBUG] Checking credential setup status for method: EncryptedFile
2025-07-01 12:32:00.668 | JERAGM\akinje   | DEBUG    | [DEBUG] Checking EncryptedFile credential storage
2025-07-01 12:32:00.700 | JERAGM\akinje   | DEBUG    | [DEBUG] Checking for encrypted file at: [REDACTED_PATH]s\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Config\SecureCredentials.xml
2025-07-01 12:32:00.730 | JERAGM\akinje   | DEBUG    | [DEBUG] EncryptedFile credentials appear to be configured and accessible
2025-07-01 12:32:01.270 | JERAGM\akinje   | INFO     | Credential setup complete. Loading main menu...
2025-07-01 12:32:01.346 | JERAGM\akinje   | INFO     | [INFO] Initializing Jira session...
2025-07-01 12:32:01.428 | JERAGM\akinje   | DEBUG    | [DEBUG] Received credential types:
2025-07-01 12:32:01.469 | JERAGM\akinje   | DEBUG    | [DEBUG] - jiraUsername type: String
2025-07-01 12:32:01.515 | JERAGM\akinje   | DEBUG    | [DEBUG] - jiraApiToken type: SecureString
2025-07-01 12:32:01.556 | JERAGM\akinje   | DEBUG    | [DEBUG] Credential validation passed
2025-07-01 12:32:01.595 | JERAGM\akinje   | DEBUG    | [DEBUG] Username format: Valid email
2025-07-01 12:32:01.635 | JERAGM\akinje   | DEBUG    | [DEBUG] API token=[REDACTED] (length: 192)
2025-07-01 12:32:01.666 | JERAGM\akinje   | DEBUG    | [DEBUG] Final PSCredential object:
2025-07-01 12:32:01.703 | JERAGM\akinje   | DEBUG    | [DEBUG] - Username: emmanuel.akinjomo***@jeragm.com
2025-07-01 12:32:01.732 | JERAGM\akinje   | DEBUG    | [DEBUG] - Password type: SecureString
2025-07-01 12:32:01.760 | JERAGM\akinje   | INFO     | [INFO] Connecting to Jira server: [SERVER]
2025-07-01 12:32:01.794 | JERAGM\akinje   | INFO     | [INFO] Using username: emmanuel.akinjomo***@jeragm.com
2025-07-01 12:32:01.823 | JERAGM\akinje   | INFO     | Initializing Jira connection | AUDIT: ServerUrl=[SERVER]; Operation=JiraConnectionInit; Username=emmanuel.akinjomo***@jeragm.com
2025-07-01 12:32:01.862 | JERAGM\akinje   | INFO     | Loading JiraPS module
2025-07-01 12:32:02.084 | JERAGM\akinje   | INFO     | Setting Jira server configuration
2025-07-01 12:32:02.110 | JERAGM\akinje   | INFO     | Creating JiraPS session for module compatibility
2025-07-01 12:32:03.056 | JERAGM\akinje   | INFO     | JiraPS session created successfully
2025-07-01 12:32:03.081 | JERAGM\akinje   | INFO     | Creating custom session using direct authentication
2025-07-01 12:32:03.121 | JERAGM\akinje   | INFO     | Testing Jira connection using GDPR-compliant endpoint
2025-07-01 12:32:03.954 | JERAGM\akinje   | INFO     | Jira connection test successful | AUDIT: UserAccountId=712020:65c99194-b77b-498d-8a7e-e1dd4dd40efa; Status=Success; UserDisplayName=Emmanuel Akinjomo; Operation=JiraConnectionTest
2025-07-01 12:32:03.976 | JERAGM\akinje   | INFO     | Jira connection established successfully.
2025-07-01 12:32:03.995 | JERAGM\akinje   | INFO     | Connected as: Emmanuel Akinjomo (emmanuel.akinjomo***@jeragm.com)
2025-07-01 12:32:04.016 | JERAGM\akinje   | INFO     | Jira connection initialized successfully using JiraPS module
2025-07-01 12:32:04.037 | JERAGM\akinje   | INFO     | [SUCCESS] Jira session initialized successfully
2025-07-01 12:32:04.056 | JERAGM\akinje   | INFO     | Jira authentication and connection successful!
2025-07-01 12:32:04.078 | JERAGM\akinje   | INFO     | JML System started in interactive mode | AUDIT: SkipJiraIntegration=False; Version=1.13; ExecutingUser=JERAGM\akinje; ComputerName=JGM-CG7THR3; CredentialsAvailable=True; Operation=SystemStartup; SecretStoreConfigured=False
2025-07-01 12:32:04.131 | JERAGM\akinje   | INFO     | ================================================================================
2025-07-01 12:32:04.151 | JERAGM\akinje   | INFO     |                     JML (Joiners, Movers and Leavers) System                      
2025-07-01 12:32:04.169 | JERAGM\akinje   | INFO     |                               Version 1.13                                    
2025-07-01 12:32:04.189 | JERAGM\akinje   | INFO     | ================================================================================
2025-07-01 12:32:04.210 | JERAGM\akinje   | INFO     | System Status:
2025-07-01 12:32:04.230 | JERAGM\akinje   | INFO     |   User: JERAGM\akinje on JGM-CG7THR3
2025-07-01 12:32:04.248 | JERAGM\akinje   | INFO     |   Configuration: Loaded
2025-07-01 12:32:04.267 | JERAGM\akinje   | INFO     |   Credential Method: EncryptedFile
2025-07-01 12:32:04.285 | JERAGM\akinje   | DEBUG    | [DEBUG] Checking credential setup status for method: EncryptedFile
2025-07-01 12:32:04.304 | JERAGM\akinje   | DEBUG    | [DEBUG] Checking EncryptedFile credential storage
2025-07-01 12:32:04.321 | JERAGM\akinje   | DEBUG    | [DEBUG] Checking for encrypted file at: [REDACTED_PATH]s\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Config\SecureCredentials.xml
2025-07-01 12:32:04.343 | JERAGM\akinje   | DEBUG    | [DEBUG] EncryptedFile credentials appear to be configured and accessible
2025-07-01 12:32:04.362 | JERAGM\akinje   | INFO     |   Vault Status: Not Configured
2025-07-01 12:32:04.380 | JERAGM\akinje   | INFO     |   Jira Integration: Enabled
2025-07-01 12:32:04.409 | JERAGM\akinje   | INFO     |   Active Directory: NOT AVAILABLE (UAT Mode)
2025-07-01 12:32:04.427 | JERAGM\akinje   | INFO     | Available Operations:
2025-07-01 12:32:04.446 | JERAGM\akinje   | INFO     |   1. Create JML Account
2025-07-01 12:32:04.465 | JERAGM\akinje   | INFO     |   2. Delete JML Account
2025-07-01 12:32:04.484 | JERAGM\akinje   | INFO     |   3. Reset JML Account Password
2025-07-01 12:32:04.508 | JERAGM\akinje   | INFO     |   4. System Information
2025-07-01 12:32:04.530 | JERAGM\akinje   | INFO     |   5. Run Setup / Repair Credentials
2025-07-01 12:32:04.552 | JERAGM\akinje   | INFO     |   7. Exit
2025-07-01 12:32:04.573 | JERAGM\akinje   | INFO     | Enter your choice: 
2025-07-01 12:32:34.275 | JERAGM\akinje   | INFO     | === CREATE JML ACCOUNT ===
2025-07-01 12:32:41.223 | JERAGM\akinje   | INFO     | === JML ACCOUNT CREATION ===
2025-07-01 12:32:41.241 | JERAGM\akinje   | INFO     | JML System v1.13
2025-07-01 12:32:41.264 | JERAGM\akinje   | INFO     | Processing Jira ticket: TESTIT-49342
2025-07-01 12:32:41.282 | JERAGM\akinje   | INFO     | [INFO] Initializing Jira session...
2025-07-01 12:32:41.302 | JERAGM\akinje   | DEBUG    | [DEBUG] Received credential types:
2025-07-01 12:32:41.323 | JERAGM\akinje   | DEBUG    | [DEBUG] - jiraUsername type: String
2025-07-01 12:32:41.345 | JERAGM\akinje   | DEBUG    | [DEBUG] - jiraApiToken type: SecureString
2025-07-01 12:32:41.366 | JERAGM\akinje   | DEBUG    | [DEBUG] Credential validation passed
2025-07-01 12:32:41.386 | JERAGM\akinje   | DEBUG    | [DEBUG] Username format: Valid email
2025-07-01 12:32:41.407 | JERAGM\akinje   | DEBUG    | [DEBUG] API token=[REDACTED] (length: 192)
2025-07-01 12:32:41.438 | JERAGM\akinje   | DEBUG    | [DEBUG] Final PSCredential object:
2025-07-01 12:32:41.459 | JERAGM\akinje   | DEBUG    | [DEBUG] - Username: emmanuel.akinjomo***@jeragm.com
2025-07-01 12:32:41.481 | JERAGM\akinje   | DEBUG    | [DEBUG] - Password type: SecureString
2025-07-01 12:32:41.505 | JERAGM\akinje   | INFO     | [INFO] Connecting to Jira server: [SERVER]
2025-07-01 12:32:41.527 | JERAGM\akinje   | INFO     | [INFO] Using username: emmanuel.akinjomo***@jeragm.com
2025-07-01 12:32:41.548 | JERAGM\akinje   | INFO     | Initializing Jira connection | AUDIT: ServerUrl=[SERVER]; Operation=JiraConnectionInit; Username=emmanuel.akinjomo***@jeragm.com
2025-07-01 12:32:41.569 | JERAGM\akinje   | INFO     | Setting Jira server configuration
2025-07-01 12:32:41.590 | JERAGM\akinje   | INFO     | Creating JiraPS session for module compatibility
2025-07-01 12:32:42.425 | JERAGM\akinje   | INFO     | JiraPS session created successfully
2025-07-01 12:32:42.445 | JERAGM\akinje   | INFO     | Creating custom session using direct authentication
2025-07-01 12:32:42.465 | JERAGM\akinje   | INFO     | Testing Jira connection using GDPR-compliant endpoint
2025-07-01 12:32:43.067 | JERAGM\akinje   | INFO     | Jira connection test successful | AUDIT: UserAccountId=712020:65c99194-b77b-498d-8a7e-e1dd4dd40efa; Status=Success; UserDisplayName=Emmanuel Akinjomo; Operation=JiraConnectionTest
2025-07-01 12:32:43.087 | JERAGM\akinje   | INFO     | Jira connection established successfully.
2025-07-01 12:32:43.107 | JERAGM\akinje   | INFO     | Connected as: Emmanuel Akinjomo (emmanuel.akinjomo***@jeragm.com)
2025-07-01 12:32:43.125 | JERAGM\akinje   | INFO     | Jira connection initialized successfully using JiraPS module
2025-07-01 12:32:43.146 | JERAGM\akinje   | INFO     | [SUCCESS] Jira session initialized successfully
2025-07-01 12:32:43.170 | JERAGM\akinje   | INFO     | Validating Jira ticket | AUDIT: Operation=JiraTicketValidation; TicketKey=TESTIT-49342
2025-07-01 12:32:44.013 | JERAGM\akinje   | INFO     | Retrieved Jira ticket successfully | AUDIT: TicketSummary=; IssueTypeExists=False; FieldsAvailable=; TicketKey=TESTIT-49342; Operation=JiraTicketRetrieval; IssueTypeType=null
2025-07-01 12:32:44.067 | JERAGM\akinje   | DEBUG    | Full ticket structure for debugging | AUDIT: TicketStructure={"customfield_10211":null,"customfield_10321":null,"customfield_10374":null,"customfield_10335":null,"customfield_10368":null,"customfield_10069":null,"customfield_10385":null,"customfield_10222":null,"Comment":[{"Body":"New Joiner Name: Test1 Test2 \n\nJob Title: Intern \n\nDepartment: IT \n\nLine Manager: Edmund Chan \n\nReporter: Hideki Magtoto \n\nModel Account: test \n\n\n\n\n\nApplication Name: C9 Trader \n| \\\\  \\\\  \\\\  \\\\  \\\\  \\\\  \\\\ \n| \\\\ [ \\\\ target=\"_blank\"  \\\\ style=\"font-size: 18px; font-family: Arial, sans-serif; color: #ffffff; text-decoration: none; padding: 10px 25px; display: inline-block; border-radius: 3px; background-color: #068000; border: 1px solid #068000;\"> \\\\ Approve \\\\ |mailto:<EMAIL>?subject=Re:%20TESTIT-49342%20C9 Trader%20Approval%20Email&body=Approved] \\\\ | | \\\\ [ \\\\ target=\"_blank\"  \\\\ style=\"font-size: 18px; font-family: Arial, sans-serif; color: #ffffff; text-decoration: none; padding: 10px 25px; display: inline-block; border-radius: 3px; background-color: #FD1B00; border: 1px solid #FD1B00;\"> \\\\ Decline \\\\ |mailto:<EMAIL>?subject=Re:%20TESTIT-49342%20Issue&body=Declined] \\\\ | \n\n\nFor any clarifications, please contact IT Support using the below channels: \n\n\n\n1. Email: <EMAIL> \n\n2. Hotline Numbers: \n\nJP: +81 800-111-0060 - Toll Free Number (Local Dialing) \n\nJP: +81 50-5050-8456 - International Dialing \n\nSG: +65 800 321 1666 - Toll Free Number (Local Dialing) \n\nSG: +65 6028 2098 - International Dialing \n\nUK: +44 20 4587 6167 \n\nUS: +1 919 280 2601 \n\n\n\nThanks & Regards \n\nEnd User Support Team \n\n\n\nTo view more information on this request, please click on this link: https://jeragm.atlassian.net/servicedesk/customer/portal/4/TESTIT-49342?created=true","RestUrl":"https://jeragm.atlassian.net/rest/api/2/issue/107637/comment/199652","UpdateAuthor":{"Locale":null,"RestUrl":"https://jeragm.atlassian.net/rest/api/2/user?accountId=qm%3Aa5ef4933-12d6-463a-98fb-7577ad45dda9%3Abc35f20b-d705-416b-ad88-5647c1a5d763","Active":true,"Groups":null,"DisplayName":"<EMAIL>","AvatarUrl":{"48x48":"https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/default-avatar.png","24x24":"https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/default-avatar.png","16x16":"https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/default-avatar.png","32x32":"https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/default-avatar.png"},"AccountId":"qm:a5ef4933-12d6-463a-98fb-7577ad45dda9:bc35f20b-d705-416b-ad88-5647c1a5d763","TimeZone":"Europe/London","Key":null,"Name":null,"EmailAddress":"<EMAIL>"},"Visibility":null,"Author":{"Locale":null,"RestUrl":"https://jeragm.atlassian.net/rest/api/2/user?accountId=qm%3Aa5ef4933-12d6-463a-98fb-7577ad45dda9%3Abc35f20b-d705-416b-ad88-5647c1a5d763","Active":true,"Groups":null,"DisplayName":"<EMAIL>","AvatarUrl":{"48x48":"https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/default-avatar.png","24x24":"https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/default-avatar.png","16x16":"https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/default-avatar.png","32x32":"https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/default-avatar.png"},"AccountId":"qm:a5ef4933-12d6-463a-98fb-7577ad45dda9:bc35f20b-d705-416b-ad88-5647c1a5d763","TimeZone":"Europe/London","Key":null,"Name":null,"EmailAddress":"<EMAIL>"},"ID":"199652","Updated":"2025-05-06T07:44:35.695+01:00","Created":"2025-05-06T07:44:35.695+01:00"},{"Body":"Approval email sent to\n\n","RestUrl":"https://jeragm.atlassian.net/rest/api/2/issue/107637/comment/199797","UpdateAuthor":{"Locale":null,"RestUrl":"https://jeragm.atlassian.net/rest/api/2/user?accountId=557058%3Af58131cb-b67d-43c7-b30d-6b58d40bd077","Active":true,"Groups":null,"DisplayName":"Automation for Jira","AvatarUrl":{"48x48":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png","24x24":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png","16x16":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png","32x32":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png"},"AccountId":"557058:f58131cb-b67d-43c7-b30d-6b58d40bd077","TimeZone":"Europe/London","Key":null,"Name":null,"EmailAddress":null},"Visibility":null,"Author":{"Locale":null,"RestUrl":"https://jeragm.atlassian.net/rest/api/2/user?accountId=557058%3Af58131cb-b67d-43c7-b30d-6b58d40bd077","Active":true,"Groups":null,"DisplayName":"Automation for Jira","AvatarUrl":{"48x48":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png","24x24":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png","16x16":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png","32x32":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png"},"AccountId":"557058:f58131cb-b67d-43c7-b30d-6b58d40bd077","TimeZone":"Europe/London","Key":null,"Name":null,"EmailAddress":null},"ID":"199797","Updated":"2025-05-07T02:57:34.707+01:00","Created":"2025-05-07T02:57:34.707+01:00"},{"Body":"Approval email sent to\n\n- [~hideki.magtoto]","RestUrl":"https://jeragm.atlassian.net/rest/api/2/issue/107637/comment/199810","UpdateAuthor":{"Locale":null,"RestUrl":"https://jeragm.atlassian.net/rest/api/2/user?accountId=557058%3Af58131cb-b67d-43c7-b30d-6b58d40bd077","Active":true,"Groups":null,"DisplayName":"Automation for Jira","AvatarUrl":{"48x48":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png","24x24":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png","16x16":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png","32x32":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png"},"AccountId":"557058:f58131cb-b67d-43c7-b30d-6b58d40bd077","TimeZone":"Europe/London","Key":null,"Name":null,"EmailAddress":null},"Visibility":null,"Author":{"Locale":null,"RestUrl":"https://jeragm.atlassian.net/rest/api/2/user?accountId=557058%3Af58131cb-b67d-43c7-b30d-6b58d40bd077","Active":true,"Groups":null,"DisplayName":"Automation for Jira","AvatarUrl":{"48x48":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png","24x24":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png","16x16":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png","32x32":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png"},"AccountId":"557058:f58131cb-b67d-43c7-b30d-6b58d40bd077","TimeZone":"Europe/London","Key":null,"Name":null,"EmailAddress":null},"ID":"199810","Updated":"2025-05-07T03:25:29.994+01:00","Created":"2025-05-07T03:25:29.994+01:00"},{"Body":"Approval email sent to\n\n- [~accountid:************************]","RestUrl":"https://jeragm.atlassian.net/rest/api/2/issue/107637/comment/199813","UpdateAuthor":{"Locale":null,"RestUrl":"https://jeragm.atlassian.net/rest/api/2/user?accountId=557058%3Af58131cb-b67d-43c7-b30d-6b58d40bd077","Active":true,"Groups":null,"DisplayName":"Automation for Jira","AvatarUrl":{"48x48":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png","24x24":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png","16x16":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png","32x32":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png"},"AccountId":"557058:f58131cb-b67d-43c7-b30d-6b58d40bd077","TimeZone":"Europe/London","Key":null,"Name":null,"EmailAddress":null},"Visibility":null,"Author":{"Locale":null,"RestUrl":"https://jeragm.atlassian.net/rest/api/2/user?accountId=557058%3Af58131cb-b67d-43c7-b30d-6b58d40bd077","Active":true,"Groups":null,"DisplayName":"Automation for Jira","AvatarUrl":{"48x48":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png","24x24":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png","16x16":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png","32x32":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png"},"AccountId":"557058:f58131cb-b67d-43c7-b30d-6b58d40bd077","TimeZone":"Europe/London","Key":null,"Name":null,"EmailAddress":null},"ID":"199813","Updated":"2025-05-07T03:27:34.07+01:00","Created":"2025-05-07T03:27:34.07+01:00"},{"Body":"TESTIT-49342 Approval email sent to\n\n- [~accountid:************************]\n","RestUrl":"https://jeragm.atlassian.net/rest/api/2/issue/107637/comment/199836","UpdateAuthor":{"Locale":null,"RestUrl":"https://jeragm.atlassian.net/rest/api/2/user?accountId=557058%3Af58131cb-b67d-43c7-b30d-6b58d40bd077","Active":true,"Groups":null,"DisplayName":"Automation for Jira","AvatarUrl":{"48x48":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png","24x24":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png","16x16":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png","32x32":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png"},"AccountId":"557058:f58131cb-b67d-43c7-b30d-6b58d40bd077","TimeZone":"Europe/London","Key":null,"Name":null,"EmailAddress":null},"Visibility":null,"Author":{"Locale":null,"RestUrl":"https://jeragm.atlassian.net/rest/api/2/user?accountId=557058%3Af58131cb-b67d-43c7-b30d-6b58d40bd077","Active":true,"Groups":null,"DisplayName":"Automation for Jira","AvatarUrl":{"48x48":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png","24x24":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png","16x16":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png","32x32":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png"},"AccountId":"557058:f58131cb-b67d-43c7-b30d-6b58d40bd077","TimeZone":"Europe/London","Key":null,"Name":null,"EmailAddress":null},"ID":"199836","Updated":"2025-05-07T05:01:10.828+01:00","Created":"2025-05-07T05:01:10.828+01:00"},{"Body":"Approval email sent to test group approvers group.","RestUrl":"https://jeragm.atlassian.net/rest/api/2/issue/107637/comment/199839","UpdateAuthor":{"Locale":null,"RestUrl":"https://jeragm.atlassian.net/rest/api/2/user?accountId=557058%3Af58131cb-b67d-43c7-b30d-6b58d40bd077","Active":true,"Groups":null,"DisplayName":"Automation for Jira","AvatarUrl":{"48x48":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png","24x24":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png","16x16":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png","32x32":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png"},"AccountId":"557058:f58131cb-b67d-43c7-b30d-6b58d40bd077","TimeZone":"Europe/London","Key":null,"Name":null,"EmailAddress":null},"Visibility":null,"Author":{"Locale":null,"RestUrl":"https://jeragm.atlassian.net/rest/api/2/user?accountId=557058%3Af58131cb-b67d-43c7-b30d-6b58d40bd077","Active":true,"Groups":null,"DisplayName":"Automation for Jira","AvatarUrl":{"48x48":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png","24x24":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png","16x16":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png","32x32":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png"},"AccountId":"557058:f58131cb-b67d-43c7-b30d-6b58d40bd077","TimeZone":"Europe/London","Key":null,"Name":null,"EmailAddress":null},"ID":"199839","Updated":"2025-05-07T05:08:16.725+01:00","Created":"2025-05-07T05:08:16.725+01:00"},{"Body":"Hi test group,\n\nNew Joiner Name: Test1 Test2 \n\nJob Title: Intern \n\nDepartment: IT \n\nLine Manager: Edmund Chan \n\nReporter: Hideki Magtoto \n\nModel Account: test \n\n\n\n\n\nApplication Name: test group \n\nAllegro Security Group: \n| \\\\  \\\\  \\\\  \\\\  \\\\  \\\\  \\\\ \n| \\\\ [ \\\\ target=\"_blank\"  \\\\ style=\"font-size: 18px; font-family: Arial, sans-serif; color: #ffffff; text-decoration: none; padding: 10px 25px; display: inline-block; border-radius: 3px; background-color: #068000; border: 1px solid #068000;\"> \\\\ Approve \\\\ |mailto:<EMAIL>?subject=Re:%20TESTIT-49342%20test group%20Approval%20Email&body=Application%20test group%20Approved.] \\\\ | | \\\\ [ \\\\ target=\"_blank\"  \\\\ style=\"font-size: 18px; font-family: Arial, sans-serif; color: #ffffff; text-decoration: none; padding: 10px 25px; display: inline-block; border-radius: 3px; background-color: #FD1B00; border: 1px solid #FD1B00;\"> \\\\ Decline \\\\ |mailto:<EMAIL>?subject=Re:%20TESTIT-49342%20test group%20Approval%20Email&body=Application%20test group%20Declined.] \\\\ | \n\n\nFor any clarifications, please contact IT Support using the below channels: \n\n\n\n1. Email: <EMAIL> \n\n2. Hotline Numbers: \n\nJP: +81 800-111-0060 - Toll Free Number (Local Dialing) \n\nJP: +81 50-5050-8456 - International Dialing \n\nSG: +65 800 321 1666 - Toll Free Number (Local Dialing) \n\nSG: +65 6028 2098 - International Dialing \n\nUK: +44 20 4587 6167 \n\nUS: +1 919 280 2601 \n\n\n\nThanks & Regards \n\nEnd User Support Team \n\n\n\nTo view more information on this request, please click on this link: https://jeragm.atlassian.net/servicedesk/customer/portal/4/TESTIT-49342?created=true","RestUrl":"https://jeragm.atlassian.net/rest/api/2/issue/107637/comment/200040","UpdateAuthor":{"Locale":null,"RestUrl":"https://jeragm.atlassian.net/rest/api/2/user?accountId=qm%3Aa5ef4933-12d6-463a-98fb-7577ad45dda9%3Abc35f20b-d705-416b-ad88-5647c1a5d763","Active":true,"Groups":null,"DisplayName":"<EMAIL>","AvatarUrl":{"48x48":"https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/default-avatar.png","24x24":"https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/default-avatar.png","16x16":"https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/default-avatar.png","32x32":"https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/default-avatar.png"},"AccountId":"qm:a5ef4933-12d6-463a-98fb-7577ad45dda9:bc35f20b-d705-416b-ad88-5647c1a5d763","TimeZone":"Europe/London","Key":null,"Name":null,"EmailAddress":"<EMAIL>"},"Visibility":null,"Author":{"Locale":null,"RestUrl":"https://jeragm.atlassian.net/rest/api/2/user?accountId=qm%3Aa5ef4933-12d6-463a-98fb-7577ad45dda9%3Abc35f20b-d705-416b-ad88-5647c1a5d763","Active":true,"Groups":null,"DisplayName":"<EMAIL>","AvatarUrl":{"48x48":"https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/default-avatar.png","24x24":"https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/default-avatar.png","16x16":"https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/default-avatar.png","32x32":"https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/default-avatar.png"},"AccountId":"qm:a5ef4933-12d6-463a-98fb-7577ad45dda9:bc35f20b-d705-416b-ad88-5647c1a5d763","TimeZone":"Europe/London","Key":null,"Name":null,"EmailAddress":"<EMAIL>"},"ID":"200040","Updated":"2025-05-08T06:43:28.373+01:00","Created":"2025-05-08T06:43:28.373+01:00"},{"Body":"[2025-06-24 12:09:11] [ERROR] Admin account password reset failed: Jira connection test failed: Jira connection test failed - could not retrieve user information","RestUrl":"https://jeragm.atlassian.net/rest/api/2/issue/107637/comment/205615","UpdateAuthor":{"Locale":null,"RestUrl":"https://jeragm.atlassian.net/rest/api/2/user?accountId=712020%3A65c99194-b77b-498d-8a7e-e1dd4dd40efa","Active":true,"Groups":null,"DisplayName":"Emmanuel Akinjomo","AvatarUrl":{"48x48":"https://secure.gravatar.com/avatar/cb1faea1c84679d2feba2fd2c5eab9b8?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FEA-2.png","24x24":"https://secure.gravatar.com/avatar/cb1faea1c84679d2feba2fd2c5eab9b8?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FEA-2.png","16x16":"https://secure.gravatar.com/avatar/cb1faea1c84679d2feba2fd2c5eab9b8?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FEA-2.png","32x32":"https://secure.gravatar.com/avatar/cb1faea1c84679d2feba2fd2c5eab9b8?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FEA-2.png"},"AccountId":"712020:65c99194-b77b-498d-8a7e-e1dd4dd40efa","TimeZone":"Europe/London","Key":null,"Name":null,"EmailAddress":"<EMAIL>"},"Visibility":null,"Author":{"Locale":null,"RestUrl":"https://jeragm.atlassian.net/rest/api/2/user?accountId=712020%3A65c99194-b77b-498d-8a7e-e1dd4dd40efa","Active":true,"Groups":null,"DisplayName":"Emmanuel Akinjomo","AvatarUrl":{"48x48":"https://secure.gravatar.com/avatar/cb1faea1c84679d2feba2fd2c5eab9b8?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FEA-2.png","24x24":"https://secure.gravatar.com/avatar/cb1faea1c84679d2feba2fd2c5eab9b8?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FEA-2.png","16x16":"https://secure.gravatar.com/avatar/cb1faea1c84679d2feba2fd2c5eab9b8?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FEA-2.png","32x32":"https://secure.gravatar.com/avatar/cb1faea1c84679d2feba2fd2c5eab9b8?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FEA-2.png"},"AccountId":"712020:65c99194-b77b-498d-8a7e-e1dd4dd40efa","TimeZone":"Europe/London","Key":null,"Name":null,"EmailAddress":"<EMAIL>"},"ID":"205615","Updated":"2025-06-24T12:09:13.688+01:00","Created":"2025-06-24T12:09:13.688+01:00"},{"Body":"[2025-06-24 15:27:55] [ERROR] Admin account creation failed: Jira ticket validation failed: Work type mismatch. Expected: 'Service Request with Approvals', Found: ''. ","RestUrl":"https://jeragm.atlassian.net/rest/api/2/issue/107637/comment/205632","UpdateAuthor":{"Locale":null,"RestUrl":"https://jeragm.atlassian.net/rest/api/2/user?accountId=712020%3A65c99194-b77b-498d-8a7e-e1dd4dd40efa","Active":true,"Groups":null,"DisplayName":"Emmanuel Akinjomo","AvatarUrl":{"48x48":"https://secure.gravatar.com/avatar/cb1faea1c84679d2feba2fd2c5eab9b8?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FEA-2.png","24x24":"https://secure.gravatar.com/avatar/cb1faea1c84679d2feba2fd2c5eab9b8?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FEA-2.png","16x16":"https://secure.gravatar.com/avatar/cb1faea1c84679d2feba2fd2c5eab9b8?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FEA-2.png","32x32":"https://secure.gravatar.com/avatar/cb1faea1c84679d2feba2fd2c5eab9b8?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FEA-2.png"},"AccountId":"712020:65c99194-b77b-498d-8a7e-e1dd4dd40efa","TimeZone":"Europe/London","Key":null,"Name":null,"EmailAddress":"<EMAIL>"},"Visibility":null,"Author":{"Locale":null,"RestUrl":"https://jeragm.atlassian.net/rest/api/2/user?accountId=712020%3A65c99194-b77b-498d-8a7e-e1dd4dd40efa","Active":true,"Groups":null,"DisplayName":"Emmanuel Akinjomo","AvatarUrl":{"48x48":"https://secure.gravatar.com/avatar/cb1faea1c84679d2feba2fd2c5eab9b8?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FEA-2.png","24x24":"https://secure.gravatar.com/avatar/cb1faea1c84679d2feba2fd2c5eab9b8?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FEA-2.png","16x16":"https://secure.gravatar.com/avatar/cb1faea1c84679d2feba2fd2c5eab9b8?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FEA-2.png","32x32":"https://secure.gravatar.com/avatar/cb1faea1c84679d2feba2fd2c5eab9b8?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FEA-2.png"},"AccountId":"712020:65c99194-b77b-498d-8a7e-e1dd4dd40efa","TimeZone":"Europe/London","Key":null,"Name":null,"EmailAddress":"<EMAIL>"},"ID":"205632","Updated":"2025-06-24T15:27:57.354+01:00","Created":"2025-06-24T15:27:57.354+01:00"}],"customfield_10320":null,"customfield_10000":"{}","customfield_10559":null,"customfield_10486":null,"customfield_10154":null,"customfield_10343":"test","customfield_10317":null,"customfield_10200":null,"customfield_10651":{"self":"https://jeragm.atlassian.net/rest/api/2/user?accountId=712020%3A4546b468-9348-49fd-8ad5-95059dd6c6b2","accountId":"712020:4546b468-9348-49fd-8ad5-95059dd6c6b2","emailAddress":"<EMAIL>","avatarUrls":{"48x48":"https://secure.gravatar.com/avatar/ad5737244b64814d9c33fec13670b324?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FKS-3.png","24x24":"https://secure.gravatar.com/avatar/ad5737244b64814d9c33fec13670b324?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FKS-3.png","16x16":"https://secure.gravatar.com/avatar/ad5737244b64814d9c33fec13670b324?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FKS-3.png","32x32":"https://secure.gravatar.com/avatar/ad5737244b64814d9c33fec13670b324?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FKS-3.png"},"displayName":"Kewal Shah","active":true,"timeZone":"Europe/London","accountType":"atlassian"},"customfield_10027":null,"customfield_10070":null,"customfield_10254":null,"security":null,"customfield_10216":null,"customfield_10195":[],"customfield_10133":null,"customfield_10009":{"hasEpicLinkFieldDependency":true,"showField":true,"nonEditableReason":{"reason":"PLUGIN_LICENSE_ERROR","message":"The Parent Link is only available to Jira Premium users."}},"customfield_11213":null,"aggregatetimeoriginalestimate":null,"customfield_10056":null,"customfield_10061":{"id":"30","name":"Time to first response","_links":{"self":"https://jeragm.atlassian.net/rest/servicedeskapi/request/107637/sla/30"},"completedCycles":[],"slaDisplayFormat":"NEW_SLA_FORMAT"},"customfield_10751":null,"customfield_10322":null,"customfield_10519":null,"customfield_10183":null,"customfield_10065":null,"ID":"107637","customfield_10560":null,"customfield_10326":{"self":"https://jeragm.atlassian.net/rest/api/2/customFieldOption/10640","value":"No","id":"10640"},"timespent":null,"customfield_10354":null,"customfield_10381":null,"customfield_10393":null,"customfield_10190":null,"customfield_10391":null,"customfield_11181":null,"customfield_10189":null,"customfield_10193":null,"customfield_10237":null,"customfield_10684":{"self":"https://jeragm.atlassian.net/rest/api/2/customFieldOption/11712","value":"TBD","id":"11712"},"customfield_10132":null,"customfield_10204":null,"customfield_10175":null,"duedate":null,"environment":null,"customfield_11212":null,"customfield_10721":null,"customfield_10235":null,"customfield_10556":null,"customfield_10203":null,"customfield_10066":null,"customfield_10372":null,"customfield_10050":null,"customfield_10058":null,"customfield_10037":null,"customfield_10172":null,"statuscategorychangedate":"2025-05-06T06:27:02.869+01:00","customfield_10030":null,"customfield_10727":null,"Key":"TESTIT-49342","issuelinks":[],"customfield_10224":0.0,"priority":{"self":"https://jeragm.atlassian.net/rest/api/2/priority/4","iconUrl":"https://jeragm.atlassian.net/images/icons/priorities/low.svg","name":"Low","id":"4"},"customfield_10662":null,"customfield_10341":null,"customfield_10346":null,"labels":[],"customfield_10155":null,"customfield_10210":null,"customfield_10557":null,"resolution":null,"customfield_11214":null,"customfield_10663":null,"customfield_10178":null,"customfield_10185":null,"customfield_10330":null,"customfield_11182":null,"customfield_10042":[{"id":"1297","name":"Waiting for approval","finalDecision":"approved","canAnswerApproval":false,"approvers":[],"createdDate":{"iso8601":"2025-05-06T06:27:02+01:00","jira":"2025-05-06T06:27:02.738+01:00","friendly":"06/May/2025 06:27","epochMillis":1746509222738},"completedDate":{"iso8601":"2025-05-06T07:44:40+01:00","jira":"2025-05-06T07:44:40.509+01:00","friendly":"06/May/2025 07:44","epochMillis":1746513880509},"_links":{"self":"https://jeragm.atlassian.net/rest/servicedeskapi/request/107637/approval/1297"}}],"customfield_10148":null,"customfield_10488":null,"customfield_10337":[],"customfield_10161":null,"customfield_10060":{"id":"5","name":"Time to resolution","_links":{"self":"https://jeragm.atlassian.net/rest/servicedeskapi/request/107637/sla/5"},"completedCycles":[],"slaDisplayFormat":"NEW_SLA_FORMAT"},"customfield_10562":null,"aggregatetimespent":null,"Transition":[{"ResultStatus":{"Name":"In Progress","Description":"This work item is being actively worked on at the moment by the assignee.","RestUrl":"https://jeragm.atlassian.net/rest/api/2/status/3","IconUrl":"https://jeragm.atlassian.net/images/icons/statuses/inprogress.png","ID":"3"},"Name":"In Progress","ID":"51"},{"ResultStatus":{"Name":"Cancelled","Description":"Tickets that are no longer needed, reasons to be documented in the ticket description.","RestUrl":"https://jeragm.atlassian.net/rest/api/2/status/10067","IconUrl":"https://jeragm.atlassian.net/images/icons/status_generic.gif","ID":"10067"},"Name":"Cancelled","ID":"71"},{"ResultStatus":{"Name":"Pending","Description":"Item needing something to move to the next stage.","RestUrl":"https://jeragm.atlassian.net/rest/api/2/status/10015","IconUrl":"https://jeragm.atlassian.net/","ID":"10015"},"Name":"Pending","ID":"111"},{"ResultStatus":{"Name":"Waiting for approval","Description":"This was auto-generated by Jira Service Desk during workflow import","RestUrl":"https://jeragm.atlassian.net/rest/api/2/status/10083","IconUrl":"https://jeragm.atlassian.net/images/icons/status_generic.gif","ID":"10083"},"Name":"for automation","ID":"171"}],"customfield_10001":null,"customfield_10783":{"self":"https://jeragm.atlassian.net/rest/api/2/customFieldOption/11864","value":"Low","id":"11864"},"customfield_10100":null,"customfield_10630":{"self":"https://jeragm.atlassian.net/rest/api/2/customFieldOption/11280","value":"TBD","id":"11280"},"customfield_10029":[],"customfield_10049":null,"customfield_10635":null,"customfield_10118":null,"customfield_10626":{"self":"https://jeragm.atlassian.net/rest/api/2/customFieldOption/11278","value":"TBD","id":"11278"},"customfield_10153":null,"customfield_10555":null,"timeestimate":null,"customfield_10223":null,"customfield_10261":null,"worklog":{"startAt":0,"maxResults":20,"total":0,"worklogs":[]},"customfield_10191":null,"customfield_10386":[{"name":"C9 Trader","groupId":"************************7ac9a8254fae","self":"https://jeragm.atlassian.net/rest/api/2/group?groupId=************************7ac9a8254fae"}],"customfield_10306":null,"customfield_10453":{"self":"https://jeragm.atlassian.net/rest/api/2/customFieldOption/11051","value":"No","id":"11051"},"versions":[],"timetracking":{},"customfield_10043":[{"self":"https://jeragm.atlassian.net/rest/api/2/user?accountId=712020%3Aef74f97d-0206-4208-9468-be9637efa0b3","accountId":"712020:ef74f97d-0206-4208-9468-be9637efa0b3","emailAddress":"<EMAIL>","avatarUrls":{"48x48":"https://secure.gravatar.com/avatar/3fca9e86bc03c306f3855e0a91bec38f?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FEC-5.png","24x24":"https://secure.gravatar.com/avatar/3fca9e86bc03c306f3855e0a91bec38f?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FEC-5.png","16x16":"https://secure.gravatar.com/avatar/3fca9e86bc03c306f3855e0a91bec38f?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FEC-5.png","32x32":"https://secure.gravatar.com/avatar/3fca9e86bc03c306f3855e0a91bec38f?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FEC-5.png"},"displayName":"Edmund Chan","active":true,"timeZone":"Europe/London","accountType":"atlassian"}],"customfield_10117":null,"customfield_10489":null,"customfield_10202":null,"customfield_10255":null,"statusCategory":{"self":"https://jeragm.atlassian.net/rest/api/2/statuscategory/2","id":2,"key":"new","colorName":"blue-gray","name":"To Do"},"customfield_10181":[{"self":"https://jeragm.atlassian.net/rest/api/2/customFieldOption/10591","value":"End User Support (IT Ops)","id":"10591"}],"customfield_10213":null,"Updated":"2025-06-24T15:27:57.354+01:00","customfield_10135":null,"customfield_10045":null,"Created":"2025-05-06T06:27:01.109+01:00","customfield_10344":"2025-05-29","customfield_10628":{"self":"https://jeragm.atlassian.net/rest/api/2/customFieldOption/11283","value":"TBD","id":"11283"},"customfield_10370":null,"customfield_10101":null,"customfield_10227":1.0,"aggregatetimeestimate":null,"customfield_10726":null,"customfield_10173":null,"HttpUrl":"https://jeragm.atlassian.net/browse/TESTIT-49342","customfield_10395":null,"customfield_10044":null,"customfield_10249":"","customfield_11215":null,"customfield_10064":{"languageCode":"en","displayName":"English"},"customfield_10238":"Intern","customfield_10717":null,"customfield_10521":null,"Creator":{"Locale":null,"RestUrl":"https://jeragm.atlassian.net/rest/api/2/user?accountId=************************","Active":true,"Groups":null,"DisplayName":"Hideki Magtoto","AvatarUrl":{"48x48":"https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/************************/a7b3db59-ab90-4a6d-bec2-565befe00179/48","24x24":"https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/************************/a7b3db59-ab90-4a6d-bec2-565befe00179/24","16x16":"https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/************************/a7b3db59-ab90-4a6d-bec2-565befe00179/16","32x32":"https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/************************/a7b3db59-ab90-4a6d-bec2-565befe00179/32"},"AccountId":"************************","TimeZone":"Singapore","Key":null,"Name":null,"EmailAddress":"<EMAIL>"},"customfield_10186":null,"customfield_10303":[{"self":"https://jeragm.atlassian.net/rest/api/2/customFieldOption/10677","value":"Cloud9","id":"10677"}],"customfield_10180":null,"customfield_10304":"Test1","timeoriginalestimate":null,"Status":"Waiting for support","customfield_10194":null,"customfield_11179":null,"customfield_10197":null,"customfield_10212":null,"customfield_10177":null,"customfield_10184":null,"customfield_10130":null,"customfield_10239":null,"customfield_10145":null,"customfield_10447":null,"customfield_10055":null,"customfield_10250":0.0,"customfield_10157":null,"customfield_10723":null,"customfield_10012":"2025-05-06T07:44:35.695+01:00","customfield_10750":null,"customfield_10062":null,"customfield_10198":null,"customfield_10398":null,"customfield_10126":null,"customfield_10048":null,"customfield_10336":null,"Reporter":{"Locale":null,"RestUrl":"https://jeragm.atlassian.net/rest/api/2/user?accountId=************************","Active":true,"Groups":null,"DisplayName":"Hideki Magtoto","AvatarUrl":{"48x48":"https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/************************/a7b3db59-ab90-4a6d-bec2-565befe00179/48","24x24":"https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/************************/a7b3db59-ab90-4a6d-bec2-565befe00179/24","16x16":"https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/************************/a7b3db59-ab90-4a6d-bec2-565befe00179/16","32x32":"https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/************************/a7b3db59-ab90-4a6d-bec2-565befe00179/32"},"AccountId":"************************","TimeZone":"Singapore","Key":null,"Name":null,"EmailAddress":"<EMAIL>"},"customfield_10115":{"self":"https://jeragm.atlassian.net/rest/api/2/customFieldOption/10181","value":"Singapore","id":"10181"},"customfield_10685":{"self":"https://jeragm.atlassian.net/rest/api/2/customFieldOption/11713","value":"TBD","id":"11713"},"customfield_10554":null,"customfield_10371":null,"customfield_10127":null,"customfield_10081":{"self":"https://jeragm.atlassian.net/rest/api/2/customFieldOption/10139","value":"TBC","id":"10139"},"customfield_10206":null,"customfield_10725":null,"customfield_10013":null,"customfield_11113":null,"customfield_10448":{"self":"https://jeragm.atlassian.net/rest/api/2/customFieldOption/11036","value":"No","id":"11036"},"customfield_10151":null,"customfield_10053":null,"customfield_10025":null,"customfield_10225":1.0,"customfield_10023":null,"customfield_10046":null,"customfield_10561":null,"customfield_10664":null,"customfield_10159":null,"Assignee":{"Locale":null,"RestUrl":"https://jeragm.atlassian.net/rest/api/2/user?accountId=************************","Active":true,"Groups":null,"DisplayName":"Pavan Kumar Posa","AvatarUrl":{"48x48":"https://secure.gravatar.com/avatar/c6d0a04d80d743256b34cd6de108f5f3?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FPP-6.png","24x24":"https://secure.gravatar.com/avatar/c6d0a04d80d743256b34cd6de108f5f3?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FPP-6.png","16x16":"https://secure.gravatar.com/avatar/c6d0a04d80d743256b34cd6de108f5f3?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FPP-6.png","32x32":"https://secure.gravatar.com/avatar/c6d0a04d80d743256b34cd6de108f5f3?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FPP-6.png"},"AccountId":"************************","TimeZone":"Singapore","Key":null,"Name":null,"EmailAddress":"<EMAIL>"},"customfield_10305":"Test2","customfield_10209":null,"customfield_10171":null,"customfield_10276":null,"Summary":"Onboarding Request","customfield_10396":null,"customfield_10102":null,"customfield_10356":[],"customfield_10199":null,"customfield_10059":null,"customfield_10068":null,"customfield_10618":null,"customfield_10149":null,"customfield_10229":null,"LastViewed":"2025-07-01T11:27:12.089+01:00","customfield_10156":null,"customfield_10363":null,"customfield_10072":{"self":"https://jeragm.atlassian.net/rest/api/2/customFieldOption/10130","value":"Under Investigation","id":"10130"},"customfield_10187":null,"customfield_10369":null,"customfield_10722":null,"customfield_10131":null,"customfield_10380":null,"customfield_10352":{"self":"https://jeragm.atlassian.net/rest/api/2/customFieldOption/10695","value":"No","id":"10695"},"customfield_10634":null,"progress":{"progress":0,"total":0},"customfield_10367":null,"customfield_10028":{"_links":{"jiraRest":"https://jeragm.atlassian.net/rest/api/2/issue/107637","web":"https://jeragm.atlassian.net/servicedesk/customer/portal/2/TESTIT-49342","agent":"https://jeragm.atlassian.net/browse/TESTIT-49342","self":"https://jeragm.atlassian.net/rest/servicedeskapi/request/107637"},"requestType":{"_expands":["field"],"id":"57","_links":{"self":"https://jeragm.atlassian.net/rest/servicedeskapi/servicedesk/2/requesttype/57"},"name":"JERAGM Onboarding Request","description":"","helpText":"","defaultName":"JERAGM Onboarding Request","issueTypeId":"10042","serviceDeskId":"2","portalId":"2","groupIds":["6"],"icon":{"id":"10566","_links":{"iconUrls":{"48x48":"https://jeragm.atlassian.net/rest/api/2/universal_avatar/view/type/SD_REQTYPE/avatar/10566?size=large","24x24":"https://jeragm.atlassian.net/rest/api/2/universal_avatar/view/type/SD_REQTYPE/avatar/10566?size=small","16x16":"https://jeragm.atlassian.net/rest/api/2/universal_avatar/view/type/SD_REQTYPE/avatar/10566?size=xsmall","32x32":"https://jeragm.atlassian.net/rest/api/2/universal_avatar/view/type/SD_REQTYPE/avatar/10566?size=medium"}}}},"currentStatus":{"status":"Waiting for support","statusCategory":"NEW","statusDate":{"iso8601":"2025-05-08T06:43:36+01:00","jira":"2025-05-08T06:43:36.954+01:00","friendly":"08/May/2025 06:43","epochMillis":1746683016954}}},"customfield_10134":null,"customfield_10041":null,"subtasks":[{"id":"107647","key":"TESTIT-49343","self":"https://jeragm.atlassian.net/rest/api/2/issue/107647","fields":{"summary":"Onboarding Request - C9 Trader Access","status":{"self":"https://jeragm.atlassian.net/rest/api/2/status/1","description":"The work item is open and ready for the assignee to start work on it.","iconUrl":"https://jeragm.atlassian.net/images/icons/statuses/open.png","name":"Open","id":"1","statusCategory":{"self":"https://jeragm.atlassian.net/rest/api/2/statuscategory/2","id":2,"key":"new","colorName":"blue-gray","name":"To Do"}},"priority":{"self":"https://jeragm.atlassian.net/rest/api/2/priority/4","iconUrl":"https://jeragm.atlassian.net/images/icons/priorities/low.svg","name":"Low","id":"4"},"issuetype":{"self":"https://jeragm.atlassian.net/rest/api/2/issuetype/10002","id":"10002","description":"A small piece of work that's part of a larger task.","iconUrl":"https://jeragm.atlassian.net/rest/api/2/universal_avatar/view/type/issuetype/avatar/10316?size=medium","name":"Sub-task","subtask":true,"avatarId":10316,"hierarchyLevel":-1}}}],"customfield_10075":null,"customfield_10128":null,"customfield_10928":null,"customfield_11122":null,"customfield_10520":null,"customfield_10729":null,"customfield_10382":null,"customfield_10585":null,"customfield_10858":null,"customfield_10629":{"self":"https://jeragm.atlassian.net/rest/api/2/customFieldOption/11284","value":"TBD","id":"11284"},"RestUrl":"https://jeragm.atlassian.net/rest/api/2/issue/107637","workratio":-1,"customfield_10129":null,"customfield_10619":null,"Project":{"Roles":null,"Key":"TESTIT","Components":null,"IssueTypes":null,"RestUrl":"https://jeragm.atlassian.net/rest/api/2/project/10066","Description":null,"Style":null,"ID":"10066","Name":"IT Support - TEST","Lead":null,"Category":{"self":"https://jeragm.atlassian.net/rest/api/2/projectCategory/10008","id":"10008","description":"IT Operations","name":"IT Operations"}},"customfield_10342":{"self":"https://jeragm.atlassian.net/rest/api/2/customFieldOption/10659","value":"JERAGM Employee","id":"10659"},"customfield_10054":null,"customfield_10004":[],"customfield_10553":null,"customfield_10063":null,"customfield_10728":null,"customfield_10120":{"self":"https://jeragm.atlassian.net/rest/api/2/customFieldOption/10515","value":"IT","id":"10515"},"customfield_10719":null,"customfield_10208":null,"issuetype":{"self":"https://jeragm.atlassian.net/rest/api/2/issuetype/10042","id":"10042","description":"For requests that require approval. Created by Jira Service Desk","iconUrl":"https://jeragm.atlassian.net/rest/api/2/universal_avatar/view/type/issuetype/avatar/10533?size=medium","name":"Service Request with Approvals","subtask":false,"avatarId":10533,"hierarchyLevel":0},"customfield_10011":"1|i02ten:","customfield_10010":null,"customfield_10383":null,"customfield_10347":{"self":"https://jeragm.atlassian.net/rest/api/2/customFieldOption/10669","value":"No","id":"10669"},"customfield_10008":null,"customfield_10275":null,"components":[{"self":"https://jeragm.atlassian.net/rest/api/2/component/10431","id":"10431","name":"Onboarding","description":"Onboarding"}],"customfield_10631":{"self":"https://jeragm.atlassian.net/rest/api/2/customFieldOption/11282","value":"TBD","id":"11282"},"watches":{"self":"https://jeragm.atlassian.net/rest/api/2/issue/TESTIT-49342/watchers","watchCount":2,"isWatching":true},"customfield_10379":null,"customfield_10201":null,"customfield_10252":null,"customfield_10182":null,"customfield_10147":null,"customfield_10024":null,"customfield_10196":null,"customfield_10047":null,"customfield_10174":null,"customfield_10158":null,"customfield_10116":null,"customfield_10057":null,"customfield_10384":null,"Description":"This onboarding request has been created. ","customfield_10720":null,"customfield_10375":null,"customfield_10214":null,"customfield_10040":null,"customfield_10215":null,"fixVersions":[],"attachment":[],"customfield_10105":null,"customfield_10348":{"self":"https://jeragm.atlassian.net/rest/api/2/customFieldOption/10671","value":"No","id":"10671"},"customfield_10319":null,"aggregateprogress":{"progress":0,"total":0},"customfield_10882":null,"customfield_10686":{"self":"https://jeragm.atlassian.net/rest/api/2/customFieldOption/11714","value":"TBD","id":"11714"},"customfield_10251":null,"customfield_10160":null,"customfield_10226":0.0,"customfield_10314":null,"customfield_10318":null,"resolutiondate":null,"customfield_10192":null,"customfield_10373":null,"customfield_10627":{"self":"https://jeragm.atlassian.net/rest/api/2/customFieldOption/11281","value":"TBD","id":"11281"}}; Operation=FullTicketStructure; TicketKey=TESTIT-49342
2025-07-01 12:32:44.094 | JERAGM\akinje   | INFO     | Work type extraction attempt for ticket TESTIT-49342 | AUDIT: IssueTypeExists=False; ExpectedWorkType=Service Request with Approvals; ExtractedWorkType=; TicketKey=TESTIT-49342; Operation=WorkTypeExtraction; IssueTypeStructure=null
2025-07-01 12:32:44.144 | JERAGM\akinje   | WARNING  | WARNING: Could not extract work type from ticket TESTIT-49342
2025-07-01 12:32:44.190 | JERAGM\akinje   | INFO     | Ticket validation completed: True | AUDIT: IsValid=True; WarningsCount=9; Operation=JiraTicketValidationResult; FieldsExtracted=0; TicketKey=TESTIT-49342
2025-07-01 12:32:44.232 | JERAGM\akinje   | INFO     | Jira Ticket Validation Warnings:
2025-07-01 12:32:44.274 | JERAGM\akinje   | INFO     |   [WARNING] Work type could not be extracted from ticket. Issue type field appears to be empty or in unexpected format.
2025-07-01 12:32:44.312 | JERAGM\akinje   | INFO     |   [WARNING] Issue type field structure: null
2025-07-01 12:32:44.359 | JERAGM\akinje   | INFO     |   [WARNING] Custom field 'Department' (customfield_10120) is empty or not found
2025-07-01 12:32:44.400 | JERAGM\akinje   | INFO     |   [WARNING] Custom field 'ITAdminAccount' (customfield_10453) is empty or not found
2025-07-01 12:32:44.434 | JERAGM\akinje   | INFO     |   [WARNING] Custom field 'ModelAccount' (customfield_10343) is empty or not found
2025-07-01 12:32:44.473 | JERAGM\akinje   | INFO     |   [WARNING] Custom field 'JobTitle' (customfield_10238) is empty or not found
2025-07-01 12:32:44.532 | JERAGM\akinje   | INFO     |   [WARNING] Custom field 'LastName' (customfield_10305) is empty or not found
2025-07-01 12:32:44.571 | JERAGM\akinje   | INFO     |   [WARNING] Custom field 'OfficeLocation' (customfield_10115) is empty or not found
2025-07-01 12:32:44.613 | JERAGM\akinje   | INFO     |   [WARNING] Custom field 'FirstName' (customfield_10304) is empty or not found
2025-07-01 12:32:44.651 | JERAGM\akinje   | INFO     | Proceeding with admin account creation despite warnings...
2025-07-01 12:32:44.697 | JERAGM\akinje   | ERROR    | Admin account creation failed: Could not extract user details from Jira ticket
2025-07-01 12:32:44.729 | JERAGM\akinje   | ERROR    | Admin account creation failed: Could not extract user details from Jira ticket | AUDIT: ErrorType=RuntimeException; Success=False; Operation=AdminAccountCreationError
2025-07-01 12:32:44.768 | JERAGM\akinje   | INFO     | Adding comment to Jira ticket | AUDIT: UseADF=True; Operation=JiraCommentAdd; IncludeTimestamp=True; TicketKey=TESTIT-49342
2025-07-01 12:32:47.393 | JERAGM\akinje   | INFO     | Comment added successfully to ticket TESTIT-49342 | AUDIT: CommentId=206556; Operation=JiraCommentSuccess; TicketKey=TESTIT-49342
2025-07-01 12:32:47.418 | JERAGM\akinje   | INFO     | Press any key to continue...
2025-07-01 12:33:27.821 | JERAGM\akinje   | INFO     | ================================================================================
2025-07-01 12:33:27.851 | JERAGM\akinje   | INFO     |                     JML (Joiners, Movers and Leavers) System                      
2025-07-01 12:33:27.873 | JERAGM\akinje   | INFO     |                               Version 1.13                                    
2025-07-01 12:33:27.890 | JERAGM\akinje   | INFO     | ================================================================================
2025-07-01 12:33:27.907 | JERAGM\akinje   | INFO     | System Status:
2025-07-01 12:33:27.924 | JERAGM\akinje   | INFO     |   User: JERAGM\akinje on JGM-CG7THR3
2025-07-01 12:33:27.952 | JERAGM\akinje   | INFO     |   Configuration: Loaded
2025-07-01 12:33:27.970 | JERAGM\akinje   | INFO     |   Credential Method: EncryptedFile
2025-07-01 12:33:27.989 | JERAGM\akinje   | DEBUG    | [DEBUG] Checking credential setup status for method: EncryptedFile
2025-07-01 12:33:28.006 | JERAGM\akinje   | DEBUG    | [DEBUG] Checking EncryptedFile credential storage
2025-07-01 12:33:28.026 | JERAGM\akinje   | DEBUG    | [DEBUG] Checking for encrypted file at: [REDACTED_PATH]s\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Config\SecureCredentials.xml
2025-07-01 12:33:28.046 | JERAGM\akinje   | DEBUG    | [DEBUG] EncryptedFile credentials appear to be configured and accessible
2025-07-01 12:33:28.068 | JERAGM\akinje   | INFO     |   Vault Status: Not Configured
2025-07-01 12:33:28.086 | JERAGM\akinje   | INFO     |   Jira Integration: Enabled
2025-07-01 12:33:28.104 | JERAGM\akinje   | INFO     |   Active Directory: NOT AVAILABLE (UAT Mode)
2025-07-01 12:33:28.124 | JERAGM\akinje   | INFO     | Available Operations:
2025-07-01 12:33:28.147 | JERAGM\akinje   | INFO     |   1. Create JML Account
2025-07-01 12:33:28.171 | JERAGM\akinje   | INFO     |   2. Delete JML Account
2025-07-01 12:33:28.192 | JERAGM\akinje   | INFO     |   3. Reset JML Account Password
2025-07-01 12:33:28.215 | JERAGM\akinje   | INFO     |   4. System Information
2025-07-01 12:33:28.235 | JERAGM\akinje   | INFO     |   5. Run Setup / Repair Credentials
2025-07-01 12:33:28.257 | JERAGM\akinje   | INFO     |   7. Exit
2025-07-01 12:33:28.277 | JERAGM\akinje   | INFO     | Enter your choice: 
2025-07-01 12:33:39.891 | JERAGM\akinje   | INFO     | === CREATE JML ACCOUNT ===
2025-07-01 12:33:41.725 | JERAGM\akinje   | INFO     | === JML ACCOUNT CREATION ===
2025-07-01 12:33:41.756 | JERAGM\akinje   | INFO     | JML System v1.13
2025-07-01 12:33:41.785 | JERAGM\akinje   | INFO     | Processing Jira ticket: TESTIT-49342
2025-07-01 12:33:41.813 | JERAGM\akinje   | INFO     | [INFO] Initializing Jira session...
2025-07-01 12:33:41.850 | JERAGM\akinje   | DEBUG    | [DEBUG] Received credential types:
2025-07-01 12:33:41.888 | JERAGM\akinje   | DEBUG    | [DEBUG] - jiraUsername type: String
2025-07-01 12:33:41.917 | JERAGM\akinje   | DEBUG    | [DEBUG] - jiraApiToken type: SecureString
2025-07-01 12:33:41.954 | JERAGM\akinje   | DEBUG    | [DEBUG] Credential validation passed
2025-07-01 12:33:41.980 | JERAGM\akinje   | DEBUG    | [DEBUG] Username format: Valid email
2025-07-01 12:33:42.010 | JERAGM\akinje   | DEBUG    | [DEBUG] API token=[REDACTED] (length: 192)
2025-07-01 12:33:42.047 | JERAGM\akinje   | DEBUG    | [DEBUG] Final PSCredential object:
2025-07-01 12:33:42.086 | JERAGM\akinje   | DEBUG    | [DEBUG] - Username: emmanuel.akinjomo***@jeragm.com
2025-07-01 12:33:42.114 | JERAGM\akinje   | DEBUG    | [DEBUG] - Password type: SecureString
2025-07-01 12:33:42.141 | JERAGM\akinje   | INFO     | [INFO] Connecting to Jira server: [SERVER]
2025-07-01 12:33:42.170 | JERAGM\akinje   | INFO     | [INFO] Using username: emmanuel.akinjomo***@jeragm.com
2025-07-01 12:33:42.198 | JERAGM\akinje   | INFO     | Initializing Jira connection | AUDIT: ServerUrl=[SERVER]; Operation=JiraConnectionInit; Username=emmanuel.akinjomo***@jeragm.com
2025-07-01 12:33:42.224 | JERAGM\akinje   | INFO     | Setting Jira server configuration
2025-07-01 12:33:42.257 | JERAGM\akinje   | INFO     | Creating JiraPS session for module compatibility
2025-07-01 12:33:43.051 | JERAGM\akinje   | INFO     | JiraPS session created successfully
2025-07-01 12:33:43.074 | JERAGM\akinje   | INFO     | Creating custom session using direct authentication
2025-07-01 12:33:43.096 | JERAGM\akinje   | INFO     | Testing Jira connection using GDPR-compliant endpoint
2025-07-01 12:33:43.390 | JERAGM\akinje   | INFO     | Jira connection test successful | AUDIT: UserAccountId=712020:65c99194-b77b-498d-8a7e-e1dd4dd40efa; Status=Success; UserDisplayName=Emmanuel Akinjomo; Operation=JiraConnectionTest
2025-07-01 12:33:43.417 | JERAGM\akinje   | INFO     | Jira connection established successfully.
2025-07-01 12:33:43.449 | JERAGM\akinje   | INFO     | Connected as: Emmanuel Akinjomo (emmanuel.akinjomo***@jeragm.com)
2025-07-01 12:33:43.483 | JERAGM\akinje   | INFO     | Jira connection initialized successfully using JiraPS module
2025-07-01 12:33:43.513 | JERAGM\akinje   | INFO     | [SUCCESS] Jira session initialized successfully
2025-07-01 12:33:43.545 | JERAGM\akinje   | INFO     | Validating Jira ticket | AUDIT: Operation=JiraTicketValidation; TicketKey=TESTIT-49342
2025-07-01 12:33:44.294 | JERAGM\akinje   | INFO     | Retrieved Jira ticket successfully | AUDIT: TicketSummary=; IssueTypeExists=False; FieldsAvailable=; TicketKey=TESTIT-49342; Operation=JiraTicketRetrieval; IssueTypeType=null
2025-07-01 12:33:44.314 | JERAGM\akinje   | DEBUG    | Full ticket structure for debugging | AUDIT: TicketStructure={"customfield_10211":null,"customfield_10321":null,"customfield_10374":null,"customfield_10335":null,"customfield_10368":null,"customfield_10069":null,"customfield_10385":null,"customfield_10222":null,"Comment":[{"Body":"New Joiner Name: Test1 Test2 \n\nJob Title: Intern \n\nDepartment: IT \n\nLine Manager: Edmund Chan \n\nReporter: Hideki Magtoto \n\nModel Account: test \n\n\n\n\n\nApplication Name: C9 Trader \n| \\\\  \\\\  \\\\  \\\\  \\\\  \\\\  \\\\ \n| \\\\ [ \\\\ target=\"_blank\"  \\\\ style=\"font-size: 18px; font-family: Arial, sans-serif; color: #ffffff; text-decoration: none; padding: 10px 25px; display: inline-block; border-radius: 3px; background-color: #068000; border: 1px solid #068000;\"> \\\\ Approve \\\\ |mailto:<EMAIL>?subject=Re:%20TESTIT-49342%20C9 Trader%20Approval%20Email&body=Approved] \\\\ | | \\\\ [ \\\\ target=\"_blank\"  \\\\ style=\"font-size: 18px; font-family: Arial, sans-serif; color: #ffffff; text-decoration: none; padding: 10px 25px; display: inline-block; border-radius: 3px; background-color: #FD1B00; border: 1px solid #FD1B00;\"> \\\\ Decline \\\\ |mailto:<EMAIL>?subject=Re:%20TESTIT-49342%20Issue&body=Declined] \\\\ | \n\n\nFor any clarifications, please contact IT Support using the below channels: \n\n\n\n1. Email: <EMAIL> \n\n2. Hotline Numbers: \n\nJP: +81 800-111-0060 - Toll Free Number (Local Dialing) \n\nJP: +81 50-5050-8456 - International Dialing \n\nSG: +65 800 321 1666 - Toll Free Number (Local Dialing) \n\nSG: +65 6028 2098 - International Dialing \n\nUK: +44 20 4587 6167 \n\nUS: +1 919 280 2601 \n\n\n\nThanks & Regards \n\nEnd User Support Team \n\n\n\nTo view more information on this request, please click on this link: https://jeragm.atlassian.net/servicedesk/customer/portal/4/TESTIT-49342?created=true","RestUrl":"https://jeragm.atlassian.net/rest/api/2/issue/107637/comment/199652","UpdateAuthor":{"Locale":null,"RestUrl":"https://jeragm.atlassian.net/rest/api/2/user?accountId=qm%3Aa5ef4933-12d6-463a-98fb-7577ad45dda9%3Abc35f20b-d705-416b-ad88-5647c1a5d763","Active":true,"Groups":null,"DisplayName":"<EMAIL>","AvatarUrl":{"48x48":"https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/default-avatar.png","24x24":"https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/default-avatar.png","16x16":"https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/default-avatar.png","32x32":"https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/default-avatar.png"},"AccountId":"qm:a5ef4933-12d6-463a-98fb-7577ad45dda9:bc35f20b-d705-416b-ad88-5647c1a5d763","TimeZone":"Europe/London","Key":null,"Name":null,"EmailAddress":"<EMAIL>"},"Visibility":null,"Author":{"Locale":null,"RestUrl":"https://jeragm.atlassian.net/rest/api/2/user?accountId=qm%3Aa5ef4933-12d6-463a-98fb-7577ad45dda9%3Abc35f20b-d705-416b-ad88-5647c1a5d763","Active":true,"Groups":null,"DisplayName":"<EMAIL>","AvatarUrl":{"48x48":"https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/default-avatar.png","24x24":"https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/default-avatar.png","16x16":"https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/default-avatar.png","32x32":"https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/default-avatar.png"},"AccountId":"qm:a5ef4933-12d6-463a-98fb-7577ad45dda9:bc35f20b-d705-416b-ad88-5647c1a5d763","TimeZone":"Europe/London","Key":null,"Name":null,"EmailAddress":"<EMAIL>"},"ID":"199652","Updated":"2025-05-06T07:44:35.695+01:00","Created":"2025-05-06T07:44:35.695+01:00"},{"Body":"Approval email sent to\n\n","RestUrl":"https://jeragm.atlassian.net/rest/api/2/issue/107637/comment/199797","UpdateAuthor":{"Locale":null,"RestUrl":"https://jeragm.atlassian.net/rest/api/2/user?accountId=557058%3Af58131cb-b67d-43c7-b30d-6b58d40bd077","Active":true,"Groups":null,"DisplayName":"Automation for Jira","AvatarUrl":{"48x48":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png","24x24":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png","16x16":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png","32x32":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png"},"AccountId":"557058:f58131cb-b67d-43c7-b30d-6b58d40bd077","TimeZone":"Europe/London","Key":null,"Name":null,"EmailAddress":null},"Visibility":null,"Author":{"Locale":null,"RestUrl":"https://jeragm.atlassian.net/rest/api/2/user?accountId=557058%3Af58131cb-b67d-43c7-b30d-6b58d40bd077","Active":true,"Groups":null,"DisplayName":"Automation for Jira","AvatarUrl":{"48x48":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png","24x24":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png","16x16":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png","32x32":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png"},"AccountId":"557058:f58131cb-b67d-43c7-b30d-6b58d40bd077","TimeZone":"Europe/London","Key":null,"Name":null,"EmailAddress":null},"ID":"199797","Updated":"2025-05-07T02:57:34.707+01:00","Created":"2025-05-07T02:57:34.707+01:00"},{"Body":"Approval email sent to\n\n- [~hideki.magtoto]","RestUrl":"https://jeragm.atlassian.net/rest/api/2/issue/107637/comment/199810","UpdateAuthor":{"Locale":null,"RestUrl":"https://jeragm.atlassian.net/rest/api/2/user?accountId=557058%3Af58131cb-b67d-43c7-b30d-6b58d40bd077","Active":true,"Groups":null,"DisplayName":"Automation for Jira","AvatarUrl":{"48x48":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png","24x24":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png","16x16":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png","32x32":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png"},"AccountId":"557058:f58131cb-b67d-43c7-b30d-6b58d40bd077","TimeZone":"Europe/London","Key":null,"Name":null,"EmailAddress":null},"Visibility":null,"Author":{"Locale":null,"RestUrl":"https://jeragm.atlassian.net/rest/api/2/user?accountId=557058%3Af58131cb-b67d-43c7-b30d-6b58d40bd077","Active":true,"Groups":null,"DisplayName":"Automation for Jira","AvatarUrl":{"48x48":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png","24x24":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png","16x16":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png","32x32":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png"},"AccountId":"557058:f58131cb-b67d-43c7-b30d-6b58d40bd077","TimeZone":"Europe/London","Key":null,"Name":null,"EmailAddress":null},"ID":"199810","Updated":"2025-05-07T03:25:29.994+01:00","Created":"2025-05-07T03:25:29.994+01:00"},{"Body":"Approval email sent to\n\n- [~accountid:************************]","RestUrl":"https://jeragm.atlassian.net/rest/api/2/issue/107637/comment/199813","UpdateAuthor":{"Locale":null,"RestUrl":"https://jeragm.atlassian.net/rest/api/2/user?accountId=557058%3Af58131cb-b67d-43c7-b30d-6b58d40bd077","Active":true,"Groups":null,"DisplayName":"Automation for Jira","AvatarUrl":{"48x48":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png","24x24":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png","16x16":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png","32x32":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png"},"AccountId":"557058:f58131cb-b67d-43c7-b30d-6b58d40bd077","TimeZone":"Europe/London","Key":null,"Name":null,"EmailAddress":null},"Visibility":null,"Author":{"Locale":null,"RestUrl":"https://jeragm.atlassian.net/rest/api/2/user?accountId=557058%3Af58131cb-b67d-43c7-b30d-6b58d40bd077","Active":true,"Groups":null,"DisplayName":"Automation for Jira","AvatarUrl":{"48x48":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png","24x24":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png","16x16":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png","32x32":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png"},"AccountId":"557058:f58131cb-b67d-43c7-b30d-6b58d40bd077","TimeZone":"Europe/London","Key":null,"Name":null,"EmailAddress":null},"ID":"199813","Updated":"2025-05-07T03:27:34.07+01:00","Created":"2025-05-07T03:27:34.07+01:00"},{"Body":"TESTIT-49342 Approval email sent to\n\n- [~accountid:************************]\n","RestUrl":"https://jeragm.atlassian.net/rest/api/2/issue/107637/comment/199836","UpdateAuthor":{"Locale":null,"RestUrl":"https://jeragm.atlassian.net/rest/api/2/user?accountId=557058%3Af58131cb-b67d-43c7-b30d-6b58d40bd077","Active":true,"Groups":null,"DisplayName":"Automation for Jira","AvatarUrl":{"48x48":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png","24x24":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png","16x16":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png","32x32":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png"},"AccountId":"557058:f58131cb-b67d-43c7-b30d-6b58d40bd077","TimeZone":"Europe/London","Key":null,"Name":null,"EmailAddress":null},"Visibility":null,"Author":{"Locale":null,"RestUrl":"https://jeragm.atlassian.net/rest/api/2/user?accountId=557058%3Af58131cb-b67d-43c7-b30d-6b58d40bd077","Active":true,"Groups":null,"DisplayName":"Automation for Jira","AvatarUrl":{"48x48":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png","24x24":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png","16x16":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png","32x32":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png"},"AccountId":"557058:f58131cb-b67d-43c7-b30d-6b58d40bd077","TimeZone":"Europe/London","Key":null,"Name":null,"EmailAddress":null},"ID":"199836","Updated":"2025-05-07T05:01:10.828+01:00","Created":"2025-05-07T05:01:10.828+01:00"},{"Body":"Approval email sent to test group approvers group.","RestUrl":"https://jeragm.atlassian.net/rest/api/2/issue/107637/comment/199839","UpdateAuthor":{"Locale":null,"RestUrl":"https://jeragm.atlassian.net/rest/api/2/user?accountId=557058%3Af58131cb-b67d-43c7-b30d-6b58d40bd077","Active":true,"Groups":null,"DisplayName":"Automation for Jira","AvatarUrl":{"48x48":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png","24x24":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png","16x16":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png","32x32":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png"},"AccountId":"557058:f58131cb-b67d-43c7-b30d-6b58d40bd077","TimeZone":"Europe/London","Key":null,"Name":null,"EmailAddress":null},"Visibility":null,"Author":{"Locale":null,"RestUrl":"https://jeragm.atlassian.net/rest/api/2/user?accountId=557058%3Af58131cb-b67d-43c7-b30d-6b58d40bd077","Active":true,"Groups":null,"DisplayName":"Automation for Jira","AvatarUrl":{"48x48":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png","24x24":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png","16x16":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png","32x32":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png"},"AccountId":"557058:f58131cb-b67d-43c7-b30d-6b58d40bd077","TimeZone":"Europe/London","Key":null,"Name":null,"EmailAddress":null},"ID":"199839","Updated":"2025-05-07T05:08:16.725+01:00","Created":"2025-05-07T05:08:16.725+01:00"},{"Body":"Hi test group,\n\nNew Joiner Name: Test1 Test2 \n\nJob Title: Intern \n\nDepartment: IT \n\nLine Manager: Edmund Chan \n\nReporter: Hideki Magtoto \n\nModel Account: test \n\n\n\n\n\nApplication Name: test group \n\nAllegro Security Group: \n| \\\\  \\\\  \\\\  \\\\  \\\\  \\\\  \\\\ \n| \\\\ [ \\\\ target=\"_blank\"  \\\\ style=\"font-size: 18px; font-family: Arial, sans-serif; color: #ffffff; text-decoration: none; padding: 10px 25px; display: inline-block; border-radius: 3px; background-color: #068000; border: 1px solid #068000;\"> \\\\ Approve \\\\ |mailto:<EMAIL>?subject=Re:%20TESTIT-49342%20test group%20Approval%20Email&body=Application%20test group%20Approved.] \\\\ | | \\\\ [ \\\\ target=\"_blank\"  \\\\ style=\"font-size: 18px; font-family: Arial, sans-serif; color: #ffffff; text-decoration: none; padding: 10px 25px; display: inline-block; border-radius: 3px; background-color: #FD1B00; border: 1px solid #FD1B00;\"> \\\\ Decline \\\\ |mailto:<EMAIL>?subject=Re:%20TESTIT-49342%20test group%20Approval%20Email&body=Application%20test group%20Declined.] \\\\ | \n\n\nFor any clarifications, please contact IT Support using the below channels: \n\n\n\n1. Email: <EMAIL> \n\n2. Hotline Numbers: \n\nJP: +81 800-111-0060 - Toll Free Number (Local Dialing) \n\nJP: +81 50-5050-8456 - International Dialing \n\nSG: +65 800 321 1666 - Toll Free Number (Local Dialing) \n\nSG: +65 6028 2098 - International Dialing \n\nUK: +44 20 4587 6167 \n\nUS: +1 919 280 2601 \n\n\n\nThanks & Regards \n\nEnd User Support Team \n\n\n\nTo view more information on this request, please click on this link: https://jeragm.atlassian.net/servicedesk/customer/portal/4/TESTIT-49342?created=true","RestUrl":"https://jeragm.atlassian.net/rest/api/2/issue/107637/comment/200040","UpdateAuthor":{"Locale":null,"RestUrl":"https://jeragm.atlassian.net/rest/api/2/user?accountId=qm%3Aa5ef4933-12d6-463a-98fb-7577ad45dda9%3Abc35f20b-d705-416b-ad88-5647c1a5d763","Active":true,"Groups":null,"DisplayName":"<EMAIL>","AvatarUrl":{"48x48":"https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/default-avatar.png","24x24":"https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/default-avatar.png","16x16":"https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/default-avatar.png","32x32":"https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/default-avatar.png"},"AccountId":"qm:a5ef4933-12d6-463a-98fb-7577ad45dda9:bc35f20b-d705-416b-ad88-5647c1a5d763","TimeZone":"Europe/London","Key":null,"Name":null,"EmailAddress":"<EMAIL>"},"Visibility":null,"Author":{"Locale":null,"RestUrl":"https://jeragm.atlassian.net/rest/api/2/user?accountId=qm%3Aa5ef4933-12d6-463a-98fb-7577ad45dda9%3Abc35f20b-d705-416b-ad88-5647c1a5d763","Active":true,"Groups":null,"DisplayName":"<EMAIL>","AvatarUrl":{"48x48":"https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/default-avatar.png","24x24":"https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/default-avatar.png","16x16":"https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/default-avatar.png","32x32":"https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/default-avatar.png"},"AccountId":"qm:a5ef4933-12d6-463a-98fb-7577ad45dda9:bc35f20b-d705-416b-ad88-5647c1a5d763","TimeZone":"Europe/London","Key":null,"Name":null,"EmailAddress":"<EMAIL>"},"ID":"200040","Updated":"2025-05-08T06:43:28.373+01:00","Created":"2025-05-08T06:43:28.373+01:00"},{"Body":"[2025-06-24 12:09:11] [ERROR] Admin account password reset failed: Jira connection test failed: Jira connection test failed - could not retrieve user information","RestUrl":"https://jeragm.atlassian.net/rest/api/2/issue/107637/comment/205615","UpdateAuthor":{"Locale":null,"RestUrl":"https://jeragm.atlassian.net/rest/api/2/user?accountId=712020%3A65c99194-b77b-498d-8a7e-e1dd4dd40efa","Active":true,"Groups":null,"DisplayName":"Emmanuel Akinjomo","AvatarUrl":{"48x48":"https://secure.gravatar.com/avatar/cb1faea1c84679d2feba2fd2c5eab9b8?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FEA-2.png","24x24":"https://secure.gravatar.com/avatar/cb1faea1c84679d2feba2fd2c5eab9b8?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FEA-2.png","16x16":"https://secure.gravatar.com/avatar/cb1faea1c84679d2feba2fd2c5eab9b8?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FEA-2.png","32x32":"https://secure.gravatar.com/avatar/cb1faea1c84679d2feba2fd2c5eab9b8?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FEA-2.png"},"AccountId":"712020:65c99194-b77b-498d-8a7e-e1dd4dd40efa","TimeZone":"Europe/London","Key":null,"Name":null,"EmailAddress":"<EMAIL>"},"Visibility":null,"Author":{"Locale":null,"RestUrl":"https://jeragm.atlassian.net/rest/api/2/user?accountId=712020%3A65c99194-b77b-498d-8a7e-e1dd4dd40efa","Active":true,"Groups":null,"DisplayName":"Emmanuel Akinjomo","AvatarUrl":{"48x48":"https://secure.gravatar.com/avatar/cb1faea1c84679d2feba2fd2c5eab9b8?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FEA-2.png","24x24":"https://secure.gravatar.com/avatar/cb1faea1c84679d2feba2fd2c5eab9b8?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FEA-2.png","16x16":"https://secure.gravatar.com/avatar/cb1faea1c84679d2feba2fd2c5eab9b8?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FEA-2.png","32x32":"https://secure.gravatar.com/avatar/cb1faea1c84679d2feba2fd2c5eab9b8?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FEA-2.png"},"AccountId":"712020:65c99194-b77b-498d-8a7e-e1dd4dd40efa","TimeZone":"Europe/London","Key":null,"Name":null,"EmailAddress":"<EMAIL>"},"ID":"205615","Updated":"2025-06-24T12:09:13.688+01:00","Created":"2025-06-24T12:09:13.688+01:00"},{"Body":"[2025-06-24 15:27:55] [ERROR] Admin account creation failed: Jira ticket validation failed: Work type mismatch. Expected: 'Service Request with Approvals', Found: ''. ","RestUrl":"https://jeragm.atlassian.net/rest/api/2/issue/107637/comment/205632","UpdateAuthor":{"Locale":null,"RestUrl":"https://jeragm.atlassian.net/rest/api/2/user?accountId=712020%3A65c99194-b77b-498d-8a7e-e1dd4dd40efa","Active":true,"Groups":null,"DisplayName":"Emmanuel Akinjomo","AvatarUrl":{"48x48":"https://secure.gravatar.com/avatar/cb1faea1c84679d2feba2fd2c5eab9b8?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FEA-2.png","24x24":"https://secure.gravatar.com/avatar/cb1faea1c84679d2feba2fd2c5eab9b8?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FEA-2.png","16x16":"https://secure.gravatar.com/avatar/cb1faea1c84679d2feba2fd2c5eab9b8?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FEA-2.png","32x32":"https://secure.gravatar.com/avatar/cb1faea1c84679d2feba2fd2c5eab9b8?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FEA-2.png"},"AccountId":"712020:65c99194-b77b-498d-8a7e-e1dd4dd40efa","TimeZone":"Europe/London","Key":null,"Name":null,"EmailAddress":"<EMAIL>"},"Visibility":null,"Author":{"Locale":null,"RestUrl":"https://jeragm.atlassian.net/rest/api/2/user?accountId=712020%3A65c99194-b77b-498d-8a7e-e1dd4dd40efa","Active":true,"Groups":null,"DisplayName":"Emmanuel Akinjomo","AvatarUrl":{"48x48":"https://secure.gravatar.com/avatar/cb1faea1c84679d2feba2fd2c5eab9b8?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FEA-2.png","24x24":"https://secure.gravatar.com/avatar/cb1faea1c84679d2feba2fd2c5eab9b8?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FEA-2.png","16x16":"https://secure.gravatar.com/avatar/cb1faea1c84679d2feba2fd2c5eab9b8?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FEA-2.png","32x32":"https://secure.gravatar.com/avatar/cb1faea1c84679d2feba2fd2c5eab9b8?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FEA-2.png"},"AccountId":"712020:65c99194-b77b-498d-8a7e-e1dd4dd40efa","TimeZone":"Europe/London","Key":null,"Name":null,"EmailAddress":"<EMAIL>"},"ID":"205632","Updated":"2025-06-24T15:27:57.354+01:00","Created":"2025-06-24T15:27:57.354+01:00"},{"Body":"[2025-07-01 12:32:44] [ERROR] Admin account creation failed: Could not extract user details from Jira ticket","RestUrl":"https://jeragm.atlassian.net/rest/api/2/issue/107637/comment/206556","UpdateAuthor":{"Locale":null,"RestUrl":"https://jeragm.atlassian.net/rest/api/2/user?accountId=712020%3A65c99194-b77b-498d-8a7e-e1dd4dd40efa","Active":true,"Groups":null,"DisplayName":"Emmanuel Akinjomo","AvatarUrl":{"48x48":"https://secure.gravatar.com/avatar/cb1faea1c84679d2feba2fd2c5eab9b8?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FEA-2.png","24x24":"https://secure.gravatar.com/avatar/cb1faea1c84679d2feba2fd2c5eab9b8?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FEA-2.png","16x16":"https://secure.gravatar.com/avatar/cb1faea1c84679d2feba2fd2c5eab9b8?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FEA-2.png","32x32":"https://secure.gravatar.com/avatar/cb1faea1c84679d2feba2fd2c5eab9b8?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FEA-2.png"},"AccountId":"712020:65c99194-b77b-498d-8a7e-e1dd4dd40efa","TimeZone":"Europe/London","Key":null,"Name":null,"EmailAddress":"<EMAIL>"},"Visibility":null,"Author":{"Locale":null,"RestUrl":"https://jeragm.atlassian.net/rest/api/2/user?accountId=712020%3A65c99194-b77b-498d-8a7e-e1dd4dd40efa","Active":true,"Groups":null,"DisplayName":"Emmanuel Akinjomo","AvatarUrl":{"48x48":"https://secure.gravatar.com/avatar/cb1faea1c84679d2feba2fd2c5eab9b8?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FEA-2.png","24x24":"https://secure.gravatar.com/avatar/cb1faea1c84679d2feba2fd2c5eab9b8?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FEA-2.png","16x16":"https://secure.gravatar.com/avatar/cb1faea1c84679d2feba2fd2c5eab9b8?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FEA-2.png","32x32":"https://secure.gravatar.com/avatar/cb1faea1c84679d2feba2fd2c5eab9b8?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FEA-2.png"},"AccountId":"712020:65c99194-b77b-498d-8a7e-e1dd4dd40efa","TimeZone":"Europe/London","Key":null,"Name":null,"EmailAddress":"<EMAIL>"},"ID":"206556","Updated":"2025-07-01T12:32:46.901+01:00","Created":"2025-07-01T12:32:46.901+01:00"}],"customfield_10320":null,"customfield_10000":"{}","customfield_10559":null,"customfield_10486":null,"customfield_10154":null,"customfield_10343":"test","customfield_10317":null,"customfield_10200":null,"customfield_10651":{"self":"https://jeragm.atlassian.net/rest/api/2/user?accountId=712020%3A4546b468-9348-49fd-8ad5-95059dd6c6b2","accountId":"712020:4546b468-9348-49fd-8ad5-95059dd6c6b2","emailAddress":"<EMAIL>","avatarUrls":{"48x48":"https://secure.gravatar.com/avatar/ad5737244b64814d9c33fec13670b324?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FKS-3.png","24x24":"https://secure.gravatar.com/avatar/ad5737244b64814d9c33fec13670b324?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FKS-3.png","16x16":"https://secure.gravatar.com/avatar/ad5737244b64814d9c33fec13670b324?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FKS-3.png","32x32":"https://secure.gravatar.com/avatar/ad5737244b64814d9c33fec13670b324?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FKS-3.png"},"displayName":"Kewal Shah","active":true,"timeZone":"Europe/London","accountType":"atlassian"},"customfield_10027":null,"customfield_10070":null,"customfield_10254":null,"security":null,"customfield_10216":null,"customfield_10195":[],"customfield_10133":null,"customfield_10009":{"hasEpicLinkFieldDependency":true,"showField":true,"nonEditableReason":{"reason":"PLUGIN_LICENSE_ERROR","message":"The Parent Link is only available to Jira Premium users."}},"customfield_11213":null,"aggregatetimeoriginalestimate":null,"customfield_10056":null,"customfield_10061":{"id":"30","name":"Time to first response","_links":{"self":"https://jeragm.atlassian.net/rest/servicedeskapi/request/107637/sla/30"},"completedCycles":[],"slaDisplayFormat":"NEW_SLA_FORMAT"},"customfield_10751":null,"customfield_10322":null,"customfield_10519":null,"customfield_10183":null,"customfield_10065":null,"ID":"107637","customfield_10560":null,"customfield_10326":{"self":"https://jeragm.atlassian.net/rest/api/2/customFieldOption/10640","value":"No","id":"10640"},"timespent":null,"customfield_10354":null,"customfield_10381":null,"customfield_10393":null,"customfield_10190":null,"customfield_10391":null,"customfield_11181":null,"customfield_10189":null,"customfield_10193":null,"customfield_10237":null,"customfield_10684":{"self":"https://jeragm.atlassian.net/rest/api/2/customFieldOption/11712","value":"TBD","id":"11712"},"customfield_10132":null,"customfield_10204":null,"customfield_10175":null,"duedate":null,"environment":null,"customfield_11212":null,"customfield_10721":null,"customfield_10235":null,"customfield_10556":null,"customfield_10203":null,"customfield_10066":null,"customfield_10372":null,"customfield_10050":null,"customfield_10058":null,"customfield_10037":null,"customfield_10172":null,"statuscategorychangedate":"2025-05-06T06:27:02.869+01:00","customfield_10030":null,"customfield_10727":null,"Key":"TESTIT-49342","issuelinks":[],"customfield_10224":0.0,"priority":{"self":"https://jeragm.atlassian.net/rest/api/2/priority/4","iconUrl":"https://jeragm.atlassian.net/images/icons/priorities/low.svg","name":"Low","id":"4"},"customfield_10662":null,"customfield_10341":null,"customfield_10346":null,"labels":[],"customfield_10155":null,"customfield_10210":null,"customfield_10557":null,"resolution":null,"customfield_11214":null,"customfield_10663":null,"customfield_10178":null,"customfield_10185":null,"customfield_10330":null,"customfield_11182":null,"customfield_10042":[{"id":"1297","name":"Waiting for approval","finalDecision":"approved","canAnswerApproval":false,"approvers":[],"createdDate":{"iso8601":"2025-05-06T06:27:02+01:00","jira":"2025-05-06T06:27:02.738+01:00","friendly":"06/May/2025 06:27","epochMillis":1746509222738},"completedDate":{"iso8601":"2025-05-06T07:44:40+01:00","jira":"2025-05-06T07:44:40.509+01:00","friendly":"06/May/2025 07:44","epochMillis":1746513880509},"_links":{"self":"https://jeragm.atlassian.net/rest/servicedeskapi/request/107637/approval/1297"}}],"customfield_10148":null,"customfield_10488":null,"customfield_10337":[],"customfield_10161":null,"customfield_10060":{"id":"5","name":"Time to resolution","_links":{"self":"https://jeragm.atlassian.net/rest/servicedeskapi/request/107637/sla/5"},"completedCycles":[],"slaDisplayFormat":"NEW_SLA_FORMAT"},"customfield_10562":null,"aggregatetimespent":null,"Transition":[{"ResultStatus":{"Name":"In Progress","Description":"This work item is being actively worked on at the moment by the assignee.","RestUrl":"https://jeragm.atlassian.net/rest/api/2/status/3","IconUrl":"https://jeragm.atlassian.net/images/icons/statuses/inprogress.png","ID":"3"},"Name":"In Progress","ID":"51"},{"ResultStatus":{"Name":"Cancelled","Description":"Tickets that are no longer needed, reasons to be documented in the ticket description.","RestUrl":"https://jeragm.atlassian.net/rest/api/2/status/10067","IconUrl":"https://jeragm.atlassian.net/images/icons/status_generic.gif","ID":"10067"},"Name":"Cancelled","ID":"71"},{"ResultStatus":{"Name":"Pending","Description":"Item needing something to move to the next stage.","RestUrl":"https://jeragm.atlassian.net/rest/api/2/status/10015","IconUrl":"https://jeragm.atlassian.net/","ID":"10015"},"Name":"Pending","ID":"111"},{"ResultStatus":{"Name":"Waiting for approval","Description":"This was auto-generated by Jira Service Desk during workflow import","RestUrl":"https://jeragm.atlassian.net/rest/api/2/status/10083","IconUrl":"https://jeragm.atlassian.net/images/icons/status_generic.gif","ID":"10083"},"Name":"for automation","ID":"171"}],"customfield_10001":null,"customfield_10783":{"self":"https://jeragm.atlassian.net/rest/api/2/customFieldOption/11864","value":"Low","id":"11864"},"customfield_10100":null,"customfield_10630":{"self":"https://jeragm.atlassian.net/rest/api/2/customFieldOption/11280","value":"TBD","id":"11280"},"customfield_10029":[],"customfield_10049":null,"customfield_10635":null,"customfield_10118":null,"customfield_10626":{"self":"https://jeragm.atlassian.net/rest/api/2/customFieldOption/11278","value":"TBD","id":"11278"},"customfield_10153":null,"customfield_10555":null,"timeestimate":null,"customfield_10223":null,"customfield_10261":null,"worklog":{"startAt":0,"maxResults":20,"total":0,"worklogs":[]},"customfield_10191":null,"customfield_10386":[{"name":"C9 Trader","groupId":"************************7ac9a8254fae","self":"https://jeragm.atlassian.net/rest/api/2/group?groupId=************************7ac9a8254fae"}],"customfield_10306":null,"customfield_10453":{"self":"https://jeragm.atlassian.net/rest/api/2/customFieldOption/11051","value":"No","id":"11051"},"versions":[],"timetracking":{},"customfield_10043":[{"self":"https://jeragm.atlassian.net/rest/api/2/user?accountId=712020%3Aef74f97d-0206-4208-9468-be9637efa0b3","accountId":"712020:ef74f97d-0206-4208-9468-be9637efa0b3","emailAddress":"<EMAIL>","avatarUrls":{"48x48":"https://secure.gravatar.com/avatar/3fca9e86bc03c306f3855e0a91bec38f?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FEC-5.png","24x24":"https://secure.gravatar.com/avatar/3fca9e86bc03c306f3855e0a91bec38f?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FEC-5.png","16x16":"https://secure.gravatar.com/avatar/3fca9e86bc03c306f3855e0a91bec38f?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FEC-5.png","32x32":"https://secure.gravatar.com/avatar/3fca9e86bc03c306f3855e0a91bec38f?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FEC-5.png"},"displayName":"Edmund Chan","active":true,"timeZone":"Europe/London","accountType":"atlassian"}],"customfield_10117":null,"customfield_10489":null,"customfield_10202":null,"customfield_10255":null,"statusCategory":{"self":"https://jeragm.atlassian.net/rest/api/2/statuscategory/2","id":2,"key":"new","colorName":"blue-gray","name":"To Do"},"customfield_10181":[{"self":"https://jeragm.atlassian.net/rest/api/2/customFieldOption/10591","value":"End User Support (IT Ops)","id":"10591"}],"customfield_10213":null,"Updated":"2025-07-01T12:32:46.901+01:00","customfield_10135":null,"customfield_10045":null,"Created":"2025-05-06T06:27:01.109+01:00","customfield_10344":"2025-05-29","customfield_10628":{"self":"https://jeragm.atlassian.net/rest/api/2/customFieldOption/11283","value":"TBD","id":"11283"},"customfield_10370":null,"customfield_10101":null,"customfield_10227":1.0,"aggregatetimeestimate":null,"customfield_10726":null,"customfield_10173":null,"HttpUrl":"https://jeragm.atlassian.net/browse/TESTIT-49342","customfield_10395":null,"customfield_10044":null,"customfield_10249":"","customfield_11215":null,"customfield_10064":{"languageCode":"en","displayName":"English"},"customfield_10238":"Intern","customfield_10717":null,"customfield_10521":null,"Creator":{"Locale":null,"RestUrl":"https://jeragm.atlassian.net/rest/api/2/user?accountId=************************","Active":true,"Groups":null,"DisplayName":"Hideki Magtoto","AvatarUrl":{"48x48":"https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/************************/a7b3db59-ab90-4a6d-bec2-565befe00179/48","24x24":"https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/************************/a7b3db59-ab90-4a6d-bec2-565befe00179/24","16x16":"https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/************************/a7b3db59-ab90-4a6d-bec2-565befe00179/16","32x32":"https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/************************/a7b3db59-ab90-4a6d-bec2-565befe00179/32"},"AccountId":"************************","TimeZone":"Singapore","Key":null,"Name":null,"EmailAddress":"<EMAIL>"},"customfield_10186":null,"customfield_10303":[{"self":"https://jeragm.atlassian.net/rest/api/2/customFieldOption/10677","value":"Cloud9","id":"10677"}],"customfield_10180":null,"customfield_10304":"Test1","timeoriginalestimate":null,"Status":"Waiting for support","customfield_10194":null,"customfield_11179":null,"customfield_10197":null,"customfield_10212":null,"customfield_10177":null,"customfield_10184":null,"customfield_10130":null,"customfield_10239":null,"customfield_10145":null,"customfield_10447":null,"customfield_10055":null,"customfield_10250":0.0,"customfield_10157":null,"customfield_10723":null,"customfield_10012":"2025-05-06T07:44:35.695+01:00","customfield_10750":null,"customfield_10062":null,"customfield_10198":null,"customfield_10398":null,"customfield_10126":null,"customfield_10048":null,"customfield_10336":null,"Reporter":{"Locale":null,"RestUrl":"https://jeragm.atlassian.net/rest/api/2/user?accountId=************************","Active":true,"Groups":null,"DisplayName":"Hideki Magtoto","AvatarUrl":{"48x48":"https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/************************/a7b3db59-ab90-4a6d-bec2-565befe00179/48","24x24":"https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/************************/a7b3db59-ab90-4a6d-bec2-565befe00179/24","16x16":"https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/************************/a7b3db59-ab90-4a6d-bec2-565befe00179/16","32x32":"https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/************************/a7b3db59-ab90-4a6d-bec2-565befe00179/32"},"AccountId":"************************","TimeZone":"Singapore","Key":null,"Name":null,"EmailAddress":"<EMAIL>"},"customfield_10115":{"self":"https://jeragm.atlassian.net/rest/api/2/customFieldOption/10181","value":"Singapore","id":"10181"},"customfield_10685":{"self":"https://jeragm.atlassian.net/rest/api/2/customFieldOption/11713","value":"TBD","id":"11713"},"customfield_10554":null,"customfield_10371":null,"customfield_10127":null,"customfield_10081":{"self":"https://jeragm.atlassian.net/rest/api/2/customFieldOption/10139","value":"TBC","id":"10139"},"customfield_10206":null,"customfield_10725":null,"customfield_10013":null,"customfield_11113":null,"customfield_10448":{"self":"https://jeragm.atlassian.net/rest/api/2/customFieldOption/11036","value":"No","id":"11036"},"customfield_10151":null,"customfield_10053":null,"customfield_10025":null,"customfield_10225":1.0,"customfield_10023":null,"customfield_10046":null,"customfield_10561":null,"customfield_10664":null,"customfield_10159":null,"Assignee":{"Locale":null,"RestUrl":"https://jeragm.atlassian.net/rest/api/2/user?accountId=************************","Active":true,"Groups":null,"DisplayName":"Pavan Kumar Posa","AvatarUrl":{"48x48":"https://secure.gravatar.com/avatar/c6d0a04d80d743256b34cd6de108f5f3?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FPP-6.png","24x24":"https://secure.gravatar.com/avatar/c6d0a04d80d743256b34cd6de108f5f3?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FPP-6.png","16x16":"https://secure.gravatar.com/avatar/c6d0a04d80d743256b34cd6de108f5f3?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FPP-6.png","32x32":"https://secure.gravatar.com/avatar/c6d0a04d80d743256b34cd6de108f5f3?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FPP-6.png"},"AccountId":"************************","TimeZone":"Singapore","Key":null,"Name":null,"EmailAddress":"<EMAIL>"},"customfield_10305":"Test2","customfield_10209":null,"customfield_10171":null,"customfield_10276":null,"Summary":"Onboarding Request","customfield_10396":null,"customfield_10102":null,"customfield_10356":[],"customfield_10199":null,"customfield_10059":null,"customfield_10068":null,"customfield_10618":null,"customfield_10149":null,"customfield_10229":null,"LastViewed":"2025-07-01T11:27:12.089+01:00","customfield_10156":null,"customfield_10363":null,"customfield_10072":{"self":"https://jeragm.atlassian.net/rest/api/2/customFieldOption/10130","value":"Under Investigation","id":"10130"},"customfield_10187":null,"customfield_10369":null,"customfield_10722":null,"customfield_10131":null,"customfield_10380":null,"customfield_10352":{"self":"https://jeragm.atlassian.net/rest/api/2/customFieldOption/10695","value":"No","id":"10695"},"customfield_10634":null,"progress":{"progress":0,"total":0},"customfield_10367":null,"customfield_10028":{"_links":{"jiraRest":"https://jeragm.atlassian.net/rest/api/2/issue/107637","web":"https://jeragm.atlassian.net/servicedesk/customer/portal/2/TESTIT-49342","agent":"https://jeragm.atlassian.net/browse/TESTIT-49342","self":"https://jeragm.atlassian.net/rest/servicedeskapi/request/107637"},"requestType":{"_expands":["field"],"id":"57","_links":{"self":"https://jeragm.atlassian.net/rest/servicedeskapi/servicedesk/2/requesttype/57"},"name":"JERAGM Onboarding Request","description":"","helpText":"","defaultName":"JERAGM Onboarding Request","issueTypeId":"10042","serviceDeskId":"2","portalId":"2","groupIds":["6"],"icon":{"id":"10566","_links":{"iconUrls":{"48x48":"https://jeragm.atlassian.net/rest/api/2/universal_avatar/view/type/SD_REQTYPE/avatar/10566?size=large","24x24":"https://jeragm.atlassian.net/rest/api/2/universal_avatar/view/type/SD_REQTYPE/avatar/10566?size=small","16x16":"https://jeragm.atlassian.net/rest/api/2/universal_avatar/view/type/SD_REQTYPE/avatar/10566?size=xsmall","32x32":"https://jeragm.atlassian.net/rest/api/2/universal_avatar/view/type/SD_REQTYPE/avatar/10566?size=medium"}}}},"currentStatus":{"status":"Waiting for support","statusCategory":"NEW","statusDate":{"iso8601":"2025-05-08T06:43:36+01:00","jira":"2025-05-08T06:43:36.954+01:00","friendly":"08/May/2025 06:43","epochMillis":1746683016954}}},"customfield_10134":null,"customfield_10041":null,"subtasks":[{"id":"107647","key":"TESTIT-49343","self":"https://jeragm.atlassian.net/rest/api/2/issue/107647","fields":{"summary":"Onboarding Request - C9 Trader Access","status":{"self":"https://jeragm.atlassian.net/rest/api/2/status/1","description":"The work item is open and ready for the assignee to start work on it.","iconUrl":"https://jeragm.atlassian.net/images/icons/statuses/open.png","name":"Open","id":"1","statusCategory":{"self":"https://jeragm.atlassian.net/rest/api/2/statuscategory/2","id":2,"key":"new","colorName":"blue-gray","name":"To Do"}},"priority":{"self":"https://jeragm.atlassian.net/rest/api/2/priority/4","iconUrl":"https://jeragm.atlassian.net/images/icons/priorities/low.svg","name":"Low","id":"4"},"issuetype":{"self":"https://jeragm.atlassian.net/rest/api/2/issuetype/10002","id":"10002","description":"A small piece of work that's part of a larger task.","iconUrl":"https://jeragm.atlassian.net/rest/api/2/universal_avatar/view/type/issuetype/avatar/10316?size=medium","name":"Sub-task","subtask":true,"avatarId":10316,"hierarchyLevel":-1}}}],"customfield_10075":null,"customfield_10128":null,"customfield_10928":null,"customfield_11122":null,"customfield_10520":null,"customfield_10729":null,"customfield_10382":null,"customfield_10585":null,"customfield_10858":null,"customfield_10629":{"self":"https://jeragm.atlassian.net/rest/api/2/customFieldOption/11284","value":"TBD","id":"11284"},"RestUrl":"https://jeragm.atlassian.net/rest/api/2/issue/107637","workratio":-1,"customfield_10129":null,"customfield_10619":null,"Project":{"Roles":null,"Key":"TESTIT","Components":null,"IssueTypes":null,"RestUrl":"https://jeragm.atlassian.net/rest/api/2/project/10066","Description":null,"Style":null,"ID":"10066","Name":"IT Support - TEST","Lead":null,"Category":{"self":"https://jeragm.atlassian.net/rest/api/2/projectCategory/10008","id":"10008","description":"IT Operations","name":"IT Operations"}},"customfield_10342":{"self":"https://jeragm.atlassian.net/rest/api/2/customFieldOption/10659","value":"JERAGM Employee","id":"10659"},"customfield_10054":null,"customfield_10004":[],"customfield_10553":null,"customfield_10063":null,"customfield_10728":null,"customfield_10120":{"self":"https://jeragm.atlassian.net/rest/api/2/customFieldOption/10515","value":"IT","id":"10515"},"customfield_10719":null,"customfield_10208":null,"issuetype":{"self":"https://jeragm.atlassian.net/rest/api/2/issuetype/10042","id":"10042","description":"For requests that require approval. Created by Jira Service Desk","iconUrl":"https://jeragm.atlassian.net/rest/api/2/universal_avatar/view/type/issuetype/avatar/10533?size=medium","name":"Service Request with Approvals","subtask":false,"avatarId":10533,"hierarchyLevel":0},"customfield_10011":"1|i02ten:","customfield_10010":null,"customfield_10383":null,"customfield_10347":{"self":"https://jeragm.atlassian.net/rest/api/2/customFieldOption/10669","value":"No","id":"10669"},"customfield_10008":null,"customfield_10275":null,"components":[{"self":"https://jeragm.atlassian.net/rest/api/2/component/10431","id":"10431","name":"Onboarding","description":"Onboarding"}],"customfield_10631":{"self":"https://jeragm.atlassian.net/rest/api/2/customFieldOption/11282","value":"TBD","id":"11282"},"watches":{"self":"https://jeragm.atlassian.net/rest/api/2/issue/TESTIT-49342/watchers","watchCount":2,"isWatching":true},"customfield_10379":null,"customfield_10201":null,"customfield_10252":null,"customfield_10182":null,"customfield_10147":null,"customfield_10024":null,"customfield_10196":null,"customfield_10047":null,"customfield_10174":null,"customfield_10158":null,"customfield_10116":null,"customfield_10057":null,"customfield_10384":null,"Description":"This onboarding request has been created. ","customfield_10720":null,"customfield_10375":null,"customfield_10214":null,"customfield_10040":null,"customfield_10215":null,"fixVersions":[],"attachment":[],"customfield_10105":null,"customfield_10348":{"self":"https://jeragm.atlassian.net/rest/api/2/customFieldOption/10671","value":"No","id":"10671"},"customfield_10319":null,"aggregateprogress":{"progress":0,"total":0},"customfield_10882":null,"customfield_10686":{"self":"https://jeragm.atlassian.net/rest/api/2/customFieldOption/11714","value":"TBD","id":"11714"},"customfield_10251":null,"customfield_10160":null,"customfield_10226":0.0,"customfield_10314":null,"customfield_10318":null,"resolutiondate":null,"customfield_10192":null,"customfield_10373":null,"customfield_10627":{"self":"https://jeragm.atlassian.net/rest/api/2/customFieldOption/11281","value":"TBD","id":"11281"}}; Operation=FullTicketStructure; TicketKey=TESTIT-49342
2025-07-01 12:33:44.333 | JERAGM\akinje   | INFO     | Work type extraction attempt for ticket TESTIT-49342 | AUDIT: IssueTypeExists=False; ExpectedWorkType=Service Request with Approvals; ExtractedWorkType=; TicketKey=TESTIT-49342; Operation=WorkTypeExtraction; IssueTypeStructure=null
2025-07-01 12:33:44.352 | JERAGM\akinje   | WARNING  | WARNING: Could not extract work type from ticket TESTIT-49342
2025-07-01 12:33:44.370 | JERAGM\akinje   | INFO     | Ticket validation completed: True | AUDIT: IsValid=True; WarningsCount=9; Operation=JiraTicketValidationResult; FieldsExtracted=0; TicketKey=TESTIT-49342
2025-07-01 12:33:44.392 | JERAGM\akinje   | INFO     | Jira Ticket Validation Warnings:
2025-07-01 12:33:44.416 | JERAGM\akinje   | INFO     |   [WARNING] Work type could not be extracted from ticket. Issue type field appears to be empty or in unexpected format.
2025-07-01 12:33:44.447 | JERAGM\akinje   | INFO     |   [WARNING] Issue type field structure: null
2025-07-01 12:33:44.468 | JERAGM\akinje   | INFO     |   [WARNING] Custom field 'Department' (customfield_10120) is empty or not found
2025-07-01 12:33:44.500 | JERAGM\akinje   | INFO     |   [WARNING] Custom field 'ITAdminAccount' (customfield_10453) is empty or not found
2025-07-01 12:33:44.529 | JERAGM\akinje   | INFO     |   [WARNING] Custom field 'ModelAccount' (customfield_10343) is empty or not found
2025-07-01 12:33:44.565 | JERAGM\akinje   | INFO     |   [WARNING] Custom field 'JobTitle' (customfield_10238) is empty or not found
2025-07-01 12:33:44.597 | JERAGM\akinje   | INFO     |   [WARNING] Custom field 'LastName' (customfield_10305) is empty or not found
2025-07-01 12:33:44.622 | JERAGM\akinje   | INFO     |   [WARNING] Custom field 'OfficeLocation' (customfield_10115) is empty or not found
2025-07-01 12:33:44.653 | JERAGM\akinje   | INFO     |   [WARNING] Custom field 'FirstName' (customfield_10304) is empty or not found
2025-07-01 12:33:44.685 | JERAGM\akinje   | INFO     | Proceeding with admin account creation despite warnings...
2025-07-01 12:33:44.717 | JERAGM\akinje   | ERROR    | Admin account creation failed: Could not extract user details from Jira ticket
2025-07-01 12:33:44.742 | JERAGM\akinje   | ERROR    | Admin account creation failed: Could not extract user details from Jira ticket | AUDIT: ErrorType=RuntimeException; Success=False; Operation=AdminAccountCreationError
2025-07-01 12:33:44.767 | JERAGM\akinje   | INFO     | Adding comment to Jira ticket | AUDIT: UseADF=True; Operation=JiraCommentAdd; IncludeTimestamp=True; TicketKey=TESTIT-49342
2025-07-01 12:33:46.525 | JERAGM\akinje   | INFO     | Comment added successfully to ticket TESTIT-49342 | AUDIT: CommentId=206557; Operation=JiraCommentSuccess; TicketKey=TESTIT-49342
2025-07-01 12:33:46.550 | JERAGM\akinje   | INFO     | Press any key to continue...
2025-07-01 12:42:10.240 | JERAGM\akinje   | INFO     | ================================================================================
2025-07-01 12:42:10.266 | JERAGM\akinje   | INFO     |                     JML (Joiners, Movers and Leavers) System                      
2025-07-01 12:42:10.292 | JERAGM\akinje   | INFO     |                               Version 1.13                                    
2025-07-01 12:42:10.314 | JERAGM\akinje   | INFO     | ================================================================================
2025-07-01 12:42:10.337 | JERAGM\akinje   | INFO     | System Status:
2025-07-01 12:42:10.362 | JERAGM\akinje   | INFO     |   User: JERAGM\akinje on JGM-CG7THR3
2025-07-01 12:42:10.385 | JERAGM\akinje   | INFO     |   Configuration: Loaded
2025-07-01 12:42:10.411 | JERAGM\akinje   | INFO     |   Credential Method: EncryptedFile
2025-07-01 12:42:10.454 | JERAGM\akinje   | DEBUG    | [DEBUG] Checking credential setup status for method: EncryptedFile
2025-07-01 12:42:10.479 | JERAGM\akinje   | DEBUG    | [DEBUG] Checking EncryptedFile credential storage
2025-07-01 12:42:10.507 | JERAGM\akinje   | DEBUG    | [DEBUG] Checking for encrypted file at: [REDACTED_PATH]s\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Config\SecureCredentials.xml
2025-07-01 12:42:10.558 | JERAGM\akinje   | DEBUG    | [DEBUG] EncryptedFile credentials appear to be configured and accessible
2025-07-01 12:42:10.584 | JERAGM\akinje   | INFO     |   Vault Status: Not Configured
2025-07-01 12:42:10.609 | JERAGM\akinje   | INFO     |   Jira Integration: Enabled
2025-07-01 12:42:10.631 | JERAGM\akinje   | INFO     |   Active Directory: NOT AVAILABLE (UAT Mode)
2025-07-01 12:42:10.653 | JERAGM\akinje   | INFO     | Available Operations:
2025-07-01 12:42:10.676 | JERAGM\akinje   | INFO     |   1. Create JML Account
2025-07-01 12:42:10.698 | JERAGM\akinje   | INFO     |   2. Delete JML Account
2025-07-01 12:42:10.719 | JERAGM\akinje   | INFO     |   3. Reset JML Account Password
2025-07-01 12:42:10.740 | JERAGM\akinje   | INFO     |   4. System Information
2025-07-01 12:42:10.761 | JERAGM\akinje   | INFO     |   5. Run Setup / Repair Credentials
2025-07-01 12:42:10.783 | JERAGM\akinje   | INFO     |   7. Exit
2025-07-01 12:42:10.806 | JERAGM\akinje   | INFO     | Enter your choice: 
2025-07-01 13:06:19.278 | JERAGM\akinje   | INFO     | === CREATE JML ACCOUNT ===
2025-07-01 13:06:22.676 | JERAGM\akinje   | INFO     | === JML ACCOUNT CREATION ===
2025-07-01 13:06:22.693 | JERAGM\akinje   | INFO     | JML System v1.13
2025-07-01 13:06:22.710 | JERAGM\akinje   | INFO     | Processing Jira ticket: TESTIT-49342
2025-07-01 13:06:22.730 | JERAGM\akinje   | INFO     | [INFO] Initializing Jira session...
2025-07-01 13:06:22.748 | JERAGM\akinje   | DEBUG    | [DEBUG] Received credential types:
2025-07-01 13:06:22.765 | JERAGM\akinje   | DEBUG    | [DEBUG] - jiraUsername type: String
2025-07-01 13:06:22.786 | JERAGM\akinje   | DEBUG    | [DEBUG] - jiraApiToken type: SecureString
2025-07-01 13:06:22.807 | JERAGM\akinje   | DEBUG    | [DEBUG] Credential validation passed
2025-07-01 13:06:22.829 | JERAGM\akinje   | DEBUG    | [DEBUG] Username format: Valid email
2025-07-01 13:06:22.848 | JERAGM\akinje   | DEBUG    | [DEBUG] API token=[REDACTED] (length: 192)
2025-07-01 13:06:22.867 | JERAGM\akinje   | DEBUG    | [DEBUG] Final PSCredential object:
2025-07-01 13:06:22.887 | JERAGM\akinje   | DEBUG    | [DEBUG] - Username: emmanuel.akinjomo***@jeragm.com
2025-07-01 13:06:22.913 | JERAGM\akinje   | DEBUG    | [DEBUG] - Password type: SecureString
2025-07-01 13:06:22.934 | JERAGM\akinje   | INFO     | [INFO] Connecting to Jira server: [SERVER]
2025-07-01 13:06:22.958 | JERAGM\akinje   | INFO     | [INFO] Using username: emmanuel.akinjomo***@jeragm.com
2025-07-01 13:06:22.977 | JERAGM\akinje   | INFO     | Initializing Jira connection | AUDIT: ServerUrl=[SERVER]; Operation=JiraConnectionInit; Username=emmanuel.akinjomo***@jeragm.com
2025-07-01 13:06:22.997 | JERAGM\akinje   | INFO     | Setting Jira server configuration
2025-07-01 13:06:23.018 | JERAGM\akinje   | INFO     | Creating JiraPS session for module compatibility
2025-07-01 13:06:23.884 | JERAGM\akinje   | INFO     | JiraPS session created successfully
2025-07-01 13:06:23.900 | JERAGM\akinje   | INFO     | Creating custom session using direct authentication
2025-07-01 13:06:23.918 | JERAGM\akinje   | INFO     | Testing Jira connection using GDPR-compliant endpoint
2025-07-01 13:06:24.811 | JERAGM\akinje   | INFO     | Jira connection test successful | AUDIT: UserAccountId=712020:65c99194-b77b-498d-8a7e-e1dd4dd40efa; Status=Success; UserDisplayName=Emmanuel Akinjomo; Operation=JiraConnectionTest
2025-07-01 13:06:24.829 | JERAGM\akinje   | INFO     | Jira connection established successfully.
2025-07-01 13:06:24.847 | JERAGM\akinje   | INFO     | Connected as: Emmanuel Akinjomo (emmanuel.akinjomo***@jeragm.com)
2025-07-01 13:06:24.862 | JERAGM\akinje   | INFO     | Jira connection initialized successfully using JiraPS module
2025-07-01 13:06:24.886 | JERAGM\akinje   | INFO     | [SUCCESS] Jira session initialized successfully
2025-07-01 13:06:24.904 | JERAGM\akinje   | INFO     | Validating Jira ticket | AUDIT: Operation=JiraTicketValidation; TicketKey=TESTIT-49342
2025-07-01 13:06:25.597 | JERAGM\akinje   | INFO     | Retrieved Jira ticket successfully | AUDIT: TicketSummary=; IssueTypeExists=False; FieldsAvailable=; TicketKey=TESTIT-49342; Operation=JiraTicketRetrieval; IssueTypeType=null
2025-07-01 13:06:25.617 | JERAGM\akinje   | DEBUG    | Full ticket structure for debugging | AUDIT: TicketStructure={"customfield_10211":null,"customfield_10321":null,"customfield_10374":null,"customfield_10335":null,"customfield_10368":null,"customfield_10069":null,"customfield_10385":null,"customfield_10222":null,"Comment":[{"Body":"New Joiner Name: Test1 Test2 \n\nJob Title: Intern \n\nDepartment: IT \n\nLine Manager: Edmund Chan \n\nReporter: Hideki Magtoto \n\nModel Account: test \n\n\n\n\n\nApplication Name: C9 Trader \n| \\\\  \\\\  \\\\  \\\\  \\\\  \\\\  \\\\ \n| \\\\ [ \\\\ target=\"_blank\"  \\\\ style=\"font-size: 18px; font-family: Arial, sans-serif; color: #ffffff; text-decoration: none; padding: 10px 25px; display: inline-block; border-radius: 3px; background-color: #068000; border: 1px solid #068000;\"> \\\\ Approve \\\\ |mailto:<EMAIL>?subject=Re:%20TESTIT-49342%20C9 Trader%20Approval%20Email&body=Approved] \\\\ | | \\\\ [ \\\\ target=\"_blank\"  \\\\ style=\"font-size: 18px; font-family: Arial, sans-serif; color: #ffffff; text-decoration: none; padding: 10px 25px; display: inline-block; border-radius: 3px; background-color: #FD1B00; border: 1px solid #FD1B00;\"> \\\\ Decline \\\\ |mailto:<EMAIL>?subject=Re:%20TESTIT-49342%20Issue&body=Declined] \\\\ | \n\n\nFor any clarifications, please contact IT Support using the below channels: \n\n\n\n1. Email: <EMAIL> \n\n2. Hotline Numbers: \n\nJP: +81 800-111-0060 - Toll Free Number (Local Dialing) \n\nJP: +81 50-5050-8456 - International Dialing \n\nSG: +65 800 321 1666 - Toll Free Number (Local Dialing) \n\nSG: +65 6028 2098 - International Dialing \n\nUK: +44 20 4587 6167 \n\nUS: +1 919 280 2601 \n\n\n\nThanks & Regards \n\nEnd User Support Team \n\n\n\nTo view more information on this request, please click on this link: https://jeragm.atlassian.net/servicedesk/customer/portal/4/TESTIT-49342?created=true","RestUrl":"https://jeragm.atlassian.net/rest/api/2/issue/107637/comment/199652","UpdateAuthor":{"Locale":null,"RestUrl":"https://jeragm.atlassian.net/rest/api/2/user?accountId=qm%3Aa5ef4933-12d6-463a-98fb-7577ad45dda9%3Abc35f20b-d705-416b-ad88-5647c1a5d763","Active":true,"Groups":null,"DisplayName":"<EMAIL>","AvatarUrl":{"48x48":"https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/default-avatar.png","24x24":"https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/default-avatar.png","16x16":"https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/default-avatar.png","32x32":"https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/default-avatar.png"},"AccountId":"qm:a5ef4933-12d6-463a-98fb-7577ad45dda9:bc35f20b-d705-416b-ad88-5647c1a5d763","TimeZone":"Europe/London","Key":null,"Name":null,"EmailAddress":"<EMAIL>"},"Visibility":null,"Author":{"Locale":null,"RestUrl":"https://jeragm.atlassian.net/rest/api/2/user?accountId=qm%3Aa5ef4933-12d6-463a-98fb-7577ad45dda9%3Abc35f20b-d705-416b-ad88-5647c1a5d763","Active":true,"Groups":null,"DisplayName":"<EMAIL>","AvatarUrl":{"48x48":"https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/default-avatar.png","24x24":"https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/default-avatar.png","16x16":"https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/default-avatar.png","32x32":"https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/default-avatar.png"},"AccountId":"qm:a5ef4933-12d6-463a-98fb-7577ad45dda9:bc35f20b-d705-416b-ad88-5647c1a5d763","TimeZone":"Europe/London","Key":null,"Name":null,"EmailAddress":"<EMAIL>"},"ID":"199652","Updated":"2025-05-06T07:44:35.695+01:00","Created":"2025-05-06T07:44:35.695+01:00"},{"Body":"Approval email sent to\n\n","RestUrl":"https://jeragm.atlassian.net/rest/api/2/issue/107637/comment/199797","UpdateAuthor":{"Locale":null,"RestUrl":"https://jeragm.atlassian.net/rest/api/2/user?accountId=557058%3Af58131cb-b67d-43c7-b30d-6b58d40bd077","Active":true,"Groups":null,"DisplayName":"Automation for Jira","AvatarUrl":{"48x48":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png","24x24":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png","16x16":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png","32x32":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png"},"AccountId":"557058:f58131cb-b67d-43c7-b30d-6b58d40bd077","TimeZone":"Europe/London","Key":null,"Name":null,"EmailAddress":null},"Visibility":null,"Author":{"Locale":null,"RestUrl":"https://jeragm.atlassian.net/rest/api/2/user?accountId=557058%3Af58131cb-b67d-43c7-b30d-6b58d40bd077","Active":true,"Groups":null,"DisplayName":"Automation for Jira","AvatarUrl":{"48x48":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png","24x24":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png","16x16":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png","32x32":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png"},"AccountId":"557058:f58131cb-b67d-43c7-b30d-6b58d40bd077","TimeZone":"Europe/London","Key":null,"Name":null,"EmailAddress":null},"ID":"199797","Updated":"2025-05-07T02:57:34.707+01:00","Created":"2025-05-07T02:57:34.707+01:00"},{"Body":"Approval email sent to\n\n- [~hideki.magtoto]","RestUrl":"https://jeragm.atlassian.net/rest/api/2/issue/107637/comment/199810","UpdateAuthor":{"Locale":null,"RestUrl":"https://jeragm.atlassian.net/rest/api/2/user?accountId=557058%3Af58131cb-b67d-43c7-b30d-6b58d40bd077","Active":true,"Groups":null,"DisplayName":"Automation for Jira","AvatarUrl":{"48x48":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png","24x24":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png","16x16":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png","32x32":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png"},"AccountId":"557058:f58131cb-b67d-43c7-b30d-6b58d40bd077","TimeZone":"Europe/London","Key":null,"Name":null,"EmailAddress":null},"Visibility":null,"Author":{"Locale":null,"RestUrl":"https://jeragm.atlassian.net/rest/api/2/user?accountId=557058%3Af58131cb-b67d-43c7-b30d-6b58d40bd077","Active":true,"Groups":null,"DisplayName":"Automation for Jira","AvatarUrl":{"48x48":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png","24x24":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png","16x16":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png","32x32":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png"},"AccountId":"557058:f58131cb-b67d-43c7-b30d-6b58d40bd077","TimeZone":"Europe/London","Key":null,"Name":null,"EmailAddress":null},"ID":"199810","Updated":"2025-05-07T03:25:29.994+01:00","Created":"2025-05-07T03:25:29.994+01:00"},{"Body":"Approval email sent to\n\n- [~accountid:************************]","RestUrl":"https://jeragm.atlassian.net/rest/api/2/issue/107637/comment/199813","UpdateAuthor":{"Locale":null,"RestUrl":"https://jeragm.atlassian.net/rest/api/2/user?accountId=557058%3Af58131cb-b67d-43c7-b30d-6b58d40bd077","Active":true,"Groups":null,"DisplayName":"Automation for Jira","AvatarUrl":{"48x48":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png","24x24":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png","16x16":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png","32x32":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png"},"AccountId":"557058:f58131cb-b67d-43c7-b30d-6b58d40bd077","TimeZone":"Europe/London","Key":null,"Name":null,"EmailAddress":null},"Visibility":null,"Author":{"Locale":null,"RestUrl":"https://jeragm.atlassian.net/rest/api/2/user?accountId=557058%3Af58131cb-b67d-43c7-b30d-6b58d40bd077","Active":true,"Groups":null,"DisplayName":"Automation for Jira","AvatarUrl":{"48x48":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png","24x24":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png","16x16":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png","32x32":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png"},"AccountId":"557058:f58131cb-b67d-43c7-b30d-6b58d40bd077","TimeZone":"Europe/London","Key":null,"Name":null,"EmailAddress":null},"ID":"199813","Updated":"2025-05-07T03:27:34.07+01:00","Created":"2025-05-07T03:27:34.07+01:00"},{"Body":"TESTIT-49342 Approval email sent to\n\n- [~accountid:************************]\n","RestUrl":"https://jeragm.atlassian.net/rest/api/2/issue/107637/comment/199836","UpdateAuthor":{"Locale":null,"RestUrl":"https://jeragm.atlassian.net/rest/api/2/user?accountId=557058%3Af58131cb-b67d-43c7-b30d-6b58d40bd077","Active":true,"Groups":null,"DisplayName":"Automation for Jira","AvatarUrl":{"48x48":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png","24x24":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png","16x16":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png","32x32":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png"},"AccountId":"557058:f58131cb-b67d-43c7-b30d-6b58d40bd077","TimeZone":"Europe/London","Key":null,"Name":null,"EmailAddress":null},"Visibility":null,"Author":{"Locale":null,"RestUrl":"https://jeragm.atlassian.net/rest/api/2/user?accountId=557058%3Af58131cb-b67d-43c7-b30d-6b58d40bd077","Active":true,"Groups":null,"DisplayName":"Automation for Jira","AvatarUrl":{"48x48":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png","24x24":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png","16x16":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png","32x32":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png"},"AccountId":"557058:f58131cb-b67d-43c7-b30d-6b58d40bd077","TimeZone":"Europe/London","Key":null,"Name":null,"EmailAddress":null},"ID":"199836","Updated":"2025-05-07T05:01:10.828+01:00","Created":"2025-05-07T05:01:10.828+01:00"},{"Body":"Approval email sent to test group approvers group.","RestUrl":"https://jeragm.atlassian.net/rest/api/2/issue/107637/comment/199839","UpdateAuthor":{"Locale":null,"RestUrl":"https://jeragm.atlassian.net/rest/api/2/user?accountId=557058%3Af58131cb-b67d-43c7-b30d-6b58d40bd077","Active":true,"Groups":null,"DisplayName":"Automation for Jira","AvatarUrl":{"48x48":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png","24x24":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png","16x16":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png","32x32":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png"},"AccountId":"557058:f58131cb-b67d-43c7-b30d-6b58d40bd077","TimeZone":"Europe/London","Key":null,"Name":null,"EmailAddress":null},"Visibility":null,"Author":{"Locale":null,"RestUrl":"https://jeragm.atlassian.net/rest/api/2/user?accountId=557058%3Af58131cb-b67d-43c7-b30d-6b58d40bd077","Active":true,"Groups":null,"DisplayName":"Automation for Jira","AvatarUrl":{"48x48":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png","24x24":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png","16x16":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png","32x32":"https://secure.gravatar.com/avatar/600529a9c8bfef89daa848e6db28ed2d?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FAJ-0.png"},"AccountId":"557058:f58131cb-b67d-43c7-b30d-6b58d40bd077","TimeZone":"Europe/London","Key":null,"Name":null,"EmailAddress":null},"ID":"199839","Updated":"2025-05-07T05:08:16.725+01:00","Created":"2025-05-07T05:08:16.725+01:00"},{"Body":"Hi test group,\n\nNew Joiner Name: Test1 Test2 \n\nJob Title: Intern \n\nDepartment: IT \n\nLine Manager: Edmund Chan \n\nReporter: Hideki Magtoto \n\nModel Account: test \n\n\n\n\n\nApplication Name: test group \n\nAllegro Security Group: \n| \\\\  \\\\  \\\\  \\\\  \\\\  \\\\  \\\\ \n| \\\\ [ \\\\ target=\"_blank\"  \\\\ style=\"font-size: 18px; font-family: Arial, sans-serif; color: #ffffff; text-decoration: none; padding: 10px 25px; display: inline-block; border-radius: 3px; background-color: #068000; border: 1px solid #068000;\"> \\\\ Approve \\\\ |mailto:<EMAIL>?subject=Re:%20TESTIT-49342%20test group%20Approval%20Email&body=Application%20test group%20Approved.] \\\\ | | \\\\ [ \\\\ target=\"_blank\"  \\\\ style=\"font-size: 18px; font-family: Arial, sans-serif; color: #ffffff; text-decoration: none; padding: 10px 25px; display: inline-block; border-radius: 3px; background-color: #FD1B00; border: 1px solid #FD1B00;\"> \\\\ Decline \\\\ |mailto:<EMAIL>?subject=Re:%20TESTIT-49342%20test group%20Approval%20Email&body=Application%20test group%20Declined.] \\\\ | \n\n\nFor any clarifications, please contact IT Support using the below channels: \n\n\n\n1. Email: <EMAIL> \n\n2. Hotline Numbers: \n\nJP: +81 800-111-0060 - Toll Free Number (Local Dialing) \n\nJP: +81 50-5050-8456 - International Dialing \n\nSG: +65 800 321 1666 - Toll Free Number (Local Dialing) \n\nSG: +65 6028 2098 - International Dialing \n\nUK: +44 20 4587 6167 \n\nUS: +1 919 280 2601 \n\n\n\nThanks & Regards \n\nEnd User Support Team \n\n\n\nTo view more information on this request, please click on this link: https://jeragm.atlassian.net/servicedesk/customer/portal/4/TESTIT-49342?created=true","RestUrl":"https://jeragm.atlassian.net/rest/api/2/issue/107637/comment/200040","UpdateAuthor":{"Locale":null,"RestUrl":"https://jeragm.atlassian.net/rest/api/2/user?accountId=qm%3Aa5ef4933-12d6-463a-98fb-7577ad45dda9%3Abc35f20b-d705-416b-ad88-5647c1a5d763","Active":true,"Groups":null,"DisplayName":"<EMAIL>","AvatarUrl":{"48x48":"https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/default-avatar.png","24x24":"https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/default-avatar.png","16x16":"https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/default-avatar.png","32x32":"https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/default-avatar.png"},"AccountId":"qm:a5ef4933-12d6-463a-98fb-7577ad45dda9:bc35f20b-d705-416b-ad88-5647c1a5d763","TimeZone":"Europe/London","Key":null,"Name":null,"EmailAddress":"<EMAIL>"},"Visibility":null,"Author":{"Locale":null,"RestUrl":"https://jeragm.atlassian.net/rest/api/2/user?accountId=qm%3Aa5ef4933-12d6-463a-98fb-7577ad45dda9%3Abc35f20b-d705-416b-ad88-5647c1a5d763","Active":true,"Groups":null,"DisplayName":"<EMAIL>","AvatarUrl":{"48x48":"https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/default-avatar.png","24x24":"https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/default-avatar.png","16x16":"https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/default-avatar.png","32x32":"https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/default-avatar.png"},"AccountId":"qm:a5ef4933-12d6-463a-98fb-7577ad45dda9:bc35f20b-d705-416b-ad88-5647c1a5d763","TimeZone":"Europe/London","Key":null,"Name":null,"EmailAddress":"<EMAIL>"},"ID":"200040","Updated":"2025-05-08T06:43:28.373+01:00","Created":"2025-05-08T06:43:28.373+01:00"},{"Body":"[2025-06-24 12:09:11] [ERROR] Admin account password reset failed: Jira connection test failed: Jira connection test failed - could not retrieve user information","RestUrl":"https://jeragm.atlassian.net/rest/api/2/issue/107637/comment/205615","UpdateAuthor":{"Locale":null,"RestUrl":"https://jeragm.atlassian.net/rest/api/2/user?accountId=712020%3A65c99194-b77b-498d-8a7e-e1dd4dd40efa","Active":true,"Groups":null,"DisplayName":"Emmanuel Akinjomo","AvatarUrl":{"48x48":"https://secure.gravatar.com/avatar/cb1faea1c84679d2feba2fd2c5eab9b8?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FEA-2.png","24x24":"https://secure.gravatar.com/avatar/cb1faea1c84679d2feba2fd2c5eab9b8?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FEA-2.png","16x16":"https://secure.gravatar.com/avatar/cb1faea1c84679d2feba2fd2c5eab9b8?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FEA-2.png","32x32":"https://secure.gravatar.com/avatar/cb1faea1c84679d2feba2fd2c5eab9b8?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FEA-2.png"},"AccountId":"712020:65c99194-b77b-498d-8a7e-e1dd4dd40efa","TimeZone":"Europe/London","Key":null,"Name":null,"EmailAddress":"<EMAIL>"},"Visibility":null,"Author":{"Locale":null,"RestUrl":"https://jeragm.atlassian.net/rest/api/2/user?accountId=712020%3A65c99194-b77b-498d-8a7e-e1dd4dd40efa","Active":true,"Groups":null,"DisplayName":"Emmanuel Akinjomo","AvatarUrl":{"48x48":"https://secure.gravatar.com/avatar/cb1faea1c84679d2feba2fd2c5eab9b8?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FEA-2.png","24x24":"https://secure.gravatar.com/avatar/cb1faea1c84679d2feba2fd2c5eab9b8?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FEA-2.png","16x16":"https://secure.gravatar.com/avatar/cb1faea1c84679d2feba2fd2c5eab9b8?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FEA-2.png","32x32":"https://secure.gravatar.com/avatar/cb1faea1c84679d2feba2fd2c5eab9b8?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FEA-2.png"},"AccountId":"712020:65c99194-b77b-498d-8a7e-e1dd4dd40efa","TimeZone":"Europe/London","Key":null,"Name":null,"EmailAddress":"<EMAIL>"},"ID":"205615","Updated":"2025-06-24T12:09:13.688+01:00","Created":"2025-06-24T12:09:13.688+01:00"},{"Body":"[2025-06-24 15:27:55] [ERROR] Admin account creation failed: Jira ticket validation failed: Work type mismatch. Expected: 'Service Request with Approvals', Found: ''. ","RestUrl":"https://jeragm.atlassian.net/rest/api/2/issue/107637/comment/205632","UpdateAuthor":{"Locale":null,"RestUrl":"https://jeragm.atlassian.net/rest/api/2/user?accountId=712020%3A65c99194-b77b-498d-8a7e-e1dd4dd40efa","Active":true,"Groups":null,"DisplayName":"Emmanuel Akinjomo","AvatarUrl":{"48x48":"https://secure.gravatar.com/avatar/cb1faea1c84679d2feba2fd2c5eab9b8?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FEA-2.png","24x24":"https://secure.gravatar.com/avatar/cb1faea1c84679d2feba2fd2c5eab9b8?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FEA-2.png","16x16":"https://secure.gravatar.com/avatar/cb1faea1c84679d2feba2fd2c5eab9b8?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FEA-2.png","32x32":"https://secure.gravatar.com/avatar/cb1faea1c84679d2feba2fd2c5eab9b8?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FEA-2.png"},"AccountId":"712020:65c99194-b77b-498d-8a7e-e1dd4dd40efa","TimeZone":"Europe/London","Key":null,"Name":null,"EmailAddress":"<EMAIL>"},"Visibility":null,"Author":{"Locale":null,"RestUrl":"https://jeragm.atlassian.net/rest/api/2/user?accountId=712020%3A65c99194-b77b-498d-8a7e-e1dd4dd40efa","Active":true,"Groups":null,"DisplayName":"Emmanuel Akinjomo","AvatarUrl":{"48x48":"https://secure.gravatar.com/avatar/cb1faea1c84679d2feba2fd2c5eab9b8?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FEA-2.png","24x24":"https://secure.gravatar.com/avatar/cb1faea1c84679d2feba2fd2c5eab9b8?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FEA-2.png","16x16":"https://secure.gravatar.com/avatar/cb1faea1c84679d2feba2fd2c5eab9b8?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FEA-2.png","32x32":"https://secure.gravatar.com/avatar/cb1faea1c84679d2feba2fd2c5eab9b8?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FEA-2.png"},"AccountId":"712020:65c99194-b77b-498d-8a7e-e1dd4dd40efa","TimeZone":"Europe/London","Key":null,"Name":null,"EmailAddress":"<EMAIL>"},"ID":"205632","Updated":"2025-06-24T15:27:57.354+01:00","Created":"2025-06-24T15:27:57.354+01:00"},{"Body":"[2025-07-01 12:32:44] [ERROR] Admin account creation failed: Could not extract user details from Jira ticket","RestUrl":"https://jeragm.atlassian.net/rest/api/2/issue/107637/comment/206556","UpdateAuthor":{"Locale":null,"RestUrl":"https://jeragm.atlassian.net/rest/api/2/user?accountId=712020%3A65c99194-b77b-498d-8a7e-e1dd4dd40efa","Active":true,"Groups":null,"DisplayName":"Emmanuel Akinjomo","AvatarUrl":{"48x48":"https://secure.gravatar.com/avatar/cb1faea1c84679d2feba2fd2c5eab9b8?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FEA-2.png","24x24":"https://secure.gravatar.com/avatar/cb1faea1c84679d2feba2fd2c5eab9b8?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FEA-2.png","16x16":"https://secure.gravatar.com/avatar/cb1faea1c84679d2feba2fd2c5eab9b8?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FEA-2.png","32x32":"https://secure.gravatar.com/avatar/cb1faea1c84679d2feba2fd2c5eab9b8?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FEA-2.png"},"AccountId":"712020:65c99194-b77b-498d-8a7e-e1dd4dd40efa","TimeZone":"Europe/London","Key":null,"Name":null,"EmailAddress":"<EMAIL>"},"Visibility":null,"Author":{"Locale":null,"RestUrl":"https://jeragm.atlassian.net/rest/api/2/user?accountId=712020%3A65c99194-b77b-498d-8a7e-e1dd4dd40efa","Active":true,"Groups":null,"DisplayName":"Emmanuel Akinjomo","AvatarUrl":{"48x48":"https://secure.gravatar.com/avatar/cb1faea1c84679d2feba2fd2c5eab9b8?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FEA-2.png","24x24":"https://secure.gravatar.com/avatar/cb1faea1c84679d2feba2fd2c5eab9b8?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FEA-2.png","16x16":"https://secure.gravatar.com/avatar/cb1faea1c84679d2feba2fd2c5eab9b8?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FEA-2.png","32x32":"https://secure.gravatar.com/avatar/cb1faea1c84679d2feba2fd2c5eab9b8?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FEA-2.png"},"AccountId":"712020:65c99194-b77b-498d-8a7e-e1dd4dd40efa","TimeZone":"Europe/London","Key":null,"Name":null,"EmailAddress":"<EMAIL>"},"ID":"206556","Updated":"2025-07-01T12:32:46.901+01:00","Created":"2025-07-01T12:32:46.901+01:00"},{"Body":"[2025-07-01 12:33:44] [ERROR] Admin account creation failed: Could not extract user details from Jira ticket","RestUrl":"https://jeragm.atlassian.net/rest/api/2/issue/107637/comment/206557","UpdateAuthor":{"Locale":null,"RestUrl":"https://jeragm.atlassian.net/rest/api/2/user?accountId=712020%3A65c99194-b77b-498d-8a7e-e1dd4dd40efa","Active":true,"Groups":null,"DisplayName":"Emmanuel Akinjomo","AvatarUrl":{"48x48":"https://secure.gravatar.com/avatar/cb1faea1c84679d2feba2fd2c5eab9b8?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FEA-2.png","24x24":"https://secure.gravatar.com/avatar/cb1faea1c84679d2feba2fd2c5eab9b8?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FEA-2.png","16x16":"https://secure.gravatar.com/avatar/cb1faea1c84679d2feba2fd2c5eab9b8?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FEA-2.png","32x32":"https://secure.gravatar.com/avatar/cb1faea1c84679d2feba2fd2c5eab9b8?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FEA-2.png"},"AccountId":"712020:65c99194-b77b-498d-8a7e-e1dd4dd40efa","TimeZone":"Europe/London","Key":null,"Name":null,"EmailAddress":"<EMAIL>"},"Visibility":null,"Author":{"Locale":null,"RestUrl":"https://jeragm.atlassian.net/rest/api/2/user?accountId=712020%3A65c99194-b77b-498d-8a7e-e1dd4dd40efa","Active":true,"Groups":null,"DisplayName":"Emmanuel Akinjomo","AvatarUrl":{"48x48":"https://secure.gravatar.com/avatar/cb1faea1c84679d2feba2fd2c5eab9b8?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FEA-2.png","24x24":"https://secure.gravatar.com/avatar/cb1faea1c84679d2feba2fd2c5eab9b8?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FEA-2.png","16x16":"https://secure.gravatar.com/avatar/cb1faea1c84679d2feba2fd2c5eab9b8?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FEA-2.png","32x32":"https://secure.gravatar.com/avatar/cb1faea1c84679d2feba2fd2c5eab9b8?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FEA-2.png"},"AccountId":"712020:65c99194-b77b-498d-8a7e-e1dd4dd40efa","TimeZone":"Europe/London","Key":null,"Name":null,"EmailAddress":"<EMAIL>"},"ID":"206557","Updated":"2025-07-01T12:33:46.241+01:00","Created":"2025-07-01T12:33:46.241+01:00"}],"customfield_10320":null,"customfield_10000":"{}","customfield_10559":null,"customfield_10486":null,"customfield_10154":null,"customfield_10343":"test","customfield_10317":null,"customfield_10200":null,"customfield_10651":{"self":"https://jeragm.atlassian.net/rest/api/2/user?accountId=712020%3A4546b468-9348-49fd-8ad5-95059dd6c6b2","accountId":"712020:4546b468-9348-49fd-8ad5-95059dd6c6b2","emailAddress":"<EMAIL>","avatarUrls":{"48x48":"https://secure.gravatar.com/avatar/ad5737244b64814d9c33fec13670b324?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FKS-3.png","24x24":"https://secure.gravatar.com/avatar/ad5737244b64814d9c33fec13670b324?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FKS-3.png","16x16":"https://secure.gravatar.com/avatar/ad5737244b64814d9c33fec13670b324?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FKS-3.png","32x32":"https://secure.gravatar.com/avatar/ad5737244b64814d9c33fec13670b324?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FKS-3.png"},"displayName":"Kewal Shah","active":true,"timeZone":"Europe/London","accountType":"atlassian"},"customfield_10027":null,"customfield_10070":null,"customfield_10254":null,"security":null,"customfield_10216":null,"customfield_10195":[],"customfield_10133":null,"customfield_10009":{"hasEpicLinkFieldDependency":true,"showField":true,"nonEditableReason":{"reason":"PLUGIN_LICENSE_ERROR","message":"The Parent Link is only available to Jira Premium users."}},"customfield_11213":null,"aggregatetimeoriginalestimate":null,"customfield_10056":null,"customfield_10061":{"id":"30","name":"Time to first response","_links":{"self":"https://jeragm.atlassian.net/rest/servicedeskapi/request/107637/sla/30"},"completedCycles":[],"slaDisplayFormat":"NEW_SLA_FORMAT"},"customfield_10751":null,"customfield_10322":null,"customfield_10519":null,"customfield_10183":null,"customfield_10065":null,"ID":"107637","customfield_10560":null,"customfield_10326":{"self":"https://jeragm.atlassian.net/rest/api/2/customFieldOption/10640","value":"No","id":"10640"},"timespent":null,"customfield_10354":null,"customfield_10381":null,"customfield_10393":null,"customfield_10190":null,"customfield_10391":null,"customfield_11181":null,"customfield_10189":null,"customfield_10193":null,"customfield_10237":null,"customfield_10684":{"self":"https://jeragm.atlassian.net/rest/api/2/customFieldOption/11712","value":"TBD","id":"11712"},"customfield_10132":null,"customfield_10204":null,"customfield_10175":null,"duedate":null,"environment":null,"customfield_11212":null,"customfield_10721":null,"customfield_10235":null,"customfield_10556":null,"customfield_10203":null,"customfield_10066":null,"customfield_10372":null,"customfield_10050":null,"customfield_10058":null,"customfield_10037":null,"customfield_10172":null,"statuscategorychangedate":"2025-05-06T06:27:02.869+01:00","customfield_10030":null,"customfield_10727":null,"Key":"TESTIT-49342","issuelinks":[],"customfield_10224":0.0,"priority":{"self":"https://jeragm.atlassian.net/rest/api/2/priority/4","iconUrl":"https://jeragm.atlassian.net/images/icons/priorities/low.svg","name":"Low","id":"4"},"customfield_10662":null,"customfield_10341":null,"customfield_10346":null,"labels":[],"customfield_10155":null,"customfield_10210":null,"customfield_10557":null,"resolution":null,"customfield_11214":null,"customfield_10663":null,"customfield_10178":null,"customfield_10185":null,"customfield_10330":null,"customfield_11182":null,"customfield_10042":[{"id":"1297","name":"Waiting for approval","finalDecision":"approved","canAnswerApproval":false,"approvers":[],"createdDate":{"iso8601":"2025-05-06T06:27:02+01:00","jira":"2025-05-06T06:27:02.738+01:00","friendly":"06/May/2025 06:27","epochMillis":1746509222738},"completedDate":{"iso8601":"2025-05-06T07:44:40+01:00","jira":"2025-05-06T07:44:40.509+01:00","friendly":"06/May/2025 07:44","epochMillis":1746513880509},"_links":{"self":"https://jeragm.atlassian.net/rest/servicedeskapi/request/107637/approval/1297"}}],"customfield_10148":null,"customfield_10488":null,"customfield_10337":[],"customfield_10161":null,"customfield_10060":{"id":"5","name":"Time to resolution","_links":{"self":"https://jeragm.atlassian.net/rest/servicedeskapi/request/107637/sla/5"},"completedCycles":[],"slaDisplayFormat":"NEW_SLA_FORMAT"},"customfield_10562":null,"aggregatetimespent":null,"Transition":[{"ResultStatus":{"Name":"In Progress","Description":"This work item is being actively worked on at the moment by the assignee.","RestUrl":"https://jeragm.atlassian.net/rest/api/2/status/3","IconUrl":"https://jeragm.atlassian.net/images/icons/statuses/inprogress.png","ID":"3"},"Name":"In Progress","ID":"51"},{"ResultStatus":{"Name":"Cancelled","Description":"Tickets that are no longer needed, reasons to be documented in the ticket description.","RestUrl":"https://jeragm.atlassian.net/rest/api/2/status/10067","IconUrl":"https://jeragm.atlassian.net/images/icons/status_generic.gif","ID":"10067"},"Name":"Cancelled","ID":"71"},{"ResultStatus":{"Name":"Pending","Description":"Item needing something to move to the next stage.","RestUrl":"https://jeragm.atlassian.net/rest/api/2/status/10015","IconUrl":"https://jeragm.atlassian.net/","ID":"10015"},"Name":"Pending","ID":"111"},{"ResultStatus":{"Name":"Waiting for approval","Description":"This was auto-generated by Jira Service Desk during workflow import","RestUrl":"https://jeragm.atlassian.net/rest/api/2/status/10083","IconUrl":"https://jeragm.atlassian.net/images/icons/status_generic.gif","ID":"10083"},"Name":"for automation","ID":"171"}],"customfield_10001":null,"customfield_10783":{"self":"https://jeragm.atlassian.net/rest/api/2/customFieldOption/11864","value":"Low","id":"11864"},"customfield_10100":null,"customfield_10630":{"self":"https://jeragm.atlassian.net/rest/api/2/customFieldOption/11280","value":"TBD","id":"11280"},"customfield_10029":[],"customfield_10049":null,"customfield_10635":null,"customfield_10118":null,"customfield_10626":{"self":"https://jeragm.atlassian.net/rest/api/2/customFieldOption/11278","value":"TBD","id":"11278"},"customfield_10153":null,"customfield_10555":null,"timeestimate":null,"customfield_10223":null,"customfield_10261":null,"worklog":{"startAt":0,"maxResults":20,"total":0,"worklogs":[]},"customfield_10191":null,"customfield_10386":[{"name":"C9 Trader","groupId":"************************7ac9a8254fae","self":"https://jeragm.atlassian.net/rest/api/2/group?groupId=************************7ac9a8254fae"}],"customfield_10306":null,"customfield_10453":{"self":"https://jeragm.atlassian.net/rest/api/2/customFieldOption/11051","value":"No","id":"11051"},"versions":[],"timetracking":{},"customfield_10043":[{"self":"https://jeragm.atlassian.net/rest/api/2/user?accountId=712020%3Aef74f97d-0206-4208-9468-be9637efa0b3","accountId":"712020:ef74f97d-0206-4208-9468-be9637efa0b3","emailAddress":"<EMAIL>","avatarUrls":{"48x48":"https://secure.gravatar.com/avatar/3fca9e86bc03c306f3855e0a91bec38f?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FEC-5.png","24x24":"https://secure.gravatar.com/avatar/3fca9e86bc03c306f3855e0a91bec38f?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FEC-5.png","16x16":"https://secure.gravatar.com/avatar/3fca9e86bc03c306f3855e0a91bec38f?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FEC-5.png","32x32":"https://secure.gravatar.com/avatar/3fca9e86bc03c306f3855e0a91bec38f?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FEC-5.png"},"displayName":"Edmund Chan","active":true,"timeZone":"Europe/London","accountType":"atlassian"}],"customfield_10117":null,"customfield_10489":null,"customfield_10202":null,"customfield_10255":null,"statusCategory":{"self":"https://jeragm.atlassian.net/rest/api/2/statuscategory/2","id":2,"key":"new","colorName":"blue-gray","name":"To Do"},"customfield_10181":[{"self":"https://jeragm.atlassian.net/rest/api/2/customFieldOption/10591","value":"End User Support (IT Ops)","id":"10591"}],"customfield_10213":null,"Updated":"2025-07-01T12:33:46.241+01:00","customfield_10135":null,"customfield_10045":null,"Created":"2025-05-06T06:27:01.109+01:00","customfield_10344":"2025-05-29","customfield_10628":{"self":"https://jeragm.atlassian.net/rest/api/2/customFieldOption/11283","value":"TBD","id":"11283"},"customfield_10370":null,"customfield_10101":null,"customfield_10227":1.0,"aggregatetimeestimate":null,"customfield_10726":null,"customfield_10173":null,"HttpUrl":"https://jeragm.atlassian.net/browse/TESTIT-49342","customfield_10395":null,"customfield_10044":null,"customfield_10249":"","customfield_11215":null,"customfield_10064":{"languageCode":"en","displayName":"English"},"customfield_10238":"Intern","customfield_10717":null,"customfield_10521":null,"Creator":{"Locale":null,"RestUrl":"https://jeragm.atlassian.net/rest/api/2/user?accountId=************************","Active":true,"Groups":null,"DisplayName":"Hideki Magtoto","AvatarUrl":{"48x48":"https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/************************/a7b3db59-ab90-4a6d-bec2-565befe00179/48","24x24":"https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/************************/a7b3db59-ab90-4a6d-bec2-565befe00179/24","16x16":"https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/************************/a7b3db59-ab90-4a6d-bec2-565befe00179/16","32x32":"https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/************************/a7b3db59-ab90-4a6d-bec2-565befe00179/32"},"AccountId":"************************","TimeZone":"Singapore","Key":null,"Name":null,"EmailAddress":"<EMAIL>"},"customfield_10186":null,"customfield_10303":[{"self":"https://jeragm.atlassian.net/rest/api/2/customFieldOption/10677","value":"Cloud9","id":"10677"}],"customfield_10180":null,"customfield_10304":"Test1","timeoriginalestimate":null,"Status":"Waiting for support","customfield_10194":null,"customfield_11179":null,"customfield_10197":null,"customfield_10212":null,"customfield_10177":null,"customfield_10184":null,"customfield_10130":null,"customfield_10239":null,"customfield_10145":null,"customfield_10447":null,"customfield_10055":null,"customfield_10250":0.0,"customfield_10157":null,"customfield_10723":null,"customfield_10012":"2025-05-06T07:44:35.695+01:00","customfield_10750":null,"customfield_10062":null,"customfield_10198":null,"customfield_10398":null,"customfield_10126":null,"customfield_10048":null,"customfield_10336":null,"Reporter":{"Locale":null,"RestUrl":"https://jeragm.atlassian.net/rest/api/2/user?accountId=************************","Active":true,"Groups":null,"DisplayName":"Hideki Magtoto","AvatarUrl":{"48x48":"https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/************************/a7b3db59-ab90-4a6d-bec2-565befe00179/48","24x24":"https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/************************/a7b3db59-ab90-4a6d-bec2-565befe00179/24","16x16":"https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/************************/a7b3db59-ab90-4a6d-bec2-565befe00179/16","32x32":"https://avatar-management--avatars.us-west-2.prod.public.atl-paas.net/************************/a7b3db59-ab90-4a6d-bec2-565befe00179/32"},"AccountId":"************************","TimeZone":"Singapore","Key":null,"Name":null,"EmailAddress":"<EMAIL>"},"customfield_10115":{"self":"https://jeragm.atlassian.net/rest/api/2/customFieldOption/10181","value":"Singapore","id":"10181"},"customfield_10685":{"self":"https://jeragm.atlassian.net/rest/api/2/customFieldOption/11713","value":"TBD","id":"11713"},"customfield_10554":null,"customfield_10371":null,"customfield_10127":null,"customfield_10081":{"self":"https://jeragm.atlassian.net/rest/api/2/customFieldOption/10139","value":"TBC","id":"10139"},"customfield_10206":null,"customfield_10725":null,"customfield_10013":null,"customfield_11113":null,"customfield_10448":{"self":"https://jeragm.atlassian.net/rest/api/2/customFieldOption/11036","value":"No","id":"11036"},"customfield_10151":null,"customfield_10053":null,"customfield_10025":null,"customfield_10225":1.0,"customfield_10023":null,"customfield_10046":null,"customfield_10561":null,"customfield_10664":null,"customfield_10159":null,"Assignee":{"Locale":null,"RestUrl":"https://jeragm.atlassian.net/rest/api/2/user?accountId=************************","Active":true,"Groups":null,"DisplayName":"Pavan Kumar Posa","AvatarUrl":{"48x48":"https://secure.gravatar.com/avatar/c6d0a04d80d743256b34cd6de108f5f3?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FPP-6.png","24x24":"https://secure.gravatar.com/avatar/c6d0a04d80d743256b34cd6de108f5f3?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FPP-6.png","16x16":"https://secure.gravatar.com/avatar/c6d0a04d80d743256b34cd6de108f5f3?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FPP-6.png","32x32":"https://secure.gravatar.com/avatar/c6d0a04d80d743256b34cd6de108f5f3?d=https%3A%2F%2Favatar-management--avatars.us-west-2.prod.public.atl-paas.net%2Finitials%2FPP-6.png"},"AccountId":"************************","TimeZone":"Singapore","Key":null,"Name":null,"EmailAddress":"<EMAIL>"},"customfield_10305":"Test2","customfield_10209":null,"customfield_10171":null,"customfield_10276":null,"Summary":"Onboarding Request","customfield_10396":null,"customfield_10102":null,"customfield_10356":[],"customfield_10199":null,"customfield_10059":null,"customfield_10068":null,"customfield_10618":null,"customfield_10149":null,"customfield_10229":null,"LastViewed":"2025-07-01T12:36:17.189+01:00","customfield_10156":null,"customfield_10363":null,"customfield_10072":{"self":"https://jeragm.atlassian.net/rest/api/2/customFieldOption/10130","value":"Under Investigation","id":"10130"},"customfield_10187":null,"customfield_10369":null,"customfield_10722":null,"customfield_10131":null,"customfield_10380":null,"customfield_10352":{"self":"https://jeragm.atlassian.net/rest/api/2/customFieldOption/10695","value":"No","id":"10695"},"customfield_10634":null,"progress":{"progress":0,"total":0},"customfield_10367":null,"customfield_10028":{"_links":{"jiraRest":"https://jeragm.atlassian.net/rest/api/2/issue/107637","web":"https://jeragm.atlassian.net/servicedesk/customer/portal/2/TESTIT-49342","agent":"https://jeragm.atlassian.net/browse/TESTIT-49342","self":"https://jeragm.atlassian.net/rest/servicedeskapi/request/107637"},"requestType":{"_expands":["field"],"id":"57","_links":{"self":"https://jeragm.atlassian.net/rest/servicedeskapi/servicedesk/2/requesttype/57"},"name":"JERAGM Onboarding Request","description":"","helpText":"","defaultName":"JERAGM Onboarding Request","issueTypeId":"10042","serviceDeskId":"2","portalId":"2","groupIds":["6"],"icon":{"id":"10566","_links":{"iconUrls":{"48x48":"https://jeragm.atlassian.net/rest/api/2/universal_avatar/view/type/SD_REQTYPE/avatar/10566?size=large","24x24":"https://jeragm.atlassian.net/rest/api/2/universal_avatar/view/type/SD_REQTYPE/avatar/10566?size=small","16x16":"https://jeragm.atlassian.net/rest/api/2/universal_avatar/view/type/SD_REQTYPE/avatar/10566?size=xsmall","32x32":"https://jeragm.atlassian.net/rest/api/2/universal_avatar/view/type/SD_REQTYPE/avatar/10566?size=medium"}}}},"currentStatus":{"status":"Waiting for support","statusCategory":"NEW","statusDate":{"iso8601":"2025-05-08T06:43:36+01:00","jira":"2025-05-08T06:43:36.954+01:00","friendly":"08/May/2025 06:43","epochMillis":1746683016954}}},"customfield_10134":null,"customfield_10041":null,"subtasks":[{"id":"107647","key":"TESTIT-49343","self":"https://jeragm.atlassian.net/rest/api/2/issue/107647","fields":{"summary":"Onboarding Request - C9 Trader Access","status":{"self":"https://jeragm.atlassian.net/rest/api/2/status/1","description":"The work item is open and ready for the assignee to start work on it.","iconUrl":"https://jeragm.atlassian.net/images/icons/statuses/open.png","name":"Open","id":"1","statusCategory":{"self":"https://jeragm.atlassian.net/rest/api/2/statuscategory/2","id":2,"key":"new","colorName":"blue-gray","name":"To Do"}},"priority":{"self":"https://jeragm.atlassian.net/rest/api/2/priority/4","iconUrl":"https://jeragm.atlassian.net/images/icons/priorities/low.svg","name":"Low","id":"4"},"issuetype":{"self":"https://jeragm.atlassian.net/rest/api/2/issuetype/10002","id":"10002","description":"A small piece of work that's part of a larger task.","iconUrl":"https://jeragm.atlassian.net/rest/api/2/universal_avatar/view/type/issuetype/avatar/10316?size=medium","name":"Sub-task","subtask":true,"avatarId":10316,"hierarchyLevel":-1}}}],"customfield_10075":null,"customfield_10128":null,"customfield_10928":null,"customfield_11122":null,"customfield_10520":null,"customfield_10729":null,"customfield_10382":null,"customfield_10585":null,"customfield_10858":null,"customfield_10629":{"self":"https://jeragm.atlassian.net/rest/api/2/customFieldOption/11284","value":"TBD","id":"11284"},"RestUrl":"https://jeragm.atlassian.net/rest/api/2/issue/107637","workratio":-1,"customfield_10129":null,"customfield_10619":null,"Project":{"Roles":null,"Key":"TESTIT","Components":null,"IssueTypes":null,"RestUrl":"https://jeragm.atlassian.net/rest/api/2/project/10066","Description":null,"Style":null,"ID":"10066","Name":"IT Support - TEST","Lead":null,"Category":{"self":"https://jeragm.atlassian.net/rest/api/2/projectCategory/10008","id":"10008","description":"IT Operations","name":"IT Operations"}},"customfield_10342":{"self":"https://jeragm.atlassian.net/rest/api/2/customFieldOption/10659","value":"JERAGM Employee","id":"10659"},"customfield_10054":null,"customfield_10004":[],"customfield_10553":null,"customfield_10063":null,"customfield_10728":null,"customfield_10120":{"self":"https://jeragm.atlassian.net/rest/api/2/customFieldOption/10515","value":"IT","id":"10515"},"customfield_10719":null,"customfield_10208":null,"issuetype":{"self":"https://jeragm.atlassian.net/rest/api/2/issuetype/10042","id":"10042","description":"For requests that require approval. Created by Jira Service Desk","iconUrl":"https://jeragm.atlassian.net/rest/api/2/universal_avatar/view/type/issuetype/avatar/10533?size=medium","name":"Service Request with Approvals","subtask":false,"avatarId":10533,"hierarchyLevel":0},"customfield_10011":"1|i02ten:","customfield_10010":null,"customfield_10383":null,"customfield_10347":{"self":"https://jeragm.atlassian.net/rest/api/2/customFieldOption/10669","value":"No","id":"10669"},"customfield_10008":null,"customfield_10275":null,"components":[{"self":"https://jeragm.atlassian.net/rest/api/2/component/10431","id":"10431","name":"Onboarding","description":"Onboarding"}],"customfield_10631":{"self":"https://jeragm.atlassian.net/rest/api/2/customFieldOption/11282","value":"TBD","id":"11282"},"watches":{"self":"https://jeragm.atlassian.net/rest/api/2/issue/TESTIT-49342/watchers","watchCount":2,"isWatching":true},"customfield_10379":null,"customfield_10201":null,"customfield_10252":null,"customfield_10182":null,"customfield_10147":null,"customfield_10024":null,"customfield_10196":null,"customfield_10047":null,"customfield_10174":null,"customfield_10158":null,"customfield_10116":null,"customfield_10057":null,"customfield_10384":null,"Description":"This onboarding request has been created. ","customfield_10720":null,"customfield_10375":null,"customfield_10214":null,"customfield_10040":null,"customfield_10215":null,"fixVersions":[],"attachment":[],"customfield_10105":null,"customfield_10348":{"self":"https://jeragm.atlassian.net/rest/api/2/customFieldOption/10671","value":"No","id":"10671"},"customfield_10319":null,"aggregateprogress":{"progress":0,"total":0},"customfield_10882":null,"customfield_10686":{"self":"https://jeragm.atlassian.net/rest/api/2/customFieldOption/11714","value":"TBD","id":"11714"},"customfield_10251":null,"customfield_10160":null,"customfield_10226":0.0,"customfield_10314":null,"customfield_10318":null,"resolutiondate":null,"customfield_10192":null,"customfield_10373":null,"customfield_10627":{"self":"https://jeragm.atlassian.net/rest/api/2/customFieldOption/11281","value":"TBD","id":"11281"}}; Operation=FullTicketStructure; TicketKey=TESTIT-49342
2025-07-01 13:06:25.636 | JERAGM\akinje   | INFO     | Work type extraction attempt for ticket TESTIT-49342 | AUDIT: IssueTypeExists=False; ExpectedWorkType=Service Request with Approvals; ExtractedWorkType=; TicketKey=TESTIT-49342; Operation=WorkTypeExtraction; IssueTypeStructure=null
2025-07-01 13:06:25.657 | JERAGM\akinje   | WARNING  | WARNING: Could not extract work type from ticket TESTIT-49342
2025-07-01 13:06:25.678 | JERAGM\akinje   | INFO     | Ticket validation completed: True | AUDIT: IsValid=True; WarningsCount=9; Operation=JiraTicketValidationResult; FieldsExtracted=0; TicketKey=TESTIT-49342
2025-07-01 13:06:25.701 | JERAGM\akinje   | INFO     | Jira Ticket Validation Warnings:
2025-07-01 13:06:25.724 | JERAGM\akinje   | INFO     |   [WARNING] Work type could not be extracted from ticket. Issue type field appears to be empty or in unexpected format.
2025-07-01 13:06:25.748 | JERAGM\akinje   | INFO     |   [WARNING] Issue type field structure: null
2025-07-01 13:06:25.769 | JERAGM\akinje   | INFO     |   [WARNING] Custom field 'Department' (customfield_10120) is empty or not found
2025-07-01 13:06:25.796 | JERAGM\akinje   | INFO     |   [WARNING] Custom field 'ITAdminAccount' (customfield_10453) is empty or not found
2025-07-01 13:06:25.817 | JERAGM\akinje   | INFO     |   [WARNING] Custom field 'ModelAccount' (customfield_10343) is empty or not found
2025-07-01 13:06:25.838 | JERAGM\akinje   | INFO     |   [WARNING] Custom field 'JobTitle' (customfield_10238) is empty or not found
2025-07-01 13:06:25.864 | JERAGM\akinje   | INFO     |   [WARNING] Custom field 'LastName' (customfield_10305) is empty or not found
2025-07-01 13:06:25.894 | JERAGM\akinje   | INFO     |   [WARNING] Custom field 'OfficeLocation' (customfield_10115) is empty or not found
2025-07-01 13:06:25.928 | JERAGM\akinje   | INFO     |   [WARNING] Custom field 'FirstName' (customfield_10304) is empty or not found
2025-07-01 13:06:25.979 | JERAGM\akinje   | INFO     | Proceeding with admin account creation despite warnings...
2025-07-01 13:06:26.026 | JERAGM\akinje   | ERROR    | Admin account creation failed: Could not extract user details from Jira ticket
2025-07-01 13:06:26.081 | JERAGM\akinje   | ERROR    | Admin account creation failed: Could not extract user details from Jira ticket | AUDIT: ErrorType=RuntimeException; Success=False; Operation=AdminAccountCreationError
2025-07-01 13:06:26.111 | JERAGM\akinje   | INFO     | Adding comment to Jira ticket | AUDIT: UseADF=True; Operation=JiraCommentAdd; IncludeTimestamp=True; TicketKey=TESTIT-49342
2025-07-01 13:06:27.958 | JERAGM\akinje   | INFO     | Comment added successfully to ticket TESTIT-49342 | AUDIT: CommentId=206564; Operation=JiraCommentSuccess; TicketKey=TESTIT-49342
2025-07-01 13:06:27.980 | JERAGM\akinje   | INFO     | Press any key to continue...
2025-07-01 17:50:59.111 | JERAGM\akinje   | INFO     | ================================================================================
2025-07-01 17:50:59.142 | JERAGM\akinje   | INFO     |                     JML (Joiners, Movers and Leavers) System                      
2025-07-01 17:50:59.162 | JERAGM\akinje   | INFO     |                               Version 1.13                                    
2025-07-01 17:50:59.182 | JERAGM\akinje   | INFO     | ================================================================================
2025-07-01 17:50:59.202 | JERAGM\akinje   | INFO     | System Status:
2025-07-01 17:50:59.221 | JERAGM\akinje   | INFO     |   User: JERAGM\akinje on JGM-CG7THR3
2025-07-01 17:50:59.242 | JERAGM\akinje   | INFO     |   Configuration: Loaded
2025-07-01 17:50:59.260 | JERAGM\akinje   | INFO     |   Credential Method: EncryptedFile
2025-07-01 17:50:59.279 | JERAGM\akinje   | DEBUG    | [DEBUG] Checking credential setup status for method: EncryptedFile
2025-07-01 17:50:59.299 | JERAGM\akinje   | DEBUG    | [DEBUG] Checking EncryptedFile credential storage
2025-07-01 17:50:59.321 | JERAGM\akinje   | DEBUG    | [DEBUG] Checking for encrypted file at: [REDACTED_PATH]s\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Config\SecureCredentials.xml
2025-07-01 17:50:59.365 | JERAGM\akinje   | DEBUG    | [DEBUG] EncryptedFile credentials appear to be configured and accessible
2025-07-01 17:50:59.384 | JERAGM\akinje   | INFO     |   Vault Status: Not Configured
2025-07-01 17:50:59.404 | JERAGM\akinje   | INFO     |   Jira Integration: Enabled
2025-07-01 17:50:59.425 | JERAGM\akinje   | INFO     |   Active Directory: NOT AVAILABLE (UAT Mode)
2025-07-01 17:50:59.449 | JERAGM\akinje   | INFO     | Available Operations:
2025-07-01 17:50:59.471 | JERAGM\akinje   | INFO     |   1. Create JML Account
2025-07-01 17:50:59.498 | JERAGM\akinje   | INFO     |   2. Delete JML Account
2025-07-01 17:50:59.519 | JERAGM\akinje   | INFO     |   3. Reset JML Account Password
2025-07-01 17:50:59.542 | JERAGM\akinje   | INFO     |   4. System Information
2025-07-01 17:50:59.562 | JERAGM\akinje   | INFO     |   5. Run Setup / Repair Credentials
2025-07-01 17:50:59.585 | JERAGM\akinje   | INFO     |   7. Exit
2025-07-01 17:50:59.610 | JERAGM\akinje   | INFO     | Enter your choice: 
