#Requires -Version 5.1

<#
.SYNOPSIS
Enhanced Jira Ticket Viewer with Enterprise Features

.DESCRIPTION
A professional WPF-based Jira ticket viewer. This version includes automatic dependency management and updates,
a responsive UI with loading indicators, and robust data validation with clear user feedback.
Compatible with PowerShell 5.1+ with dynamic Unicode support.

.NOTES
Version: 3.7
Author: Enhanced for Enterprise Use
Security Level: Production Ready
PowerShell Compatibility: 5.1+ (Unicode emojis in 7+, ASCII fallback in 5.x)
#>

# PowerShell version and compatibility check
if ($PSVersionTable.PSVersion.Major -lt 5) {
    Write-Host "ERROR: This script requires PowerShell 5.1 or higher. Current version: $($PSVersionTable.PSVersion)" -ForegroundColor Red
    Write-Host "Please upgrade PowerShell or use PowerShell Core 6+" -ForegroundColor Yellow
    exit 1
}

# Dynamic icon selection based on PowerShell version - using character codes to avoid parsing issues
$script:UseUnicode = $PSVersionTable.PSVersion.Major -ge 7

function Get-Icons {
    if ($script:UseUnicode) {
        # Unicode emojis for PowerShell 7+
        return @{
            Connection = [char]0x1F510  # Lock emoji
            Login = [char]0x1F511       # Key emoji
            Logout = [char]0x1F6AA      # Door emoji
            Ticket = [char]0x1F3AB      # Ticket emoji
            Load = [char]0x1F4CB        # Clipboard emoji
            Refresh = [char]0x1F504     # Refresh emoji
            Actions = [char]0x26A1      # Lightning emoji
            Comment = [char]0x1F4AC     # Speech bubble emoji
            Export = [char]0x1F4BE      # Floppy disk emoji
            Copy = [char]0x1F4CB        # Clipboard emoji
            Browser = [char]0x1F310     # Globe emoji
            Details = [char]0x1F4C4     # Page emoji
            Description = [char]0x1F4C4 # Page emoji
            Comments = [char]0x1F4AC    # Speech bubble emoji
            User = [char]0x1F464        # User emoji
            Date = [char]0x1F4C5        # Calendar emoji
            Success = [char]0x1F389     # Party emoji
            Connected = [char]0x2705    # Check mark emoji
            Cache = [char]0x1F4E6       # Package emoji
            Network = [char]0x1F310     # Globe emoji
            TicketIcon = [char]0x1F3AB  # Ticket emoji
        }
    } else {
        # ASCII alternatives for PowerShell 5.x
        return @{
            Connection = "[*]"
            Login = "[+]"
            Logout = "[-]"
            Ticket = "[T]"
            Load = "[L]"
            Refresh = "[R]"
            Actions = "[A]"
            Comment = "[C]"
            Export = "[E]"
            Copy = "[X]"
            Browser = "[B]"
            Details = "[D]"
            Description = "[D]"
            Comments = "[C]"
            User = "[U]"
            Date = "[T]"
            Success = "[OK]"
            Connected = "[OK]"
            Cache = "[CACHE]"
            Network = "[NET]"
            TicketIcon = "[T]"
        }
    }
}

$script:Icons = Get-Icons

Write-Host "PowerShell Version: $($PSVersionTable.PSVersion) - Compatible" -ForegroundColor Green
Write-Host "Unicode Support: $(if ($script:UseUnicode) { 'Enabled (Emojis)' } else { 'Disabled (ASCII)' })" -ForegroundColor Cyan

# Load required assemblies for WPF with PowerShell 5.x compatibility
try {
    Add-Type -AssemblyName PresentationCore
    Add-Type -AssemblyName PresentationFramework  
    Add-Type -AssemblyName System.Windows.Forms
    Add-Type -AssemblyName WindowsBase
    Write-Host "Successfully loaded WPF assemblies" -ForegroundColor Green
} catch {
    Write-Host "Failed to load WPF assemblies: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Please ensure .NET Framework 4.5+ is installed" -ForegroundColor Yellow
    exit 1
}

#region Setup and Configuration

# Function to ensure NuGet provider is installed
function Install-NuGetProviderSilently {
    try {
        Write-Host "Checking NuGet provider..." -ForegroundColor Cyan
        $nugetProvider = Get-PackageProvider -Name NuGet -ErrorAction SilentlyContinue
        
        if (-not $nugetProvider -or $nugetProvider.Version -lt [Version]"*********") {
            Write-Host "Installing NuGet provider silently..." -ForegroundColor Yellow
            
            # Set TLS 1.2 for secure downloads
            [Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12
            
            # Install NuGet provider without prompts
            Install-PackageProvider -Name NuGet -MinimumVersion ********* -Force -Scope CurrentUser -Confirm:$false | Out-Null
            Write-Host "NuGet provider installed successfully" -ForegroundColor Green
        } else {
            Write-Host "NuGet provider already available" -ForegroundColor Green
        }
    } catch {
        Write-Host "Warning: Could not install NuGet provider automatically: $($_.Exception.Message)" -ForegroundColor Yellow
        Write-Host "Continuing with module installation..." -ForegroundColor Yellow
    }
}

# Function to set PowerShell Gallery as trusted repository
function Set-PSGalleryTrusted {
    try {
        $psGallery = Get-PSRepository -Name PSGallery -ErrorAction SilentlyContinue
        if ($psGallery -and $psGallery.InstallationPolicy -ne 'Trusted') {
            Write-Host "Setting PowerShell Gallery as trusted repository..." -ForegroundColor Cyan
            Set-PSRepository -Name PSGallery -InstallationPolicy Trusted -Confirm:$false
            Write-Host "PowerShell Gallery set as trusted" -ForegroundColor Green
        }
    } catch {
        Write-Host "Warning: Could not set PSGallery as trusted: $($_.Exception.Message)" -ForegroundColor Yellow
    }
}

# Ensure prerequisites are in place
Install-NuGetProviderSilently
Set-PSGalleryTrusted

# Import required modules with error handling and automatic updates.
$requiredModules = @('JiraPS')
foreach ($module in $requiredModules) {
    try {
        Write-Host "Checking module: $module" -ForegroundColor Cyan
        
        if (-not (Get-Module -ListAvailable -Name $module)) {
            Write-Host "Installing missing module: $module. This may take a moment..." -ForegroundColor Yellow
            
            # Set TLS 1.2 for secure downloads
            [Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12
            
            try {
                # Install module with all prompts suppressed
                Install-Module -Name $module -Scope CurrentUser -Force -AllowClobber -Confirm:$false -SkipPublisherCheck -AcceptLicense -ErrorAction Stop
                Write-Host "Module $module installed successfully" -ForegroundColor Green
            } catch {
                Write-Host "First attempt failed. Trying alternative installation method..." -ForegroundColor Yellow
                
                # Alternative method: Install without publisher check and license acceptance
                try {
                    Install-Module -Name $module -Scope CurrentUser -Force -AllowClobber -Confirm:$false -ErrorAction Stop
                    Write-Host "Module $module installed successfully (alternative method)" -ForegroundColor Green
                } catch {
                    # Final fallback: Try with minimal parameters
                    Install-Module -Name $module -Force -ErrorAction Stop
                    Write-Host "Module $module installed successfully (minimal method)" -ForegroundColor Green
                }
            }
        } else {
            Write-Host "Module $module found. Checking for updates..." -ForegroundColor Yellow
            try {
                Update-Module -Name $module -Force -Confirm:$false -AcceptLicense -ErrorAction SilentlyContinue
                Write-Host "Module $module updated successfully" -ForegroundColor Green
            } catch {
                Write-Host "Update check failed, continuing with existing version" -ForegroundColor Yellow
            }
        }
        
        Import-Module $module -Force -ErrorAction Stop
        Write-Host "Successfully loaded module: $module" -ForegroundColor Green
    }
    catch {
        $errorMsg = "Failed to load or update required module '$module': $($_.Exception.Message). Please ensure you have an internet connection."
        Write-Host $errorMsg -ForegroundColor Red
        
        # Try one more time with basic installation
        try {
            Write-Host "Attempting final fallback installation..." -ForegroundColor Yellow
            Install-Module -Name $module -Confirm:$false -Force
            Import-Module $module -Force
            Write-Host "Successfully loaded module: $module (fallback method)" -ForegroundColor Green
        } catch {
            Write-Host "All installation methods failed for module: $module" -ForegroundColor Red
            try {
                [System.Windows.MessageBox]::Show($errorMsg, "Module Error", "OK", "Error")
            } catch {
                # MessageBox might not be available yet
                Write-Host "Press any key to exit..." -ForegroundColor Red
                $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
            }
            exit 1
        }
    }
}

# Enhanced configuration and logging
$script:Config = @{
    MaxCacheSize = 50
    CacheExpiryMinutes = 30
    MaxComments = 10
    LogLevel = 'DEBUG' # Set to 'DEBUG' for verbose field logging in the console
    SecuritySettings = @{
        MaxLoginAttempts = 3
        SessionTimeoutMinutes = 60
        RequireHttps = $true
    }
}

# Initialize cache with PowerShell 5.x compatibility
try {
    $script:Cache = New-Object 'System.Collections.Concurrent.ConcurrentDictionary[string, object]'
    Write-Host "Using ConcurrentDictionary for cache" -ForegroundColor Green
} catch {
    Write-Host "ConcurrentDictionary not available, using Hashtable" -ForegroundColor Yellow
    $script:Cache = @{}
}
$script:LoginAttempts = 0
$script:SessionStartTime = $null
$script:CurrentCredential = $null

#endregion

#region Core Functions

# Helper for PowerShell 5.1 compatibility
function Get-ValueOrDefault {
    param($Value, $Default)
    if ($null -ne $Value -and $Value -isnot [System.DBNull]) { return $Value }
    return $Default
}

# Enhanced logging function
function Write-AppLog {
    param([string]$Message, [string]$Level = 'INFO')
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss.fff"
    $logEntry = "[$timestamp] [$Level] $Message"
    
    switch ($Level) {
        'ERROR' { Write-Host $logEntry -ForegroundColor Red }
        'WARN'  { Write-Host $logEntry -ForegroundColor Yellow }
        'INFO'  { Write-Host $logEntry -ForegroundColor Green }
        'DEBUG' { if ($script:Config.LogLevel -eq 'DEBUG') { Write-Host $logEntry -ForegroundColor Gray } }
    }
}

# Enhanced input validation
function Test-JiraUrl {
    param([string]$Url)
    if ([string]::IsNullOrWhiteSpace($Url)) { return @{ IsValid = $false; Error = "URL cannot be empty" } }
    if ($script:Config.SecuritySettings.RequireHttps -and -not $Url.StartsWith('https://')) { return @{ IsValid = $false; Error = "HTTPS is required for security" } }
    try { 
        if ($PSVersionTable.PSVersion.Major -ge 6) {
            [System.Uri]::new($Url) | Out-Null
        } else {
            New-Object System.Uri($Url) | Out-Null
        }
        return @{ IsValid = $true } 
    } catch { 
        return @{ IsValid = $false; Error = "Invalid URL format" } 
    }
}

function Test-TicketKey {
    param([string]$TicketKey)
    if ([string]::IsNullOrWhiteSpace($TicketKey)) { return @{ IsValid = $false; Error = "Ticket key cannot be empty" } }
    if ($TicketKey -notmatch '^[A-Z][A-Z0-9]+-\d+$') { return @{ IsValid = $false; Error = "Invalid ticket key format (e.g., PROJECT-123)" } }
    return @{ IsValid = $true }
}

# Caching and Session functions
function Get-CachedTicket {
    param([string]$TicketKey)
    if ($script:Cache.ContainsKey($TicketKey)) {
        $cacheEntry = $script:Cache[$TicketKey]
        if (((Get-Date) - $cacheEntry.Timestamp).TotalMinutes -lt $script:Config.CacheExpiryMinutes) {
            Write-AppLog "Cache hit for ticket: $TicketKey" -Level 'DEBUG'
            return $cacheEntry.Data
        }
        Write-AppLog "Cache expired for ticket: $TicketKey" -Level 'DEBUG'
        # Handle both ConcurrentDictionary and Hashtable
        if ($script:Cache -is [System.Collections.Concurrent.ConcurrentDictionary[string, object]]) {
            $script:Cache.TryRemove($TicketKey, [ref]$null) | Out-Null
        } else {
            $script:Cache.Remove($TicketKey)
        }
    }
    return $null
}

function Set-CachedTicket {
    param([string]$TicketKey, [object]$TicketData)
    if ($script:Cache.Count -ge $script:Config.MaxCacheSize) {
        $oldestKey = ($script:Cache.GetEnumerator() | Sort-Object { $_.Value.Timestamp } | Select-Object -First 1).Key
        # Handle both ConcurrentDictionary and Hashtable
        if ($script:Cache -is [System.Collections.Concurrent.ConcurrentDictionary[string, object]]) {
            $script:Cache.TryRemove($oldestKey, [ref]$null) | Out-Null
        } else {
            $script:Cache.Remove($oldestKey)
        }
        Write-AppLog "Cache evicted oldest entry: $oldestKey" -Level 'DEBUG'
    }
    $script:Cache[$TicketKey] = @{ Data = $TicketData; Timestamp = Get-Date }
    Write-AppLog "Cached ticket: $TicketKey" -Level 'DEBUG'
}

function Test-SessionValid {
    return ($null -ne $script:SessionStartTime) -and (((Get-Date) - $script:SessionStartTime).TotalMinutes -lt $script:Config.SecuritySettings.SessionTimeoutMinutes)
}

function Reset-Session {
    $script:CurrentCredential = $null
    $script:SessionStartTime = $null
    $script:LoginAttempts = 0
    Write-AppLog "Session reset" -Level 'INFO'
}

#endregion

#region UI (XAML Definition and Control Handlers)

# Generate XAML with appropriate icons
function Get-XamlContent {
    return @"
<Window xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="Jira Ticket Viewer - Enterprise Edition v3.7" Height="700" Width="1200" WindowStartupLocation="CenterScreen"
        MinHeight="600" MinWidth="900" x:Name="MainWindow" Background="#FFF0F0F0">
    <Window.Resources>
        <Style TargetType="Button">
            <Setter Property="Padding" Value="8,4"/><Setter Property="Margin" Value="0,5"/><Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/><Setter Property="FontWeight" Value="SemiBold"/><Setter Property="Cursor" Value="Hand"/>
        </Style>
    </Window.Resources>
    <Grid>
        <Grid.RowDefinitions><RowDefinition Height="Auto"/><RowDefinition Height="*"/><RowDefinition Height="Auto"/></Grid.RowDefinitions>
        <Border Grid.Row="0" Padding="10,5" Background="#FF2D2D30">
            <Grid>
                <StackPanel Orientation="Horizontal">
                    <TextBlock Name="StatusText" Text="Ready" VerticalAlignment="Center" Foreground="White"/>
                    <TextBlock Name="SessionInfo" Text="" Margin="20,0,0,0" VerticalAlignment="Center" Foreground="#FFAAAAAA"/>
                    <Button Name="ClearCacheButton" Content="Clear Cache" Margin="20,0,0,0" Padding="6,2" FontSize="10" IsEnabled="False" Background="#FF555555"/>
                </StackPanel>
            </Grid>
        </Border>
        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions><ColumnDefinition Width="320" MinWidth="280"/><ColumnDefinition Width="5"/><ColumnDefinition Width="*"/></Grid.ColumnDefinitions>
            <ScrollViewer Grid.Column="0" VerticalScrollBarVisibility="Auto" Background="#FFF0F0F0">
                <StackPanel Margin="15">
                    <Border Background="White" Padding="10" CornerRadius="5" Margin="0,0,0,10">
                        <StackPanel>
                            <TextBlock FontWeight="Bold" Text="$($script:Icons.Connection) Connection" Margin="0,0,0,10" FontSize="14"/>
                            <TextBlock Text="Jira URL:" FontWeight="SemiBold"/><TextBox Name="ServerBox" Text="https://jeragm.atlassian.net"/>
                            <TextBlock Text="Username:" Margin="0,8,0,0" FontWeight="SemiBold"/><TextBox Name="UserBox"/>
                            <TextBlock Text="API Token:" Margin="0,8,0,0" FontWeight="SemiBold"/><PasswordBox Name="TokenBox"/>
                            <Button Name="AuthButton" Content="$($script:Icons.Login) Login" Height="32" Margin="0,10,0,0" Background="#FF0078D4"/>
                            <Button Name="LogoutButton" Content="$($script:Icons.Logout) Logout" Height="28" IsEnabled="False" Background="#FFDC3545"/>
                        </StackPanel>
                    </Border>
                    <Border Background="White" Padding="10" CornerRadius="5" Margin="0,0,0,10">
                        <StackPanel>
                            <TextBlock FontWeight="Bold" Text="$($script:Icons.Ticket) Ticket Lookup" Margin="0,0,0,10" FontSize="14"/>
                            <TextBlock Text="Ticket Key:" FontWeight="SemiBold"/><TextBox Name="TicketBox" IsEnabled="False"/>
                            <StackPanel Orientation="Horizontal" Margin="0,10,0,0">
                                <Button Name="LoadButton" Content="$($script:Icons.Load) Load Ticket" IsEnabled="False" Width="120" Background="#FF0078D4"/>
                                <Button Name="RefreshButton" Content="$($script:Icons.Refresh)" Width="30" Margin="5,0,0,0" IsEnabled="False" ToolTip="Refresh (bypass cache)" Background="#FF6C757D"/>
                            </StackPanel>
                        </StackPanel>
                    </Border>
                    <Border Background="White" Padding="10" CornerRadius="5">
                          <StackPanel>
                            <TextBlock FontWeight="Bold" Text="$($script:Icons.Actions) Quick Actions" Margin="0,0,0,10" FontSize="14"/>
                            <Button Name="AddCommentButton" Content="$($script:Icons.Comment) Add Comment" IsEnabled="False" Background="#FF0078D4"/>
                            <Button Name="ExportButton" Content="$($script:Icons.Export) Export to File" IsEnabled="False" Background="#FF28A745"/>
                            <Button Name="CopyButton" Content="$($script:Icons.Copy) Copy to Clipboard" IsEnabled="False" Background="#FF6C757D"/>
                            <Button Name="OpenBrowserButton" Content="$($script:Icons.Browser) Open in Browser" IsEnabled="False" Background="#FF17A2B8"/>
                        </StackPanel>
                    </Border>
                </StackPanel>
            </ScrollViewer>
            <GridSplitter Grid.Column="1" Width="5" HorizontalAlignment="Stretch" Background="#FFCCCCCC"/>
            <Grid Grid.Column="2" Margin="15">
                <Grid.RowDefinitions><RowDefinition Height="Auto"/><RowDefinition Height="*"/><RowDefinition Height="5"/><RowDefinition Height="200"/></Grid.RowDefinitions>
                <Border Grid.Row="0" Padding="10" CornerRadius="5" Margin="0,0,0,10" Background="White">
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Name="ContentTitle" Text="$($script:Icons.Details) Ticket Details" FontWeight="Bold" FontSize="16" VerticalAlignment="Center"/>
                        <TextBlock Name="CacheIndicator" Text="" Margin="10,0,0,0" VerticalAlignment="Center" Foreground="#FF6C757D"/>
                    </StackPanel>
                </Border>
                <DataGrid Name="TicketGrid" Grid.Row="1" AutoGenerateColumns="False" IsReadOnly="True" HorizontalScrollBarVisibility="Auto">
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="Field" Binding="{Binding Field}" Width="200" FontWeight="SemiBold"/>
                        <DataGridTextColumn Header="Value" Binding="{Binding Value}" Width="*"/>
                    </DataGrid.Columns>
                </DataGrid>
                <GridSplitter Grid.Row="2" Height="5" HorizontalAlignment="Stretch" VerticalAlignment="Center" Background="#FFCCCCCC"/>
                <TextBox Name="DetailsBox" Grid.Row="3" IsReadOnly="True" TextWrapping="Wrap" VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Auto" FontFamily="Consolas" FontSize="12" Padding="10"/>
            </Grid>
        </Grid>
        <Border Grid.Row="2" Padding="10,5" Background="White" BorderBrush="#FFCCCCCC" BorderThickness="0,1,0,0">
            <Grid>
                <StackPanel Orientation="Horizontal">
                    <TextBlock Name="CacheStats" VerticalAlignment="Center" Foreground="#FF6C757D"/>
                    <TextBlock Name="PerformanceStats" Margin="20,0,0,0" VerticalAlignment="Center" Foreground="#FF6C757D"/>
                </StackPanel>
                <TextBlock Text="Jira Ticket Viewer v3.7 - Enterprise Edition" FontSize="10" VerticalAlignment="Center" HorizontalAlignment="Right" Foreground="#FF6C757D"/>
            </Grid>
        </Border>
    </Grid>
</Window>
"@
}

$xaml = Get-XamlContent

try {
    # PowerShell 5.x compatible XAML parsing
    $stringReader = New-Object System.IO.StringReader($xaml)
    $xmlReader = [System.Xml.XmlReader]::Create($stringReader)
    $window = [Windows.Markup.XamlReader]::Load($xmlReader)
    $xmlReader.Close()
    $stringReader.Close()
    Write-AppLog "XAML parsed successfully" -Level 'DEBUG'
} catch {
    $errorMsg = "Failed to load UI: $($_.Exception.Message)"
    Write-AppLog $errorMsg -Level 'ERROR'
    try {
        [System.Windows.MessageBox]::Show($errorMsg, "Critical Error", "OK", "Error")
    } catch {
        Write-Host $errorMsg -ForegroundColor Red
    }
    exit 1
}

# Initialize controls dictionary with proper error handling
$controls = @{}
$controlNames = @('ServerBox','UserBox','TokenBox','AuthButton','LogoutButton','TicketBox','LoadButton','RefreshButton','StatusText','SessionInfo','ContentTitle','CacheIndicator','CacheStats','PerformanceStats','ClearCacheButton','ExportButton','CopyButton','OpenBrowserButton','AddCommentButton','TicketGrid','DetailsBox')

# Find and validate all controls
$missingControls = @()
foreach ($controlName in $controlNames) {
    try {
        $control = $window.FindName($controlName)
        if ($null -ne $control) { 
            $controls[$controlName] = $control 
            Write-AppLog "Found control: $controlName" -Level 'DEBUG'
        } else { 
            $missingControls += $controlName
            Write-AppLog "Control not found: $controlName" -Level 'ERROR'
        }
    } catch {
        $missingControls += $controlName
        Write-AppLog "Error finding control $controlName`: $($_.Exception.Message)" -Level 'ERROR'
    }
}

# Report missing controls and exit if any are critical
if ($missingControls.Count -gt 0) {
    $errorMessage = "The following UI elements could not be found: $($missingControls -join ', '). This indicates a problem with the XAML definition."
    Write-AppLog $errorMessage -Level 'ERROR'
    [System.Windows.MessageBox]::Show($errorMessage, "Fatal UI Error", "OK", "Error")
    exit 1
}

Write-AppLog "All UI controls initialized successfully" -Level 'INFO'

#endregion

#region UI Functions

function Update-Status {
    param([string]$Message, [string]$Level = 'INFO')
    try {
        if ($controls.StatusText) {
            $controls.StatusText.Text = "[$(Get-Date -Format "HH:mm:ss")] $Message"
            $color = switch ($Level) {
                'ERROR'   { '#FFFF8A8A' }
                'WARN'    { '#FFFFE08A' }
                'SUCCESS' { '#FFB9FFB9' }
                default   { 'White' }
            }
            $controls.StatusText.Foreground = $color
        }
        Write-AppLog $Message -Level $Level
    } catch {
        Write-AppLog "Error updating status: $($_.Exception.Message)" -Level 'ERROR'
    }
}

function Update-SessionInfo {
    try {
        if ($controls.SessionInfo) {
            if ($script:SessionStartTime) {
                $remainingMinutes = $script:Config.SecuritySettings.SessionTimeoutMinutes - ((Get-Date) - $script:SessionStartTime).TotalMinutes
                $controls.SessionInfo.Text = "Session: $([math]::Round($remainingMinutes, 0))m remaining"
            } else {
                $controls.SessionInfo.Text = "Not logged in"
            }
        }
    } catch {
        Write-AppLog "Error updating session info: $($_.Exception.Message)" -Level 'ERROR'
    }
}

function Update-CacheStats {
    try {
        if ($controls.CacheStats) {
            $controls.CacheStats.Text = "Cache: $($script:Cache.Count) items"
        }
    } catch {
        Write-AppLog "Error updating cache stats: $($_.Exception.Message)" -Level 'ERROR'
    }
}

function Set-UIAfterLogout {
    try {
        if ($controls.TicketBox) { $controls.TicketBox.IsEnabled = $false }
        if ($controls.LogoutButton) { $controls.LogoutButton.IsEnabled = $false }
        if ($controls.ClearCacheButton) { $controls.ClearCacheButton.IsEnabled = $false }
        if ($controls.AuthButton) { 
            $controls.AuthButton.Content = "$($script:Icons.Login) Login"
            $controls.AuthButton.IsEnabled = $true 
        }
        Set-LoadingState $false # This also re-enables relevant buttons after logout
        if ($controls.TicketGrid) { $controls.TicketGrid.ItemsSource = $null }
        if ($controls.DetailsBox) { $controls.DetailsBox.Text = "Logged out. Please login again to continue." }
        if ($controls.ContentTitle) { $controls.ContentTitle.Text = "$($script:Icons.Details) Ticket Details" }
        if ($controls.CacheIndicator) { $controls.CacheIndicator.Text = "" }
        Update-SessionInfo
        Update-CacheStats
    } catch {
        Write-AppLog "Error setting UI after logout: $($_.Exception.Message)" -Level 'ERROR'
    }
}

function Set-LoadingState {
    param([bool]$IsLoading)
    try {
        $areControlsEnabled = -not $IsLoading
        # Only disable action buttons, not login/logout
        @($controls.LoadButton, $controls.RefreshButton, $controls.AddCommentButton, $controls.ExportButton, $controls.CopyButton, $controls.OpenBrowserButton) | ForEach-Object {
            if ($_) { $_.IsEnabled = $areControlsEnabled }
        }
    } catch {
        Write-AppLog "Error setting loading state: $($_.Exception.Message)" -Level 'ERROR'
    }
}

#endregion

#region Event Handlers

# Add event handlers with null checks
if ($controls.AuthButton) {
    $controls.AuthButton.Add_Click({
    try {
        if ($script:LoginAttempts -ge $script:Config.SecuritySettings.MaxLoginAttempts) { throw "Maximum login attempts exceeded. Please restart." }
        $urlValidation = Test-JiraUrl -Url $controls.ServerBox.Text; if (-not $urlValidation.IsValid) { throw $urlValidation.Error }
        if ([string]::IsNullOrWhiteSpace($controls.UserBox.Text)) { throw "Please enter a username." }
        if ([string]::IsNullOrWhiteSpace($controls.TokenBox.Password)) { throw "Please enter an API token." }
        
        Update-Status "Connecting..." -Level 'INFO'; $controls.AuthButton.IsEnabled = $false
        
        Set-JiraConfigServer -Server $controls.ServerBox.Text
        $secureToken = ConvertTo-SecureString $controls.TokenBox.Password -AsPlainText -Force
        $credential = New-Object System.Management.Automation.PSCredential($controls.UserBox.Text, $secureToken)
        
        Get-JiraServerInfo -Credential $credential | Out-Null
        
        $script:CurrentCredential = $credential; $script:SessionStartTime = Get-Date; $script:LoginAttempts = 0
        
        $controls.TicketBox.IsEnabled = $true; $controls.LoadButton.IsEnabled = $true; $controls.RefreshButton.IsEnabled = $true
        $controls.LogoutButton.IsEnabled = $true; $controls.ClearCacheButton.IsEnabled = $true
        $controls.AuthButton.Content = "$($script:Icons.Connected) Connected"
        Update-Status "Successfully connected to Jira" -Level 'SUCCESS'
        $controls.DetailsBox.Text = "$($script:Icons.Success) Successfully connected! You can now load a ticket."
    } catch {
        $script:LoginAttempts++
        $controls.TokenBox.Clear()
        Update-Status "Login failed: $($_.Exception.Message)" -Level 'ERROR'
        [System.Windows.MessageBox]::Show($window, $_.Exception.Message, "Authentication Error", "OK", "Error")
    } finally {
        if ($controls.AuthButton.Content -ne "$($script:Icons.Connected) Connected") { $controls.AuthButton.IsEnabled = $true }
    }
    })
}

if ($controls.LogoutButton) {
    $controls.LogoutButton.Add_Click({ Reset-Session; Set-UIAfterLogout; Update-Status "Logged out" -Level 'INFO' })
}

$loadTicketHandler = {
    param([bool]$BypassCache = $false)
    try {
        if (-not (Test-SessionValid)) { throw "Session expired. Please login again." }
        $ticketValidation = Test-TicketKey -TicketKey $controls.TicketBox.Text; if (-not $ticketValidation.IsValid) { throw $ticketValidation.Error }
        
        Set-LoadingState $true
        $ticketKey = $controls.TicketBox.Text.ToUpper()
        Update-Status "Loading ticket: $ticketKey..." -Level 'INFO'
        
        $ticket = if (-not $BypassCache) { Get-CachedTicket -TicketKey $ticketKey }
        if ($ticket) { $controls.CacheIndicator.Text = "$($script:Icons.Cache) (from cache)" }
        
        if (-not $ticket) {
            $startTime = Get-Date
            # Request standard Jira fields only
            $ticket = Get-JiraIssue -Key $ticketKey -Credential $script:CurrentCredential -ErrorAction Stop
            $loadTime = ((Get-Date) - $startTime).TotalMilliseconds
            Write-AppLog "Ticket loaded successfully: $ticketKey" -Level 'DEBUG'
            Set-CachedTicket -TicketKey $ticketKey -TicketData $ticket
            $controls.CacheIndicator.Text = "$($script:Icons.Network) (loaded in $([math]::Round($loadTime, 0))ms)"; $controls.PerformanceStats.Text = "Last load: $([math]::Round($loadTime, 0))ms"
        }
        
        $fields = if ($null -ne $ticket.Fields) { $ticket.Fields } else { $ticket }
        
        # --- BASIC VALIDATION ---
        # Validate that we have the basic ticket data
        if ([string]::IsNullOrEmpty($ticket.Key)) {
            throw "Ticket Data Error: Unable to retrieve ticket key for $ticketKey."
        }
        
        Write-AppLog "Ticket validation passed for $ticketKey" -Level 'INFO'

        # --- Data Population ---
        $ticketData = @()
        $ticketData += [PSCustomObject]@{ Field = "Key"; Value = $ticket.Key }
        $ticketData += [PSCustomObject]@{ Field = "Summary"; Value = (Get-ValueOrDefault $fields.summary 'No Title') }
        $ticketData += [PSCustomObject]@{ Field = "Type"; Value = (Get-ValueOrDefault $fields.issuetype.name 'Unknown') }
        $ticketData += [PSCustomObject]@{ Field = "Status"; Value = (Get-ValueOrDefault $fields.status.name 'Unknown') }
        $ticketData += [PSCustomObject]@{ Field = "Priority"; Value = (Get-ValueOrDefault $fields.priority.name 'Unknown') }
        $ticketData += [PSCustomObject]@{ Field = "Reporter"; Value = (Get-ValueOrDefault $fields.reporter.displayName 'Unknown') }
        $ticketData += [PSCustomObject]@{ Field = "Assignee"; Value = (Get-ValueOrDefault $fields.assignee.displayName 'Unassigned') }
        $ticketData += [PSCustomObject]@{ Field = "Created"; Value = (Get-ValueOrDefault $fields.created 'Unknown') }
        $ticketData += [PSCustomObject]@{ Field = "Updated"; Value = (Get-ValueOrDefault $fields.updated 'Unknown') }
        $ticketData += [PSCustomObject]@{ Field = "Resolution"; Value = (Get-ValueOrDefault $fields.resolution.name 'Unresolved') }
        
        # Add project information if available
        if ($fields.project) {
            $ticketData += [PSCustomObject]@{ Field = "Project"; Value = "$($fields.project.name) ($($fields.project.key))" }
        }
        
        # Add components if available
        if ($fields.components -and $fields.components.Count -gt 0) {
            $componentNames = $fields.components | ForEach-Object { $_.name }
            $ticketData += [PSCustomObject]@{ Field = "Components"; Value = ($componentNames -join ', ') }
        }
        
        # Add labels if available
        if ($fields.labels -and $fields.labels.Count -gt 0) {
            $ticketData += [PSCustomObject]@{ Field = "Labels"; Value = ($fields.labels -join ', ') }
        }
        $controls.TicketGrid.ItemsSource = $ticketData
        
        $detailsOutput = @("$($script:Icons.Description) DESCRIPTION", "----------------", (Get-ValueOrDefault $fields.description "No Description Provided."), "", "$($script:Icons.Comments) RECENT COMMENTS (Last $($script:Config.MaxComments))", "----------------------------------")
        $comments = Get-JiraIssueComment -Issue $ticket -Credential $script:CurrentCredential | Sort-Object Created -Descending | Select-Object -First $script:Config.MaxComments
        if ($comments) {
            foreach ($comment in $comments) { $detailsOutput += "$($script:Icons.User) $(Get-ValueOrDefault $comment.Author.displayName 'Unknown') $($script:Icons.Date) $("{0:G}" -f $comment.created)", (Get-ValueOrDefault $comment.body "No content"), "" }
        } else { $detailsOutput += "No comments found." }
        $controls.DetailsBox.Text = $detailsOutput -join "`r`n"
        
        $controls.ContentTitle.Text = "$($script:Icons.TicketIcon) $ticketKey - $(Get-ValueOrDefault $fields.summary '')"
        Update-Status "Ticket loaded and validated successfully" -Level 'SUCCESS'; Update-CacheStats
    } catch {
        Update-Status "Error: $($_.Exception.Message)" -Level 'ERROR'
        [System.Windows.MessageBox]::Show($window, $_.Exception.Message, "Load Error", "OK", "Error")
    } finally { Set-LoadingState $false }
}

if ($controls.LoadButton) {
    $controls.LoadButton.Add_Click({ & $loadTicketHandler $false })
}
if ($controls.RefreshButton) {
    $controls.RefreshButton.Add_Click({ & $loadTicketHandler $true })
}

if ($controls.AddCommentButton) {
    $controls.AddCommentButton.Add_Click({
    $commentWindow = New-Object System.Windows.Window; $commentWindow.Title = "Add Comment to $($controls.TicketBox.Text)"; $commentWindow.Width = 450; $commentWindow.Height = 250; $commentWindow.WindowStartupLocation = "CenterOwner"; $commentWindow.Owner = $window
    $grid = New-Object System.Windows.Controls.Grid; $grid.Margin = '10'; $grid.RowDefinitions.Add((New-Object System.Windows.Controls.RowDefinition -Property @{ Height = 'Auto' })); $grid.RowDefinitions.Add((New-Object System.Windows.Controls.RowDefinition -Property @{ Height = '*' })); $grid.RowDefinitions.Add((New-Object System.Windows.Controls.RowDefinition -Property @{ Height = 'Auto' }))
    $commentTextBox = New-Object System.Windows.Controls.TextBox; $commentTextBox.AcceptsReturn = $true; $commentTextBox.TextWrapping = "Wrap"; [System.Windows.Controls.Grid]::SetRow($commentTextBox, 1); $grid.Children.Add($commentTextBox)
    $submitButton = New-Object System.Windows.Controls.Button; $submitButton.Content = "Submit Comment"; $submitButton.Margin = '0,10,0,0'; $submitButton.Background = '#FF0078D4'; [System.Windows.Controls.Grid]::SetRow($submitButton, 2)
    $submitButton.Add_Click({
        try {
            $submitButton.IsEnabled = $false; Update-Status "Adding comment..." -Level 'INFO'
            if (-not [string]::IsNullOrWhiteSpace($commentTextBox.Text)) {
                Add-JiraIssueComment -Issue $controls.TicketBox.Text -Comment $commentTextBox.Text -Credential $script:CurrentCredential
                $commentWindow.Close(); & $loadTicketHandler $true
            } else { Update-Status "Comment cannot be empty" -Level 'WARN' }
        } catch { Update-Status "Failed to add comment: $($_.Exception.Message)" -Level 'ERROR'; [System.Windows.MessageBox]::Show($window, $_.Exception.Message, "Comment Error", "OK", "Error") } finally { $submitButton.IsEnabled = $true }
    }); $grid.Children.Add($submitButton)
    $commentWindow.Content = $grid; $commentWindow.ShowDialog() | Out-Null
    })
}

if ($controls.ClearCacheButton) {
    $controls.ClearCacheButton.Add_Click({ $script:Cache.Clear(); Update-CacheStats; Update-Status "Cache cleared" -Level 'INFO' })
}
if ($controls.ExportButton) {
    $controls.ExportButton.Add_Click({ try { Set-LoadingState $true; $saveDialog = New-Object System.Windows.Forms.SaveFileDialog; $saveDialog.Filter = "Text files (*.txt)|*.txt"; $saveDialog.FileName = "jira-ticket-$($controls.TicketBox.Text).txt"; if ($saveDialog.ShowDialog() -eq [System.Windows.Forms.DialogResult]::OK) { $exportContent = "--- TICKET DETAILS --`r`n" + ($controls.TicketGrid.ItemsSource | Format-Table | Out-String) + "`r`n--- DESCRIPTION & COMMENTS ---`r`n" + $controls.DetailsBox.Text; $exportContent | Out-File -FilePath $saveDialog.FileName -Encoding UTF8; Update-Status "Ticket exported" -Level 'SUCCESS' } } catch { Update-Status "Export failed" -Level 'ERROR'; [System.Windows.MessageBox]::Show($window, $_.Exception.Message, "Export Error", "OK", "Error") } finally { Set-LoadingState $false } })
}
if ($controls.CopyButton) {
    $controls.CopyButton.Add_Click({ try { Set-LoadingState $true; [System.Windows.Clipboard]::SetText(($controls.TicketGrid.ItemsSource | Format-Table | Out-String) + "`r`n" + $controls.DetailsBox.Text); Update-Status "Copied to clipboard" -Level 'SUCCESS' } catch { Update-Status "Copy failed" -Level 'ERROR' } finally { Set-LoadingState $false } })
}
if ($controls.OpenBrowserButton) {
    $controls.OpenBrowserButton.Add_Click({ try { Set-LoadingState $true; Start-Process "$($controls.ServerBox.Text)/browse/$($controls.TicketBox.Text)"; Update-Status "Opened in browser" -Level 'SUCCESS' } catch { Update-Status "Open failed" -Level 'ERROR' } finally { Set-LoadingState $false } })
}

# Keyboard shortcuts & Session timer
$window.Add_KeyDown({ param($s, $e) if ($e.Key -eq 'F5' -and $controls.RefreshButton.IsEnabled) { & $loadTicketHandler $true } if ($e.Key -eq 'Enter' -and $controls.TicketBox.IsFocused) { & $loadTicketHandler $false } })
$sessionTimer = New-Object System.Windows.Threading.DispatcherTimer; $sessionTimer.Interval = [TimeSpan]::FromMinutes(1); $sessionTimer.Add_Tick({ Update-SessionInfo; if ($script:SessionStartTime -and -not (Test-SessionValid)) { Update-Status "Session expired" -Level 'WARN'; Reset-Session; Set-UIAfterLogout } }); $sessionTimer.Start()

#endregion

# Initialize and run application
Update-Status "Ready - Please login" -Level 'INFO'
Update-SessionInfo
Update-CacheStats
Write-AppLog "Application started successfully" -Level 'INFO'

try { $window.ShowDialog() | Out-Null } finally { $sessionTimer.Stop(); Write-AppLog "Application closed" -Level 'INFO' }