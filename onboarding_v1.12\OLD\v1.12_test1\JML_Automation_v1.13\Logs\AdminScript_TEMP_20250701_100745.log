﻿2025-07-01 10:07:46.027 | akinje          | INFO     | JML Automation Script v1.13 starting...
2025-07-01 10:07:46.052 | akinje          | DEBUG    | Script path: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13
2025-07-01 10:07:46.090 | akinje          | DEBUG    | PowerShell version: 5.1.19041.5848
2025-07-01 10:07:46.520 | akinje          | DEBUG    | Execution policy: Bypass
2025-07-01 10:07:46.535 | akinje          | DEBUG    | User: akinje
2025-07-01 10:07:46.548 | akinje          | DEBUG    | Computer: JGM-CG7THR3
2025-07-01 10:07:46.961 | akinje          | INFO     | === JML ENVIRONMENT VALIDATION ===
2025-07-01 10:07:47.098 | akin<PERSON>          | INFO     | Verifying PowerShell Version...
2025-07-01 10:07:47.121 | akinje          | INFO     | === JML ENVIRONMENT VALIDATION ===
2025-07-01 10:07:47.138 | akinje          | INFO     | Verifying PowerShell Version...
2025-07-01 10:07:47.465 | akinje          | WARNING  | WARNING: PowerShell 7 or higher is recommended for optimal performance and security.
2025-07-01 10:07:47.577 | akinje          | INFO     | Current version: PowerShell 5.1.19041.5848
2025-07-01 10:07:47.608 | akinje          | WARNING  | PowerShell 7+ recommended. Current: 5.1.19041.5848
2025-07-01 10:07:47.730 | akinje          | INFO     | Searching for PowerShell 7 installation...
2025-07-01 10:07:47.812 | akinje          | INFO     |   Checking: C:\Program Files\PowerShell\7\pwsh.exe
2025-07-01 10:07:49.324 | akinje          | INFO     |   Found PowerShell 7.5.1 at: C:\Program Files\PowerShell\7\pwsh.exe
2025-07-01 10:07:49.575 | akinje          | INFO     | PowerShell 7 is installed at: C:\Program Files\PowerShell\7\pwsh.exe
2025-07-01 10:07:49.629 | akinje          | INFO     | Options:
2025-07-01 10:07:49.774 | akinje          | INFO     |   1. Continue with current PowerShell 5.x
2025-07-01 10:07:49.845 | akinje          | INFO     |   2. Switch to PowerShell 7 (recommended)
2025-07-01 10:07:50.166 | akinje          | INFO     |   3. Exit and manually switch
2025-07-01 10:07:50.320 | akinje          | INFO     | Select option (1-3) [Default: 2 in 10 seconds]: 
2025-07-01 10:08:00.422 | akinje          | INFO     | 2 (default)
2025-07-01 10:08:00.451 | akinje          | INFO     | === POWERSHELL 7 EXECUTION OPTIONS ===
2025-07-01 10:08:00.480 | akinje          | INFO     | PowerShell 7 is available at: C:\Program Files\PowerShell\7\pwsh.exe
2025-07-01 10:08:00.515 | akinje          | INFO     | How would you like to run the script in PowerShell 7?
2025-07-01 10:08:00.534 | akinje          | INFO     |   [N] New Window - Launch in a new PowerShell 7 window
2025-07-01 10:08:00.577 | akinje          | INFO     |   [C] Current Window - Run PowerShell 7 in this window (default)
2025-07-01 10:08:00.632 | akinje          | INFO     | Choose option (N/C) - Waiting 5 seconds (default: Current Window)...
2025-07-01 10:08:05.738 | akinje          | INFO     | Selected: Current Window (default)
2025-07-01 10:08:05.873 | akinje          | INFO     | Starting PowerShell 7 in current window...
2025-07-01 10:08:05.898 | akinje          | INFO     | Script will continue in PowerShell 7...
