﻿2025-07-01 12:37:37.705 | akinje          | INFO     | JML Automation Script v1.13 starting...
2025-07-01 12:37:37.734 | akinje          | DEBUG    | Script path: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13
2025-07-01 12:37:37.764 | akinje          | DEBUG    | PowerShell version: 5.1.19041.5848
2025-07-01 12:37:37.966 | akinje          | DEBUG    | Execution policy: Unrestricted
2025-07-01 12:37:37.976 | akinje          | DEBUG    | User: akinje
2025-07-01 12:37:37.982 | akinje          | DEBUG    | Computer: JGM-CG7THR3
2025-07-01 12:37:38.318 | akinje          | INFO     | === JML ENVIRONMENT VALIDATION ===
2025-07-01 12:37:38.345 | akin<PERSON>          | INFO     | Verifying PowerShell Version...
2025-07-01 12:37:38.365 | akinje          | INFO     | === JML ENVIRONMENT VALIDATION ===
2025-07-01 12:37:38.374 | akinje          | INFO     | Verifying PowerShell Version...
2025-07-01 12:37:38.455 | akinje          | WARNING  | WARNING: PowerShell 7 or higher is recommended for optimal performance and security.
2025-07-01 12:37:38.492 | akinje          | INFO     | Current version: PowerShell 5.1.19041.5848
2025-07-01 12:37:38.516 | akinje          | WARNING  | PowerShell 7+ recommended. Current: 5.1.19041.5848
2025-07-01 12:37:38.594 | akinje          | INFO     | Searching for PowerShell 7 installation...
2025-07-01 12:37:38.650 | akinje          | INFO     |   Checking: C:\Program Files\PowerShell\7\pwsh.exe
2025-07-01 12:37:39.475 | akinje          | INFO     |   Found PowerShell 7.5.1 at: C:\Program Files\PowerShell\7\pwsh.exe
2025-07-01 12:37:39.504 | akinje          | INFO     | PowerShell 7 is installed at: C:\Program Files\PowerShell\7\pwsh.exe
2025-07-01 12:37:39.576 | akinje          | INFO     | Options:
2025-07-01 12:37:39.590 | akinje          | INFO     |   1. Continue with current PowerShell 5.x
2025-07-01 12:37:39.609 | akinje          | INFO     |   2. Switch to PowerShell 7 (recommended)
2025-07-01 12:37:39.629 | akinje          | INFO     |   3. Exit and manually switch
2025-07-01 12:37:39.668 | akinje          | INFO     | Select option (1-3) [Default: 2 in 10 seconds]: 
2025-07-01 12:37:49.730 | akinje          | INFO     | 2 (default)
2025-07-01 12:37:49.749 | akinje          | INFO     | === POWERSHELL 7 EXECUTION OPTIONS ===
2025-07-01 12:37:49.760 | akinje          | INFO     | PowerShell 7 is available at: C:\Program Files\PowerShell\7\pwsh.exe
2025-07-01 12:37:49.773 | akinje          | INFO     | How would you like to run the script in PowerShell 7?
2025-07-01 12:37:49.784 | akinje          | INFO     |   [N] New Window - Launch in a new PowerShell 7 window
2025-07-01 12:37:49.799 | akinje          | INFO     |   [C] Current Window - Run PowerShell 7 in this window (default)
2025-07-01 12:37:49.816 | akinje          | INFO     | Choose option (N/C) - Waiting 5 seconds (default: Current Window)...
2025-07-01 12:37:54.907 | akinje          | INFO     | Selected: Current Window (default)
2025-07-01 12:37:54.936 | akinje          | INFO     | Starting PowerShell 7 in current window...
2025-07-01 12:37:54.951 | akinje          | INFO     | Script will continue in PowerShell 7...
