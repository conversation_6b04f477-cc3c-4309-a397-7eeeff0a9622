﻿{
    "Duration":  15.1213742,
    "StartTime":  {
                      "value":  "\/Date(1751483149199)\/",
                      "DisplayHint":  2,
                      "DateTime":  "Wednesday, July 2, 2025 8:05:49 PM"
                  },
    "SkippedTests":  0,
    "FailedTests":  4,
    "Errors":  [
                   "Script Loading: Script loading failed: FAILED: A parameter cannot be found that matches parameter name \u0027WhatIf\u0027.",
                   "Phase 1 Integration: Write-ErrorLog: Feature missing",
                   "Region Structure: Unbalanced regions: 31 regions, 30 endregions",
                   "Documentation Coverage: Low documentation: 7.03% comment lines"
               ],
    "PassedTests":  23,
    "TestDetails":  [
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751483151373)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Wednesday, July 2, 2025 8:05:51 PM"
                                          },
                            "Name":  "File Existence",
                            "Message":  "Script file found",
                            "Details":  null
                        },
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751483151623)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Wednesday, July 2, 2025 8:05:51 PM"
                                          },
                            "Name":  "File Size",
                            "Message":  "Script size: 0.35 MB (367907 bytes)",
                            "Details":  null
                        },
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751483152268)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Wednesday, July 2, 2025 8:05:52 PM"
                                          },
                            "Name":  "Line Count",
                            "Message":  "Total lines: 9856",
                            "Details":  null
                        },
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751483153194)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Wednesday, July 2, 2025 8:05:53 PM"
                                          },
                            "Name":  "PowerShell Parser",
                            "Message":  "No syntax errors detected",
                            "Details":  null
                        },
                        {
                            "Status":  "FAILED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751483159690)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Wednesday, July 2, 2025 8:05:59 PM"
                                          },
                            "Name":  "Script Loading",
                            "Message":  "Script loading failed: FAILED: A parameter cannot be found that matches parameter name \u0027WhatIf\u0027.",
                            "Details":  null
                        },
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751483160002)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Wednesday, July 2, 2025 8:06:00 PM"
                                          },
                            "Name":  "Class Definition: AdvancedLoggingManager",
                            "Message":  "Class definition found",
                            "Details":  null
                        },
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751483160273)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Wednesday, July 2, 2025 8:06:00 PM"
                                          },
                            "Name":  "Class Definition: VersionManager",
                            "Message":  "Class definition found",
                            "Details":  null
                        },
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751483160313)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Wednesday, July 2, 2025 8:06:00 PM"
                                          },
                            "Name":  "Class Definition: TestingFramework",
                            "Message":  "Class definition found",
                            "Details":  null
                        },
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751483160550)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Wednesday, July 2, 2025 8:06:00 PM"
                                          },
                            "Name":  "Class Definition: DocumentationGenerator",
                            "Message":  "Class definition found",
                            "Details":  null
                        },
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751483160757)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Wednesday, July 2, 2025 8:06:00 PM"
                                          },
                            "Name":  "Function Definition: Write-StructuredLog",
                            "Message":  "Function definition found",
                            "Details":  null
                        },
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751483160891)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Wednesday, July 2, 2025 8:06:00 PM"
                                          },
                            "Name":  "Function Definition: Get-ScriptVersion",
                            "Message":  "Function definition found",
                            "Details":  null
                        },
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751483161014)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Wednesday, July 2, 2025 8:06:01 PM"
                                          },
                            "Name":  "Function Definition: Register-Test",
                            "Message":  "Function definition found",
                            "Details":  null
                        },
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751483161223)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Wednesday, July 2, 2025 8:06:01 PM"
                                          },
                            "Name":  "Function Definition: New-Documentation",
                            "Message":  "Function definition found",
                            "Details":  null
                        },
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751483161351)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Wednesday, July 2, 2025 8:06:01 PM"
                                          },
                            "Name":  "Function Definition: Show-ScriptHelp",
                            "Message":  "Function definition found",
                            "Details":  null
                        },
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751483161441)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Wednesday, July 2, 2025 8:06:01 PM"
                                          },
                            "Name":  "Phase 1 Integration: ConfigurationManager",
                            "Message":  "Feature preserved",
                            "Details":  null
                        },
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751483161485)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Wednesday, July 2, 2025 8:06:01 PM"
                                          },
                            "Name":  "Phase 1 Integration: Get-ConfigurationValue",
                            "Message":  "Feature preserved",
                            "Details":  null
                        },
                        {
                            "Status":  "FAILED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751483161560)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Wednesday, July 2, 2025 8:06:01 PM"
                                          },
                            "Name":  "Phase 1 Integration: Write-ErrorLog",
                            "Message":  "Feature missing",
                            "Details":  null
                        },
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751483161616)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Wednesday, July 2, 2025 8:06:01 PM"
                                          },
                            "Name":  "Phase 2 Integration: AutoCompletionHistory",
                            "Message":  "Feature preserved",
                            "Details":  null
                        },
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751483161666)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Wednesday, July 2, 2025 8:06:01 PM"
                                          },
                            "Name":  "Phase 2 Integration: Apply-ModernUIIntegration",
                            "Message":  "Feature preserved",
                            "Details":  null
                        },
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751483161785)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Wednesday, July 2, 2025 8:06:01 PM"
                                          },
                            "Name":  "Phase 2 Integration: Show-ProgressDialog",
                            "Message":  "Feature preserved",
                            "Details":  null
                        },
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751483161891)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Wednesday, July 2, 2025 8:06:01 PM"
                                          },
                            "Name":  "Phase 3 Integration: OrganizationalDataManager",
                            "Message":  "Feature preserved",
                            "Details":  null
                        },
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751483161964)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Wednesday, July 2, 2025 8:06:01 PM"
                                          },
                            "Name":  "Phase 3 Integration: BatchOperationManager",
                            "Message":  "Feature preserved",
                            "Details":  null
                        },
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751483162269)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Wednesday, July 2, 2025 8:06:02 PM"
                                          },
                            "Name":  "Phase 3 Integration: EnhancedJiraManager",
                            "Message":  "Feature preserved",
                            "Details":  null
                        },
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751483162346)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Wednesday, July 2, 2025 8:06:02 PM"
                                          },
                            "Name":  "Phase 3 Integration: AdvancedADGroupManager",
                            "Message":  "Feature preserved",
                            "Details":  null
                        },
                        {
                            "Status":  "FAILED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751483162629)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Wednesday, July 2, 2025 8:06:02 PM"
                                          },
                            "Name":  "Region Structure",
                            "Message":  "Unbalanced regions: 31 regions, 30 endregions",
                            "Details":  null
                        },
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751483162738)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Wednesday, July 2, 2025 8:06:02 PM"
                                          },
                            "Name":  "Error Handling",
                            "Message":  "Good error handling coverage: 127 try blocks",
                            "Details":  null
                        },
                        {
                            "Status":  "FAILED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751483164319)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Wednesday, July 2, 2025 8:06:04 PM"
                                          },
                            "Name":  "Documentation Coverage",
                            "Message":  "Low documentation: 7.03% comment lines",
                            "Details":  null
                        }
                    ],
    "EndTime":  {
                    "value":  "\/Date(1751483164320)\/",
                    "DisplayHint":  2,
                    "DateTime":  "Wednesday, July 2, 2025 8:06:04 PM"
                },
    "TotalTests":  27
}
