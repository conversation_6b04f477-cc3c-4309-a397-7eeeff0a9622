# Simple targeted fix for the main problematic areas
$scriptPath = "OnboardingFromJiraGUI.ps1"

# Read the file line by line and fix specific lines
$lines = Get-Content $scriptPath

for ($i = 0; $i -lt $lines.Count; $i++) {
    $line = $lines[$i]
    
    # Fix specific lines with System.Windows.Media.Brushes
    if ($line -match '\[System\.Windows\.Media\.Brushes\]::(\w+)') {
        $brushName = $matches[1]
        $lines[$i] = $line -replace '\[System\.Windows\.Media\.Brushes\]::\w+', "(`$brushesType = [System.Type]::GetType('System.Windows.Media.Brushes')).GetProperty('$brushName').GetValue(`$null)"
    }
    
    # Fix GridUnitType references
    if ($line -match '\[System\.Windows\.GridUnitType\]::(\w+)') {
        $unitType = $matches[1]
        $lines[$i] = $line -replace '\[System\.Windows\.GridUnitType\]::\w+', "([System.Type]::GetType('System.Windows.GridUnitType'))::$unitType"
    }
    
    # Fix Grid static method calls
    if ($line -match '\[System\.Windows\.Controls\.Grid\]::(\w+)') {
        $methodName = $matches[1]
        $lines[$i] = $line -replace '\[System\.Windows\.Controls\.Grid\]::\w+\(([^)]+)\)', "([System.Type]::GetType('System.Windows.Controls.Grid')).GetMethod('$methodName').Invoke(`$null, @(`$1))"
    }
    
    # Fix function parameter types
    if ($line -match '\[System\.Windows\.Controls\.Grid\]\$') {
        $lines[$i] = $line -replace '\[System\.Windows\.Controls\.Grid\]', '[Object]'
    }
}

# Write back to file
$lines | Set-Content $scriptPath -Encoding UTF8

Write-Host "Applied targeted fixes to WPF type references!" -ForegroundColor Green