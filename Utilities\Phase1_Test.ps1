# Phase 1 Test - Centralized Configuration
# Test that the centralized configuration is working

# Load the config and function from the main file (lines 18-111 contain our config and function)
$configLines = Get-Content "/mnt/c/Users/<USER>/OneDrive - JERA Global Markets/Azure Devops/it-endusersupport-automation/scripts/Utilities/OnboardingFromJiraGUI_v2.ps1" | Select-Object -Skip 17 -First 94
$configScript = $configLines -join "`n"

# Execute the config part
Invoke-Expression $configScript

Write-Host "=== Phase 1 Configuration Test ===" -ForegroundColor Green

# Test 1: Check if config is loaded
Write-Host "Test 1: Configuration loaded" -ForegroundColor Cyan
if ($script:AppConfig) {
    Write-Host "✅ AppConfig exists" -ForegroundColor Green
    Write-Host "Version: $($script:AppConfig.UI.WindowTitle)" -ForegroundColor Yellow
} else {
    Write-Host "❌ AppConfig not found" -ForegroundColor Red
}

# Test 2: Test Get-AppConfig function
Write-Host "`nTest 2: Get-AppConfig function" -ForegroundColor Cyan
$jiraUrl = Get-AppConfig -Path "Jira.DefaultUrl"
if ($jiraUrl -eq "https://jeragm.atlassian.net") {
    Write-Host "✅ Jira URL: $jiraUrl" -ForegroundColor Green
} else {
    Write-Host "❌ Jira URL incorrect: $jiraUrl" -ForegroundColor Red
}

# Test 3: Test field mappings
Write-Host "`nTest 3: Field mappings" -ForegroundColor Cyan
$fieldMappings = Get-AppConfig -Path "Jira.FieldMappings"
if ($fieldMappings -and $fieldMappings.FirstName.CustomFieldId -eq "customfield_10304") {
    Write-Host "✅ Field mappings loaded correctly" -ForegroundColor Green
    Write-Host "FirstName field: $($fieldMappings.FirstName.CustomFieldId)" -ForegroundColor Yellow
} else {
    Write-Host "❌ Field mappings incorrect" -ForegroundColor Red
}

# Test 4: Test OU mappings
Write-Host "`nTest 4: OU mappings" -ForegroundColor Cyan
$ouMappings = Get-AppConfig -Path "ActiveDirectory.OUMappings"
if ($ouMappings -and $ouMappings.Singapore) {
    Write-Host "✅ OU mappings loaded correctly" -ForegroundColor Green
    Write-Host "Singapore OU: $($ouMappings.Singapore)" -ForegroundColor Yellow
} else {
    Write-Host "❌ OU mappings incorrect" -ForegroundColor Red
}

# Test 5: Test window settings
Write-Host "`nTest 5: UI Configuration" -ForegroundColor Cyan
$windowSize = Get-AppConfig -Path "UI.WindowSize"
if ($windowSize -and $windowSize.Width -eq 700) {
    Write-Host "✅ UI settings loaded correctly" -ForegroundColor Green
    Write-Host "Window size: $($windowSize.Width) x $($windowSize.Height)" -ForegroundColor Yellow
} else {
    Write-Host "❌ UI settings incorrect" -ForegroundColor Red
}

Write-Host "`n=== Phase 1 Test Complete ===" -ForegroundColor Green