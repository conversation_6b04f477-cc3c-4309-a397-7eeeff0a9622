# Targeted WPF fixes - only fix the essential parse-time issues
$scriptPath = "OnboardingFromJiraGUI.ps1"
$content = Get-Content $scriptPath -Raw

Write-Host "Applying targeted WPF fixes..." -ForegroundColor Yellow

# Fix only function parameters that cause parse errors
$content = $content -replace '\[System\.Windows\.Controls\.Grid\]\$', '[Object]$'
$content = $content -replace '\[System\.Windows\.Controls\.DockPanel\]\$', '[Object]$'

# Fix only the specific static references that cause parse errors
# Use a more conservative approach - wrap in try/catch at runtime
$pattern1 = '\[System\.Windows\.Media\.Brushes\]::(\w+)'
$replacement1 = 'try { [System.Windows.Media.Brushes]::$1 } catch { $null }'

$pattern2 = '\[System\.Windows\.GridUnitType\]::(\w+)'  
$replacement2 = 'try { [System.Windows.GridUnitType]::$1 } catch { "Star" }'

$pattern3 = '\[System\.Windows\.Controls\.Grid\]::(\w+)\(([^)]+)\)'
$replacement3 = 'try { [System.Windows.Controls.Grid]::$1($2) } catch { }'

$pattern4 = '\[System\.Windows\.Controls\.DockPanel\]::(\w+)\(([^)]+)\)'
$replacement4 = 'try { [System.Windows.Controls.DockPanel]::$1($2) } catch { }'

$content = $content -replace $pattern1, $replacement1
$content = $content -replace $pattern2, $replacement2  
$content = $content -replace $pattern3, $replacement3
$content = $content -replace $pattern4, $replacement4

$content | Set-Content $scriptPath -Encoding UTF8

Write-Host "Applied targeted WPF fixes" -ForegroundColor Green