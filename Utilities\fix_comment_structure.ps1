# Fix the corrupted comment structure
$scriptPath = "OnboardingFromJiraGUI.ps1"
$content = Get-Content $scriptPath -Raw

Write-Host "Fixing corrupted comment structure..." -ForegroundColor Yellow

# Fix the corrupted error message that includes the comment start
$content = $content -replace 'Write-Error "Failed to load WPF assemblies: #Requires -Version 5\.1\s+<#', 'Write-Error "Failed to load WPF assemblies: $_"'

# Remove any duplicate comment blocks that might have been created
$content = $content -replace '<#\s*\.SYNOPSIS[^#]*?#>\s*<#', '<#'

$content | Set-Content $scriptPath -Encoding UTF8

Write-Host "Fixed corrupted comment structure" -ForegroundColor Green