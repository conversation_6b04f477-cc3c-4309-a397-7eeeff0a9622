# Final fix for function parameter type declaration
$scriptPath = "OnboardingFromJiraGUI.ps1"

# Read the content
$content = Get-Content $scriptPath -Raw

# Fix function parameter type declarations
$content = $content -replace '\[System\.Type\]::GetType\("([^"]+)"\)\$', '[$1]$'

# Write the fixed content back
$content | Set-Content $scriptPath -Encoding UTF8

Write-Host "Function parameter types have been fixed!" -ForegroundColor Green