#Requires -Version 5.1

<#
.SYNOPSIS
    Diagnostic script to identify SAM corruption source in OnboardingFromJiraGUI

.DESCRIPTION
    This script will help identify exactly where the SAM corruption is occurring
    by testing the SAM generation function in isolation and then testing the
    GUI assignment process step by step.
#>

# Load WPF assemblies first
try {
    Write-Host "Loading WPF assemblies..." -ForegroundColor Yellow
    Add-Type -AssemblyName PresentationCore
    Add-Type -AssemblyName PresentationFramework  
    Add-Type -AssemblyName System.Windows.Forms
    Add-Type -AssemblyName WindowsBase
    Write-Host "WPF assemblies loaded successfully" -ForegroundColor Green
} catch {
    Write-Error "Failed to load WPF assemblies: $($_.Exception.Message)"
    exit 1
}

# Copy the exact SAM generation function from the main script
function Get-SamAccountName {
    param([string]$FirstName, [string]$LastName)

    # Remove spaces from names for SAM account generation (matching bulk import logic)
    $originalFirstName = $FirstName
    $originalLastName = $LastName
    $FirstName = $FirstName -replace '\s+', ''
    $LastName = $LastName -replace '\s+', ''

    if ($FirstName -ne $originalFirstName -or $LastName -ne $originalLastName) {
        Write-Host "Removed spaces from names: '$originalFirstName $originalLastName' -> '$FirstName $LastName'" -ForegroundColor Gray
    }

    # Get first 5 letters of last name (or less if name is shorter) - SAME AS BULK IMPORT
    $lastPart = $LastName.Substring(0, [Math]::Min(5, $LastName.Length))

    # Start with just first letter of first name - SAME AS BULK IMPORT
    $firstNameIndex = 1
    $isUnique = $false
    $minLengthSatisfied = $false

    Write-Host "Generating SAM account name for $FirstName $LastName" -ForegroundColor Gray

    while (-not $isUnique -and $firstNameIndex -le $FirstName.Length) {
        # Get increasing portions of the first name - SAME AS BULK IMPORT
        $firstPart = $FirstName.Substring(0, $firstNameIndex)
        $proposedSam = ("{0}{1}" -f $lastPart, $firstPart).ToLower()

        # Check if the proposed SAM meets the minimum length requirement of 6 characters - SAME AS BULK IMPORT
        if ($proposedSam.Length -lt 6) {
            # If the proposed SAM is too short, add more characters from the first name if possible
            if ($firstNameIndex -lt $FirstName.Length) {
                $firstNameIndex++
                Write-Host "SAM account name '$proposedSam' is less than 6 characters, adding more letters from first name" -ForegroundColor Yellow
                continue
            }
            else {
                # If we've used all first name characters and still under 6, flag it but continue
                Write-Host "Warning: SAM account name '$proposedSam' is less than 6 characters but using all available name parts" -ForegroundColor Yellow
                $minLengthSatisfied = $false
            }
        }
        else {
            $minLengthSatisfied = $true
        }

        try {
            Write-Host "Checking if SAM account name '$proposedSam' exists" -ForegroundColor Gray

            # In simulation mode, assume it doesn't exist
            $isUnique = $true
            Write-Host "Generated SAM account name: $proposedSam (simulation mode)" -ForegroundColor Green
        }
        catch {
            Write-Host "Error checking SAM account existence: $($_.Exception.Message)" -ForegroundColor Red
            throw "Error checking SAM account existence: $($_.Exception.Message)"
        }
    }

    if (-not $isUnique) {
        $errorMsg = "Could not generate unique SAM account name for $FirstName $LastName after trying all combinations"
        Write-Host $errorMsg -ForegroundColor Red
        throw $errorMsg
    }

    return [string]$proposedSam
}

# Test the SAM generation function in isolation
Write-Host "`n=== TESTING SAM GENERATION FUNCTION IN ISOLATION ===" -ForegroundColor Cyan
$testFirstName = "test2"
$testLastName = "test"

Write-Host "Input: FirstName='$testFirstName', LastName='$testLastName'" -ForegroundColor White

$generatedSam = Get-SamAccountName -FirstName $testFirstName -LastName $testLastName
Write-Host "Generated SAM: '$generatedSam'" -ForegroundColor Green
Write-Host "SAM Type: $($generatedSam.GetType().FullName)" -ForegroundColor Gray
Write-Host "SAM Length: $($generatedSam.Length)" -ForegroundColor Gray

# Test string conversion
$samAsString = [string]$generatedSam
Write-Host "SAM as [string]: '$samAsString'" -ForegroundColor Green

# Test variable isolation pattern
$samForGui = "$generatedSam"
Write-Host "SAM with isolation pattern: '$samForGui'" -ForegroundColor Green

Write-Host "`n=== TESTING GUI TEXTBOX ASSIGNMENT ===" -ForegroundColor Cyan

# Create a simple WPF window with a TextBox to test assignment
$xaml = @"
<Window xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        Title="SAM Test" Height="200" Width="400">
    <Grid>
        <TextBox Name="SamBox" HorizontalAlignment="Center" VerticalAlignment="Center" Width="200" Height="30"/>
    </Grid>
</Window>
"@

try {
    $window = [Windows.Markup.XamlReader]::Parse($xaml)
    $samBox = $window.FindName("SamBox")
    
    Write-Host "Created test window with TextBox" -ForegroundColor Green
    
    # Test direct assignment
    Write-Host "`nTesting direct assignment..." -ForegroundColor Yellow
    $samBox.Text = $generatedSam
    Write-Host "TextBox.Text after direct assignment: '$($samBox.Text)'" -ForegroundColor $(if ($samBox.Text -eq $generatedSam) { "Green" } else { "Red" })
    
    # Test with string conversion
    Write-Host "`nTesting with [string] conversion..." -ForegroundColor Yellow
    $samBox.Text = [string]$generatedSam
    Write-Host "TextBox.Text after [string] conversion: '$($samBox.Text)'" -ForegroundColor $(if ($samBox.Text -eq $generatedSam) { "Green" } else { "Red" })
    
    # Test with isolation pattern
    Write-Host "`nTesting with isolation pattern..." -ForegroundColor Yellow
    $samForGuiTest = "$generatedSam"
    $samBox.Text = $samForGuiTest
    Write-Host "TextBox.Text after isolation pattern: '$($samBox.Text)'" -ForegroundColor $(if ($samBox.Text -eq $generatedSam) { "Green" } else { "Red" })
    
    # Test with ToString()
    Write-Host "`nTesting with ToString()..." -ForegroundColor Yellow
    $samBox.Text = $generatedSam.ToString()
    Write-Host "TextBox.Text after ToString(): '$($samBox.Text)'" -ForegroundColor $(if ($samBox.Text -eq $generatedSam) { "Green" } else { "Red" })
    
    Write-Host "`n=== SUMMARY ===" -ForegroundColor Cyan
    Write-Host "Expected SAM: '$generatedSam'" -ForegroundColor White
    Write-Host "Final TextBox value: '$($samBox.Text)'" -ForegroundColor White
    
    if ($samBox.Text -eq $generatedSam) {
        Write-Host "✅ SUCCESS: No corruption detected in this test" -ForegroundColor Green
    } else {
        Write-Host "❌ CORRUPTION DETECTED: TextBox value differs from expected" -ForegroundColor Red
        Write-Host "This suggests the corruption is happening elsewhere in the main script" -ForegroundColor Yellow
    }
    
} catch {
    Write-Host "Error testing GUI assignment: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`nPress any key to exit..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
