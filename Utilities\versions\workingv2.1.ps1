#Requires -Version 5.1

# Load WPF Assemblies - MUST be at the top before any WPF types are referenced
try {
    Add-Type -AssemblyName PresentationFramework -ErrorAction Stop
    Add-Type -AssemblyName PresentationCore -ErrorAction Stop  
    Add-Type -AssemblyName WindowsBase -ErrorAction Stop
    Add-Type -AssemblyName System.Windows.Forms -ErrorAction Stop
} catch {
    Write-Error "Failed to load required WPF assemblies: $($_.Exception.Message)"
    exit 1
}

# OnboardingFromJiraGUI_v2.ps1
# Complete implementation with PowerShell-specific WPF solutions

# CENTRALIZED CONFIGURATION
$script:AppConfig = @{
    Jira = @{
        DefaultUrl = "https://jeragm.atlassian.net"
        FieldMappings = @{
            FirstName = @{ CustomFieldId = "customfield_10304"; DisplayName = "First Name" }
            LastName = @{ CustomFieldId = "customfield_10305"; DisplayName = "Last Name" }
            JobTitle = @{ CustomFieldId = "customfield_10238"; DisplayName = "Job Title" }
            Department = @{ CustomFieldId = "customfield_10120"; DisplayName = "Department" }
            ModelAccount = @{ CustomFieldId = "customfield_10343"; DisplayName = "Model Account" }
            OfficeLocation = @{ CustomFieldId = "customfield_10115"; DisplayName = "Office Location" }
            EmployeeType = @{ CustomFieldId = "customfield_10342"; DisplayName = "Employee Type" }
            EffectiveDate = @{ CustomFieldId = "customfield_10344"; DisplayName = "Effective Date" }
        }
        KnownCustomFields = @("customfield_10304", "customfield_10305", "customfield_10238", "customfield_10120", "customfield_10343", "customfield_10115")
    }
    ActiveDirectory = @{
        OUMappings = @{
            "Singapore" = "OU=Users,OU=Singapore,DC=jeragm,DC=com"
            "United Kingdom" = "OU=Users,OU=London,DC=jeragm,DC=com"
            "Japan" = "OU=Tokyo,OU=Users,OU=Accounts,OU=JeraGM,DC=JERAGM,DC=COM"
            "Tokyo" = "OU=Tokyo,OU=Users,OU=Accounts,OU=JeraGM,DC=JERAGM,DC=COM"
            "USA" = "OU=Users,OU=USA,DC=jeragm,DC=com"
            "United States" = "OU=Users,OU=USA,DC=jeragm,DC=com"
        }
        DefaultOU = "OU=Users,OU=Singapore,DC=jeragm,DC=com"
        ValidationRules = @{
            FirstName = @{
                Required = $true; MinLength = 2; MaxLength = 50
                Pattern = '^[a-zA-Z\s\-\.]+$'
                ErrorMessage = "First name must be 2-50 characters, letters only"
            }
            LastName = @{
                Required = $true; MinLength = 2; MaxLength = 50
                Pattern = '^[a-zA-Z\s\-\.]+$'
                ErrorMessage = "Last name must be 2-50 characters, letters only"
            }
            SamAccountName = @{
                Required = $true; MinLength = 3; MaxLength = 20
                Pattern = '^[a-zA-Z0-9\.]+$'
                ErrorMessage = "Username must be 3-20 characters, alphanumeric and dots only"
            }
        }
        Company = "JERA Global Markets"
        DefaultPassword = "TempPass123!"
        Domain = "jeragm.com"
    }
    UI = @{
        WindowTitle = "Onboarding from Jira GUI v3.0"
        WindowSize = @{ Width = 700; Height = 600 }
        SessionTimeoutMinutes = 60
        Colors = @{
            Primary = "#2196F3"
            Success = "#4CAF50"
            Warning = "#FF9800"
            Error = "#F44336"
            Secondary = "#666666"
            Disabled = "#CCCCCC"
        }
        Spacing = @{
            DefaultMargin = 20
            FieldSpacing = 10
            ButtonHeight = 35
            SeparatorSpacing = 15
        }
    }
    Validation = @{
        RequiredFields = @('FirstName', 'LastName', 'Department', 'JobTitle')
        UsernameMaxLength = 20
        CommentTemplate = @"
User onboarding completed:
- Name: {FirstName} {LastName}
- Username: {Username}
- Department: {Department}
- Job Title: {JobTitle}
- OU: {OU}

Account has been created and is ready for use.
"@
    }
}

# Configuration accessor function
function Get-AppConfig([string]$Path) {
    $parts = $Path -split '\.'
    $current = $script:AppConfig
    foreach ($part in $parts) {
        if ($current.ContainsKey($part)) { 
            $current = $current[$part] 
        } else { 
            return $null 
        }
    }
    return $current
}

# Standardized Error Handling Helper
class ErrorHelper {
    static [hashtable] Success([object]$data, [string]$operation = "") {
        $result = @{ Success = $true; Data = $data }
        if ($operation) { $result.Operation = $operation }
        return $result
    }
    
    static [hashtable] Failure([string]$error, [string]$operation = "", [System.Exception]$exception = $null) {
        $result = @{ Success = $false; ErrorMessage = $error }
        if ($operation) { $result.Operation = $operation }
        if ($exception) { $result.Exception = $exception }
        return $result
    }
    
    static [hashtable] SafeExecute([scriptblock]$operation, [string]$operationName, $logger = $null) {
        try {
            if ($logger) { $logger.Debug($operationName, "Starting operation") }
            $result = & $operation
            if ($logger) { $logger.Debug($operationName, "Operation completed successfully") }
            return [ErrorHelper]::Success($result, $operationName)
        } catch {
            if ($logger) { $logger.Error("$operationName failed", $_.Exception) }
            return [ErrorHelper]::Failure($_.Exception.Message, $operationName, $_.Exception)
        }
    }
}

# SECTION 0: POWERSHELL WPF INFRASTRUCTURE

# Custom Property Change Notification System for PowerShell Classes
class ObservableProperty {
    [string]$Name
    [object]$Value
    [scriptblock]$OnChanged

    ObservableProperty([string]$name, [object]$initialValue, [scriptblock]$onChanged) {
        $this.Name = $name
        $this.Value = $initialValue
        $this.OnChanged = $onChanged
    }

    [void] SetValue([object]$newValue) {
        if ($this.Value -ne $newValue) {
            $oldValue = $this.Value
            $this.Value = $newValue
            if ($this.OnChanged) {
                & $this.OnChanged $this.Name $oldValue $newValue
            }
        }
    }
}

# PowerShell-compatible Observable Object Base Class
class ObservableObject {
    hidden [hashtable]$_properties = @{}
    hidden [System.Collections.Generic.List[scriptblock]]$_propertyChangedHandlers = [System.Collections.Generic.List[scriptblock]]::new()

    # Register a property for observation
    hidden [void] RegisterProperty([string]$name, [object]$initialValue) {
        $this._properties[$name] = [ObservableProperty]::new($name, $initialValue, {
            param($propName, $oldValue, $newValue)
            $this.NotifyPropertyChanged($propName)
        })
    }

    # Add property changed event handler
    [void] AddPropertyChangedHandler([scriptblock]$handler) {
        $this._propertyChangedHandlers.Add($handler)
    }

    # Notify all handlers of property change
    hidden [void] NotifyPropertyChanged([string]$propertyName) {
        foreach ($handler in $this._propertyChangedHandlers) {
            & $handler $propertyName
        }
    }

    # Get property value
    [object] GetProperty([string]$name) {
        if ($this._properties.ContainsKey($name)) {
            return $this._properties[$name].Value
        }
        return $null
    }

    # Set property value with change notification
    [void] SetProperty([string]$name, [object]$value) {
        if ($this._properties.ContainsKey($name)) {
            $this._properties[$name].SetValue($value)
        }
    }
}

# PowerShell-compatible Value Converter
class BooleanToVisibilityConverter {
    [object] Convert([object]$value, [type]$targetType, [object]$parameter, [System.Globalization.CultureInfo]$culture) {
        if ($value -is [bool] -and $value) {
            return 'Visible'
        }
        return 'Collapsed'
    }

    [object] ConvertBack([object]$value, [type]$targetType, [object]$parameter, [System.Globalization.CultureInfo]$culture) {
        return $value -eq 'Visible'
    }
}

class InverseBooleanConverter {
    [object] Convert([object]$value, [type]$targetType, [object]$parameter, [System.Globalization.CultureInfo]$culture) {
        if ($value -is [bool]) { return -not $value }
        return $true
    }

    [object] ConvertBack([object]$value, [type]$targetType, [object]$parameter, [System.Globalization.CultureInfo]$culture) {
        if ($value -is [bool]) { return -not $value }
        return $false
    }
}

class BooleanToOppositeBooleanConverter {
    [object] Convert([object]$value, [type]$targetType, [object]$parameter, [System.Globalization.CultureInfo]$culture) { 
        if ($value -is [bool]) { return -not $value }
        return $true
    }
    [object] ConvertBack([object]$value, [type]$targetType, [object]$parameter, [System.Globalization.CultureInfo]$culture) { 
        if ($value -is [bool]) { return -not $value }
        return $false
    }
}

# PowerShell Threading Helper
class AsyncOperationHelper {
    static [void] RunOnUIThread([object]$dispatcher, [scriptblock]$action) {
        if ($dispatcher.CheckAccess()) {
            & $action
        } else {
            $dispatcher.Invoke($action)
        }
    }

    static [void] RunAsync([scriptblock]$backgroundWork, [scriptblock]$onComplete, [object]$uiDispatcher, [object[]]$argumentList = @()) {
        Write-Host "DEBUG: AsyncOperationHelper.RunAsync called" -ForegroundColor Cyan
        try {
            Write-Host "DEBUG: Creating runspace" -ForegroundColor Cyan
            $runspace = [runspacefactory]::CreateRunspace()
            $runspace.Open()
            
            Write-Host "DEBUG: Creating PowerShell instance" -ForegroundColor Cyan
            $powershell = [powershell]::Create()
            $powershell.Runspace = $runspace
            
            # Add the background work
            Write-Host "DEBUG: Adding script to PowerShell instance" -ForegroundColor Cyan
            $powershell.AddScript({
                param($work, $args)
                try {
                    Write-Host "DEBUG: Background script executing" -ForegroundColor Cyan
                    $result = & $work @args
                    Write-Host "DEBUG: Background script completed successfully" -ForegroundColor Cyan
                    return @{ Success = $true; Result = $result }
                } catch {
                    Write-Host "DEBUG: Background script failed: $($_.Exception.Message)" -ForegroundColor Red
                    return @{ Success = $false; Error = $_.Exception.Message }
                }
            }).AddArgument($backgroundWork).AddArgument($argumentList) | Out-Null

            # Start async execution
            Write-Host "DEBUG: Starting async execution" -ForegroundColor Cyan
            $handle = $powershell.BeginInvoke()

            # Schedule completion check
            Write-Host "DEBUG: Setting up completion timer" -ForegroundColor Cyan
            $timer = New-Object System.Windows.Threading.DispatcherTimer
            $timer.Interval = [timespan]::FromMilliseconds(100)
            $timer.Add_Tick({
                Write-Host "DEBUG: Timer tick, checking if completed" -ForegroundColor Gray
                if ($handle.IsCompleted) {
                    Write-Host "DEBUG: Async operation completed" -ForegroundColor Green
                    $timer.Stop()
                    try {
                        $result = $powershell.EndInvoke($handle)
                        Write-Host "DEBUG: Got result, about to dispatch to UI" -ForegroundColor Green
                        if ($uiDispatcher -and $onComplete) {
                            $uiDispatcher.InvokeAsync({
                                Write-Host "DEBUG: UI dispatcher executing completion callback" -ForegroundColor Green
                                & $onComplete $result[0]
                            }.GetNewClosure())
                        }
                    } catch {
                        Write-Warning "Async operation failed: $($_.Exception.Message)"
                    } finally {
                        if ($powershell) { $powershell.Dispose() }
                        if ($runspace) { $runspace.Close() }
                    }
                }
            }.GetNewClosure())
            $timer.Start()
            Write-Host "DEBUG: Timer started" -ForegroundColor Cyan
        } catch {
            Write-Error "Failed to start async operation: $($_.Exception.Message)"
            if ($onComplete) {
                & $onComplete @{ Success = $false; Error = $_.Exception.Message }
            }
        }
    }
}

# SECTION 1: SERVICE CLASSES
class LoggingService {
    [string]$LogLevel = 'INFO'
    [bool]$DebugMode = $true  # Default ON as requested
    [System.Collections.Generic.List[string]]$LogHistory

    LoggingService() {
        $this.LogHistory = [System.Collections.Generic.List[string]]::new()
    }

    hidden [int] GetLevelValue([string]$level) {
        switch ($level.ToUpper()) {
            'DEBUG'   { return 0 }
            'VERBOSE' { return 1 }
            'INFO'    { return 2 }
            'WARN'    { return 3 }
            'ERROR'   { return 4 }
            'SECURITY'{ return 5 }
            default   { return 2 }
        }
        return 2 # Fallback return
    }

    hidden WriteLog([string]$level, [string]$message) {
        if ($this.GetLevelValue($level) -ge $this.GetLevelValue($this.LogLevel)) {
            $logEntry = "[{0}] [{1}] {2}" -f (Get-Date -Format 'yyyy-MM-dd HH:mm:ss'), $level.ToUpper(), $message
            Write-Host $logEntry
            $this.LogHistory.Add($logEntry)
            
            # Keep only last 1000 entries to prevent memory issues
            if ($this.LogHistory.Count -gt 1000) {
                $this.LogHistory.RemoveAt(0)
            }
        }
    }

    [void] Info([string]$Message) { $this.WriteLog('INFO', $Message) }
    [void] Warn([string]$Message) { $this.WriteLog('WARN', $Message) }
    [void] Error([string]$Message, [System.Exception]$Exception) { 
        $errMsg = $Message
        if ($Exception) { $errMsg += " | Exception: $($Exception.Message)" }
        $this.WriteLog('ERROR', $errMsg)
    }
    [void] Verbose([string]$Message) { $this.WriteLog('VERBOSE', $Message) }
    [void] SecurityAudit([string]$action, [string]$result) { $this.WriteLog('SECURITY', "Action: $action, Result: $result") }
    
    # Enhanced Debug method with operation context and mode control
    [void] Debug([string]$operation, [string]$message) { 
        if ($this.DebugMode) {
            $this.WriteLog('DEBUG', "[$operation] $message")
        }
    }
    
    # Backward compatibility - single parameter debug
    [void] Debug([string]$message) { 
        if ($this.DebugMode) {
            $this.WriteLog('DEBUG', $message)
        }
    }
    
    # Debug mode control
    [void] SetDebugMode([bool]$enabled) {
        $this.DebugMode = $enabled
        $this.Info("Debug mode " + $(if ($enabled) { "enabled" } else { "disabled" }))
    }
    
    [bool] GetDebugMode() {
        return $this.DebugMode
    }
    
    [string[]] GetRecentLogs([int]$count = 50) {
        $start = [Math]::Max(0, $this.LogHistory.Count - $count)
        $end = $this.LogHistory.Count - $start
        return $this.LogHistory.GetRange($start, $end).ToArray()
    }
}

class ConfigurationService {
    [string[]] GetOUList() {
        $ouMappings = Get-AppConfig -Path "ActiveDirectory.OUMappings"
        return $ouMappings.Values
    }
    
    [string] GetOUForLocation([string]$officeLocation) {
        $ouMappings = Get-AppConfig -Path "ActiveDirectory.OUMappings"
        if ($ouMappings.ContainsKey($officeLocation)) {
            return $ouMappings[$officeLocation]
        }
        return Get-AppConfig -Path "ActiveDirectory.DefaultOU"
    }

    [hashtable] GetJiraFieldMappings() {
        return Get-AppConfig -Path "Jira.FieldMappings"
    }

    [hashtable] GetValidationRules() {
        return Get-AppConfig -Path "ActiveDirectory.ValidationRules"
    }

    [hashtable] ValidateField([string]$fieldName, [string]$value) {
        $rules = $this.GetValidationRules()
        if (-not $rules.ContainsKey($fieldName)) {
            return @{ IsValid = $true; ErrorMessage = "" }
        }

        $rule = $rules[$fieldName]
        
        # Check required
        if ($rule.Required -and [string]::IsNullOrWhiteSpace($value)) {
            return @{ IsValid = $false; ErrorMessage = "$fieldName is required" }
        }

        $trimmedValue = $value.Trim()

        # Check length and pattern
        if ($rule.ContainsKey('MinLength') -and $trimmedValue.Length -lt $rule.MinLength) {
            return @{ IsValid = $false; ErrorMessage = "$fieldName must be at least $($rule.MinLength) characters" }
        }

        if ($rule.ContainsKey('Pattern') -and $trimmedValue -notmatch $rule.Pattern) {
            return @{ IsValid = $false; ErrorMessage = $rule.ErrorMessage }
        }

        return @{ IsValid = $true; ErrorMessage = "" }
    }

    [int] GetSessionTimeoutMinutes() { 
        return Get-AppConfig -Path "UI.SessionTimeoutMinutes"
    }
    
    [string] GetWindowTitle() { 
        return Get-AppConfig -Path "UI.WindowTitle"
    }
    
    [string] GetDefaultJiraUrl() { 
        return Get-AppConfig -Path "Jira.DefaultUrl"
    }
}

# JiraRestClient - Unified REST API client for all Jira operations
class JiraRestClient {
    $log
    
    JiraRestClient($loggingService) {
        $this.log = $loggingService
    }
    
    # Unified REST API method with operation-specific debug context
    [hashtable] InvokeJiraAPI([hashtable]$authInfo, [string]$endpoint, [string]$method, [object]$body, [string]$operation) {
        return [ErrorHelper]::SafeExecute({
            $this.log.Debug("REST-$operation", "Starting $method request to $endpoint")
            
            # Create auth headers using ErrorHelper
            $headersResult = $this.CreateAuthHeaders($authInfo)
            if (-not $headersResult.Success) {
                throw "Failed to create auth headers: $($headersResult.ErrorMessage)"
            }
            
            $headers = $headersResult.Data
            $fullUrl = "$($authInfo.Url)$endpoint"
            
            $this.log.Debug("REST-$operation", "URL: $fullUrl")
            $this.log.Debug("REST-$operation", "Method: $method")
            
            # Prepare request parameters
            $requestParams = @{
                Uri = $fullUrl
                Headers = $headers
                Method = $method
                ErrorAction = 'Stop'
            }
            
            # Add body if provided (for POST requests)
            if ($body) {
                # Add Content-Type header when we have a body
                $headers["Content-Type"] = "application/json"
                
                if ($body -is [string]) {
                    $requestParams['Body'] = $body
                    $this.log.Debug("REST-$operation", "Body length: $($body.Length) chars")
                } else {
                    $jsonBody = $body | ConvertTo-Json -Depth 10
                    $requestParams['Body'] = $jsonBody
                    $this.log.Debug("REST-$operation", "JSON body length: $($jsonBody.Length) chars")
                }
            }
            
            # Execute the REST API call
            $this.log.Debug("REST-$operation", "Executing REST API call...")
            $response = Invoke-RestMethod @requestParams
            
            $this.log.Debug("REST-$operation", "REST API call completed successfully")
            
            # Return the response
            return $response
            
        }, "REST-$operation", $this.log)
    }
    
    # Helper method to create auth headers (same as JiraService but isolated)
    hidden [hashtable] CreateAuthHeaders([hashtable]$authInfo) {
        return [ErrorHelper]::SafeExecute({
            $apiToken = [Runtime.InteropServices.Marshal]::PtrToStringAuto([Runtime.InteropServices.Marshal]::SecureStringToBSTR($authInfo.ApiToken))
            $base64AuthInfo = [Convert]::ToBase64String([Text.Encoding]::ASCII.GetBytes("$($authInfo.Username):$apiToken"))
            return @{
                "Authorization" = "Basic $base64AuthInfo"
                "Accept" = "application/json"
            }
        }, "CreateAuthHeaders", $this.log)
    }
}

class JiraService {
    $log
    $restClient
    [bool]$UseJiraPS = $false
    
    JiraService($loggingService) { 
        $this.log = $loggingService 
        $this.restClient = [JiraRestClient]::new($loggingService)
        # Check if JiraPS module is available
        try {
            $jiraPSModule = Get-Module -ListAvailable -Name JiraPS
            if ($jiraPSModule) {
                $this.UseJiraPS = $true
                $this.log.Info("JiraPS module detected, will use JiraPS commands")
            } else {
                $this.log.Info("JiraPS module not found, using mock data")
            }
        } catch {
            $this.log.Warn("Could not check for JiraPS module: $($_.Exception.Message)")
        }
    }
    
    [hashtable] TestAuthentication([hashtable]$authInfo) {
        return [ErrorHelper]::SafeExecute({
            $this.log.Debug("Authentication", "Testing Jira authentication for $($authInfo.Username)")
            
            # Use simple direct REST API approach (proven to work with SSO)
            $this.log.Debug("Authentication", "Using direct REST API for authentication")
            
            $jiraUrl = $authInfo.Url
            $username = $authInfo.Username
            $apiTokenSecure = $authInfo.ApiToken
            $apiToken = [Runtime.InteropServices.Marshal]::PtrToStringAuto([Runtime.InteropServices.Marshal]::SecureStringToBSTR($apiTokenSecure))
            
            # Create basic auth header (same approach as FIXED version)
            $base64AuthInfo = [Convert]::ToBase64String([Text.Encoding]::ASCII.GetBytes("$username`:$apiToken"))
            $headers = @{
                "Authorization" = "Basic $base64AuthInfo"
                "Accept" = "application/json"
            }
            
            # Test authentication with /myself endpoint (same as FIXED version)
            $response = Invoke-RestMethod -Uri "$jiraUrl/rest/api/3/myself" -Headers $headers -Method Get -ErrorAction Stop
            
            if ($response -and $response.accountId) {
                $this.log.Debug("Authentication", "Success! User: $($response.displayName), Email: $($response.emailAddress)")
                return @{
                    Success = $true
                    User = $response
                    DisplayName = $response.displayName
                    Email = $response.emailAddress
                    AccountId = $response.accountId
                }
            } else {
                throw "Invalid response from Jira authentication endpoint"
            }
        }, "JiraAuthentication", $this.log)
    }
    
    [hashtable] AuthenticateAndStore([string]$url, [string]$username, [string]$apiToken) {
        return [ErrorHelper]::SafeExecute({
            $this.log.Debug("Authentication", "Full authentication flow for $username")
            
            # Create secure string from API token
            $secureApiToken = ConvertTo-SecureString $apiToken -AsPlainText -Force
            
            # Create auth info structure
            $authInfo = @{
                Url = $url
                Username = $username
                ApiToken = $secureApiToken
            }
            
            # Test the authentication
            $testResult = $this.TestAuthentication($authInfo)
            if (-not $testResult.Success) {
                throw "Authentication test failed: $($testResult.ErrorMessage)"
            }
            
            return @{
                AuthInfo = $authInfo
                UserDetails = $testResult.Data
            }
        }, "AuthenticateAndStore", $this.log)
    }

    [hashtable] GetTicketDetails([string]$ticketId, [hashtable]$authInfo) {
        return [ErrorHelper]::SafeExecute({
            $this.log.Info("Fetching ticket details for $ticketId")
            
            if ($authInfo -and $authInfo.ApiToken) {
                $result = $this.GetTicketUsingRestAPI($ticketId, $authInfo)
            } elseif ($this.UseJiraPS) {
                $result = $this.GetTicketUsingJiraPS($ticketId)
            } else {
                $result = $this.GetTicketUsingMockData($ticketId)
            }
            
            # If the sub-method already returned error format, extract data or re-throw
            if ($result.Success -eq $false) {
                throw $result.ErrorMessage
            }
            
            return $result.Data
        }, "TicketFetch", $this.log)
    }
    
    [hashtable] GetTicketUsingRestAPI([string]$ticketId, [hashtable]$authInfo) {
        return [ErrorHelper]::SafeExecute({
            $this.log.Info("Using REST API to fetch ticket $ticketId")
            
            # Use unified REST client for ticket fetch
            $result = $this.restClient.InvokeJiraAPI($authInfo, "/rest/api/3/issue/$ticketId", "Get", $null, "TicketFetch")
            
            if (-not $result.Success) {
                throw "Ticket fetch REST call failed: $($result.ErrorMessage)"
            }
            
            $response = $result.Data
            if ($response) {
                $this.log.Debug("REST-TicketFetch", "Successfully fetched issue: $($response.key)")
                $this.log.Debug("REST-TicketFetch", "Summary: $($response.fields.summary)")
                
                # Convert to our format
                $ticketData = @{
                    "summary" = $response.fields.summary
                    "description" = $response.fields.description
                }
                
                # Extract custom fields
                $customFieldsFound = 0
                $customFieldProperties = $response.fields.PSObject.Properties | Where-Object { $_.Name -like "customfield_*" }
                
                foreach ($prop in $customFieldProperties) {
                    $value = $prop.Value
                    if ($value -and $value -ne $null) {
                        # Handle different value types
                        $displayValue = if ($value -is [string]) { 
                            $value 
                        } elseif ($value -is [System.Collections.Hashtable] -or $value.PSObject) {
                            if ($value.value) { $value.value }
                            elseif ($value.displayName) { $value.displayName }
                            elseif ($value.name) { $value.name }
                            else { $value.ToString() }
                        } else { 
                            $value.ToString() 
                        }
                        
                        $ticketData[$prop.Name] = $displayValue
                        $customFieldsFound++
                        $this.log.Debug("REST-TicketFetch", "Custom field $($prop.Name) = '$displayValue'")
                    }
                }
                
                $this.log.Debug("REST-TicketFetch", "Found $customFieldsFound custom fields via REST API")
                return $ticketData
            } else {
                throw "No data returned from Jira API"
            }
        }, "REST-TicketFetch", $this.log)
    }
    
    [hashtable] GetTicketUsingJiraPS([string]$ticketId) {
        try {
            $this.log.Info("Using JiraPS to fetch ticket $ticketId")
            
            # Import JiraPS module
            Import-Module JiraPS -Force
            
            # Check if we have an active session
            $session = Get-JiraSession -ErrorAction SilentlyContinue
            if (-not $session) {
                Write-Host "DEBUG: No active JiraPS session found. Authentication required." -ForegroundColor Yellow
                $this.log.Warn("No active JiraPS session. Please authenticate first with: Set-JiraConfigServer and New-JiraSession")
                throw "No active JiraPS session. Please authenticate first."
            }
            
            Write-Host "DEBUG: Active JiraPS session found for server: $($session.Server)" -ForegroundColor Green
            
            # Get the Jira issue using JiraPS
            Write-Host "DEBUG: Calling Get-JiraIssue for $ticketId" -ForegroundColor Cyan
            $issue = Get-JiraIssue -Key $ticketId -ErrorAction Stop
            
            if (-not $issue) {
                throw "No issue returned from Get-JiraIssue"
            }
            
            Write-Host "DEBUG: Successfully retrieved issue object" -ForegroundColor Green
            
            # Convert to our standard format
            $ticketData = @{
                summary = if ($issue.Summary) { $issue.Summary } else { "" }
                description = if ($issue.Description) { $issue.Description } else { "" }
            }
            
            Write-Host "DEBUG: Issue object retrieved successfully" -ForegroundColor Cyan
            
            # Try multiple ways to get custom fields
            $customFieldsFound = 0
            
            # Method 1: Check CustomFields property
            if ($issue.PSObject.Properties['CustomFields']) {
                Write-Host "DEBUG: CustomFields property exists" -ForegroundColor Cyan
                $customFields = $issue.CustomFields
                if ($customFields) {
                    Write-Host "DEBUG: CustomFields has $($customFields.Count) items" -ForegroundColor Cyan
                    foreach ($field in $customFields) {
                        if ($field -and $field.PSObject.Properties['Id'] -and $field.PSObject.Properties['Value']) {
                            Write-Host "DEBUG: CustomField - Id: $($field.Id), Value: $($field.Value)" -ForegroundColor Yellow
                            $ticketData[$field.Id] = $field.Value
                            $customFieldsFound++
                        }
                    }
                }
            } else {
                Write-Host "DEBUG: No CustomFields property found" -ForegroundColor Red
            }
            
            # Method 2: Check direct properties on issue object
            $customFieldProperties = $issue.PSObject.Properties | Where-Object { $_.Name -like "customfield_*" }
            Write-Host "DEBUG: Found $($customFieldProperties.Count) direct custom field properties" -ForegroundColor Cyan
            foreach ($prop in $customFieldProperties) {
                if ($prop.Value) {
                    Write-Host "DEBUG: Direct CustomField - Name: $($prop.Name), Value: $($prop.Value)" -ForegroundColor Yellow
                    $ticketData[$prop.Name] = $prop.Value
                    $customFieldsFound++
                }
            }
            
            # Method 3: Check Fields property
            if ($issue.PSObject.Properties['Fields'] -and $issue.Fields) {
                Write-Host "DEBUG: Fields property exists" -ForegroundColor Cyan
                $fieldsCustomFields = $issue.Fields.PSObject.Properties | Where-Object { $_.Name -like "customfield_*" }
                Write-Host "DEBUG: Found $($fieldsCustomFields.Count) custom fields in Fields property" -ForegroundColor Cyan
                foreach ($prop in $fieldsCustomFields) {
                    if ($prop.Value) {
                        Write-Host "DEBUG: Fields CustomField - Name: $($prop.Name), Value: $($prop.Value)" -ForegroundColor Yellow
                        $ticketData[$prop.Name] = $prop.Value
                        $customFieldsFound++
                    }
                }
            }
            
            # Method 4: Try accessing known custom fields directly
            $knownCustomFields = Get-AppConfig -Path "Jira.KnownCustomFields"
            foreach ($fieldId in $knownCustomFields) {
                try {
                    $value = $null
                    if ($issue.PSObject.Properties[$fieldId] -and $issue.PSObject.Properties[$fieldId].Value) {
                        $value = $issue.PSObject.Properties[$fieldId].Value
                    } elseif ($issue.Fields -and $issue.Fields.PSObject.Properties[$fieldId] -and $issue.Fields.PSObject.Properties[$fieldId].Value) {
                        $value = $issue.Fields.PSObject.Properties[$fieldId].Value
                    }
                    
                    if ($value) {
                        Write-Host "DEBUG: Direct access CustomField - $fieldId = $value" -ForegroundColor Green
                        $ticketData[$fieldId] = $value
                        $customFieldsFound++
                    }
                } catch {
                    # Continue silently
                }
            }
            
            # Method 5: Get comments and add them for fallback parsing
            try {
                Write-Host "DEBUG: Attempting to fetch comments..." -ForegroundColor Cyan
                $comments = Get-JiraComment -Issue $ticketId -ErrorAction Stop
                if ($comments) {
                    $allComments = ($comments | ForEach-Object { $_.Body }) -join "`n"
                    $ticketData["comments"] = $allComments
                    Write-Host "DEBUG: Added $($comments.Count) comments for fallback parsing" -ForegroundColor Cyan
                }
            } catch {
                Write-Host "DEBUG: Could not fetch comments: $($_.Exception.Message)" -ForegroundColor Yellow
            }
            
            Write-Host "DEBUG: Total custom fields found: $customFieldsFound" -ForegroundColor Green
            Write-Host "DEBUG: Final ticketData keys: $($ticketData.Keys -join ', ')" -ForegroundColor Green
            
            $this.log.Info("Successfully fetched ticket $ticketId using JiraPS")
            return @{ Success = $true; Data = $ticketData }
            
        } catch {
            $this.log.Error("JiraPS ticket fetch failed for $ticketId", $_.Exception)
            Write-Host "DEBUG: Exception details: $($_.Exception.Message)" -ForegroundColor Red
            Write-Host "DEBUG: Exception type: $($_.Exception.GetType().FullName)" -ForegroundColor Red
            if ($_.Exception.InnerException) {
                Write-Host "DEBUG: Inner exception: $($_.Exception.InnerException.Message)" -ForegroundColor Red
            }
            $this.log.Info("Falling back to mock data")
            return $this.GetTicketUsingMockData($ticketId)
        }
    }
    
    [hashtable] GetTicketUsingMockData([string]$ticketId) {
        $this.log.Info("Using mock data for ticket $ticketId")
        Start-Sleep -Milliseconds 800
        
        # Mock data based on real JERAGM Jira ticket structure
        # Using actual field IDs from your Jira system
        $mockData = @{ 
            summary = "Onboarding Request - Test User ($ticketId)"
            description = "New Joiner Name: Test User`nJob Title: Test Analyst`nDepartment: IT`nModel Account: testmodel"
            # Core onboarding fields (these are the exact IDs from your Jira)
            customfield_10304 = "Test"           # First Name
            customfield_10305 = "User"           # Last Name  
            customfield_10238 = "Test Analyst"   # Job Title
            customfield_10120 = "IT"             # Department
            customfield_10343 = "testmodel"      # Model Account
            customfield_10115 = "Singapore"      # Office Location
            customfield_10342 = "JERAGM Employee" # Employee Type
            customfield_10344 = "$(Get-Date -Format 'ddd, dd MMM yyyy HH:mm:ss +0000')" # Effective Date
        }
        return [ErrorHelper]::Success($mockData, "MockTicketFetch")
    }

    [hashtable] PostComment([string]$ticketId, [string]$comment, [hashtable]$authInfo) {
        try {
            $this.log.Info("Posting comment to ticket $ticketId")
            
            if ($authInfo -and $authInfo.ApiToken) {
                return $this.PostCommentUsingRestAPI($ticketId, $comment, $authInfo)
            } elseif ($this.UseJiraPS) {
                return $this.PostCommentUsingJiraPS($ticketId, $comment)
            } else {
                return $this.PostCommentUsingMock($ticketId, $comment)
            }
        } catch {
            $this.log.Error("Failed to post comment to ticket $ticketId", $_.Exception)
            return @{ Success = $false; ErrorMessage = $_.Exception.Message }
        }
    }
    
    [hashtable] PostCommentUsingRestAPI([string]$ticketId, [string]$comment, [hashtable]$authInfo) {
        return [ErrorHelper]::SafeExecute({
            $this.log.Info("Using REST API to post comment to $ticketId")
            
            # Create comment body structure
            $commentBody = @{
                body = @{
                    type = "doc"
                    version = 1
                    content = @(
                        @{
                            type = "paragraph"
                            content = @(
                                @{
                                    type = "text"
                                    text = $comment
                                }
                            )
                        }
                    )
                }
            }
            
            $this.log.Debug("REST-CommentPost", "Comment preview: $($comment.Substring(0, [Math]::Min(50, $comment.Length)))...")
            
            # Use unified REST client for comment posting
            $result = $this.restClient.InvokeJiraAPI($authInfo, "/rest/api/3/issue/$ticketId/comment", "Post", $commentBody, "CommentPost")
            
            if (-not $result.Success) {
                throw "Comment post REST call failed: $($result.ErrorMessage)"
            }
            
            $response = $result.Data
            if ($response -and $response.id) {
                $this.log.Debug("REST-CommentPost", "Comment posted successfully with ID: $($response.id)")
                return @{ CommentId = $response.id }
            } else {
                throw "Invalid response from Jira comment API"
            }
        }, "REST-CommentPost", $this.log)
    }
    
    [hashtable] PostCommentUsingJiraPS([string]$ticketId, [string]$comment) {
        try {
            $this.log.Info("Using JiraPS to post comment to $ticketId")
            
            # Add comment using JiraPS
            Add-JiraComment -Issue $ticketId -Comment $comment
            
            $this.log.Info("Successfully posted comment to $ticketId using JiraPS")
            return @{ Success = $true }
            
        } catch {
            $this.log.Error("JiraPS comment posting failed for $ticketId", $_.Exception)
            $this.log.Info("Falling back to mock comment posting")
            return $this.PostCommentUsingMock($ticketId, $comment)
        }
    }
    
    [hashtable] PostCommentUsingMock([string]$ticketId, [string]$comment) {
        $this.log.Info("MOCK: Comment posted to ticket $ticketId")
        Start-Sleep -Milliseconds 300
        return @{ Success = $true }
    }
}

class ActiveDirectoryService {
    [bool]$Simulate
    $log

    ActiveDirectoryService([bool]$simulateMode, $loggingService) { 
        $this.Simulate = $simulateMode
        $this.log = $loggingService
    }

    [hashtable] DoesUserExist([string]$samAccountName) {
        return [ErrorHelper]::SafeExecute({
            $this.log.Info("Checking for existence of user $samAccountName")
            # MOCK IMPLEMENTATION - Replace with actual Get-ADUser call
            # $user = Get-ADUser -Identity $samAccountName -ErrorAction SilentlyContinue
            # return $user -ne $null
            return $false  # Mock: user doesn't exist
        }, "UserExistenceCheck", $this.log)
    }

    [hashtable] CreateUser([hashtable]$userDetails) {
        if ($this.Simulate) {
            $this.log.Info("SIMULATION: Creating user $($userDetails.SamAccountName)")
            Start-Sleep -Milliseconds 500
            return [ErrorHelper]::Success("SIMULATION: User created successfully.", "UserCreation-Simulation")
        }
        
        return [ErrorHelper]::SafeExecute({
            $this.log.Info("Creating AD User $($userDetails.SamAccountName)")
            # New-ADUser @userDetails
            # Mock success for now
            return "User $($userDetails.SamAccountName) created successfully."
        }, "UserCreation", $this.log)
    }
}

# SECTION 2: STATE MANAGEMENT CLASS
class AppState {
    # Direct properties - simpler approach
    [string]$StatusMessage = "Ready."
    [bool]$IsBusy = $false
    [string[]]$ValidationErrors = @()
    [int]$CurrentStepIndex = 0
    [hashtable]$StepValidationStatus = @{ 0 = $false; 1 = $false; 2 = $false; 3 = $false }
    [string]$JiraUrlInput = (Get-AppConfig -Path "Jira.DefaultUrl")
    [string]$JiraUsernameInput = ""
    [securestring]$ApiTokenInput = $null
    [string]$TicketIdInput = ""
    [hashtable]$CurrentOnboardingData = @{}
    [string]$SelectedOU = ""
    [bool]$IsJiraAuthenticated = $false
    [string]$AuthenticationStatus = "Not authenticated"
    
    # Property change event handlers
    hidden [System.Collections.Generic.List[scriptblock]]$PropertyChangedHandlers = [System.Collections.Generic.List[scriptblock]]::new()
    
    [void] AddPropertyChangedHandler([scriptblock]$handler) {
        $this.PropertyChangedHandlers.Add($handler)
    }
    
    [void] NotifyPropertyChanged([string]$propertyName) {
        Write-Host "DEBUG: [PROPERTY CHANGE] NotifyPropertyChanged called for: '$propertyName'" -ForegroundColor DarkMagenta
        foreach ($handler in $this.PropertyChangedHandlers) {
            try {
                Write-Host "DEBUG: [PROPERTY CHANGE] Executing handler for: '$propertyName'" -ForegroundColor DarkMagenta
                & $handler $propertyName
                Write-Host "DEBUG: [PROPERTY CHANGE] Handler completed for: '$propertyName'" -ForegroundColor DarkMagenta
            } catch {
                Write-Warning "Property change handler error for '$propertyName': $($_.Exception.Message)"
            }
        }
    }
    
    # Helper methods to trigger property change notifications
    [void] SetStatusMessage([string]$value) { 
        Write-Host "DEBUG: [STATE] Setting StatusMessage: '$value'" -ForegroundColor Magenta
        $this.StatusMessage = $value
        $this.NotifyPropertyChanged('StatusMessage')
    }
    
    [void] SetIsBusy([bool]$value) { 
        Write-Host "DEBUG: [STATE] Setting IsBusy: $value" -ForegroundColor Magenta
        $this.IsBusy = $value
        $this.NotifyPropertyChanged('IsBusy')
    }
    
    [void] SetCurrentStepIndex([int]$value) { 
        Write-Host "DEBUG: [STATE] Setting CurrentStepIndex: $value" -ForegroundColor Magenta
        $this.CurrentStepIndex = $value
        $this.NotifyPropertyChanged('CurrentStepIndex')
    }
    
    [void] SetCurrentOnboardingData([hashtable]$value) { 
        Write-Host "DEBUG: [STATE] Setting CurrentOnboardingData with $($value.Keys.Count) keys: $($value.Keys -join ', ')" -ForegroundColor Magenta
        $this.CurrentOnboardingData = $value
        $this.NotifyPropertyChanged('CurrentOnboardingData')
    }
    
    [void] SetSelectedOU([string]$value) { 
        Write-Host "DEBUG: [STATE] Setting SelectedOU: '$value'" -ForegroundColor Magenta
        $this.SelectedOU = $value
        $this.NotifyPropertyChanged('SelectedOU')
    }
    
    # Additional setter methods for input fields
    [void] SetJiraUrlInput([string]$value) { 
        Write-Host "DEBUG: [STATE] Setting JiraUrlInput: '$value'" -ForegroundColor Magenta
        $this.JiraUrlInput = $value
        $this.NotifyPropertyChanged('JiraUrlInput')
    }
    
    [void] SetJiraUsernameInput([string]$value) { 
        Write-Host "DEBUG: [STATE] Setting JiraUsernameInput: '$value'" -ForegroundColor Magenta
        $this.JiraUsernameInput = $value
        $this.NotifyPropertyChanged('JiraUsernameInput')
    }
    
    [void] SetTicketIdInput([string]$value) { 
        Write-Host "DEBUG: [STATE] Setting TicketIdInput: '$value'" -ForegroundColor Magenta
        $this.TicketIdInput = $value
        $this.NotifyPropertyChanged('TicketIdInput')
    }
    
    [void] SetIsJiraAuthenticated([bool]$value) { 
        Write-Host "DEBUG: [STATE] Setting IsJiraAuthenticated: $value" -ForegroundColor Magenta
        $this.IsJiraAuthenticated = $value
        $this.NotifyPropertyChanged('IsJiraAuthenticated')
    }
    
    [void] SetAuthenticationStatus([string]$value) { 
        Write-Host "DEBUG: [STATE] Setting AuthenticationStatus: '$value'" -ForegroundColor Magenta
        $this.AuthenticationStatus = $value
        $this.NotifyPropertyChanged('AuthenticationStatus')
    }
}

# SECTION 3: VIEWMODEL CLASS
class WizardViewModel {
    $log
    $config
    $jira
    $ad
    $state
    $view

    WizardViewModel($loggingService, $configService, $jiraService, $adService, $appState) {
        $this.log = $loggingService
        $this.config = $configService
        $this.jira = $jiraService
        $this.ad = $adService
        $this.state = $appState
    }

    # This method is called by the View to link them
    RegisterView($view) {
        $this.view = $view
    }

    # --- Private Helper Methods ---
    hidden RunAsync($scriptBlock, $onSuccess, $onFailure, $argumentList = @()) {
        $this.log.Debug("RunAsync", "Called, IsBusy: $($this.state.IsBusy)")
        if ($this.state.IsBusy) { 
            $this.log.Debug("RunAsync", "Already busy, returning")
            return 
        }
        $this.state.SetIsBusy($true)
        $this.log.Debug("RunAsync", "Set IsBusy to true")

        try {
            $this.log.Debug("RunAsync", "About to call AsyncOperationHelper.RunAsync")
            # Use the AsyncOperationHelper for improved threading
            $capturedSuccess = $onSuccess
            $capturedFailure = $onFailure
            $capturedState = $this.state
            
            [AsyncOperationHelper]::RunAsync(
                $scriptBlock,
                {
                    param($result)
                    try {
                        $this.log.Debug("RunAsync", "Completion handler called")
                        $capturedState.SetIsBusy($false)
                        if ($capturedSuccess) {
                            $this.log.Debug("RunAsync", "About to call success handler")
                            & $capturedSuccess $result
                        }
                    } catch {
                        Write-Warning "Success handler failed: $($_.Exception.Message)"
                        $capturedState.SetIsBusy($false)
                    }
                }.GetNewClosure(),
                $this.view.window.Dispatcher,
                $argumentList
            )
            $this.log.Debug("RunAsync", "AsyncOperationHelper.RunAsync call completed")
        } catch {
            $this.log.Debug("RunAsync", "Exception in RunAsync: $($_.Exception.Message)")
            $this.state.SetIsBusy($false)
            if ($onFailure) {
                & $onFailure $_.Exception.Message
            }
        }
    }

    hidden [hashtable] ParseTicketData($jiraTicket) {
        $parsedData = @{}
        $mappings = $this.config.GetJiraFieldMappings()
        
        Write-Host "DEBUG: Parsing ticket data..." -ForegroundColor Cyan
        Write-Host "DEBUG: Available data keys: $($jiraTicket.Data.Keys -join ', ')" -ForegroundColor Cyan
        
        # First try to get data from custom fields
        foreach ($key in $mappings.Keys) {
            $fieldId = $mappings[$key].CustomFieldId
            Write-Host "DEBUG: Looking for $key in field $fieldId" -ForegroundColor Gray
            if ($jiraTicket.Data.ContainsKey($fieldId)) {
                $value = $jiraTicket.Data[$fieldId]
                Write-Host "DEBUG: Found $key = '$value'" -ForegroundColor Green
                $parsedData[$key] = $value
            } else {
                Write-Host "DEBUG: Field $fieldId not found for $key" -ForegroundColor Red
            }
        }
        
        # If we didn't get the required fields, try parsing from comments or description
        if ($parsedData.Count -lt 4) {
            Write-Host "DEBUG: Custom fields incomplete, trying comment parsing..." -ForegroundColor Yellow
            $parsedFromComments = $this.ParseFromComments($jiraTicket.Data)
            foreach ($key in $parsedFromComments.Keys) {
                if (-not $parsedData.ContainsKey($key)) {
                    $parsedData[$key] = $parsedFromComments[$key]
                    Write-Host "DEBUG: Added from comments: $key = '$($parsedFromComments[$key])'" -ForegroundColor Green
                }
            }
        }
        
        Write-Host "DEBUG: Final parsed data: $($parsedData.Keys -join ', ')" -ForegroundColor Cyan
        return $parsedData
    }
    
    hidden [hashtable] ParseFromComments($ticketData) {
        $parsedData = @{}
        
        try {
            # Look for structured data in description or comments
            $textToSearch = ""
            if ($ticketData.ContainsKey('description')) {
                $textToSearch += $ticketData.description + "`n"
            }
            if ($ticketData.ContainsKey('comments')) {
                $textToSearch += $ticketData.comments + "`n"
            }
            
            Write-Host "DEBUG: Searching in text content for structured data..." -ForegroundColor Yellow
            
            # Parse using regex patterns
            $patterns = @{
                FirstName = @("New Joiner Name:\s*([^\s]+)", "First Name:\s*([^\r\n]+)")
                LastName = @("New Joiner Name:\s*\S+\s+(\S+)", "Last Name:\s*([^\r\n]+)")
                JobTitle = @("Job Title:\s*([^\r\n]+)")
                Department = @("Department:\s*([^\r\n]+)")
                ModelAccount = @("Model Account:\s*([^\r\n]+)")
            }
            
            foreach ($field in $patterns.Keys) {
                foreach ($pattern in $patterns[$field]) {
                    if ($textToSearch -match $pattern) {
                        $value = $matches[1].Trim()
                        if (![string]::IsNullOrEmpty($value)) {
                            $parsedData[$field] = $value
                            Write-Host "DEBUG: Regex found $field = '$value'" -ForegroundColor Green
                            break
                        }
                    }
                }
            }
            
        } catch {
            Write-Host "DEBUG: Comment parsing failed: $($_.Exception.Message)" -ForegroundColor Red
        }
        
        return $parsedData
    }

    # --- Public Commands ---
    AuthenticateJira() {
        $this.log.Info("AuthenticateJira command executed.")
        $this.state.SetStatusMessage("Authenticating with Jira...")
        $this.state.SetIsBusy($true)
        
        try {
            # Get API token from the UI
            $apiToken = $this.view.controls.apiTokenBox.Password
            if ([string]::IsNullOrEmpty($apiToken)) {
                throw "API Token is required for authentication"
            }
            
            # Test authentication by making a simple API call (same as FIXED version)
            $jiraUrl = $this.state.JiraUrlInput
            $username = $this.state.JiraUsernameInput
            
            Write-Host "DEBUG: Testing Jira authentication..." -ForegroundColor Cyan
            Write-Host "DEBUG: URL: $jiraUrl" -ForegroundColor Gray
            Write-Host "DEBUG: Username: $username" -ForegroundColor Gray
            
            # Create basic auth header (exact same as FIXED version)
            $base64AuthInfo = [Convert]::ToBase64String([Text.Encoding]::ASCII.GetBytes("$username`:$apiToken"))
            $headers = @{
                "Authorization" = "Basic $base64AuthInfo"
                "Accept" = "application/json"
            }
            
            # Test authentication with /myself endpoint (exact same as FIXED version)
            $response = Invoke-RestMethod -Uri "$jiraUrl/rest/api/3/myself" -Headers $headers -Method Get -ErrorAction Stop
            
            if ($response -and $response.accountId) {
                $this.state.SetIsJiraAuthenticated($true)
                $this.state.SetAuthenticationStatus("✅ Authenticated as $($response.displayName)")
                $this.state.SetStatusMessage("Successfully authenticated with Jira!")
                $this.log.Info("Jira authentication successful for user: $($response.displayName)")
                Write-Host "DEBUG: Authentication successful!" -ForegroundColor Green
                Write-Host "DEBUG: User: $($response.displayName)" -ForegroundColor Green
                Write-Host "DEBUG: Email: $($response.emailAddress)" -ForegroundColor Green
                
                # Store credentials for future use
                $this.state.ApiTokenInput = ConvertTo-SecureString $apiToken -AsPlainText -Force
            } else {
                throw "Invalid response from Jira authentication"
            }
            
        } catch {
            $this.state.SetIsJiraAuthenticated($false)
            $this.state.SetAuthenticationStatus("❌ Authentication failed")
            $this.state.SetStatusMessage("Authentication failed: $($_.Exception.Message)")
            $this.log.Error("Jira authentication failed", $_.Exception)
            Write-Host "DEBUG: Authentication failed: $($_.Exception.Message)" -ForegroundColor Red
        } finally {
            $this.state.SetIsBusy($false)
            # Update UI authentication status
            $this.UpdateAuthenticationStatusDisplay()
        }
    }
    
    # Helper method to update authentication status in UI
    hidden UpdateAuthenticationStatusDisplay() {
        if ($this.view -and $this.view.controls.authStatusLabel) {
            $this.view.controls.authStatusLabel.Content = "Status: $($this.state.AuthenticationStatus)"
            if ($this.state.IsJiraAuthenticated) {
                $appConfig = Get-AppConfig
                $this.view.controls.authStatusLabel.Foreground = $appConfig.UI.Colors.Success  # Green
                # Enable fetch button when authenticated
                if ($this.view.controls.fetchButton) {
                    $this.view.controls.fetchButton.IsEnabled = $true
                    $this.view.controls.fetchButton.Background = $appConfig.UI.Colors.Primary  # Blue
                    $this.view.controls.fetchButton.Foreground = "White"
                }
            } else {
                $this.view.controls.authStatusLabel.Foreground = "#F44336"  # Red
                # Disable fetch button when not authenticated
                if ($this.view.controls.fetchButton) {
                    $this.view.controls.fetchButton.IsEnabled = $false
                    $appConfig = Get-AppConfig
                    $this.view.controls.fetchButton.Background = $appConfig.UI.Colors.Disabled  # Gray
                    $this.view.controls.fetchButton.Foreground = "#666666"
                }
            }
        }
    }
    
    FetchTicketDetails() {
        $this.log.Info("FetchTicketDetails command executed.")
        
        # Check if authenticated first
        if (-not $this.state.IsJiraAuthenticated) {
            $this.state.SetStatusMessage("Please authenticate with Jira first using the Login button.")
            $this.log.Warn("Attempted to fetch ticket without authentication")
            return
        }
        
        $this.state.SetStatusMessage("Fetching ticket $($this.state.TicketIdInput)...")
        $this.state.SetIsBusy($true)

        # Simplified approach - run synchronously for now to avoid threading issues
        try {
            $ticketId = $this.state.TicketIdInput
            Write-Host "DEBUG: FetchTicketDetails called with ticket ID: '$ticketId'" -ForegroundColor Magenta
            Write-Host "DEBUG: Ticket ID length: $($ticketId.Length)" -ForegroundColor Magenta
            Write-Host "DEBUG: Ticket ID is null or empty: $([string]::IsNullOrEmpty($ticketId))" -ForegroundColor Magenta
            
            $this.log.Debug("TicketFetch", "Starting ticket fetch for: $($this.state.TicketIdInput)")
            
            # Prepare authentication info
            $authInfo = $null
            if ($this.state.IsJiraAuthenticated -and $this.state.ApiTokenInput) {
                $authInfo = @{
                    Url = $this.state.JiraUrlInput
                    Username = $this.state.JiraUsernameInput
                    ApiToken = $this.state.ApiTokenInput
                }
            }
            
            $result = $this.jira.GetTicketDetails($ticketId, $authInfo)
            $this.log.Debug("TicketFetch", "Result - Success: $($result.Success)")
            
            if ($result.Success) {
                $this.state.SetStatusMessage("Ticket fetched successfully.")
                $parsedData = $this.ParseTicketData($result)
                $this.log.Debug("TicketFetch", "Parsed data keys: $($parsedData.Keys -join ', ')")
                $this.state.SetCurrentOnboardingData($parsedData)
                $this.state.StepValidationStatus[0] = $true
                $this.log.Debug("TicketFetch", "Step 0 validation set to TRUE")
            } else {
                $this.state.SetStatusMessage("Error: $($result.ErrorMessage)")
                $this.state.StepValidationStatus[0] = $false
                $this.log.Debug("TicketFetch", "Step 0 validation set to FALSE due to error")
            }
        } catch {
            $this.log.Debug("TicketFetch", "Exception in FetchTicketDetails: $($_.Exception.Message)")
            $this.state.SetStatusMessage("Error: $($_.Exception.Message)")
            $this.state.StepValidationStatus[0] = $false
        } finally {
            $this.state.SetIsBusy($false)
        }
    }

    ValidateUserDetails() {
        $this.log.Info("ValidateUserDetails command executed.")
        $this.state.SetStatusMessage("Validating user details...")
        
        $data = $this.state.CurrentOnboardingData
        $errors = @()
        
        # Ensure we have the required data
        if ($null -eq $data -or $data.Count -eq 0) {
            $this.state.SetStatusMessage("No user data found. Please fetch ticket details first.")
            return
        }
        
        # Validate first name
        if ($data.ContainsKey('FirstName')) {
            $fnValidation = $this.config.ValidateField('FirstName', $data.FirstName)
            if (-not $fnValidation.IsValid) { $errors += $fnValidation.ErrorMessage }
        } else {
            $errors += "First name is required"
        }
        
        # Validate last name
        if ($data.ContainsKey('LastName')) {
            $lnValidation = $this.config.ValidateField('LastName', $data.LastName)
            if (-not $lnValidation.IsValid) { $errors += $lnValidation.ErrorMessage }
        } else {
            $errors += "Last name is required"
        }
        
        # Check if user already exists (if we have both names)
        if ($data.ContainsKey('FirstName') -and $data.ContainsKey('LastName')) {
            $suggestedUsername = $this.GenerateUsername($data.FirstName, $data.LastName)
            $userExistsResult = $this.ad.DoesUserExist($suggestedUsername)
            
            if ($userExistsResult.Success -and $userExistsResult.Exists) {
                $errors += "User with username '$suggestedUsername' already exists"
            }
        }
        
        if ($errors.Count -eq 0) {
            $this.state.SetStatusMessage("Validation successful. Proceed to next step.")
            $this.state.StepValidationStatus[1] = $true
        } else {
            $this.state.SetStatusMessage("Validation failed: $($errors -join '; ')")
            $this.state.ValidationErrors = $errors
            $this.state.StepValidationStatus[1] = $false
        }
    }

    CreateUserAccount() {
        $this.log.Info("CreateUserAccount command executed.")
        $this.state.SetStatusMessage("Creating user account...")
        $this.state.SetIsBusy($true)
        
        try {
            $data = $this.state.CurrentOnboardingData
            
            # Ensure we have the required data
            if ($null -eq $data -or $data.Count -eq 0) {
                $this.state.SetStatusMessage("No user data found. Please complete previous steps first.")
                return
            }
            
            if (-not $data.ContainsKey('FirstName') -or -not $data.ContainsKey('LastName')) {
                $this.state.SetStatusMessage("First name and last name are required.")
                return
            }
            
            $username = $this.GenerateUsername($data.FirstName, $data.LastName)
            
            # Determine OU based on office location from Jira
            $targetOU = $this.state.SelectedOU
            if ([string]::IsNullOrEmpty($targetOU) -and $data.ContainsKey('OfficeLocation')) {
                $targetOU = $this.config.GetOUForLocation($data.OfficeLocation)
                $this.state.SetSelectedOU($targetOU)
                Write-Host "DEBUG: Auto-selected OU based on office location '$($data.OfficeLocation)': $targetOU" -ForegroundColor Green
            }
            
            if ([string]::IsNullOrEmpty($targetOU)) {
                $targetOU = Get-AppConfig -Path "ActiveDirectory.DefaultOU"
                $this.state.SetSelectedOU($targetOU)
                Write-Host "DEBUG: Using default OU: $targetOU" -ForegroundColor Yellow
            }
            
            $domain = Get-AppConfig -Path "ActiveDirectory.Domain"
            $company = Get-AppConfig -Path "ActiveDirectory.Company"
            $defaultPassword = Get-AppConfig -Path "ActiveDirectory.DefaultPassword"
            
            $userDetails = @{
                GivenName = $data.FirstName
                Surname = $data.LastName
                Name = "$($data.FirstName) $($data.LastName)"
                SamAccountName = $username
                UserPrincipalName = "$username@$domain"
                Title = if ($data.ContainsKey('JobTitle')) { $data.JobTitle } else { "" }
                Department = if ($data.ContainsKey('Department')) { $data.Department } else { "" }
                Company = $company
                Path = $targetOU
                AccountPassword = (ConvertTo-SecureString $defaultPassword -AsPlainText -Force)
                ChangePasswordAtLogon = $true
                Enabled = $true
            }

            $this.log.Debug("UserCreation", "Creating user with details - Username: $username, Full Name: $($userDetails.Name), Department: $($userDetails.Department), OU: $targetOU")
            
            $result = $this.ad.CreateUser($userDetails)
            
            if ($result.Success) {
                $this.state.SetStatusMessage("User account created successfully!")
                $this.state.StepValidationStatus[2] = $true
                $this.log.SecurityAudit("User Creation", "SUCCESS: $username")
                $this.log.Debug("UserCreation", "Step 2 validation set to TRUE")
            } else {
                $errorMsg = if ($result.ErrorMessage) { $result.ErrorMessage } else { "Unknown error occurred" }
                $this.state.SetStatusMessage("Error creating user: $errorMsg")
                $this.state.StepValidationStatus[2] = $false
                $this.log.SecurityAudit("User Creation", "FAILED: $username")
                $this.log.Debug("UserCreation", "Step 2 validation set to FALSE - Error: $errorMsg")
                
                # Log additional details if available
                if ($result.Operation) {
                    $this.log.Debug("UserCreation", "Failed operation: $($result.Operation)")
                }
            }
        } catch {
            $this.log.Debug("UserCreation", "Exception in CreateUserAccount: $($_.Exception.Message)")
            $this.state.SetStatusMessage("Error: $($_.Exception.Message)")
            $this.state.StepValidationStatus[2] = $false
        } finally {
            $this.state.SetIsBusy($false)
        }
    }

    PostJiraComment() {
        $this.log.Info("PostJiraComment command executed.")
        $this.state.SetStatusMessage("Posting comment to Jira ticket...")
        
        $data = $this.state.CurrentOnboardingData
        $username = $this.GenerateUsername($data.FirstName, $data.LastName)
        
        $comment = @"
User onboarding completed:
- Name: $($data.FirstName) $($data.LastName)
- Username: $username
- Department: $($data.Department)
- Job Title: $($data.JobTitle)
- OU: $($this.state.SelectedOU)

Account has been created and is ready for use.
"@

        $scriptBlock = { 
            param($ticketId, $comment, $authInfo, $logService) 
            try {
                Write-Host "DEBUG: Background script - Starting comment post" -ForegroundColor Cyan
                Write-Host "DEBUG: Background script - TicketId: $ticketId" -ForegroundColor Cyan
                Write-Host "DEBUG: Background script - AuthInfo null?: $($null -eq $authInfo)" -ForegroundColor Cyan
                
                if ($null -eq $authInfo) {
                    Write-Host "DEBUG: Background script - No auth info, using mock" -ForegroundColor Yellow
                    Start-Sleep -Milliseconds 300
                    return @{ Success = $true; Message = "Mock comment posted" }
                }
                
                # Execute REST API call directly in background  
                Write-Host "DEBUG: Background REST-CommentPost - Using REST API to post comment" -ForegroundColor Cyan
                
                # Extract credentials
                $jiraUrl = $authInfo.Url
                $username = $authInfo.Username
                $apiTokenSecure = $authInfo.ApiToken
                $apiToken = [Runtime.InteropServices.Marshal]::PtrToStringAuto([Runtime.InteropServices.Marshal]::SecureStringToBSTR($apiTokenSecure))
                
                # Create auth header (simplified from unified pattern)
                $base64AuthInfo = [Convert]::ToBase64String([Text.Encoding]::ASCII.GetBytes("$username`:$apiToken"))
                $headers = @{
                    "Authorization" = "Basic $base64AuthInfo"
                    "Accept" = "application/json"
                    "Content-Type" = "application/json"
                }
                
                # Create comment body (simplified from unified pattern)
                $commentBody = @{
                    body = @{
                        type = "doc"
                        version = 1
                        content = @(
                            @{
                                type = "paragraph"
                                content = @(
                                    @{
                                        type = "text"
                                        text = $comment
                                    }
                                )
                            }
                        )
                    }
                } | ConvertTo-Json -Depth 10
                
                Write-Host "DEBUG: Background REST-CommentPost - URL: $jiraUrl/rest/api/3/issue/$ticketId/comment" -ForegroundColor Gray
                Write-Host "DEBUG: Background REST-CommentPost - Comment preview: $($comment.Substring(0, [Math]::Min(50, $comment.Length)))..." -ForegroundColor Gray
                
                # Post the comment
                $response = Invoke-RestMethod -Uri "$jiraUrl/rest/api/3/issue/$ticketId/comment" -Headers $headers -Method Post -Body $commentBody -ErrorAction Stop
                
                if ($response -and $response.id) {
                    Write-Host "DEBUG: Background REST-CommentPost - Comment posted successfully with ID: $($response.id)" -ForegroundColor Green
                    return @{ Success = $true; CommentId = $response.id }
                } else {
                    throw "Invalid response from Jira comment API"
                }
                
            } catch {
                Write-Host "DEBUG: Background REST-CommentPost - Exception: $($_.Exception.Message)" -ForegroundColor Red
                Write-Host "DEBUG: Background REST-CommentPost - Exception Type: $($_.Exception.GetType().Name)" -ForegroundColor Red
                # Return mock result as fallback
                Write-Host "DEBUG: Background REST-CommentPost - Falling back to mock" -ForegroundColor Yellow
                Start-Sleep -Milliseconds 300
                return @{ Success = $true; Message = "Mock comment posted (REST API failed: $($_.Exception.Message))" }
            }
        }

        $onSuccess = {
            param($result)
            Write-Host "DEBUG: PostJiraComment onSuccess called" -ForegroundColor Green
            try {
                if ($result.Success) {
                    if ($result.CommentId) {
                        $this.state.SetStatusMessage("Comment posted to Jira successfully! (ID: $($result.CommentId))")
                        Write-Host "DEBUG: Comment posted with ID: $($result.CommentId)" -ForegroundColor Green
                    } else {
                        $this.state.SetStatusMessage("Comment posted to Jira successfully!")
                        Write-Host "DEBUG: Comment posted successfully (mock mode)" -ForegroundColor Green
                    }
                    $this.state.StepValidationStatus[3] = $true
                } else {
                    $this.state.SetStatusMessage("Error posting comment: $($result.ErrorMessage)")
                    Write-Host "DEBUG: Comment posting failed: $($result.ErrorMessage)" -ForegroundColor Red
                }
            } catch {
                Write-Host "ERROR: Exception in onSuccess callback: $($_.Exception.Message)" -ForegroundColor Red
                $this.state.SetStatusMessage("Error processing comment result: $($_.Exception.Message)")
            } finally {
                # CRITICAL: Always re-enable UI regardless of success/failure
                $this.state.SetIsBusy($false)
                Write-Host "DEBUG: IsBusy set to false, UI should be re-enabled" -ForegroundColor Green
            }
        }.GetNewClosure()

        $onFailure = { 
            param($reason) 
            $this.state.SetStatusMessage("Failed to post comment: $reason")
            $this.state.SetIsBusy($false)
        }.GetNewClosure()

        # Prepare authentication info for comment posting
        $authInfo = $null
        if ($this.state.IsJiraAuthenticated -and $this.state.ApiTokenInput) {
            $authInfo = @{
                Url = $this.state.JiraUrlInput
                Username = $this.state.JiraUsernameInput
                ApiToken = $this.state.ApiTokenInput
            }
            Write-Host "DEBUG: Using authenticated REST API for comment posting" -ForegroundColor Green
        } else {
            Write-Host "DEBUG: No authentication available, will use fallback method" -ForegroundColor Yellow
        }

        $argumentList = @($this.state.TicketIdInput, $comment, $authInfo, $this.log)
        $this.RunAsync($scriptBlock, $onSuccess, $onFailure, $argumentList)
    }

    hidden [string] GenerateUsername([string]$firstName, [string]$lastName) {
        # Generate username as first initial + last name, max 20 chars
        if ([string]::IsNullOrEmpty($firstName) -or [string]::IsNullOrEmpty($lastName)) {
            return "tempuser"
        }
        
        $cleanFirstName = $firstName -replace '[^a-zA-Z]', ''
        $cleanLastName = $lastName -replace '[^a-zA-Z]', ''
        
        if ($cleanFirstName.Length -eq 0 -or $cleanLastName.Length -eq 0) {
            return "tempuser"
        }
        
        $username = ($cleanFirstName.Substring(0,1) + $cleanLastName).ToLower()
        if ($username.Length -gt 20) {
            $username = $username.Substring(0, 20)
        }
        return $username
    }

    GoToNextStep() {
        Write-Host "DEBUG: GoToNextStep called. Current step: $($this.state.CurrentStepIndex)" -ForegroundColor Magenta
        Write-Host "DEBUG: Step 0 validation: $($this.state.StepValidationStatus[0])" -ForegroundColor Magenta
        Write-Host "DEBUG: Step 1 validation: $($this.state.StepValidationStatus[1])" -ForegroundColor Magenta
        Write-Host "DEBUG: Step 2 validation: $($this.state.StepValidationStatus[2])" -ForegroundColor Magenta
        Write-Host "DEBUG: Step 3 validation: $($this.state.StepValidationStatus[3])" -ForegroundColor Magenta
        
        $currentStatus = $this.state.StepValidationStatus
        if ($currentStatus[$this.state.CurrentStepIndex]) {
            Write-Host "DEBUG: Current step is valid, proceeding to next step" -ForegroundColor Green
            if ($this.state.CurrentStepIndex -lt 3) {
                $newStep = $this.state.CurrentStepIndex + 1
                Write-Host "DEBUG: Moving to step $newStep" -ForegroundColor Green
                $this.state.SetCurrentStepIndex($newStep)
                
                # Auto-validate Step 1 (Review) if we have required data
                if ($newStep -eq 1 -and $this.state.CurrentOnboardingData.Count -gt 0) {
                    $requiredFields = @('FirstName', 'LastName', 'Department', 'JobTitle')
                    $hasAllRequired = $true
                    foreach ($field in $requiredFields) {
                        if (-not $this.state.CurrentOnboardingData[$field]) {
                            $hasAllRequired = $false
                            break
                        }
                    }
                    if ($hasAllRequired) {
                        $this.state.StepValidationStatus[1] = $true
                        Write-Host "DEBUG: Auto-validated Step 1 - required data present" -ForegroundColor Green
                    }
                }
            } else {
                Write-Host "DEBUG: On final step, closing window" -ForegroundColor Green
                $this.view.window.Close()
            }
        } else {
            Write-Host "DEBUG: Current step is NOT valid, cannot proceed" -ForegroundColor Red
            $this.state.SetStatusMessage("Please complete the current step correctly before proceeding.")
        }
    }

    GoToPreviousStep() {
        if ($this.state.CurrentStepIndex -gt 0) {
            $this.state.SetCurrentStepIndex($this.state.CurrentStepIndex - 1)
        }
    }
}
# SECTION 4: VIEW CLASS

# UIFactory - Consolidated UI element creation with standardized styling
class UIFactory {
    static $AppConfig = (Get-AppConfig)
    
    # Create standardized title label (large, bold text)
    static [object] CreateTitleLabel([string]$content) {
        $label = New-Object System.Windows.Controls.Label
        $label.Content = $content
        $label.FontSize = 16
        $label.FontWeight = 'Bold'
        return $label
    }
    
    # Create standardized content label (normal text)
    static [object] CreateLabel([string]$content, [string]$margin = "0") {
        $label = New-Object System.Windows.Controls.Label
        $label.Content = $content
        if ($margin -ne "0") {
            $label.Margin = $margin
        }
        return $label
    }
    
    # Create error label with red styling
    static [object] CreateErrorLabel([string]$content, [string]$margin = "0,10,0,0") {
        $label = New-Object System.Windows.Controls.Label
        $label.Content = $content
        $label.Margin = $margin
        $label.FontSize = 12
        $label.Foreground = "#CC0000"
        $label.TextWrapping = 'Wrap'
        return $label
    }
    
    # Create standardized action button (green style)
    static [object] CreateActionButton([string]$content, [string]$margin = "0,10,0,0") {
        $button = New-Object System.Windows.Controls.Button
        $button.Content = $content
        $button.Margin = $margin
        $button.Height = 35
        $button.MinWidth = 120
        $button.Background = "#4CAF50"
        $button.Foreground = "White"
        $button.FontWeight = "Bold"
        return $button
    }
    
    # Create standardized text input
    static [object] CreateTextBox([string]$initialText = "", [string]$margin = "0") {
        $textBox = New-Object System.Windows.Controls.TextBox
        if ($initialText -ne "") {
            $textBox.Text = $initialText
        }
        if ($margin -ne "0") {
            $textBox.Margin = $margin
        }
        return $textBox
    }
    
    # Create standardized password input
    static [object] CreatePasswordBox([string]$margin = "0") {
        $passwordBox = New-Object System.Windows.Controls.PasswordBox
        if ($margin -ne "0") {
            $passwordBox.Margin = $margin
        }
        return $passwordBox
    }
    
    # Create standardized combo box
    static [object] CreateComboBox([array]$items = @(), [string]$margin = "0") {
        $comboBox = New-Object System.Windows.Controls.ComboBox
        if ($items.Count -gt 0) {
            foreach ($item in $items) {
                $comboBox.Items.Add($item) | Out-Null
            }
        }
        if ($margin -ne "0") {
            $comboBox.Margin = $margin
        }
        return $comboBox
    }
    
    # Create standardized stack panel
    static [object] CreateStackPanel([string]$orientation = "Vertical", [string]$margin = "20") {
        $panel = New-Object System.Windows.Controls.StackPanel
        $panel.Orientation = $orientation
        if ($margin -ne "0") {
            $panel.Margin = $margin
        }
        return $panel
    }
    
    # Create error panel with red background
    static [object] CreateErrorPanel([string]$margin = "20") {
        $panel = [UIFactory]::CreateStackPanel("Vertical", $margin)
        $panel.Background = "#FFEEEE"  # Light red background
        return $panel
    }
    
    # Create separator line
    static [object] CreateSeparator([string]$margin = "0,15,0,15") {
        $separator = New-Object System.Windows.Controls.Separator
        $separator.Margin = $margin
        return $separator
    }
    
    # Helper method to add click handler with proper closure and debug logging
    static [void] AddClickHandler([object]$button, [scriptblock]$handler) {
        $button.add_Click({
            try {
                Write-Host "DEBUG: [UI EVENT] Button clicked: '$($button.Content)'" -ForegroundColor Yellow
                $result = & $handler
                Write-Host "DEBUG: [UI EVENT] Button click handler completed for: '$($button.Content)'" -ForegroundColor Yellow
                return $result
            } catch {
                Write-Host "ERROR: [UI EVENT] Button click handler failed for '$($button.Content)': $($_.Exception.Message)" -ForegroundColor Red
                throw
            }
        }.GetNewClosure())
    }
    
    # Helper method to add text changed handler with proper closure and debug logging
    static [void] AddTextChangedHandler([object]$textBox, [scriptblock]$handler) {
        $textBox.add_TextChanged({
            try {
                Write-Host "DEBUG: [UI EVENT] TextBox changed: '$($textBox.Text)'" -ForegroundColor Yellow
                $result = & $handler
                Write-Host "DEBUG: [UI EVENT] TextBox change handler completed" -ForegroundColor Yellow
                return $result
            } catch {
                Write-Host "ERROR: [UI EVENT] TextBox change handler failed: $($_.Exception.Message)" -ForegroundColor Red
            }
        }.GetNewClosure())
    }
    
    # Helper method to add selection changed handler with debug logging
    static [void] AddSelectionChangedHandler([object]$comboBox, [scriptblock]$handler) {
        $comboBox.add_SelectionChanged({
            try {
                Write-Host "DEBUG: [UI EVENT] ComboBox selection changed to: '$($comboBox.SelectedItem)'" -ForegroundColor Yellow
                $result = & $handler
                Write-Host "DEBUG: [UI EVENT] ComboBox selection change handler completed" -ForegroundColor Yellow
                return $result
            } catch {
                Write-Host "ERROR: [UI EVENT] ComboBox selection change handler failed: $($_.Exception.Message)" -ForegroundColor Red
            }
        }.GetNewClosure())
    }
}

class WizardView {
    $viewModel
    $window
    $controls = @{}
    
    # PHASE 4: Store critical UI references as class properties
    $contentArea
    $mainPanel  
    $navPanel
    $statusBar
    $statusText
    $prevButton
    $nextButton

    WizardView($wizardViewModel) {
        $this.viewModel = $wizardViewModel
        $this.controls = @{}  # Initialize controls dictionary
    }

    hidden [void] Bind($control, $property, [string]$path, $converter = $null) {
        $binding = New-Object System.Windows.Data.Binding $path
        $binding.Source = $this.viewModel.state
        $binding.Mode = 'TwoWay'
        $binding.UpdateSourceTrigger = 'PropertyChanged'
        if ($converter) { $binding.Converter = $converter }
        $control.SetBinding($property, $binding)
    }

    # PHASE 3: Add UI validation function
    hidden [bool] ValidateUIReferences($contentArea, $prevButton, $nextButton, $statusText, $mainPanel) {
        $isValid = $true
        
        Write-Host "DEBUG: Validating UI references..." -ForegroundColor Yellow
        
        if ($contentArea -eq $null) { 
            Write-Host "ERROR: contentArea is null" -ForegroundColor Red
            $isValid = $false 
        } else {
            Write-Host "DEBUG: contentArea is valid" -ForegroundColor Green
        }
        
        if ($prevButton -eq $null) { 
            Write-Host "ERROR: prevButton is null" -ForegroundColor Red
            $isValid = $false 
        } else {
            Write-Host "DEBUG: prevButton is valid" -ForegroundColor Green
        }
        
        if ($nextButton -eq $null) { 
            Write-Host "ERROR: nextButton is null" -ForegroundColor Red
            $isValid = $false 
        } else {
            Write-Host "DEBUG: nextButton is valid" -ForegroundColor Green
        }
        
        if ($statusText -eq $null) { 
            Write-Host "ERROR: statusText is null" -ForegroundColor Red
            $isValid = $false 
        } else {
            Write-Host "DEBUG: statusText is valid" -ForegroundColor Green
        }
        
        if ($mainPanel -eq $null) { 
            Write-Host "ERROR: mainPanel is null" -ForegroundColor Red
            $isValid = $false 
        } else {
            Write-Host "DEBUG: mainPanel is valid" -ForegroundColor Green
        }
        
        if ($this.window -eq $null) { 
            Write-Host "ERROR: window is null" -ForegroundColor Red
            $isValid = $false 
        } else {
            Write-Host "DEBUG: window is valid" -ForegroundColor Green
        }
        
        Write-Host "DEBUG: UI validation result: $isValid" -ForegroundColor $(if ($isValid) { "Green" } else { "Red" })
        return $isValid
    }

    # PHASE 6: Add fallback content creation for error scenarios (Updated with UIFactory)
    hidden [object] CreateErrorStep([string]$errorMessage) {
        Write-Host "DEBUG: Creating error step content using UIFactory" -ForegroundColor Red
        
        # Create error panel using UIFactory
        $panel = [UIFactory]::CreateErrorPanel()

        # Error Title using UIFactory
        $titleLabel = [UIFactory]::CreateTitleLabel("⚠️ Error Loading Step Content")
        $titleLabel.Foreground = "#AA0000"
        $panel.Children.Add($titleLabel)

        # Error Message using UIFactory
        $messageLabel = [UIFactory]::CreateErrorLabel($errorMessage)
        $panel.Children.Add($messageLabel)

        # Refresh Button using UIFactory
        $refreshButton = [UIFactory]::CreateActionButton("🔄 Refresh Content", "0,20,0,0")
        
        $capturedViewModel = $this.viewModel
        [UIFactory]::AddClickHandler($refreshButton, { 
            try {
                # Trigger a refresh by setting the current step again
                $currentStep = $capturedViewModel.state.CurrentStepIndex
                $capturedViewModel.state.SetCurrentStepIndex($currentStep)
                Write-Host "DEBUG: Manual refresh triggered for step $currentStep" -ForegroundColor Yellow
            } catch {
                Write-Host "ERROR: Manual refresh failed: $($_.Exception.Message)" -ForegroundColor Red
            }
        })
        $panel.Children.Add($refreshButton)

        return $panel
    }

    hidden [object] CreateWizardStep0() { # Jira Connection & Ticket Input Step (Updated with UIFactory)
        $panel = [UIFactory]::CreateStackPanel("Vertical", "20")

        # Title using UIFactory
        $titleLabel = [UIFactory]::CreateTitleLabel("Step 1: Connect to Jira and Fetch Ticket")
        $panel.Children.Add($titleLabel)

        # Jira URL using UIFactory
        $urlLabel = [UIFactory]::CreateLabel("Jira URL:", "0")
        $panel.Children.Add($urlLabel)

        $urlTextBox = [UIFactory]::CreateTextBox($this.viewModel.state.JiraUrlInput, "0")
        $capturedViewModelState = $this.viewModel.state
        $capturedUrlTextBox = $urlTextBox
        [UIFactory]::AddTextChangedHandler($urlTextBox, { 
            try {
                if ($capturedViewModelState -and $capturedUrlTextBox) {
                    Write-Host "DEBUG: Jira URL textbox changed to: '$($capturedUrlTextBox.Text)'" -ForegroundColor Cyan
                    $capturedViewModelState.SetJiraUrlInput($capturedUrlTextBox.Text)
                    Write-Host "DEBUG: ViewModel JiraUrlInput updated to: '$($capturedViewModelState.JiraUrlInput)'" -ForegroundColor Green
                }
            } catch {
                Write-Warning "Error updating Jira URL: $($_.Exception.Message)"
            }
        })
        $panel.Children.Add($urlTextBox)

        # Username using UIFactory
        $userLabel = [UIFactory]::CreateLabel("Username:", "0,10,0,0")
        $panel.Children.Add($userLabel)

        $userTextBox = [UIFactory]::CreateTextBox($this.viewModel.state.JiraUsernameInput, "0")
        $capturedViewModelState = $this.viewModel.state
        $capturedUserTextBox = $userTextBox
        [UIFactory]::AddTextChangedHandler($userTextBox, { 
            try {
                if ($capturedViewModelState -and $capturedUserTextBox) {
                    Write-Host "DEBUG: Username textbox changed to: '$($capturedUserTextBox.Text)'" -ForegroundColor Cyan
                    $capturedViewModelState.SetJiraUsernameInput($capturedUserTextBox.Text)
                    Write-Host "DEBUG: ViewModel JiraUsernameInput updated to: '$($capturedViewModelState.JiraUsernameInput)'" -ForegroundColor Green
                }
            } catch {
                Write-Warning "Error updating username: $($_.Exception.Message)"
            }
        }.GetNewClosure())
        $panel.Children.Add($userTextBox)

        # API Token using UIFactory
        $tokenLabel = [UIFactory]::CreateLabel("API Token:", "0,10,0,0")
        $panel.Children.Add($tokenLabel)

        $tokenBox = [UIFactory]::CreatePasswordBox("0")
        $this.controls.apiTokenBox = $tokenBox  # Store reference for access
        $panel.Children.Add($tokenBox)
        
        # Login Button using UIFactory
        $loginButton = [UIFactory]::CreateActionButton("[Lock] Login to Jira", "0,10,0,0")
        $capturedVM = $this.viewModel
        [UIFactory]::AddClickHandler($loginButton, {
            try {
                if ($capturedVM) {
                    $capturedVM.AuthenticateJira()
                } else {
                    Write-Host "ERROR: viewModel is null in Login button click" -ForegroundColor Red
                }
            } catch {
                Write-Host "ERROR in Login button click: $($_.Exception.Message)" -ForegroundColor Red
            }
        }.GetNewClosure())
        $panel.Children.Add($loginButton)
        
        # Authentication Status using UIFactory
        $authStatusLabel = [UIFactory]::CreateLabel("Status: Not authenticated", "0,5,0,0")
        $authStatusLabel.Foreground = "#666666"
        $this.controls.authStatusLabel = $authStatusLabel  # Store reference for updates
        $panel.Children.Add($authStatusLabel)
        
        # Separator using UIFactory
        $separator = [UIFactory]::CreateSeparator("0,15,0,15")
        $panel.Children.Add($separator)

        # Ticket ID using UIFactory
        $ticketLabel = [UIFactory]::CreateLabel("Jira Ticket ID:", "0,10,0,0")
        $panel.Children.Add($ticketLabel)

        $ticketTextBox = [UIFactory]::CreateTextBox($this.viewModel.state.TicketIdInput, "0")
        $viewModelState = $this.viewModel.state
        $capturedTicketTextBox = $ticketTextBox
        [UIFactory]::AddTextChangedHandler($ticketTextBox, { 
            try {
                if ($viewModelState -and $capturedTicketTextBox) {
                    Write-Host "DEBUG: Ticket ID textbox changed to: '$($capturedTicketTextBox.Text)'" -ForegroundColor Cyan
                    $viewModelState.SetTicketIdInput($capturedTicketTextBox.Text)
                    Write-Host "DEBUG: ViewModel TicketIdInput updated to: '$($viewModelState.TicketIdInput)'" -ForegroundColor Green
                }
            } catch {
                Write-Warning "Error updating ticket ID: $($_.Exception.Message)"
            }
        }.GetNewClosure())
        $panel.Children.Add($ticketTextBox)

        # Fetch Button using UIFactory (disabled until authenticated)
        $fetchButton = [UIFactory]::CreateActionButton("[Ticket] Fetch Ticket Details", "0,15,0,0")
        $appConfig = Get-AppConfig
        $fetchButton.IsEnabled = $false  # Disabled until authenticated
        $fetchButton.Background = $appConfig.UI.Colors.Disabled
        $this.controls.fetchButton = $fetchButton  # Store reference
        $capturedVM = $this.viewModel
        [UIFactory]::AddClickHandler($fetchButton, {
            try {
                if ($capturedVM) {
                    $capturedVM.FetchTicketDetails()
                } else {
                    Write-Host "ERROR: viewModel is null in Fetch button click" -ForegroundColor Red
                }
            } catch {
                Write-Host "ERROR in Fetch button click: $($_.Exception.Message)" -ForegroundColor Red
            }
        }.GetNewClosure())
        $panel.Children.Add($fetchButton)

        return $panel
    }

    hidden [object] CreateWizardStep1() { # User Details Review & Edit (Partially Updated with UIFactory)
        $panel = [UIFactory]::CreateStackPanel("Vertical", "20")

        # Title using UIFactory
        $titleLabel = [UIFactory]::CreateTitleLabel("Step 2: Review and Edit User Details")
        $panel.Children.Add($titleLabel)

        # Create form fields for user data
        $data = $this.viewModel.state.CurrentOnboardingData
        if ($null -eq $data) { $data = @{} }

        # First Name using UIFactory
        $fnLabel = [UIFactory]::CreateLabel("First Name:", "0,10,0,0")
        $panel.Children.Add($fnLabel)
        
        $fnTextBox = [UIFactory]::CreateTextBox($(if ($data.ContainsKey('FirstName')) { $data.FirstName } else { "" }), "0")
        $capturedViewModelState = $this.viewModel.state
        [UIFactory]::AddTextChangedHandler($fnTextBox, { 
            try {
                if (-not $capturedViewModelState.CurrentOnboardingData) { 
                    $capturedViewModelState.CurrentOnboardingData = @{} 
                }
                $capturedViewModelState.CurrentOnboardingData['FirstName'] = $fnTextBox.Text
            } catch {
                Write-Warning "Error updating first name: $($_.Exception.Message)"
            }
        }.GetNewClosure())
        $panel.Children.Add($fnTextBox)

        # Last Name
        $lnLabel = New-Object System.Windows.Controls.Label
        $lnLabel.Content = "Last Name:"
        $lnLabel.Margin = "0,10,0,0"
        $panel.Children.Add($lnLabel)

        $lnTextBox = New-Object System.Windows.Controls.TextBox
        if ($data.ContainsKey('LastName')) { $lnTextBox.Text = $data.LastName }
        $capturedViewModelState = $this.viewModel.state
        $lnTextBox.add_TextChanged({ 
            try {
                if (-not $capturedViewModelState.CurrentOnboardingData) { 
                    $capturedViewModelState.CurrentOnboardingData = @{} 
                }
                $capturedViewModelState.CurrentOnboardingData['LastName'] = $lnTextBox.Text
            } catch {
                Write-Warning "Error updating last name: $($_.Exception.Message)"
            }
        }.GetNewClosure())
        $panel.Children.Add($lnTextBox)

        # Job Title
        $jtLabel = New-Object System.Windows.Controls.Label
        $jtLabel.Content = "Job Title:"
        $jtLabel.Margin = "0,10,0,0"
        $panel.Children.Add($jtLabel)

        $jtTextBox = New-Object System.Windows.Controls.TextBox
        if ($data.ContainsKey('JobTitle')) { $jtTextBox.Text = $data.JobTitle }
        $capturedViewModelState = $this.viewModel.state
        $jtTextBox.add_TextChanged({ 
            try {
                if (-not $capturedViewModelState.CurrentOnboardingData) { 
                    $capturedViewModelState.CurrentOnboardingData = @{} 
                }
                $capturedViewModelState.CurrentOnboardingData['JobTitle'] = $jtTextBox.Text
            } catch {
                Write-Warning "Error updating job title: $($_.Exception.Message)"
            }
        }.GetNewClosure())
        $panel.Children.Add($jtTextBox)

        # Department
        $deptLabel = New-Object System.Windows.Controls.Label
        $deptLabel.Content = "Department:"
        $deptLabel.Margin = "0,10,0,0"
        $panel.Children.Add($deptLabel)

        $deptTextBox = New-Object System.Windows.Controls.TextBox
        if ($data.ContainsKey('Department')) { $deptTextBox.Text = $data.Department }
        $capturedViewModelState = $this.viewModel.state
        $deptTextBox.add_TextChanged({ 
            try {
                if (-not $capturedViewModelState.CurrentOnboardingData) { 
                    $capturedViewModelState.CurrentOnboardingData = @{} 
                }
                $capturedViewModelState.CurrentOnboardingData['Department'] = $deptTextBox.Text
            } catch {
                Write-Warning "Error updating department: $($_.Exception.Message)"
            }
        }.GetNewClosure())
        $panel.Children.Add($deptTextBox)

        # OU Selection
        $ouLabel = New-Object System.Windows.Controls.Label
        $ouLabel.Content = "Organizational Unit:"
        $ouLabel.Margin = "0,10,0,0"
        $panel.Children.Add($ouLabel)

        $ouComboBox = New-Object System.Windows.Controls.ComboBox
        $this.viewModel.config.GetOUList() | ForEach-Object { $ouComboBox.Items.Add($_) }
        if ($ouComboBox.Items.Count -gt 0) { $ouComboBox.SelectedIndex = 0 }
        $capturedViewModelState = $this.viewModel.state
        $ouComboBox.add_SelectionChanged({ 
            Write-Host "DEBUG: [UI EVENT] OU ComboBox selection changed to: '$($ouComboBox.SelectedItem)'" -ForegroundColor Yellow
            if ($ouComboBox.SelectedItem) {
                $capturedViewModelState.SetSelectedOU($ouComboBox.SelectedItem.ToString())
                Write-Host "DEBUG: [UI EVENT] OU selection handler completed" -ForegroundColor Yellow
            }
        }.GetNewClosure())
        $panel.Children.Add($ouComboBox)

        # Validate button using UIFactory
        $validateButton = [UIFactory]::CreateActionButton("Validate Details", "0,15,0,0")
        $validateButton.Height = 30  # Override default height
        $capturedViewModel = $this.viewModel
        [UIFactory]::AddClickHandler($validateButton, { 
            $capturedViewModel.ValidateUserDetails() 
        }.GetNewClosure())
        $panel.Children.Add($validateButton)

        return $panel
    }

    hidden [object] CreateWizardStep2() { # User Creation Configuration
        $panel = New-Object System.Windows.Controls.StackPanel
        $panel.Margin = 20

        # Title
        $titleLabel = New-Object System.Windows.Controls.Label
        $titleLabel.Content = "Step 3: Configure User Account"
        $titleLabel.FontSize = 16
        $titleLabel.FontWeight = 'Bold'
        $panel.Children.Add($titleLabel)

        # Generated Username
        $userLabel = New-Object System.Windows.Controls.Label
        $userLabel.Content = "Generated Username:"
        $userLabel.Margin = "0,10,0,0"
        $panel.Children.Add($userLabel)

        $userTextBlock = New-Object System.Windows.Controls.TextBlock
        $userTextBlock.Text = "Will be generated based on first/last name"
        $userTextBlock.FontStyle = 'Italic'
        $panel.Children.Add($userTextBlock)

        # Password Options
        $passLabel = New-Object System.Windows.Controls.Label
        $passLabel.Content = "Password Options:"
        $passLabel.Margin = "0,15,0,0"
        $panel.Children.Add($passLabel)

        $tempPassCheck = New-Object System.Windows.Controls.CheckBox
        $tempPassCheck.Content = "Generate temporary password"
        $tempPassCheck.IsChecked = $true
        $panel.Children.Add($tempPassCheck)

        $mustChangeCheck = New-Object System.Windows.Controls.CheckBox
        $mustChangeCheck.Content = "User must change password at next logon"
        $mustChangeCheck.IsChecked = $true
        $mustChangeCheck.Margin = "0,5,0,0"
        $panel.Children.Add($mustChangeCheck)

        # Groups
        $groupLabel = New-Object System.Windows.Controls.Label
        $groupLabel.Content = "Default Groups:"
        $groupLabel.Margin = "0,15,0,0"
        $panel.Children.Add($groupLabel)

        $groupTextBlock = New-Object System.Windows.Controls.TextBlock
        $groupTextBlock.Text = "Domain Users (automatic)"
        $groupTextBlock.FontStyle = 'Italic'
        $panel.Children.Add($groupTextBlock)

        # Create User button using UIFactory
        $createButton = [UIFactory]::CreateActionButton("Create User Account", "0,20,0,0")
        $createButton.Background = 'LightGreen'  # Override default green color
        $capturedViewModel = $this.viewModel
        [UIFactory]::AddClickHandler($createButton, { 
            $capturedViewModel.CreateUserAccount() 
        }.GetNewClosure())
        $panel.Children.Add($createButton)

        return $panel
    }

    hidden [object] CreateWizardStep3() { # Summary and Completion
        $panel = New-Object System.Windows.Controls.StackPanel
        $panel.Margin = 20

        # Title
        $titleLabel = New-Object System.Windows.Controls.Label
        $titleLabel.Content = "Step 4: Summary and Completion"
        $titleLabel.FontSize = 16
        $titleLabel.FontWeight = 'Bold'
        $panel.Children.Add($titleLabel)

        # Results
        $resultsLabel = New-Object System.Windows.Controls.Label
        $resultsLabel.Content = "Operation Results:"
        $resultsLabel.Margin = "0,10,0,0"
        $panel.Children.Add($resultsLabel)

        $resultsTextBox = New-Object System.Windows.Controls.TextBox
        $resultsTextBox.IsReadOnly = $true
        $resultsTextBox.Height = 150
        $resultsTextBox.TextWrapping = 'Wrap'
        $resultsTextBox.VerticalScrollBarVisibility = 'Auto'
        $resultsTextBox.Text = "User creation results will appear here..."
        $panel.Children.Add($resultsTextBox)

        # Action buttons
        $buttonPanel = New-Object System.Windows.Controls.StackPanel
        $buttonPanel.Orientation = 'Horizontal'
        $buttonPanel.Margin = "0,15,0,0"

        $commentButton = [UIFactory]::CreateActionButton("Post Comment to Jira", "0,0,10,0")
        $commentButton.Height = 30  # Override default height
        $capturedViewModel = $this.viewModel
        [UIFactory]::AddClickHandler($commentButton, { 
            $capturedViewModel.PostJiraComment() 
        }.GetNewClosure())
        $buttonPanel.Children.Add($commentButton)

        $finishButton = [UIFactory]::CreateActionButton("Finish", "0")
        $finishButton.Height = 30  # Override default height
        $capturedWindow = $this.window
        [UIFactory]::AddClickHandler($finishButton, { 
            $capturedWindow.Close() 
        }.GetNewClosure())
        $buttonPanel.Children.Add($finishButton)

        $panel.Children.Add($buttonPanel)

        return $panel
    }

    Show() {
        try {
            # Ensure WPF assemblies are loaded
            if (-not ([System.Management.Automation.PSTypeName]'System.Windows.Window').Type) {
                throw "WPF assemblies not properly loaded"
            }
            
            $this.window = New-Object System.Windows.Window
            $this.window.Title = Get-AppConfig -Path "UI.WindowTitle"
            $windowSize = Get-AppConfig -Path "UI.WindowSize"
            $this.window.Width = $windowSize.Width
            $this.window.Height = $windowSize.Height
            $this.window.WindowStartupLocation = 'CenterScreen'

            # Main Layout - Use simple StackPanel but place status bar at bottom
            $this.mainPanel = New-Object System.Windows.Controls.StackPanel
            $this.window.Content = $this.mainPanel

            # Content Area with ScrollViewer - takes up most space
            $scrollViewer = New-Object System.Windows.Controls.ScrollViewer
            $scrollViewer.VerticalScrollBarVisibility = 'Auto'
            $scrollViewer.HorizontalScrollBarVisibility = 'Disabled'
            $scrollViewer.Height = 510  # Leave exactly 65px for navigation (40) + status (25)
            $scrollViewer.Margin = 5

            $this.contentArea = New-Object System.Windows.Controls.ContentControl
            $scrollViewer.Content = $this.contentArea
            $this.mainPanel.Children.Add($scrollViewer)

            # Navigation buttons - fixed height
            $this.navPanel = New-Object System.Windows.Controls.StackPanel
            $this.navPanel.Orientation = 'Horizontal'
            $this.navPanel.HorizontalAlignment = 'Right'
            $this.navPanel.Margin = 10
            $this.navPanel.Height = 40

            $this.prevButton = New-Object System.Windows.Controls.Button
            $this.prevButton.Content = "Previous"
            $this.prevButton.Width = 80
            $this.prevButton.Height = 30
            $this.prevButton.Margin = "0,0,10,0"
            $capturedViewModel = $this.viewModel
            $this.prevButton.add_Click({ 
                Write-Host "DEBUG: [UI EVENT] Previous button clicked" -ForegroundColor Yellow
                $capturedViewModel.GoToPreviousStep() 
                Write-Host "DEBUG: [UI EVENT] Previous button handler completed" -ForegroundColor Yellow
            }.GetNewClosure())
            $this.navPanel.Children.Add($this.prevButton)

            $this.nextButton = New-Object System.Windows.Controls.Button
            $this.nextButton.Content = "Next"
            $this.nextButton.Width = 80
            $this.nextButton.Height = 30
            $this.nextButton.add_Click({ 
                Write-Host "DEBUG: [UI EVENT] Next button clicked (content: '$($this.nextButton.Content)')" -ForegroundColor Yellow
                $capturedViewModel.GoToNextStep() 
                Write-Host "DEBUG: [UI EVENT] Next button handler completed" -ForegroundColor Yellow
            }.GetNewClosure())
            $this.navPanel.Children.Add($this.nextButton)

            $this.mainPanel.Children.Add($this.navPanel)

            # Status Bar with Debug Toggle - fixed at bottom, always visible
            $this.statusBar = New-Object System.Windows.Controls.Primitives.StatusBar
            $this.statusBar.Height = 25
            
            # Status text (left side)
            $this.statusText = New-Object System.Windows.Controls.TextBlock
            $this.statusText.Text = $this.viewModel.state.StatusMessage
            $this.statusBar.Items.Add($this.statusText)
            
            # Add separator for visual separation
            $separator = New-Object System.Windows.Controls.Separator
            $separator.Width = 1
            $separator.Margin = "10,0,10,0"
            $this.statusBar.Items.Add($separator)
            
            # Debug ComboBox (right side) - persistent across step changes
            $debugCombo = New-Object System.Windows.Controls.ComboBox
            $debugCombo.Items.Add("Debug: ON") | Out-Null
            $debugCombo.Items.Add("Debug: OFF") | Out-Null
            $debugCombo.SelectedIndex = 0  # Default ON as requested
            $debugCombo.Width = 100
            $debugCombo.Margin = "0,2,5,2"
            
            # Wire up debug toggle event with persistent state
            $capturedViewModel = $this.viewModel
            $debugCombo.add_SelectionChanged({
                Write-Host "DEBUG: [UI EVENT] Debug combo selection changed to index: $($debugCombo.SelectedIndex)" -ForegroundColor Yellow
                $isDebugOn = ($debugCombo.SelectedIndex -eq 0)
                $capturedViewModel.log.SetDebugMode($isDebugOn)
                Write-Host "DEBUG: [UI EVENT] Debug mode changed to: $(if ($isDebugOn) { 'ON' } else { 'OFF' })" -ForegroundColor $(if ($isDebugOn) { 'Green' } else { 'Red' })
            }.GetNewClosure())
            
            $this.statusBar.Items.Add($debugCombo)
            $this.mainPanel.Children.Add($this.statusBar)

            # PHASE 4: Capture UI variables from class properties (stronger references)
            Write-Host "DEBUG: Capturing UI variables from class properties for closures..." -ForegroundColor Yellow
            $capturedView = $this
            $capturedViewModel = $this.viewModel
            $capturedContentArea = $this.contentArea
            $capturedPrevButton = $this.prevButton  
            $capturedNextButton = $this.nextButton
            $capturedStatusText = $this.statusText
            $capturedMainPanel = $this.mainPanel
            $capturedWindow = $this.window
            
            # PHASE 3: Comprehensive UI validation
            $validationResult = $this.ValidateUIReferences($capturedContentArea, $capturedPrevButton, $capturedNextButton, $capturedStatusText, $capturedMainPanel)
            if (-not $validationResult) {
                Write-Host "CRITICAL ERROR: UI validation failed! Application may not work correctly." -ForegroundColor Red
                # Continue anyway but with warnings
            }

            # Manual binding for status message - FIXED to use captured variables
            $this.viewModel.state.AddPropertyChangedHandler({
                param($propertyName)
                if ($propertyName -eq 'StatusMessage') {
                    try {
                        # Capture variables for nested closure
                        $nestedCapturedWindow = $capturedWindow
                        $nestedCapturedStatusText = $capturedStatusText
                        $nestedCapturedViewModel = $capturedViewModel
                        
                        if ($nestedCapturedWindow.Dispatcher) {
                            $nestedCapturedWindow.Dispatcher.InvokeAsync({
                                try {
                                    $nestedCapturedStatusText.Text = $nestedCapturedViewModel.state.StatusMessage
                                    Write-Host "DEBUG: Status updated to: $($nestedCapturedViewModel.state.StatusMessage)" -ForegroundColor Cyan
                                } catch {
                                    Write-Host "ERROR: Status update failed: $($_.Exception.Message)" -ForegroundColor Red
                                }
                            }.GetNewClosure())
                        }
                    } catch {
                        Write-Host "ERROR: Status handler failed: $($_.Exception.Message)" -ForegroundColor Red
                    }
                }
            }.GetNewClosure())

            # Logic to switch wizard steps based on state.CurrentStepIndex
            $this.viewModel.state.AddPropertyChangedHandler({
                param($propertyName)
                if ($propertyName -eq 'CurrentStepIndex') {
                    try {
                        $stepIndex = $capturedViewModel.state.CurrentStepIndex
                        Write-Host "DEBUG: UI updating to step $stepIndex" -ForegroundColor Magenta
                        
                        # Capture variables for nested closure
                        $nestedCapturedWindow = $capturedWindow
                        $nestedCapturedContentArea = $capturedContentArea
                        $nestedCapturedPrevButton = $capturedPrevButton
                        $nestedCapturedNextButton = $capturedNextButton
                        $nestedCapturedView = $capturedView
                        $nestedStepIndex = $stepIndex
                        
                        if ($nestedCapturedWindow.Dispatcher) {
                            $nestedCapturedWindow.Dispatcher.InvokeAsync({
                                try {
                                    Write-Host "DEBUG: Dispatcher executing step update to $nestedStepIndex" -ForegroundColor Magenta
                                    Write-Host "DEBUG: nestedCapturedContentArea in closure null? $($nestedCapturedContentArea -eq $null)" -ForegroundColor Gray
                                    
                                    # PHASE 3: Enhanced validation and error handling
                                    if ($nestedCapturedContentArea -eq $null) {
                                        Write-Host "ERROR: nestedCapturedContentArea is null - cannot update UI!" -ForegroundColor Red
                                        return
                                    }
                                    
                                    if ($nestedCapturedView -eq $null) {
                                        Write-Host "ERROR: nestedCapturedView is null - cannot create content!" -ForegroundColor Red
                                        return
                                    }
                                    
                                    switch ($nestedStepIndex) {
                                        0 { 
                                            Write-Host "DEBUG: Creating step 0 content" -ForegroundColor Magenta
                                            $newContent = $null
                                            try {
                                                $newContent = $nestedCapturedView.CreateWizardStep0()
                                                if ($newContent -eq $null) {
                                                    Write-Host "ERROR: CreateWizardStep0 returned null content!" -ForegroundColor Red
                                                    $newContent = $nestedCapturedView.CreateErrorStep("Step 0 content creation returned null")
                                                } else {
                                                    Write-Host "DEBUG: Step 0 content created successfully, type: $($newContent.GetType().Name)" -ForegroundColor Green
                                                }
                                            } catch {
                                                Write-Host "ERROR: Failed to create step 0 content: $($_.Exception.Message)" -ForegroundColor Red
                                                $newContent = $nestedCapturedView.CreateErrorStep("Step 0 creation failed: $($_.Exception.Message)")
                                            }
                                            
                                            Write-Host "DEBUG: Assigning step 0 content to nestedCapturedContentArea" -ForegroundColor Magenta
                                            $nestedCapturedContentArea.Content = $newContent
                                            Write-Host "DEBUG: Step 0 content assignment completed" -ForegroundColor Green
                                        }
                                        1 { 
                                            Write-Host "DEBUG: Creating step 1 content" -ForegroundColor Magenta
                                            $newContent = $null
                                            try {
                                                $newContent = $nestedCapturedView.CreateWizardStep1()
                                                if ($newContent -eq $null) {
                                                    Write-Host "ERROR: CreateWizardStep1 returned null content!" -ForegroundColor Red
                                                    $newContent = $nestedCapturedView.CreateErrorStep("Step 1 content creation returned null")
                                                } else {
                                                    Write-Host "DEBUG: Step 1 content created successfully, type: $($newContent.GetType().Name)" -ForegroundColor Green
                                                }
                                            } catch {
                                                Write-Host "ERROR: Failed to create step 1 content: $($_.Exception.Message)" -ForegroundColor Red
                                                $newContent = $nestedCapturedView.CreateErrorStep("Step 1 creation failed: $($_.Exception.Message)")
                                            }
                                            
                                            Write-Host "DEBUG: Assigning step 1 content to nestedCapturedContentArea" -ForegroundColor Magenta
                                            $nestedCapturedContentArea.Content = $newContent
                                            Write-Host "DEBUG: Step 1 content assignment completed" -ForegroundColor Green
                                        }
                                        2 { 
                                            Write-Host "DEBUG: Creating step 2 content" -ForegroundColor Magenta
                                            $newContent = $null
                                            try {
                                                $newContent = $nestedCapturedView.CreateWizardStep2()
                                                if ($newContent -eq $null) {
                                                    Write-Host "ERROR: CreateWizardStep2 returned null content!" -ForegroundColor Red
                                                    $newContent = $nestedCapturedView.CreateErrorStep("Step 2 content creation returned null")
                                                } else {
                                                    Write-Host "DEBUG: Step 2 content created successfully, type: $($newContent.GetType().Name)" -ForegroundColor Green
                                                }
                                            } catch {
                                                Write-Host "ERROR: Failed to create step 2 content: $($_.Exception.Message)" -ForegroundColor Red
                                                $newContent = $nestedCapturedView.CreateErrorStep("Step 2 creation failed: $($_.Exception.Message)")
                                            }
                                            
                                            Write-Host "DEBUG: Assigning step 2 content to nestedCapturedContentArea" -ForegroundColor Magenta
                                            $nestedCapturedContentArea.Content = $newContent
                                            Write-Host "DEBUG: Step 2 content assignment completed" -ForegroundColor Green
                                        }
                                        3 { 
                                            Write-Host "DEBUG: Creating step 3 content" -ForegroundColor Magenta
                                            $newContent = $null
                                            try {
                                                $newContent = $nestedCapturedView.CreateWizardStep3()
                                                if ($newContent -eq $null) {
                                                    Write-Host "ERROR: CreateWizardStep3 returned null content!" -ForegroundColor Red
                                                    $newContent = $nestedCapturedView.CreateErrorStep("Step 3 content creation returned null")
                                                } else {
                                                    Write-Host "DEBUG: Step 3 content created successfully, type: $($newContent.GetType().Name)" -ForegroundColor Green
                                                }
                                            } catch {
                                                Write-Host "ERROR: Failed to create step 3 content: $($_.Exception.Message)" -ForegroundColor Red
                                                $newContent = $nestedCapturedView.CreateErrorStep("Step 3 creation failed: $($_.Exception.Message)")
                                            }
                                            
                                            Write-Host "DEBUG: Assigning step 3 content to nestedCapturedContentArea" -ForegroundColor Magenta
                                            $nestedCapturedContentArea.Content = $newContent
                                            Write-Host "DEBUG: Step 3 content assignment completed" -ForegroundColor Green
                                        }
                                        default {
                                            Write-Host "ERROR: Invalid step index: $nestedStepIndex" -ForegroundColor Red
                                            return
                                        }
                                    }
                                    
                                    # Update navigation buttons using nested captured references with enhanced validation
                                    Write-Host "DEBUG: Updating navigation buttons" -ForegroundColor Magenta
                                    try {
                                        if ($nestedCapturedPrevButton -eq $null) {
                                            Write-Host "ERROR: nestedCapturedPrevButton is null!" -ForegroundColor Red
                                        } else {
                                            $nestedCapturedPrevButton.IsEnabled = ($nestedStepIndex -gt 0)
                                            Write-Host "DEBUG: Previous button enabled: $($nestedStepIndex -gt 0)" -ForegroundColor Gray
                                        }
                                        
                                        if ($nestedCapturedNextButton -eq $null) {
                                            Write-Host "ERROR: nestedCapturedNextButton is null!" -ForegroundColor Red
                                        } else {
                                            $nestedCapturedNextButton.Content = if ($nestedStepIndex -eq 3) { "Finish" } else { "Next" }
                                            Write-Host "DEBUG: Next button content: $($nestedCapturedNextButton.Content)" -ForegroundColor Gray
                                        }
                                        
                                        Write-Host "DEBUG: Navigation buttons updated successfully" -ForegroundColor Green
                                    } catch {
                                        Write-Host "ERROR: Failed to update navigation buttons: $($_.Exception.Message)" -ForegroundColor Red
                                    }
                                    
                                    Write-Host "DEBUG: UI step update completed for step $nestedStepIndex" -ForegroundColor Green
                                } catch {
                                    Write-Host "ERROR: Content assignment failed: $($_.Exception.Message)" -ForegroundColor Red
                                    Write-Host "ERROR: Stack trace: $($_.ScriptStackTrace)" -ForegroundColor Red
                                }
                            }.GetNewClosure())
                        }
                    } catch {
                        Write-Host "ERROR: Step change handler failed: $($_.Exception.Message)" -ForegroundColor Red
                        Write-Host "ERROR: Stack trace: $($_.ScriptStackTrace)" -ForegroundColor Red
                    }
                }
            }.GetNewClosure())

            # Handle IsBusy state - FIXED to use captured variables
            $this.viewModel.state.AddPropertyChangedHandler({
                param($propertyName)
                if ($propertyName -eq 'IsBusy') {
                    try {
                        # Capture variables for nested closure
                        $nestedCapturedWindow = $capturedWindow
                        $nestedCapturedMainPanel = $capturedMainPanel
                        $nestedCapturedViewModel = $capturedViewModel
                        
                        if ($nestedCapturedWindow.Dispatcher) {
                            $nestedCapturedWindow.Dispatcher.InvokeAsync({
                                try {
                                    $nestedCapturedMainPanel.IsEnabled = -not $nestedCapturedViewModel.state.IsBusy
                                    Write-Host "DEBUG: IsBusy state updated: $($nestedCapturedViewModel.state.IsBusy)" -ForegroundColor Cyan
                                } catch {
                                    Write-Host "ERROR: IsBusy update failed: $($_.Exception.Message)" -ForegroundColor Red
                                }
                            }.GetNewClosure())
                        }
                    } catch {
                        Write-Host "ERROR: IsBusy handler failed: $($_.Exception.Message)" -ForegroundColor Red
                    }
                }
            }.GetNewClosure())

            # Initial content load - PHASE 4: Use class properties directly
            Write-Host "DEBUG: Loading initial content using class properties..." -ForegroundColor Yellow
            $this.contentArea.Content = $this.CreateWizardStep0()
            $this.prevButton.IsEnabled = $false
            Write-Host "DEBUG: Initial content loaded successfully" -ForegroundColor Green

            if (-not $this.window) {
                Write-Error "Window object is null - cannot show dialog"
                return
            }
            
            # Add window event logging
            $this.window.add_Loaded({
                Write-Host "DEBUG: [WINDOW EVENT] Window loaded" -ForegroundColor Blue
            })
            
            $this.window.add_Activated({
                Write-Host "DEBUG: [WINDOW EVENT] Window activated/focused" -ForegroundColor Blue
            })
            
            $this.window.add_Deactivated({
                Write-Host "DEBUG: [WINDOW EVENT] Window deactivated/lost focus" -ForegroundColor Blue
            })
            
            $this.window.add_Closing({
                Write-Host "DEBUG: [WINDOW EVENT] Window closing" -ForegroundColor Blue
            })
            
            $this.window.add_Closed({
                Write-Host "DEBUG: [WINDOW EVENT] Window closed" -ForegroundColor Blue
            })
            
            Write-Host "DEBUG: [WINDOW EVENT] About to show window dialog" -ForegroundColor Blue
            $this.window.ShowDialog() | Out-Null
            Write-Host "DEBUG: [WINDOW EVENT] Window dialog completed" -ForegroundColor Blue
        } catch {
            Write-Error "Failed to create or show window: $($_.Exception.Message)"
            throw
        }
    }
}

# SECTION 5: SCRIPT EXECUTION

try {
    # 1. Instantiate all services and state
    Write-Host "Initializing services..." -ForegroundColor Green
    $logService = [LoggingService]::new()
    $configService = [ConfigurationService]::new()
    $jiraService = [JiraService]::new($logService)
    $adService = [ActiveDirectoryService]::new($true, $logService) # Start in simulation mode
    $appState = [AppState]::new()

    # 2. Instantiate the ViewModel, injecting all dependencies
    Write-Host "Creating ViewModel..." -ForegroundColor Green
    $viewModel = [WizardViewModel]::new($logService, $configService, $jiraService, $adService, $appState)

    # 3. Instantiate the View, injecting the ViewModel
    Write-Host "Creating View..." -ForegroundColor Green
    $view = [WizardView]::new($viewModel)

    # 4. Link View back to ViewModel (for dispatcher access)
    Write-Host "Linking View and ViewModel..." -ForegroundColor Green
    $viewModel.RegisterView($view)

    # 5. Start the application
    Write-Host "Starting application..." -ForegroundColor Green
    $logService.Info("Application starting...")
    $view.Show()
    $logService.Info("Application closed.")
    
} catch {
    Write-Error "Application failed to start: $($_.Exception.Message)"
    Write-Error "Stack trace: $($_.ScriptStackTrace)"
    if ($logService) {
        $logService.Error("Application startup failed", $_.Exception)
    }
    Read-Host "Press Enter to exit"
}
