﻿2025-07-01 12:27:49.445 | akinje          | INFO     | JML Automation Script v1.13 starting...
2025-07-01 12:27:49.459 | akinje          | DEBUG    | Script path: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13
2025-07-01 12:27:49.474 | akinje          | DEBUG    | PowerShell version: 5.1.19041.5848
2025-07-01 12:27:49.481 | akinje          | DEBUG    | Execution policy: Bypass
2025-07-01 12:27:49.486 | akinje          | DEBUG    | User: akinje
2025-07-01 12:27:49.492 | akinje          | DEBUG    | Computer: JGM-CG7THR3
2025-07-01 12:28:11.864 | akinje          | INFO     | === JML ENVIRONMENT VALIDATION ===
2025-07-01 12:28:11.870 | akin<PERSON>          | INFO     | Verifying PowerShell Version...
2025-07-01 12:28:11.874 | akinje          | INFO     | === JML ENVIRONMENT VALIDATION ===
2025-07-01 12:28:11.876 | akinje          | INFO     | Verifying PowerShell Version...
2025-07-01 12:28:11.919 | akinje          | WARNING  | WARNING: PowerShell 7 or higher is recommended for optimal performance and security.
2025-07-01 12:28:11.923 | akinje          | INFO     | Current version: PowerShell 5.1.19041.5848
2025-07-01 12:28:11.926 | akinje          | WARNING  | PowerShell 7+ recommended. Current: 5.1.19041.5848
2025-07-01 12:28:11.932 | akinje          | INFO     | Searching for PowerShell 7 installation...
2025-07-01 12:28:11.940 | akinje          | INFO     |   Checking: C:\Program Files\PowerShell\7\pwsh.exe
2025-07-01 12:28:12.148 | akinje          | INFO     |   Found PowerShell 7.5.1 at: C:\Program Files\PowerShell\7\pwsh.exe
2025-07-01 12:28:12.152 | akinje          | INFO     | PowerShell 7 is installed at: C:\Program Files\PowerShell\7\pwsh.exe
2025-07-01 12:28:12.157 | akinje          | INFO     | Options:
2025-07-01 12:28:12.161 | akinje          | INFO     |   1. Continue with current PowerShell 5.x
2025-07-01 12:28:12.166 | akinje          | INFO     |   2. Switch to PowerShell 7 (recommended)
2025-07-01 12:28:12.171 | akinje          | INFO     |   3. Exit and manually switch
2025-07-01 12:28:12.181 | akinje          | INFO     | Select option (1-3) [Default: 2 in 10 seconds]: 
2025-07-01 12:28:15.808 | akinje          | INFO     | 1
2025-07-01 12:28:15.818 | akinje          | WARNING  | WARNING: Continuing with PowerShell 5.x. Some features may not work optimally.
2025-07-01 12:28:15.825 | akinje          | INFO     | === CHECKING REQUIRED MODULES ===
2025-07-01 12:28:15.828 | akinje          | INFO     | === CHECKING REQUIRED MODULES ===
2025-07-01 12:28:15.857 | akinje          | INFO     | Checking for required modules and available updates...
2025-07-01 12:28:27.021 | akinje          | INFO     | Checking module: Microsoft.PowerShell.SecretManagement...
2025-07-01 12:28:27.169 | akinje          | INFO     |   Found installed version 1.1.2
2025-07-01 12:28:31.154 | akinje          | INFO     |   Already up to date
2025-07-01 12:28:31.172 | akinje          | INFO     | Checking module: Microsoft.PowerShell.SecretStore...
2025-07-01 12:28:32.087 | akinje          | INFO     |   Found installed version 1.0.6
2025-07-01 12:28:34.140 | akinje          | INFO     |   Already up to date
2025-07-01 12:28:34.155 | akinje          | INFO     | Checking module: Microsoft.Graph...
2025-07-01 12:28:34.544 | akinje          | INFO     |   Found installed version 2.28.0
2025-07-01 12:28:44.463 | akinje          | INFO     |   Already up to date
2025-07-01 12:28:45.091 | akinje          | INFO     | Checking module: JiraPS...
2025-07-01 12:28:45.882 | akinje          | INFO     |   Found installed version 2.14.7
2025-07-01 12:28:52.260 | akinje          | INFO     |   Already up to date
2025-07-01 12:28:52.275 | akinje          | INFO     | Checking module: ImportExcel...
2025-07-01 12:28:52.927 | akinje          | INFO     |   Found installed version 7.8.10
2025-07-01 12:28:55.793 | akinje          | INFO     |   Already up to date
2025-07-01 12:28:55.806 | akinje          | INFO     | Checking module: PSWriteHTML...
2025-07-01 12:28:56.983 | akinje          | INFO     |   Found installed version 1.36.0
2025-07-01 12:28:59.448 | akinje          | INFO     |   Already up to date
2025-07-01 12:28:59.474 | akinje          | INFO     | All modules are up to date.
2025-07-01 12:28:59.486 | akinje          | INFO     | Verifying module availability...
2025-07-01 12:28:59.614 | akinje          | INFO     |   [VERIFIED] Microsoft.PowerShell.SecretManagement v1.1.2 is available.
2025-07-01 12:28:59.829 | akinje          | INFO     |   [VERIFIED] Microsoft.PowerShell.SecretStore v1.0.6 is available.
2025-07-01 12:28:59.941 | akinje          | INFO     |   [VERIFIED] Microsoft.Graph v2.28.0 is available.
2025-07-01 12:29:00.058 | akinje          | INFO     |   [VERIFIED] JiraPS v2.14.7 is available.
2025-07-01 12:29:00.178 | akinje          | INFO     |   [VERIFIED] ImportExcel v7.8.10 is available.
2025-07-01 12:29:00.307 | akinje          | INFO     |   [VERIFIED] PSWriteHTML v1.36.0 is available.
2025-07-01 12:29:00.324 | akinje          | INFO     | All required modules are successfully installed and available.
2025-07-01 12:29:00.336 | akinje          | INFO     | === JML ENVIRONMENT VALIDATION COMPLETE ===
2025-07-01 12:29:00.347 | akinje          | DEBUG    | [DEBUG] Starting JML script execution...
2025-07-01 12:29:00.357 | akinje          | DEBUG    | [DEBUG] Script directory: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13
2025-07-01 12:29:00.371 | akinje          | DEBUG    | [DEBUG] Checking ActiveDirectory module availability...
2025-07-01 12:29:00.490 | akinje          | INFO     | [INFO] ActiveDirectory module is not available - running in UAT mode
2025-07-01 12:29:00.501 | akinje          | INFO     | [INFO] This is normal for UAT/test environments
2025-07-01 12:29:00.512 | akinje          | DEBUG    | [DEBUG] Initializing JML paths...
2025-07-01 12:29:00.532 | akinje          | DEBUG    | [DEBUG] JML Paths initialized globally.
2025-07-01 12:29:00.546 | akinje          | DEBUG    | [DEBUG] Project Root: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13
2025-07-01 12:29:00.560 | akinje          | DEBUG    | [DEBUG] Modules Root: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Modules
2025-07-01 12:29:00.575 | akinje          | DEBUG    | [DEBUG] Config Dir: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Config
2025-07-01 12:29:00.654 | akinje          | DEBUG    | [DEBUG] Updated ConfigPath to use Config directory: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Config\AdminAccountConfig.psd1
2025-07-01 12:29:00.665 | akinje          | DEBUG    | [DEBUG] Starting enhanced module loading...
2025-07-01 12:29:00.686 | akinje          | INFO     | Loading JML modules from nested structure...
2025-07-01 12:29:00.698 | akinje          | INFO     | Modules Root: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Modules
2025-07-01 12:29:00.727 | akinje          | INFO     |   Loading: JML-Core/JML-Utilities [CRITICAL]
2025-07-01 12:29:00.737 | akinje          | INFO     |     Description: General utility functions
2025-07-01 12:29:00.753 | akinje          | INFO     |     Path: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Modules\JML-Core\JML-Utilities.psd1 (manifest)
2025-07-01 12:29:00.762 | akinje          | INFO     |     Attempting to import module using manifest...
2025-07-01 12:29:01.042 | akinje          | WARNING  | WARNING: JML-Security: JML-Configuration module functions not available. Some features may be limited.
2025-07-01 12:29:01.267 | akinje          | WARNING  | WARNING: JML-Logging: Missing dependencies: Get-ModuleConfiguration (JML-Configuration), Protect-SensitiveData (JML-Security). Some features may be limited.
2025-07-01 12:29:01.285 | akinje          | INFO     |     [SUCCESS] Module loaded: JML-Core/JML-Utilities
2025-07-01 12:29:01.297 | akinje          | INFO     |   Loading: JML-Core/JML-Configuration [CRITICAL]
2025-07-01 12:29:01.309 | akinje          | INFO     |     Description: Core configuration management
2025-07-01 12:29:01.324 | akinje          | INFO     |     Path: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Modules\JML-Core\JML-Configuration.psd1 (manifest)
2025-07-01 12:29:01.339 | akinje          | INFO     |     Attempting to import module using manifest...
2025-07-01 12:29:01.411 | akinje          | INFO     |     [SUCCESS] Module loaded: JML-Core/JML-Configuration
2025-07-01 12:29:01.427 | akinje          | INFO     |     [VERIFIED] Function Initialize-SmartConfiguration is available
2025-07-01 12:29:01.438 | akinje          | INFO     |     [VERIFIED] Function Get-ModuleConfiguration is available
2025-07-01 12:29:01.451 | akinje          | INFO     |   Loading: JML-Core/JML-Security [CRITICAL]
2025-07-01 12:29:01.460 | akinje          | INFO     |     Description: Security and credential management
2025-07-01 12:29:01.473 | akinje          | INFO     |     Path: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Modules\JML-Core\JML-Security.psd1 (manifest)
2025-07-01 12:29:01.485 | akinje          | INFO     |     Attempting to import module using manifest...
2025-07-01 12:29:01.565 | akinje          | INFO     |     [SUCCESS] Module loaded: JML-Core/JML-Security
2025-07-01 12:29:01.576 | akinje          | INFO     |     [VERIFIED] Function Get-SecureCredential is available
2025-07-01 12:29:01.588 | akinje          | INFO     |     [VERIFIED] Function Set-SecureCredential is available
2025-07-01 12:29:01.599 | akinje          | INFO     |     [VERIFIED] Function Get-StringHash is available
2025-07-01 12:29:01.611 | akinje          | INFO     |   Loading: JML-Core/JML-Logging [CRITICAL]
2025-07-01 12:29:01.621 | akinje          | INFO     |     Description: Secure logging functionality
2025-07-01 12:29:01.631 | akinje          | INFO     |     Path: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Modules\JML-Core\JML-Logging.psd1 (manifest)
2025-07-01 12:29:01.642 | akinje          | INFO     |     Attempting to import module using manifest...
2025-07-01 12:29:01.702 | akinje          | INFO     |     [SUCCESS] Module loaded: JML-Core/JML-Logging
2025-07-01 12:29:01.709 | akinje          | INFO     |     [VERIFIED] Function Write-SecureLog is available
2025-07-01 12:29:01.718 | akinje          | INFO     |     [VERIFIED] Function Initialize-SecureLogging is available
2025-07-01 12:29:01.729 | akinje          | INFO     |   Loading: JML-Features/JML-ActiveDirectory [CRITICAL]
2025-07-01 12:29:01.738 | akinje          | INFO     |     Description: Active Directory operations
2025-07-01 12:29:01.750 | akinje          | INFO     |     Path: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Modules\JML-Features\JML-ActiveDirectory.psd1 (manifest)
2025-07-01 12:29:01.760 | akinje          | INFO     |     Attempting to import module using manifest...
2025-07-01 12:29:01.924 | akinje          | INFO     |     [SUCCESS] Module loaded: JML-Features/JML-ActiveDirectory
2025-07-01 12:29:01.935 | akinje          | INFO     |   Loading: JML-Features/JML-Email [CRITICAL]
2025-07-01 12:29:01.946 | akinje          | INFO     |     Description: Email notification functionality
2025-07-01 12:29:01.957 | akinje          | INFO     |     Path: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Modules\JML-Features\JML-Email.psd1 (manifest)
2025-07-01 12:29:01.969 | akinje          | INFO     |     Attempting to import module using manifest...
2025-07-01 12:29:02.134 | akinje          | INFO     |     [SUCCESS] Module loaded: JML-Features/JML-Email
2025-07-01 12:29:02.144 | akinje          | INFO     |   Loading: JML-Features/JML-Jira [CRITICAL]
2025-07-01 12:29:02.153 | akinje          | INFO     |     Description: Jira integration
2025-07-01 12:29:02.164 | akinje          | INFO     |     Path: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Modules\JML-Features\JML-Jira.psd1 (manifest)
2025-07-01 12:29:02.177 | akinje          | INFO     |     Attempting to import module using manifest...
2025-07-01 12:29:02.449 | akinje          | INFO     |     [SUCCESS] Module loaded: JML-Features/JML-Jira
2025-07-01 12:29:02.461 | akinje          | INFO     |   Loading: JML-Infrastructure/JML-Setup [CRITICAL]
2025-07-01 12:29:02.470 | akinje          | INFO     |     Description: Setup and environment validation
2025-07-01 12:29:02.479 | akinje          | INFO     |     Path: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Modules\JML-Infrastructure\JML-Setup.psd1 (manifest)
2025-07-01 12:29:02.493 | akinje          | INFO     |     Attempting to import module using manifest...
2025-07-01 12:29:03.210 | akinje          | INFO     |     [SUCCESS] Module loaded: JML-Infrastructure/JML-Setup
2025-07-01 12:29:03.226 | akinje          | INFO     |   Loading: JML-Infrastructure/JML-Monitoring [CRITICAL]
2025-07-01 12:29:03.234 | akinje          | INFO     |     Description: System monitoring
2025-07-01 12:29:03.244 | akinje          | INFO     |     Path: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Modules\JML-Infrastructure\JML-Monitoring.psd1 (manifest)
2025-07-01 12:29:03.256 | akinje          | INFO     |     Attempting to import module using manifest...
2025-07-01 12:29:03.455 | akinje          | INFO     |     [SUCCESS] Module loaded: JML-Infrastructure/JML-Monitoring
2025-07-01 12:29:03.469 | akinje          | INFO     |   Loading: JML-Infrastructure/JML-UIManager [CRITICAL]
2025-07-01 12:29:03.479 | akinje          | INFO     |     Description: Modern hybrid dashboard UI with real-time status
2025-07-01 12:29:03.501 | akinje          | INFO     |     Path: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Modules\JML-Infrastructure\JML-UIManager.psd1 (manifest)
2025-07-01 12:29:03.513 | akinje          | INFO     |     Attempting to import module using manifest...
2025-07-01 12:29:03.814 | akinje          | ERROR    |     [ERROR] Import failed for 'JML-Infrastructure/JML-UIManager': The running command stopped because the preference variable "ErrorActionPreference" or common parameter is set to Stop: At C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Modules\JML-Infrastructure\JML-UIManager.psm1:1425 char:22
+                     }
+                      ~
The Try statement is missing its Catch or Finally block.

At C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Modules\JML-Infrastructure\JML-UIManager.psm1:1570 char:22
+     'Add-UILogMessage'
+                      ~
The string is missing the terminator: '.

At C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Modules\JML-Infrastructure\JML-UIManager.psm1:1407 char:44
+             ShowEnhancedHierarchicalMenu = {
+                                            ~
Missing closing '}' in statement block or type definition.

At C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Modules\JML-Infrastructure\JML-UIManager.psm1:1572 char:1
+ 
The hash literal was incomplete.

At C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Modules\JML-Infrastructure\JML-UIManager.psm1:71 char:9
+     try {
+         ~
Missing closing '}' in statement block or type definition.

At C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Modules\JML-Infrastructure\JML-UIManager.psm1:1572 char:1
+ 
The Try statement is missing its Catch or Finally block.

At C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Modules\JML-Infrastructure\JML-UIManager.psm1:63 char:31
+ function New-UIManagerService {
+                               ~
Missing closing '}' in statement block or type definition.
2025-07-01 12:29:03.835 | akinje          | INFO     |     [DEBUG] File exists: True, Path: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Modules\JML-Infrastructure\JML-UIManager.psd1
2025-07-01 12:29:03.846 | akinje          | ERROR    |     [DEBUG] Exception type: ActionPreferenceStopException
2025-07-01 12:29:03.863 | akinje          | INFO     |     [CRITICAL] This is a required module - script cannot continue
2025-07-01 12:29:03.875 | akinje          | INFO     | Module Loading Summary:
2025-07-01 12:29:03.885 | akinje          | INFO     |   Successfully loaded: 9/10
2025-07-01 12:29:03.897 | akinje          | ERROR    |   Failed to load: 1
2025-07-01 12:29:03.907 | akinje          | INFO     |     Critical failures: 1
2025-07-01 12:29:03.926 | akinje          | ERROR    | CRITICAL ERROR: One or more critical JML modules failed to load.
2025-07-01 12:29:03.942 | akinje          | INFO     | The script cannot continue without all required modules.
2025-07-01 12:29:03.964 | akinje          | INFO     | Critical Module Failures:
2025-07-01 12:29:03.976 | akinje          | INFO     |   - JML-Infrastructure/JML-UIManager
2025-07-01 12:29:03.989 | akinje          | ERROR    | Failed Module Details:
2025-07-01 12:29:03.999 | akinje          | INFO     |   - JML-Infrastructure/JML-UIManager
2025-07-01 12:29:04.016 | akinje          | ERROR    |     Error: Import failed for 'JML-Infrastructure/JML-UIManager': The running command stopped because the preference variable "ErrorActionPreference" or common parameter is set to Stop: At C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Modules\JML-Infrastructure\JML-UIManager.psm1:1425 char:22
+                     }
+                      ~
The Try statement is missing its Catch or Finally block.

At C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Modules\JML-Infrastructure\JML-UIManager.psm1:1570 char:22
+     'Add-UILogMessage'
+                      ~
The string is missing the terminator: '.

At C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Modules\JML-Infrastructure\JML-UIManager.psm1:1407 char:44
+             ShowEnhancedHierarchicalMenu = {
+                                            ~
Missing closing '}' in statement block or type definition.

At C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Modules\JML-Infrastructure\JML-UIManager.psm1:1572 char:1
+ 
The hash literal was incomplete.

At C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Modules\JML-Infrastructure\JML-UIManager.psm1:71 char:9
+     try {
+         ~
Missing closing '}' in statement block or type definition.

At C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Modules\JML-Infrastructure\JML-UIManager.psm1:1572 char:1
+ 
The Try statement is missing its Catch or Finally block.

At C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Modules\JML-Infrastructure\JML-UIManager.psm1:63 char:31
+ function New-UIManagerService {
+                               ~
Missing closing '}' in statement block or type definition.
2025-07-01 12:29:04.031 | akinje          | INFO     | Troubleshooting Steps:
2025-07-01 12:29:04.041 | akinje          | INFO     | 1. Verify all module files exist in the nested structure:
2025-07-01 12:29:04.051 | akinje          | INFO     |    - Core modules: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Modules\JML-Core
2025-07-01 12:29:04.062 | akinje          | INFO     |    - Feature modules: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Modules\JML-Features
2025-07-01 12:29:04.075 | akinje          | INFO     |    - Infrastructure modules: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Modules\JML-Infrastructure
2025-07-01 12:29:04.084 | akinje          | INFO     | 2. Check file permissions and ensure files are not blocked
2025-07-01 12:29:04.095 | akinje          | INFO     | 3. Run with -RunSetup to validate the environment
2025-07-01 12:29:04.107 | akinje          | INFO     | 4. Check PowerShell execution policy: Get-ExecutionPolicy
2025-07-01 12:29:04.119 | akinje          | INFO     | 5. For syntax errors in modules, check the module files for missing braces or invalid syntax
