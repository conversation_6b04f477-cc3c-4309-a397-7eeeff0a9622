@{
    # JML (Joiners, Movers and Leavers) Automation System Configuration
    # Version: 1.13 - Enhanced with Mathematical Optimization
    # Security Level: Enhanced
    # Performance: Optimized for Concurrent Operations
    
    # =============================================================================
    # GENERAL SETTINGS - Enhanced for v1.13
    # =============================================================================
    
    # Script behavior settings
    ScriptSettings = @{
        # Default domain for user accounts
        DefaultDomain = "jeragm.com"
        
        # Maximum retry attempts for operations
        MaxRetryAttempts = 3
        
        # Base delay for exponential backoff (seconds)
        BaseRetryDelay = 2
        
        # Maximum delay for exponential backoff (seconds)
        MaxRetryDelay = 30
        
        # Operation timeout in seconds
        OperationTimeout = 300
        
        # Enable progress indicators
        ShowProgress = $true
        
        # Enable detailed audit logging
        EnableAuditLogging = $true
        
        # NEW v1.13 SETTINGS - Performance Optimization
        EnableNonInteractiveMode = $true
        EnableDryRunMode = $true
        EnableBatchOperations = $true
        DefaultOperationMode = "Interactive"  # Interactive, NonInteractive, DryRun, Batch
        MaxConcurrentOperations = 3
        OperationQueueSize = 10
        EnableProgressReporting = $true
        ProgressReportingInterval = 2  # seconds
        
        # Performance optimization settings
        EnableParallelProcessing = $true
        ParallelProcessingEfficiency = 0.3  # 30% efficiency gain
        EnableCaching = $true
        CacheHitRate = 0.6  # 60% expected cache hit rate
        CacheEfficiency = 0.8  # 80% time saved on cache hits
        
        # Feature staging for phased rollout
        FeatureStaging = @{
            Phase1Features = @("NonInteractive", "EnhancedLogging", "BasicErrorHandling")
            Phase2Features = @("DryRun", "Reporting", "MoverFunctionality")
            Phase3Features = @("AdvancedSetup", "HealthChecks", "Analytics")
            CurrentPhase = 1  # Start with Phase 1
        }
    }
    
    # =============================================================================
    # PERFORMANCE CONFIGURATION - NEW for v1.13
    # =============================================================================
    
    Performance = @{
        # Execution time targets (seconds)
        TargetExecutionTimes = @{
            CreateOperation = 25  # Target: ≤10% increase from baseline 22s
            DeleteOperation = 21  # Target: ≤10% increase from baseline 19s
            ResetOperation = 22   # Target: ≤10% increase from baseline 20s
        }
        
        # Performance monitoring
        EnablePerformanceMonitoring = $true
        PerformanceThresholds = @{
            SlowOperationWarning = 30  # seconds
            SlowOperationError = 45    # seconds
            HighMemoryUsage = 100      # MB
            HighCpuUsage = 80          # percentage
        }
        
        # Optimization settings
        OptimizationSettings = @{
            EnableCodeOptimization = $true
            CodeOptimizationFactor = 0.95  # 5% improvement
            EnableNetworkOptimization = $true
            NetworkOptimizationFactor = 0.7  # 30% reduction via caching
            EnableMemoryOptimization = $true
            MemoryOptimizationTarget = 89  # MB target
        }
        
        # Concurrent processing
        ConcurrentProcessing = @{
            EnableConcurrency = $true
            MaxConcurrentOperations = 3
            ConcurrencyMode = "ThreadPool"  # ThreadPool, RunspacePool, Jobs
            ThreadPoolMinThreads = 2
            ThreadPoolMaxThreads = 5
            QueueTimeout = 300  # seconds
        }
    }
    
    # =============================================================================
    # LOGGING CONFIGURATION - Enhanced for v1.13
    # =============================================================================
    
    Logging = @{
        # Log directory path (supports environment variables)
        LogDirectory = "C:\Temp\Scripts\Desktop Support\Logs"
        
        # Log file retention in days
        LogRetentionDays = 30
        
        # Maximum log file size in MB
        MaxLogFileSizeMB = 10
        
        # Enable log file rotation
        EnableLogRotation = $true
        
        # Log levels to write to file
        FileLogLevels = @('DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL')
        
        # Log levels to write to console
        ConsoleLogLevels = @('INFO', 'WARNING', 'ERROR', 'CRITICAL')
        
        # Enable sensitive data redaction
        EnableDataRedaction = $true
        
        # NEW v1.13 LOGGING FEATURES
        TransactionIDLoggingEnabled = $true
        TransactionIDFormat = "JML-{0:yyyyMMdd}-{1:D6}"  # JML-20250109-000001
        PerformanceLoggingEnabled = $true
        PerformanceThresholds = @{
            CreateOperation = 30  # seconds
            DeleteOperation = 15  # seconds
            ResetOperation = 20   # seconds
        }
        LogCompressionEnabled = $true
        LogCompressionFormat = "zip"
        LogArchiveRetentionDays = 365
        
        # Enhanced audit trail
        AuditTrail = @{
            EnableTransactionTracking = $true
            EnablePerformanceMetrics = $true
            EnableResourceUsageTracking = $true
            EnableConcurrencyTracking = $true
        }
        
        # Data redaction settings
        DataRedaction = @{
            # Redact UPNs (user***@domain.com)
            RedactUPNs = $true
            
            # Redact email addresses
            RedactEmailAddresses = $true
            
            # Redact distinguished names (hash or complete redaction)
            RedactDistinguishedNames = $true
            
            # Redact server names/URLs
            RedactServerNames = $true
            
            # Hash algorithm for sensitive data (SHA256, SHA1, MD5)
            HashAlgorithm = "SHA256"
            
            # Salt for hashing (change this for your environment)
            HashSalt = "AdminAccountScript2024"
        }
    }
    
    # =============================================================================
    # ACTIVE DIRECTORY CONFIGURATION - Enhanced for v1.13
    # =============================================================================
    
    ActiveDirectory = @{
        # OU mappings for admin accounts
        OUMappings = @{
            "Singapore" = "OU=Singapore,OU=Admins,OU=Accounts,OU=JeraGM,DC=jeragm,DC=com"
            "United Kingdom" = "OU=London,OU=Admins,OU=Accounts,OU=JeraGM,DC=jeragm,DC=com"
            "London" = "OU=London,OU=Admins,OU=Accounts,OU=JeraGM,DC=jeragm,DC=com"
        }
        
        # Default OU if mapping not found
        DefaultOU = "OU=London,OU=Admins,OU=Accounts,OU=JeraGM,DC=jeragm,DC=com"
        
        # AD query optimization settings
        QueryOptimization = @{
            # Properties to retrieve for user queries
            UserProperties = @(
                'GivenName', 'Surname', 'DisplayName', 'SamAccountName',
                'UserPrincipalName', 'EmailAddress', 'Enabled', 'DistinguishedName'
            )
            
            # Maximum results for AD queries
            MaxResults = 1000
            
            # Query timeout in seconds
            QueryTimeout = 30
            
            # Enable result caching
            EnableCaching = $true
            
            # Cache expiration in minutes
            CacheExpirationMinutes = 15
        }
        
        # Password policy settings
        PasswordPolicy = @{
            # Password length
            MinLength = 12
            
            # Number of special characters required
            MinSpecialChars = 2
            
            # Enable password complexity
            RequireComplexity = $true
            
            # Password expiration settings
            PasswordNeverExpires = $false
            
            # Require password change at next logon
            ChangePasswordAtLogon = $true
        }
        
        # NEW v1.13 AD FEATURES
        ModificationSettings = @{
            AllowOUChanges = $true
            AllowDescriptionChanges = $true
            AllowAttributeUpdates = @('Department', 'Title', 'Manager')
            RequireApprovalForChanges = $false
        }
        
        HealthCheckSettings = @{
            EnableConnectivityTests = $true
            TestDomainControllers = @("dc1.jeragm.com", "dc2.jeragm.com")
            ConnectivityTestTimeout = 10  # seconds
            PermissionTestEnabled = $true
        }
        
        # Performance optimization
        PerformanceSettings = @{
            EnableParallelQueries = $true
            MaxParallelQueries = 3
            QueryBatchSize = 100
            EnableQueryCaching = $true
            CacheTimeout = 900  # 15 minutes
        }
    }
    
    # =============================================================================
    # EMAIL CONFIGURATION - Enhanced for v1.13
    # =============================================================================
    
    Email = @{
        # SMTP server settings
        SmtpServer = "smtp.jeragm.com"
        
        # SMTP port (25, 587, 465)
        SmtpPort = 25
        
        # Use SSL/TLS
        UseSSL = $false
        
        # Default sender address
        DefaultFrom = "<EMAIL>"
        
        # Support team email
        SupportEmail = "<EMAIL>"
        
        # Email retry settings
        RetrySettings = @{
            MaxAttempts = 3
            BaseDelay = 2
            MaxDelay = 16
            EnableExponentialBackoff = $true
        }
        
        # Email timeout in seconds
        Timeout = 30
        
        # Enable email notifications
        EnableNotifications = $true
        
        # NEW v1.13 EMAIL FEATURES
        TemplateSettings = @{
            UseExternalTemplates = $false
            TemplateDirectory = ".\Templates"
            Templates = @{
                Create = "AdminAccountCreated.html"
                Delete = "AdminAccountDeleted.html"
                Reset = "AdminAccountReset.html"
                Modify = "AdminAccountModified.html"
                Alert = "OperationalAlert.html"
            }
        }
        
        NotificationSettings = @{
            EnableOperationalAlerts = $true
            OperationalAlertRecipients = @("<EMAIL>")
            AlertThresholds = @{
                FailureRate = 0.05  # 5%
                ResponseTime = 60   # seconds
            }
        }
        
        # Performance optimization
        PerformanceSettings = @{
            EnableAsyncSending = $true
            BatchSize = 5
            BatchDelay = 1  # seconds between batches
            EnableConnectionPooling = $true
            MaxConnections = 3
        }
    }
    
    # =============================================================================
    # JIRA CONFIGURATION - Enhanced for v1.13
    # =============================================================================
    
    Jira = @{
        # Jira server URL (placeholder - will be replaced with [JIRA-INSTANCE] in logs)
        ServerUrl = "https://jeragm.atlassian.net"
        
        # Expected work types for validation
        ExpectedWorkTypes = @{
            CreateAdmin = "Service Request with Approvals"
            DeleteAdmin = "Service Request with Approvals"
            ResetAdmin = "Service Request with Approvals"
            ModifyAdmin = "Service Request with Approvals"  # NEW for v1.13
        }
        
        # Expected request types for validation
        ExpectedRequestTypes = @{
            CreateAdmin = "JERAGM Onboarding Request"
            DeleteAdmin = "Remove Admin Account"
            ResetAdmin = "Reset Admin Account"
            ModifyAdmin = "Modify Admin Account"  # NEW for v1.13
        }
        
        # Custom field IDs for data extraction
        CustomFields = @{
            FirstName = "customfield_10304"
            LastName = "customfield_10305"
            OfficeLocation = "customfield_10115"
            ITAdminAccount = "customfield_10453"
            Department = "customfield_10120"
            JobTitle = "customfield_10238"
            ModelAccount = "customfield_10343"
        }
        
        # API settings
        ApiSettings = @{
            # Request timeout in seconds
            Timeout = 60
            
            # Retry settings
            RetrySettings = @{
                MaxAttempts = 5
                BaseDelay = 1
                MaxDelay = 32
                EnableExponentialBackoff = $true
            }
            
            # Rate limiting
            RateLimit = @{
                RequestsPerMinute = 60
                EnableRateLimit = $true
            }
        }
        
        # Attachment settings
        AttachmentSettings = @{
            # Maximum file size in MB
            MaxFileSizeMB = 10
            
            # Allowed file types
            AllowedFileTypes = @('.log', '.txt', '.pdf', '.docx', '.xlsx')
            
            # Enable chunked upload for large files
            EnableChunkedUpload = $true
            
            # Chunk size in KB
            ChunkSizeKB = 1024
        }
        
        # Comment formatting settings
        CommentFormatting = @{
            # Use Atlassian Document Format (ADF) if supported
            PreferADF = $true
            
            # Fallback to wiki markup
            FallbackToWikiMarkup = $true
            
            # Enable rich formatting
            EnableRichFormatting = $true
        }
        
        # NEW v1.13 JIRA FEATURES
        ValidationSettings = @{
            EnableStrictValidation = $true
            RequiredFields = @("FirstName", "LastName", "OfficeLocation")
            AllowPartialFieldData = $false
            ValidateTicketStatus = $true
            AllowedStatuses = @("Open", "In Progress", "Pending")
        }
        
        AutomationSettings = @{
            EnableAutoTransition = $false
            TransitionMappings = @{
                CreateSuccess = "Resolved"
                CreateFailure = "Failed"
                DeleteSuccess = "Resolved"
                ResetSuccess = "Resolved"
                ModifySuccess = "Resolved"  # NEW for v1.13
            }
        }
        
        # Performance optimization
        PerformanceSettings = @{
            EnableConnectionPooling = $true
            MaxConnections = 3
            ConnectionTimeout = 30  # seconds
            EnableRequestCaching = $true
            CacheTimeout = 300  # 5 minutes
        }
    }
    
    # =============================================================================
    # SECURITY CONFIGURATION - Enhanced for v1.13
    # =============================================================================
    
    Security = @{
        # Credential storage settings
        CredentialStorage = @{
            # Primary method: SecretManagement, CredentialManager, EncryptedFile
            PrimaryMethod = "SecretManagement"
            
            # Fallback methods in order of preference
            FallbackMethods = @("CredentialManager", "EncryptedFile")
            
            # Secret vault name for SecretManagement
            SecretVaultName = "JMLAutomationVault"
            
            # Credential names in vault
            CredentialNames = @{
                JiraUsername = "JMLScript-JiraUsername"
                JiraApiToken = "JMLScript-JiraApiToken"
                SmtpCredentials = "JMLScript-SmtpCredentials"
            }
            
            # Encrypted file settings (fallback)
            EncryptedFileSettings = @{
                FilePath = ".\Modules\SecureCredentials.xml"
                UseUserScope = $true
                EnableCompression = $true
            }
        }
        
        # Input validation settings
        InputValidation = @{
            # Enable strict validation
            EnableStrictValidation = $true
            
            # Maximum input length
            MaxInputLength = 256
            
            # Allowed characters regex patterns
            AllowedPatterns = @{
                UPN = '^[\w][\w\.-]{1,62}[\w]@[\w][\w-]{1,62}\.[a-zA-Z]{2,}$'
                SamAccountName = '^[a-zA-Z0-9._-]{1,20}$'
                DisplayName = '^[a-zA-Z0-9\s._-]{1,64}$'
            }
            
            # Sanitization settings
            EnableSanitization = $true
            RemoveHtmlTags = $true
            RemoveScriptTags = $true
        }
        
        # Audit trail settings
        AuditTrail = @{
            # Enable comprehensive audit logging
            EnableAuditTrail = $true
            
            # Log user identity for all operations
            LogUserIdentity = $true
            
            # Log function parameters (with redaction)
            LogFunctionParameters = $true
            
            # Log execution context
            LogExecutionContext = $true
            
            # Audit log retention in days
            AuditRetentionDays = 90
        }
        
        # NEW v1.13 SECURITY FEATURES
        ConcurrencySecuritySettings = @{
            EnableConcurrentOperationTracking = $true
            MaxConcurrentOperationsPerUser = 2
            EnableOperationLocking = $true
            LockTimeout = 300  # seconds
        }
        
        EnhancedValidation = @{
            EnableCrossReferenceValidation = $true
            EnableDuplicateDetection = $true
            EnableIntegrityChecks = $true
            ValidationTimeout = 30  # seconds
        }
    }
    
    # =============================================================================
    # REPORTING CONFIGURATION - NEW for v1.13
    # =============================================================================
    
    Reporting = @{
        EnableReporting = $true
        ReportDirectory = "C:\Temp\Scripts\Desktop Support\Reports"
        ReportRetentionDays = 90
        DefaultReportFormat = "HTML"  # HTML, CSV, JSON, XML
        
        ReportTypes = @{
            Summary = @{
                Enabled = $true
                IncludeMetrics = @("OperationCount", "SuccessRate", "AverageTime")
                GroupBy = @("Operation", "Date", "User")
            }
            Performance = @{
                Enabled = $true
                IncludeMetrics = @("ExecutionTime", "RetryCount", "ErrorRate", "ConcurrencyLevel")
                Thresholds = @{
                    SlowOperation = 30  # seconds
                    HighErrorRate = 0.1  # 10%
                }
            }
            Audit = @{
                Enabled = $true
                IncludeFields = @("Timestamp", "User", "Operation", "Target", "Result", "TransactionID")
                RedactSensitiveData = $true
            }
            Capacity = @{
                Enabled = $true
                IncludeMetrics = @("ThroughputPerHour", "ConcurrentOperations", "QueueLength", "ResourceUsage")
                TrackingInterval = 300  # 5 minutes
            }
        }
        
        ScheduledReports = @{
            Enabled = $false
            Schedule = @{
                Daily = @{
                    Enabled = $false
                    Time = "08:00"
                    Recipients = @("<EMAIL>")
                }
                Weekly = @{
                    Enabled = $true
                    Day = "Monday"
                    Time = "09:00"
                    Recipients = @("<EMAIL>")
                }
                Monthly = @{
                    Enabled = $true
                    Day = 1
                    Time = "09:00"
                    Recipients = @("<EMAIL>")
                }
            }
        }
        
        # Performance analytics
        Analytics = @{
            EnableTrendAnalysis = $true
            EnablePredictiveAnalytics = $false  # Future feature
            EnableAnomalyDetection = $true
            BaselineCalculationDays = 30
        }
    }
    
    # =============================================================================
    # USER EXPERIENCE CONFIGURATION - Enhanced for v1.13
    # =============================================================================
    
    UserExperience = @{
        # Console output settings
        ConsoleOutput = @{
            # Color scheme
            Colors = @{
                Success = "Green"
                Warning = "Yellow"
                Error = "Red"
                Information = "Cyan"
                Debug = "Gray"
                Progress = "Blue"
            }
            
            # Enable colored output
            EnableColors = $true
            
            # Message formatting
            EnableTimestamps = $true
            EnableLogLevels = $true
            EnableProgressBars = $true
        }
        
        # Progress indicator settings
        ProgressIndicators = @{
            # Enable progress bars
            EnableProgressBars = $true
            
            # Show estimated time remaining
            ShowTimeRemaining = $true
            
            # Show percentage completion
            ShowPercentage = $true
            
            # Update interval in milliseconds
            UpdateInterval = 500
        }
        
        # Input prompts
        InputPrompts = @{
            # Enable input validation feedback
            EnableValidationFeedback = $true
            
            # Show help text for inputs
            ShowHelpText = $true
            
            # Enable auto-completion where possible
            EnableAutoCompletion = $true
        }
        
        # NEW v1.13 UX FEATURES
        NavigationEnhancements = @{
            EnableBackNavigation = $true
            EnableContextualHelp = $true
            EnableBreadcrumbs = $true
            ShowOperationProgress = $true
        }
        
        ErrorRecoveryGuidance = @{
            EnableIntelligentTroubleshooting = $true
            ShowRecoveryOptions = $true
            EnableAutoRetryPrompts = $true
            ProvideContactInformation = $true
        }
        
        BatchOperations = @{
            EnableBatchMode = $true
            MaxBatchSize = 10
            BatchProgressReporting = $true
            EnableBatchValidation = $true
        }
    }
    
    # =============================================================================
    # MICROSERVICE ARCHITECTURE - NEW for v1.13
    # =============================================================================
    
    MicroserviceArchitecture = @{
        EnableMicroserviceMode = $false  # Phase 3 feature
        
        Services = @{
            AuthenticationService = @{
                Enabled = $false
                Timeout = 2  # seconds
                MaxRetries = 2
            }
            ValidationService = @{
                Enabled = $false
                Timeout = 1  # seconds
                MaxRetries = 2
            }
            ADOperationsService = @{
                Enabled = $false
                Timeout = 5  # seconds
                MaxRetries = 3
            }
            JiraService = @{
                Enabled = $false
                Timeout = 8  # seconds
                MaxRetries = 3
            }
            EmailService = @{
                Enabled = $false
                Timeout = 4  # seconds
                MaxRetries = 2
            }
            LoggingService = @{
                Enabled = $false
                Timeout = 1  # seconds
                MaxRetries = 1
            }
        }
        
        CoordinationSettings = @{
            CoordinationOverhead = 2  # seconds
            EnableServiceHealthChecks = $true
            HealthCheckInterval = 60  # seconds
            EnableCircuitBreaker = $true
        }
    }
    
    # =============================================================================
    # ASYNCHRONOUS PROCESSING - NEW for v1.13
    # =============================================================================
    
    AsynchronousProcessing = @{
        EnableAsyncMode = $false  # Phase 3 feature
        
        ImmediateResponse = @{
            ResponseTime = 2  # seconds
            IncludeTransactionID = $true
            IncludeEstimatedCompletion = $true
        }
        
        BackgroundProcessing = @{
            ProcessingTimeout = 300  # seconds
            EnableStatusUpdates = $true
            StatusUpdateInterval = 30  # seconds
            EnableNotificationOnCompletion = $true
        }
        
        QueueManagement = @{
            MaxQueueSize = 50
            QueueTimeout = 3600  # 1 hour
            EnablePriorityQueuing = $true
            PriorityLevels = @("High", "Normal", "Low")
        }
    }
}