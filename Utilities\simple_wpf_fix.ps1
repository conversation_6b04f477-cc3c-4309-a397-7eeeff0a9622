# Simple string-based replacement for problematic types
$scriptPath = "OnboardingFromJiraGUI.ps1"
$content = Get-Content $scriptPath -Raw

Write-Host "Applying simple string-based WPF fixes..." -ForegroundColor Yellow

# Revert the try/catch and use simpler approaches
$content = $content -replace 'try \{ \[System\.Windows\.GridUnitType\]::(\w+) \} catch \{ "Star" \}', '"$1"'
$content = $content -replace 'try \{ \[System\.Windows\.Media\.Brushes\]::(\w+) \} catch \{ \$null \}', '"$1"'

# Fix the remaining issues with direct string replacement
$content = $content -replace 'try \{ \[System\.Windows\.Controls\.Grid\]::(\w+)\(([^)]+)\) \} catch \{ \}', '& { [System.Windows.Controls.Grid]::$1($2) }'
$content = $content -replace 'try \{ \[System\.Windows\.Controls\.DockPanel\]::(\w+)\(([^)]+)\) \} catch \{ \}', '& { [System.Windows.Controls.DockPanel]::$1($2) }'

$content | Set-Content $scriptPath -Encoding UTF8

Write-Host "Applied simple string-based WPF fixes" -ForegroundColor Green