# Simple syntax check script
$errors = @()
$tokens = @()

try {
    $null = [System.Management.Automation.Language.Parser]::ParseFile("OnboardingFromJiraGUI.ps1", [ref]$tokens, [ref]$errors)
    
    if ($errors.Count -eq 0) {
        Write-Host "✓ No syntax errors found in OnboardingFromJiraGUI.ps1" -ForegroundColor Green
    } else {
        Write-Host "✗ Found $($errors.Count) syntax error(s):" -ForegroundColor Red
        foreach ($error in $errors) {
            Write-Host "  Line $($error.Extent.StartLineNumber): $($error.Message)" -ForegroundColor Yellow
        }
    }
} catch {
    Write-Host "Error checking syntax: $($_.Exception.Message)" -ForegroundColor Red
}
