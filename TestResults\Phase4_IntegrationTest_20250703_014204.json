﻿{
    "Duration":  2.8373773,
    "StartTime":  {
                      "value":  "\/Date(1751503321217)\/",
                      "DisplayHint":  2,
                      "DateTime":  "Thursday, July 3, 2025 1:42:01 AM"
                  },
    "SkippedTests":  0,
    "FailedTests":  2,
    "Errors":  [
                   "Script Loading: Script loading failed: FAILED: At C:\\Users\\<USER>\\OneDrive - JERA Global Markets\\Azure Devops\\it-endusersupport-automation\\scripts\\Utilities\\OnboardingFromJiraGUI.ps1:8948 char:11 +     [void]SaveDocumentation([string]$Path, [string]$Content) { +           ~~~~~~~~~~~~~~~~~ The member \u0027SaveDocumentation\u0027 is already defined.",
                   "Documentation Coverage: Low documentation: 6.99% comment lines"
               ],
    "PassedTests":  25,
    "TestDetails":  [
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751503321726)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Thursday, July 3, 2025 1:42:01 AM"
                                          },
                            "Name":  "File Existence",
                            "Message":  "Script file found",
                            "Details":  null
                        },
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751503321783)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Thursday, July 3, 2025 1:42:01 AM"
                                          },
                            "Name":  "File Size",
                            "Message":  "Script size: 0.36 MB (377572 bytes)",
                            "Details":  null
                        },
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751503321922)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Thursday, July 3, 2025 1:42:01 AM"
                                          },
                            "Name":  "Line Count",
                            "Message":  "Total lines: 10149",
                            "Details":  null
                        },
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751503322119)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Thursday, July 3, 2025 1:42:02 AM"
                                          },
                            "Name":  "PowerShell Parser",
                            "Message":  "No syntax errors detected",
                            "Details":  null
                        },
                        {
                            "Status":  "FAILED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751503323152)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Thursday, July 3, 2025 1:42:03 AM"
                                          },
                            "Name":  "Script Loading",
                            "Message":  "Script loading failed: FAILED: At C:\\Users\\<USER>\\OneDrive - JERA Global Markets\\Azure Devops\\it-endusersupport-automation\\scripts\\Utilities\\OnboardingFromJiraGUI.ps1:8948 char:11 +     [void]SaveDocumentation([string]$Path, [string]$Content) { +           ~~~~~~~~~~~~~~~~~ The member \u0027SaveDocumentation\u0027 is already defined.",
                            "Details":  null
                        },
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751503323173)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Thursday, July 3, 2025 1:42:03 AM"
                                          },
                            "Name":  "Class Definition: AdvancedLoggingManager",
                            "Message":  "Class definition found",
                            "Details":  null
                        },
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751503323183)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Thursday, July 3, 2025 1:42:03 AM"
                                          },
                            "Name":  "Class Definition: VersionManager",
                            "Message":  "Class definition found",
                            "Details":  null
                        },
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751503323194)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Thursday, July 3, 2025 1:42:03 AM"
                                          },
                            "Name":  "Class Definition: TestingFramework",
                            "Message":  "Class definition found",
                            "Details":  null
                        },
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751503323209)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Thursday, July 3, 2025 1:42:03 AM"
                                          },
                            "Name":  "Class Definition: DocumentationGenerator",
                            "Message":  "Class definition found",
                            "Details":  null
                        },
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751503323224)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Thursday, July 3, 2025 1:42:03 AM"
                                          },
                            "Name":  "Function Definition: Write-StructuredLog",
                            "Message":  "Function definition found",
                            "Details":  null
                        },
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751503323236)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Thursday, July 3, 2025 1:42:03 AM"
                                          },
                            "Name":  "Function Definition: Get-ScriptVersion",
                            "Message":  "Function definition found",
                            "Details":  null
                        },
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751503323248)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Thursday, July 3, 2025 1:42:03 AM"
                                          },
                            "Name":  "Function Definition: Register-Test",
                            "Message":  "Function definition found",
                            "Details":  null
                        },
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751503323258)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Thursday, July 3, 2025 1:42:03 AM"
                                          },
                            "Name":  "Function Definition: New-Documentation",
                            "Message":  "Function definition found",
                            "Details":  null
                        },
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751503323270)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Thursday, July 3, 2025 1:42:03 AM"
                                          },
                            "Name":  "Function Definition: Show-ScriptHelp",
                            "Message":  "Function definition found",
                            "Details":  null
                        },
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751503323284)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Thursday, July 3, 2025 1:42:03 AM"
                                          },
                            "Name":  "Phase 1 Integration: ConfigurationManager",
                            "Message":  "Feature preserved",
                            "Details":  null
                        },
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751503323298)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Thursday, July 3, 2025 1:42:03 AM"
                                          },
                            "Name":  "Phase 1 Integration: Get-ConfigurationValue",
                            "Message":  "Feature preserved",
                            "Details":  null
                        },
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751503323322)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Thursday, July 3, 2025 1:42:03 AM"
                                          },
                            "Name":  "Phase 1 Integration: Write-ErrorLog",
                            "Message":  "Feature preserved",
                            "Details":  null
                        },
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751503323354)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Thursday, July 3, 2025 1:42:03 AM"
                                          },
                            "Name":  "Phase 2 Integration: AutoCompletionHistory",
                            "Message":  "Feature preserved",
                            "Details":  null
                        },
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751503323413)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Thursday, July 3, 2025 1:42:03 AM"
                                          },
                            "Name":  "Phase 2 Integration: Apply-ModernUIIntegration",
                            "Message":  "Feature preserved",
                            "Details":  null
                        },
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751503323585)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Thursday, July 3, 2025 1:42:03 AM"
                                          },
                            "Name":  "Phase 2 Integration: Show-ProgressDialog",
                            "Message":  "Feature preserved",
                            "Details":  null
                        },
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751503323622)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Thursday, July 3, 2025 1:42:03 AM"
                                          },
                            "Name":  "Phase 3 Integration: OrganizationalDataManager",
                            "Message":  "Feature preserved",
                            "Details":  null
                        },
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751503323639)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Thursday, July 3, 2025 1:42:03 AM"
                                          },
                            "Name":  "Phase 3 Integration: BatchOperationManager",
                            "Message":  "Feature preserved",
                            "Details":  null
                        },
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751503323653)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Thursday, July 3, 2025 1:42:03 AM"
                                          },
                            "Name":  "Phase 3 Integration: EnhancedJiraManager",
                            "Message":  "Feature preserved",
                            "Details":  null
                        },
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751503323668)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Thursday, July 3, 2025 1:42:03 AM"
                                          },
                            "Name":  "Phase 3 Integration: AdvancedADGroupManager",
                            "Message":  "Feature preserved",
                            "Details":  null
                        },
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751503323733)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Thursday, July 3, 2025 1:42:03 AM"
                                          },
                            "Name":  "Region Structure",
                            "Message":  "Balanced regions: 31 regions",
                            "Details":  null
                        },
                        {
                            "Status":  "PASSED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751503323751)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Thursday, July 3, 2025 1:42:03 AM"
                                          },
                            "Name":  "Error Handling",
                            "Message":  "Good error handling coverage: 132 try blocks",
                            "Details":  null
                        },
                        {
                            "Status":  "FAILED",
                            "Timestamp":  {
                                              "value":  "\/Date(1751503324054)\/",
                                              "DisplayHint":  2,
                                              "DateTime":  "Thursday, July 3, 2025 1:42:04 AM"
                                          },
                            "Name":  "Documentation Coverage",
                            "Message":  "Low documentation: 6.99% comment lines",
                            "Details":  null
                        }
                    ],
    "EndTime":  {
                    "value":  "\/Date(1751503324054)\/",
                    "DisplayHint":  2,
                    "DateTime":  "Thursday, July 3, 2025 1:42:04 AM"
                },
    "TotalTests":  27
}
