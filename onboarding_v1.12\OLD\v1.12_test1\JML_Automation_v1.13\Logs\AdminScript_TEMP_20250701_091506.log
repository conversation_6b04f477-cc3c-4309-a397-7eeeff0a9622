2025-07-01 09:15:06.125 | akinje          | INFO     | JML Automation Script v1.13 starting...
2025-07-01 09:15:06.140 | akinje          | DEBUG    | Script path: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13
2025-07-01 09:15:06.171 | akinje          | DEBUG    | PowerShell version: 7.5.1
2025-07-01 09:15:08.040 | akinje          | DEBUG    | Execution policy: Bypass
2025-07-01 09:15:08.064 | akinje          | DEBUG    | User: akinje
2025-07-01 09:15:08.083 | akinje          | DEBUG    | Computer: JGM-CG7THR3
2025-07-01 09:15:08.345 | akinje          | INFO     | === JML ENVIRONMENT VALIDATION ===
2025-07-01 09:15:08.398 | akinje          | INFO     | Verifying PowerShell Version...
2025-07-01 09:15:08.435 | akinje          | INFO     | === JML ENVIRONMENT VALIDATION ===
2025-07-01 09:15:08.480 | akinje          | INFO     | Verifying PowerShell Version...
2025-07-01 09:15:08.546 | akinje          | INFO     | PowerShell 7+ detected (version 7.5.1). Checking for updates...
2025-07-01 09:15:08.628 | akinje          | INFO     | PowerShell 7+ detected (version 7.5.1)
2025-07-01 09:15:08.667 | akinje          | INFO     | Checking for PowerShell updates online...
2025-07-01 09:15:08.715 | akinje          | DEBUG    | Checking for PowerShell updates online...
2025-07-01 09:15:09.229 | akinje          | INFO     | A newer version of PowerShell (7.5.2) is available.
2025-07-01 09:15:09.264 | akinje          | INFO     | Current version: 7.5.1
2025-07-01 09:15:09.300 | akinje          | INFO     | Download from: https://github.com/PowerShell/PowerShell/releases
2025-07-01 09:15:09.337 | akinje          | INFO     | === CHECKING REQUIRED MODULES ===
2025-07-01 09:15:09.367 | akinje          | INFO     | === CHECKING REQUIRED MODULES ===
2025-07-01 09:15:09.428 | akinje          | INFO     | Checking for required modules and available updates...
2025-07-01 09:15:30.115 | akinje          | INFO     | Checking module: Microsoft.PowerShell.SecretManagement...
2025-07-01 09:15:30.322 | akinje          | INFO     |   Found installed version 1.1.2
2025-07-01 09:15:31.365 | akinje          | INFO     |   Already up to date
2025-07-01 09:15:31.438 | akinje          | INFO     | Checking module: Microsoft.PowerShell.SecretStore...
2025-07-01 09:15:32.398 | akinje          | INFO     |   Found installed version 1.0.6
2025-07-01 09:15:33.773 | akinje          | INFO     |   Already up to date
2025-07-01 09:15:33.826 | akinje          | INFO     | Checking module: Microsoft.Graph...
2025-07-01 09:15:34.152 | akinje          | INFO     |   Found installed version 2.28.0
2025-07-01 09:15:35.126 | akinje          | INFO     |   Already up to date
2025-07-01 09:15:35.179 | akinje          | INFO     | Checking module: JiraPS...
2025-07-01 09:15:35.936 | akinje          | INFO     |   Found installed version 2.14.7
2025-07-01 09:15:36.904 | akinje          | INFO     |   Already up to date
2025-07-01 09:15:36.944 | akinje          | INFO     | Checking module: ImportExcel...
2025-07-01 09:15:37.263 | akinje          | INFO     |   Found installed version 7.8.10
2025-07-01 09:15:38.648 | akinje          | INFO     |   Already up to date
2025-07-01 09:15:38.678 | akinje          | INFO     | Checking module: PSWriteHTML...
2025-07-01 09:15:39.952 | akinje          | INFO     |   Found installed version 1.36.0
2025-07-01 09:15:40.799 | akinje          | INFO     |   Already up to date
2025-07-01 09:15:40.836 | akinje          | INFO     | All modules are up to date.
2025-07-01 09:15:40.870 | akinje          | INFO     | Verifying module availability...
2025-07-01 09:15:40.979 | akinje          | INFO     |   [VERIFIED] Microsoft.PowerShell.SecretManagement v1.1.2 is available.
2025-07-01 09:15:41.092 | akinje          | INFO     |   [VERIFIED] Microsoft.PowerShell.SecretStore v1.0.6 is available.
2025-07-01 09:15:41.206 | akinje          | INFO     |   [VERIFIED] Microsoft.Graph v2.28.0 is available.
2025-07-01 09:15:41.300 | akinje          | INFO     |   [VERIFIED] JiraPS v2.14.7 is available.
2025-07-01 09:15:41.428 | akinje          | INFO     |   [VERIFIED] ImportExcel v7.8.10 is available.
2025-07-01 09:15:41.566 | akinje          | INFO     |   [VERIFIED] PSWriteHTML v1.36.0 is available.
2025-07-01 09:15:41.601 | akinje          | INFO     | All required modules are successfully installed and available.
2025-07-01 09:15:41.633 | akinje          | INFO     | === JML ENVIRONMENT VALIDATION COMPLETE ===
2025-07-01 09:15:41.666 | akinje          | DEBUG    | [DEBUG] Starting JML script execution...
2025-07-01 09:15:41.699 | akinje          | DEBUG    | [DEBUG] Script directory: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13
2025-07-01 09:15:41.730 | akinje          | DEBUG    | [DEBUG] Checking ActiveDirectory module availability...
2025-07-01 09:15:41.853 | akinje          | INFO     | [INFO] ActiveDirectory module is not available - running in UAT mode
2025-07-01 09:15:41.886 | akinje          | INFO     | [INFO] This is normal for UAT/test environments
2025-07-01 09:15:41.921 | akinje          | DEBUG    | [DEBUG] Initializing JML paths...
2025-07-01 09:15:41.976 | akinje          | DEBUG    | [DEBUG] JML Paths initialized globally.
2025-07-01 09:15:42.008 | akinje          | DEBUG    | [DEBUG] Project Root: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13
2025-07-01 09:15:42.045 | akinje          | DEBUG    | [DEBUG] Modules Root: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Modules
2025-07-01 09:15:42.081 | akinje          | DEBUG    | [DEBUG] Config Dir: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Config
2025-07-01 09:15:42.116 | akinje          | DEBUG    | [DEBUG] Updated ConfigPath to use Config directory: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Config\AdminAccountConfig.psd1
2025-07-01 09:15:42.143 | akinje          | DEBUG    | [DEBUG] Starting enhanced module loading...
2025-07-01 09:15:42.183 | akinje          | INFO     | Loading JML modules from nested structure...
2025-07-01 09:15:42.214 | akinje          | INFO     | Modules Root: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Modules
2025-07-01 09:15:42.250 | akinje          | INFO     |   Loading: JML-Core/JML-Utilities [CRITICAL]
2025-07-01 09:15:42.280 | akinje          | INFO     |     Description: General utility functions
2025-07-01 09:15:42.314 | akinje          | INFO     |     Path: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Modules\JML-Core\JML-Utilities.psd1 (manifest)
2025-07-01 09:15:42.352 | akinje          | INFO     |     Attempting to import module using manifest...
2025-07-01 09:15:42.687 | akinje          | WARNING  | WARNING: JML-Security: JML-Configuration module functions not available. Some features may be limited.
2025-07-01 09:15:43.070 | akinje          | WARNING  | WARNING: JML-Logging: Missing dependencies: Get-ModuleConfiguration (JML-Configuration), Protect-SensitiveData (JML-Security). Some features may be limited.
2025-07-01 09:15:43.120 | akinje          | INFO     |     [SUCCESS] Module loaded: JML-Core/JML-Utilities
2025-07-01 09:15:43.346 | akinje          | INFO     |     [WARNING] Critical function Get-StringHash not available after import
2025-07-01 09:15:43.515 | akinje          | INFO     |     [WARNING] Critical function Test-ModuleAvailability not available after import
2025-07-01 09:15:43.569 | akinje          | INFO     |   Loading: JML-Core/JML-Configuration [CRITICAL]
2025-07-01 09:15:43.607 | akinje          | INFO     |     Description: Core configuration management
2025-07-01 09:15:43.650 | akinje          | INFO     |     Path: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Modules\JML-Core\JML-Configuration.psd1 (manifest)
2025-07-01 09:15:43.694 | akinje          | INFO     |     Attempting to import module using manifest...
2025-07-01 09:15:43.779 | akinje          | INFO     |     [SUCCESS] Module loaded: JML-Core/JML-Configuration
2025-07-01 09:15:43.823 | akinje          | INFO     |     [VERIFIED] Function Initialize-SmartConfiguration is available
2025-07-01 09:15:43.869 | akinje          | INFO     |     [VERIFIED] Function Get-ModuleConfiguration is available
2025-07-01 09:15:43.917 | akinje          | INFO     |   Loading: JML-Core/JML-Security [CRITICAL]
2025-07-01 09:15:43.959 | akinje          | INFO     |     Description: Security and credential management
2025-07-01 09:15:44.006 | akinje          | INFO     |     Path: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Modules\JML-Core\JML-Security.psd1 (manifest)
2025-07-01 09:15:44.042 | akinje          | INFO     |     Attempting to import module using manifest...
2025-07-01 09:15:44.112 | akinje          | INFO     |     [SUCCESS] Module loaded: JML-Core/JML-Security
2025-07-01 09:15:44.154 | akinje          | INFO     |     [VERIFIED] Function Get-SecureCredential is available
2025-07-01 09:15:44.370 | akinje          | INFO     |     [WARNING] Critical function Set-SecureCredential not available after import
2025-07-01 09:15:44.418 | akinje          | INFO     |   Loading: JML-Core/JML-Logging [CRITICAL]
2025-07-01 09:15:44.470 | akinje          | INFO     |     Description: Secure logging functionality
2025-07-01 09:15:44.512 | akinje          | INFO     |     Path: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Modules\JML-Core\JML-Logging.psd1 (manifest)
2025-07-01 09:15:44.561 | akinje          | INFO     |     Attempting to import module using manifest...
2025-07-01 09:15:44.658 | akinje          | INFO     |     [SUCCESS] Module loaded: JML-Core/JML-Logging
2025-07-01 09:15:44.717 | akinje          | INFO     |     [VERIFIED] Function Write-SecureLog is available
2025-07-01 09:15:44.896 | akinje          | INFO     |     [WARNING] Critical function Initialize-LoggingSystem not available after import
2025-07-01 09:15:44.935 | akinje          | INFO     |   Loading: JML-Features/JML-ActiveDirectory [OPTIONAL]
2025-07-01 09:15:44.974 | akinje          | INFO     |     Description: Active Directory operations
2025-07-01 09:15:45.041 | akinje          | INFO     |     Path: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Modules\JML-Features\JML-ActiveDirectory.psd1 (manifest)
2025-07-01 09:15:45.099 | akinje          | INFO     |     Attempting to import module using manifest...
2025-07-01 09:15:45.252 | akinje          | INFO     |     [SUCCESS] Module loaded: JML-Features/JML-ActiveDirectory
2025-07-01 09:15:45.295 | akinje          | INFO     |   Loading: JML-Features/JML-Email [OPTIONAL]
2025-07-01 09:15:45.333 | akinje          | INFO     |     Description: Email notification functionality
2025-07-01 09:15:45.372 | akinje          | INFO     |     Path: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Modules\JML-Features\JML-Email.psd1 (manifest)
2025-07-01 09:15:45.415 | akinje          | INFO     |     Attempting to import module using manifest...
2025-07-01 09:15:45.550 | akinje          | INFO     |     [SUCCESS] Module loaded: JML-Features/JML-Email
2025-07-01 09:15:45.621 | akinje          | INFO     |   Loading: JML-Features/JML-Jira [OPTIONAL]
2025-07-01 09:15:45.716 | akinje          | INFO     |     Description: Jira integration
2025-07-01 09:15:45.807 | akinje          | INFO     |     Path: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Modules\JML-Features\JML-Jira.psd1 (manifest)
2025-07-01 09:15:45.861 | akinje          | INFO     |     Attempting to import module using manifest...
2025-07-01 09:15:45.956 | akinje          | INFO     |     [SUCCESS] Module loaded: JML-Features/JML-Jira
2025-07-01 09:15:46.017 | akinje          | INFO     |   Loading: JML-Infrastructure/JML-Setup [OPTIONAL]
2025-07-01 09:15:46.102 | akinje          | INFO     |     Description: Setup and environment validation
2025-07-01 09:15:46.164 | akinje          | INFO     |     Path: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Modules\JML-Infrastructure\JML-Setup.psd1 (manifest)
2025-07-01 09:15:46.217 | akinje          | INFO     |     Attempting to import module using manifest...
2025-07-01 09:15:46.452 | akinje          | INFO     |     [SUCCESS] Module loaded: JML-Infrastructure/JML-Setup
2025-07-01 09:15:46.511 | akinje          | INFO     |   Loading: JML-Infrastructure/JML-Monitoring [OPTIONAL]
2025-07-01 09:15:46.543 | akinje          | INFO     |     Description: System monitoring
2025-07-01 09:15:46.601 | akinje          | INFO     |     Path: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Modules\JML-Infrastructure\JML-Monitoring.psd1 (manifest)
2025-07-01 09:15:46.662 | akinje          | INFO     |     Attempting to import module using manifest...
2025-07-01 09:15:46.766 | akinje          | INFO     |     [SUCCESS] Module loaded: JML-Infrastructure/JML-Monitoring
2025-07-01 09:15:46.813 | akinje          | INFO     |   Loading: JML-Infrastructure/JML-UIManager [OPTIONAL]
2025-07-01 09:15:46.854 | akinje          | INFO     |     Description: Modern hybrid dashboard UI with real-time status
2025-07-01 09:15:46.892 | akinje          | INFO     |     Path: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Modules\JML-Infrastructure\JML-UIManager.psd1 (manifest)
2025-07-01 09:15:46.929 | akinje          | INFO     |     Attempting to import module using manifest...
2025-07-01 09:15:47.039 | akinje          | INFO     |     [SUCCESS] Module loaded: JML-Infrastructure/JML-UIManager
2025-07-01 09:15:47.077 | akinje          | INFO     | Module Loading Summary:
2025-07-01 09:15:47.146 | akinje          | INFO     |   Successfully loaded: 10/10
2025-07-01 09:15:47.193 | akinje          | DEBUG    | [DEBUG] Enhanced module loading completed successfully
2025-07-01 09:15:47.234 | akinje          | DEBUG    | [DEBUG] Performing post-loading verification...
2025-07-01 09:15:47.278 | akinje          | DEBUG    | [DEBUG] ✓ Initialize-SmartConfiguration is available
2025-07-01 09:15:47.323 | akinje          | DEBUG    | [DEBUG] ✓ Get-ModuleConfiguration is available
2025-07-01 09:16:23.233 | akinje          | INFO     | Configuration loaded from: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Config\AdminAccountConfig.psd1
2025-07-01 09:16:23.272 | akinje          | INFO     | [SUCCESS] Configuration initialized successfully
2025-07-01 09:16:23.300 | akinje          | DEBUG    | [DEBUG] Initializing credential storage selection...
2025-07-01 09:16:23.369 | akinje          | INFO     | ================================================================================
2025-07-01 09:16:23.401 | akinje          | INFO     |                     JML Credential Storage Selection                      
2025-07-01 09:16:23.438 | akinje          | INFO     | ================================================================================
2025-07-01 09:16:23.469 | akinje          | INFO     | Please select your preferred credential storage method:
2025-07-01 09:16:23.512 | akinje          | INFO     |   1. Encrypted File (Recommended for Portability)
2025-07-01 09:16:23.555 | akinje          | INFO     |      - Stores credentials in an encrypted XML file
2025-07-01 09:16:23.586 | akinje          | INFO     |      - Portable across different machines
2025-07-01 09:16:23.627 | akinje          | INFO     |      - Uses Windows DPAPI for encryption
2025-07-01 09:16:23.746 | akinje          | INFO     |   2. PowerShell Secret Management / SecretStore
2025-07-01 09:16:23.790 | akinje          | INFO     |      - Uses Microsoft.PowerShell.SecretStore module
2025-07-01 09:16:23.826 | akinje          | INFO     |      - Requires vault password for access
2025-07-01 09:16:23.866 | akinje          | INFO     |      - More secure but requires setup
2025-07-01 09:16:23.900 | akinje          | INFO     |   3. Windows Credential Manager
2025-07-01 09:16:23.932 | akinje          | INFO     |      - Uses Windows built-in credential storage
2025-07-01 09:16:23.969 | akinje          | INFO     |      - Integrated with Windows security
2025-07-01 09:16:24.001 | akinje          | INFO     |      - Machine-specific storage
2025-07-01 09:16:24.056 | akinje          | INFO     | Enter your choice (1-3) [Default: 1 in 10 seconds]: 
2025-07-01 09:16:34.174 | akinje          | INFO     | 1 (default - Encrypted File)
2025-07-01 09:16:34.278 | akinje          | INFO     | Selected: Encrypted File storage
2025-07-01 09:16:34.314 | akinje          | INFO     | Credentials will be stored in an encrypted XML file for portability.
2025-07-01 09:16:34.351 | akinje          | INFO     | Credential storage method set for this session: EncryptedFile
2025-07-01 09:16:34.384 | akinje          | INFO     | Press any key to continue...
2025-07-01 09:16:58.695 | akinje          | DEBUG    | [DEBUG] Credential storage method selected: EncryptedFile
2025-07-01 09:16:58.752 | akinje          | DEBUG    | [DEBUG] Using credential method for status check: EncryptedFile
2025-07-01 09:16:58.819 | akinje          | DEBUG    | [DEBUG] Checking credential setup status for method: EncryptedFile
2025-07-01 09:16:58.857 | akinje          | DEBUG    | [DEBUG] Checking EncryptedFile credential storage
2025-07-01 09:16:58.907 | akinje          | DEBUG    | [DEBUG] Checking for encrypted file at: C:\Users\<USER>\OneDrive - JERA Global Markets\Azure Devops\it-endusersupport-automation\scripts\onboarding_v1.12\OLD\v1.12_test1\JML_Automation_v1.13\Config\SecureCredentials.xml
2025-07-01 09:16:58.962 | akinje          | DEBUG    | [DEBUG] EncryptedFile credentials appear to be configured and accessible
2025-07-01 09:16:59.015 | akinje          | INFO     | === JML SERVICE-ORIENTED ARCHITECTURE INITIALIZATION ===
2025-07-01 09:16:59.049 | akinje          | INFO     | Creating JML context...
2025-07-01 09:16:59.128 | akinje          | INFO     | Registering core services...
2025-07-01 09:16:59.165 | akinje          | DEBUG    | Transitioned from early logging to full logging system
