﻿[2025-07-03 01:32:40.998] [DEBUG] [Testing] [17] Test registered: ConfigurationValidation in suite Unit
[2025-07-03 01:32:41.211] [DEBUG] [Testing] [17] Test registered: ConfigurationValidation in Unit
[2025-07-03 01:32:41.242] [DEBUG] [Testing] [17] Test registered: LoggingSystem in suite Unit
[2025-07-03 01:32:41.267] [DEBUG] [Testing] [17] Test registered: LoggingSystem in Unit
[2025-07-03 01:32:41.290] [DEBUG] [Testing] [17] Test registered: VersionManager in suite Unit
[2025-07-03 01:32:41.306] [DEBUG] [Testing] [17] Test registered: VersionManager in Unit
[2025-07-03 01:32:41.323] [DEBUG] [Testing] [17] Test registered: JiraConnection in suite Integration
[2025-07-03 01:32:41.345] [DEBUG] [Testing] [17] Test registered: JiraConnection in Integration
[2025-07-03 01:32:41.367] [DEBUG] [Testing] [17] Test registered: ActiveDirectoryConnection in suite Integration
[2025-07-03 01:32:41.385] [DEBUG] [Testing] [17] Test registered: ActiveDirectoryConnection in Integration
[2025-07-03 01:32:41.403] [DEBUG] [Testing] [17] Test registered: StartupTime in suite Performance
[2025-07-03 01:32:41.422] [DEBUG] [Testing] [17] Test registered: StartupTime in Performance
[2025-07-03 01:32:41.437] [DEBUG] [Testing] [17] Test registered: MemoryUsage in suite Performance
[2025-07-03 01:32:41.454] [DEBUG] [Testing] [17] Test registered: MemoryUsage in Performance
[2025-07-03 01:32:41.474] [DEBUG] [Testing] [17] Test registered: CredentialHandling in suite Security
[2025-07-03 01:32:41.491] [DEBUG] [Testing] [17] Test registered: CredentialHandling in Security
[2025-07-03 01:32:41.511] [DEBUG] [Testing] [17] Test registered: InputValidation in suite Security
[2025-07-03 01:32:41.530] [DEBUG] [Testing] [17] Test registered: InputValidation in Security
[2025-07-03 01:32:41.547] [DEBUG] [Testing] [17] Test registered: WindowCreation in suite UI
[2025-07-03 01:32:41.566] [DEBUG] [Testing] [17] Test registered: WindowCreation in UI
[2025-07-03 01:32:41.586] [INFO] [Testing] [17] Core tests initialized
[2025-07-03 01:32:41.671] [INFO] [Documentation] [17] Initializing documentation system
[2025-07-03 01:32:41.815] [INFO] [Documentation] [17] Starting documentation generation for: api
[2025-07-03 01:32:42.376] [ERROR] [Documentation] [17] Documentation generation failed: The term 'if' is not recognized as the name of a cmdlet, function, script file, or operable program. Check the spelling of the name, or if a path was included, verify that the path is correct and try again. | Stack: Write-StructuredLog:6605 -> :8548 -> New-Documentation:8772 -> New-APIDocumentation:8778 -> Initialize-DocumentationSystem:9153 -> OnboardingFromJiraGUI.ps1:9167 -> <ScriptBlock>:1
[2025-07-03 01:32:42.488] [INFO] [Documentation] [17] Documentation generation completed for: api
[2025-07-03 01:32:42.519] [INFO] [Documentation] [17] API documentation generated: 0 files
[2025-07-03 01:32:42.546] [INFO] [Documentation] [17] Starting documentation generation for: help
[2025-07-03 01:32:42.588] [INFO] [Documentation] [17] Documentation generation completed for: help
[2025-07-03 01:32:42.610] [INFO] [Documentation] [17] Help system documentation generated
[2025-07-03 01:32:42.635] [INFO] [Documentation] [17] Documentation system initialized
[2025-07-03 01:32:42.901] [INFO] [Application] [17] Initializing enhanced UI components...
[2025-07-03 01:32:43.216] [DEBUG] [Application] [17] Theme applied to controls
[2025-07-03 01:32:43.250] [DEBUG] [Application] [17] Modern theme applied successfully
[2025-07-03 01:32:43.507] [DEBUG] [Application] [17] Field validation initialized
[2025-07-03 01:32:43.574] [DEBUG] [Application] [17] Responsive layout initialized
[2025-07-03 01:32:43.808] [DEBUG] [Application] [17] Workflow step 'Connection' status: Pending
[2025-07-03 01:32:43.919] [INFO] [Application] [17] Enhanced UI initialization completed
[2025-07-03 01:32:44.021] [INFO] [Application] [17] Welcome! Please connect to Jira to begin.
[2025-07-03 01:32:44.087] [INFO] [Application] [17] OnboardingFromJiraGUI started successfully
[2025-07-03 01:32:44.196] [INFO] [Application] [17] [OK] Monitoring system initialized
[2025-07-03 01:32:44.295] [INFO] [Audit] [17] Audit: akinje performed PermissionInit on UserPermissions - Success | Context: SessionId=Unknown; Action=PermissionInit; Timestamp=07/03/2025 01:32:44; Result=Success; User=akinje; Detected=Standard user privileges; Target=UserPermissions; PrivilegeLevel=Standard
[2025-07-03 01:32:44.344] [INFO] [Application] [17] [OK] User permissions initialized for JERAGM\akinje
[2025-07-03 01:32:44.387] [DEBUG] [Application] [17]  Cache cleanup scheduled every 30 minutes
[2025-07-03 01:32:44.432] [INFO] [Application] [17] Applying Modern UI Integration...
[2025-07-03 01:32:44.874] [DEBUG] [Application] [17] Auto-completion setup for LastNameBox
[2025-07-03 01:32:44.925] [DEBUG] [Application] [17] Auto-completion setup for JobTitleBox
[2025-07-03 01:32:44.986] [DEBUG] [Application] [17] Auto-completion setup for FirstNameBox
[2025-07-03 01:32:45.046] [DEBUG] [Application] [17] Auto-completion setup for DepartmentBox
[2025-07-03 01:32:45.130] [DEBUG] [Application] [17] Auto-completion setup for ModelAccountBox
[2025-07-03 01:32:45.206] [DEBUG] [Application] [17] Auto-completion integration completed
[2025-07-03 01:32:45.380] [INFO] [Application] [17] Modern UI Integration completed successfully
[2025-07-03 01:35:31.989] [DEBUG] [Testing] [18] Test registered: ConfigurationValidation in suite Unit
[2025-07-03 01:35:32.358] [DEBUG] [Testing] [18] Test registered: ConfigurationValidation in Unit
[2025-07-03 01:35:32.396] [DEBUG] [Testing] [18] Test registered: LoggingSystem in suite Unit
[2025-07-03 01:35:32.427] [DEBUG] [Testing] [18] Test registered: LoggingSystem in Unit
[2025-07-03 01:35:32.463] [DEBUG] [Testing] [18] Test registered: VersionManager in suite Unit
[2025-07-03 01:35:32.497] [DEBUG] [Testing] [18] Test registered: VersionManager in Unit
[2025-07-03 01:35:32.534] [DEBUG] [Testing] [18] Test registered: JiraConnection in suite Integration
[2025-07-03 01:35:32.565] [DEBUG] [Testing] [18] Test registered: JiraConnection in Integration
[2025-07-03 01:35:32.639] [DEBUG] [Testing] [18] Test registered: ActiveDirectoryConnection in suite Integration
[2025-07-03 01:35:32.670] [DEBUG] [Testing] [18] Test registered: ActiveDirectoryConnection in Integration
[2025-07-03 01:35:32.702] [DEBUG] [Testing] [18] Test registered: StartupTime in suite Performance
[2025-07-03 01:35:32.736] [DEBUG] [Testing] [18] Test registered: StartupTime in Performance
[2025-07-03 01:35:32.767] [DEBUG] [Testing] [18] Test registered: MemoryUsage in suite Performance
[2025-07-03 01:35:32.793] [DEBUG] [Testing] [18] Test registered: MemoryUsage in Performance
[2025-07-03 01:35:32.820] [DEBUG] [Testing] [18] Test registered: CredentialHandling in suite Security
[2025-07-03 01:35:32.849] [DEBUG] [Testing] [18] Test registered: CredentialHandling in Security
[2025-07-03 01:35:32.883] [DEBUG] [Testing] [18] Test registered: InputValidation in suite Security
[2025-07-03 01:35:32.912] [DEBUG] [Testing] [18] Test registered: InputValidation in Security
[2025-07-03 01:35:32.942] [DEBUG] [Testing] [18] Test registered: WindowCreation in suite UI
[2025-07-03 01:35:32.971] [DEBUG] [Testing] [18] Test registered: WindowCreation in UI
[2025-07-03 01:35:33.000] [INFO] [Testing] [18] Core tests initialized
[2025-07-03 01:35:33.099] [INFO] [Documentation] [18] Initializing documentation system
[2025-07-03 01:35:33.209] [INFO] [Documentation] [18] Starting documentation generation for: api
[2025-07-03 01:35:33.992] [ERROR] [Documentation] [18] Documentation generation failed: The term 'if' is not recognized as the name of a cmdlet, function, script file, or operable program. Check the spelling of the name, or if a path was included, verify that the path is correct and try again. | Stack: Write-StructuredLog:6684 -> :8627 -> New-Documentation:8851 -> New-APIDocumentation:8857 -> Initialize-DocumentationSystem:9232 -> OnboardingFromJiraGUI.ps1:9246 -> <ScriptBlock>:1
[2025-07-03 01:35:34.320] [INFO] [Documentation] [18] Documentation generation completed for: api
[2025-07-03 01:35:34.362] [INFO] [Documentation] [18] API documentation generated: 0 files
[2025-07-03 01:35:34.401] [INFO] [Documentation] [18] Starting documentation generation for: help
[2025-07-03 01:35:34.455] [INFO] [Documentation] [18] Documentation generation completed for: help
[2025-07-03 01:35:34.484] [INFO] [Documentation] [18] Help system documentation generated
[2025-07-03 01:35:34.513] [INFO] [Documentation] [18] Documentation system initialized
[2025-07-03 01:35:34.824] [INFO] [Application] [18] Initializing enhanced UI components...
[2025-07-03 01:35:34.974] [DEBUG] [Application] [18] Theme applied to controls
[2025-07-03 01:35:35.010] [DEBUG] [Application] [18] Modern theme applied successfully
[2025-07-03 01:35:35.124] [DEBUG] [Application] [18] Field validation initialized
[2025-07-03 01:35:35.181] [DEBUG] [Application] [18] Responsive layout initialized
[2025-07-03 01:35:35.394] [DEBUG] [Application] [18] Workflow step 'Connection' status: Pending
[2025-07-03 01:35:35.510] [INFO] [Application] [18] Enhanced UI initialization completed
[2025-07-03 01:35:35.606] [INFO] [Application] [18] Welcome! Please connect to Jira to begin.
[2025-07-03 01:35:35.652] [INFO] [Application] [18] OnboardingFromJiraGUI started successfully
[2025-07-03 01:35:35.727] [INFO] [Application] [18] [OK] Monitoring system initialized
[2025-07-03 01:35:35.850] [INFO] [Audit] [18] Audit: akinje performed PermissionInit on UserPermissions - Success | Context: SessionId=Unknown; Action=PermissionInit; Timestamp=07/03/2025 01:35:35; Result=Success; User=akinje; Detected=Standard user privileges; Target=UserPermissions; PrivilegeLevel=Standard
[2025-07-03 01:35:35.924] [INFO] [Application] [18] [OK] User permissions initialized for JERAGM\akinje
[2025-07-03 01:35:35.970] [DEBUG] [Application] [18]  Cache cleanup scheduled every 30 minutes
[2025-07-03 01:35:36.044] [INFO] [Application] [18] Applying Modern UI Integration...
[2025-07-03 01:35:36.219] [DEBUG] [Application] [18] Auto-completion setup for LastNameBox
[2025-07-03 01:35:36.246] [DEBUG] [Application] [18] Auto-completion setup for JobTitleBox
[2025-07-03 01:35:36.288] [DEBUG] [Application] [18] Auto-completion setup for FirstNameBox
[2025-07-03 01:35:36.328] [DEBUG] [Application] [18] Auto-completion setup for DepartmentBox
[2025-07-03 01:35:36.358] [DEBUG] [Application] [18] Auto-completion setup for ModelAccountBox
[2025-07-03 01:35:36.384] [DEBUG] [Application] [18] Auto-completion integration completed
[2025-07-03 01:35:36.478] [INFO] [Application] [18] Modern UI Integration completed successfully
[2025-07-03 01:37:57.992] [DEBUG] [Testing] [16] Test registered: ConfigurationValidation in suite Unit
[2025-07-03 01:37:58.455] [DEBUG] [Testing] [16] Test registered: ConfigurationValidation in Unit
[2025-07-03 01:37:58.560] [DEBUG] [Testing] [16] Test registered: LoggingSystem in suite Unit
[2025-07-03 01:37:58.615] [DEBUG] [Testing] [16] Test registered: LoggingSystem in Unit
[2025-07-03 01:37:58.673] [DEBUG] [Testing] [16] Test registered: VersionManager in suite Unit
[2025-07-03 01:37:58.715] [DEBUG] [Testing] [16] Test registered: VersionManager in Unit
[2025-07-03 01:37:58.760] [DEBUG] [Testing] [16] Test registered: JiraConnection in suite Integration
[2025-07-03 01:37:58.801] [DEBUG] [Testing] [16] Test registered: JiraConnection in Integration
[2025-07-03 01:37:58.858] [DEBUG] [Testing] [16] Test registered: ActiveDirectoryConnection in suite Integration
[2025-07-03 01:37:58.894] [DEBUG] [Testing] [16] Test registered: ActiveDirectoryConnection in Integration
[2025-07-03 01:37:58.949] [DEBUG] [Testing] [16] Test registered: StartupTime in suite Performance
[2025-07-03 01:37:58.985] [DEBUG] [Testing] [16] Test registered: StartupTime in Performance
[2025-07-03 01:37:59.025] [DEBUG] [Testing] [16] Test registered: MemoryUsage in suite Performance
[2025-07-03 01:37:59.065] [DEBUG] [Testing] [16] Test registered: MemoryUsage in Performance
[2025-07-03 01:37:59.108] [DEBUG] [Testing] [16] Test registered: CredentialHandling in suite Security
[2025-07-03 01:37:59.147] [DEBUG] [Testing] [16] Test registered: CredentialHandling in Security
[2025-07-03 01:37:59.196] [DEBUG] [Testing] [16] Test registered: InputValidation in suite Security
[2025-07-03 01:37:59.231] [DEBUG] [Testing] [16] Test registered: InputValidation in Security
[2025-07-03 01:37:59.274] [DEBUG] [Testing] [16] Test registered: WindowCreation in suite UI
[2025-07-03 01:37:59.316] [DEBUG] [Testing] [16] Test registered: WindowCreation in UI
[2025-07-03 01:37:59.360] [INFO] [Testing] [16] Core tests initialized
[2025-07-03 01:37:59.514] [INFO] [Documentation] [16] Initializing documentation system
[2025-07-03 01:37:59.800] [INFO] [Documentation] [16] Starting documentation generation for: api
[2025-07-03 01:38:00.605] [ERROR] [Documentation] [16] Documentation generation failed: The term 'if' is not recognized as the name of a cmdlet, function, script file, or operable program. Check the spelling of the name, or if a path was included, verify that the path is correct and try again. | Stack: Write-StructuredLog:6684 -> :8627 -> New-Documentation:8886 -> New-APIDocumentation:8892 -> Initialize-DocumentationSystem:9267 -> OnboardingFromJiraGUI.ps1:9281 -> <ScriptBlock>:1
[2025-07-03 01:38:00.804] [INFO] [Documentation] [16] Documentation generation completed for: api
[2025-07-03 01:38:00.853] [INFO] [Documentation] [16] API documentation generated: 0 files
[2025-07-03 01:38:00.916] [INFO] [Documentation] [16] Starting documentation generation for: help
[2025-07-03 01:38:00.999] [INFO] [Documentation] [16] Documentation generation completed for: help
[2025-07-03 01:38:01.046] [INFO] [Documentation] [16] Help system documentation generated
[2025-07-03 01:38:01.079] [INFO] [Documentation] [16] Documentation system initialized
[2025-07-03 01:38:01.440] [INFO] [Application] [16] Initializing enhanced UI components...
[2025-07-03 01:38:01.639] [DEBUG] [Application] [16] Theme applied to controls
[2025-07-03 01:38:01.693] [DEBUG] [Application] [16] Modern theme applied successfully
[2025-07-03 01:38:01.977] [DEBUG] [Application] [16] Field validation initialized
[2025-07-03 01:38:02.087] [DEBUG] [Application] [16] Responsive layout initialized
[2025-07-03 01:38:02.470] [DEBUG] [Application] [16] Workflow step 'Connection' status: Pending
[2025-07-03 01:38:02.659] [INFO] [Application] [16] Enhanced UI initialization completed
[2025-07-03 01:38:02.778] [INFO] [Application] [16] Welcome! Please connect to Jira to begin.
[2025-07-03 01:38:02.851] [INFO] [Application] [16] OnboardingFromJiraGUI started successfully
[2025-07-03 01:38:02.966] [INFO] [Application] [16] [OK] Monitoring system initialized
[2025-07-03 01:38:03.141] [INFO] [Audit] [16] Audit: akinje performed PermissionInit on UserPermissions - Success | Context: SessionId=Unknown; Action=PermissionInit; Timestamp=07/03/2025 01:38:03; Result=Success; User=akinje; Detected=Standard user privileges; Target=UserPermissions; PrivilegeLevel=Standard
[2025-07-03 01:38:03.213] [INFO] [Application] [16] [OK] User permissions initialized for JERAGM\akinje
[2025-07-03 01:38:03.290] [DEBUG] [Application] [16]  Cache cleanup scheduled every 30 minutes
[2025-07-03 01:38:03.688] [INFO] [Application] [16] Applying Modern UI Integration...
[2025-07-03 01:38:04.021] [DEBUG] [Application] [16] Auto-completion setup for LastNameBox
[2025-07-03 01:38:04.061] [DEBUG] [Application] [16] Auto-completion setup for JobTitleBox
[2025-07-03 01:38:04.104] [DEBUG] [Application] [16] Auto-completion setup for FirstNameBox
[2025-07-03 01:38:04.145] [DEBUG] [Application] [16] Auto-completion setup for DepartmentBox
[2025-07-03 01:38:04.190] [DEBUG] [Application] [16] Auto-completion setup for ModelAccountBox
[2025-07-03 01:38:04.258] [DEBUG] [Application] [16] Auto-completion integration completed
[2025-07-03 01:38:04.402] [INFO] [Application] [16] Modern UI Integration completed successfully
[2025-07-03 01:44:22.471] [DEBUG] [Testing] [15] Test registered: ConfigurationValidation in suite Unit
[2025-07-03 01:44:22.991] [DEBUG] [Testing] [15] Test registered: ConfigurationValidation in Unit
[2025-07-03 01:44:23.049] [DEBUG] [Testing] [15] Test registered: LoggingSystem in suite Unit
[2025-07-03 01:44:23.086] [DEBUG] [Testing] [15] Test registered: LoggingSystem in Unit
[2025-07-03 01:44:23.143] [DEBUG] [Testing] [15] Test registered: VersionManager in suite Unit
[2025-07-03 01:44:23.194] [DEBUG] [Testing] [15] Test registered: VersionManager in Unit
[2025-07-03 01:44:23.240] [DEBUG] [Testing] [15] Test registered: JiraConnection in suite Integration
[2025-07-03 01:44:23.319] [DEBUG] [Testing] [15] Test registered: JiraConnection in Integration
[2025-07-03 01:44:23.358] [DEBUG] [Testing] [15] Test registered: ActiveDirectoryConnection in suite Integration
[2025-07-03 01:44:23.393] [DEBUG] [Testing] [15] Test registered: ActiveDirectoryConnection in Integration
[2025-07-03 01:44:23.432] [DEBUG] [Testing] [15] Test registered: StartupTime in suite Performance
[2025-07-03 01:44:23.465] [DEBUG] [Testing] [15] Test registered: StartupTime in Performance
[2025-07-03 01:44:23.503] [DEBUG] [Testing] [15] Test registered: MemoryUsage in suite Performance
[2025-07-03 01:44:23.534] [DEBUG] [Testing] [15] Test registered: MemoryUsage in Performance
[2025-07-03 01:44:23.573] [DEBUG] [Testing] [15] Test registered: CredentialHandling in suite Security
[2025-07-03 01:44:23.610] [DEBUG] [Testing] [15] Test registered: CredentialHandling in Security
[2025-07-03 01:44:23.644] [DEBUG] [Testing] [15] Test registered: InputValidation in suite Security
[2025-07-03 01:44:23.682] [DEBUG] [Testing] [15] Test registered: InputValidation in Security
[2025-07-03 01:44:23.719] [DEBUG] [Testing] [15] Test registered: WindowCreation in suite UI
[2025-07-03 01:44:23.761] [DEBUG] [Testing] [15] Test registered: WindowCreation in UI
[2025-07-03 01:44:23.798] [INFO] [Testing] [15] Core tests initialized
[2025-07-03 01:44:23.948] [INFO] [Documentation] [15] Initializing documentation system
[2025-07-03 01:44:24.063] [INFO] [Documentation] [15] Starting documentation generation for: api
[2025-07-03 01:44:25.057] [ERROR] [Documentation] [15] Documentation generation failed: The term 'if' is not recognized as the name of a cmdlet, function, script file, or operable program. Check the spelling of the name, or if a path was included, verify that the path is correct and try again. | Stack: Write-StructuredLog:6684 -> :8627 -> New-Documentation:8964 -> New-APIDocumentation:8970 -> Initialize-DocumentationSystem:9345 -> OnboardingFromJiraGUI.ps1:9359 -> <ScriptBlock>:1
[2025-07-03 01:44:25.383] [INFO] [Documentation] [15] Documentation generation completed for: api
[2025-07-03 01:44:25.444] [INFO] [Documentation] [15] API documentation generated: 0 files
[2025-07-03 01:44:25.498] [INFO] [Documentation] [15] Starting documentation generation for: help
[2025-07-03 01:44:25.569] [INFO] [Documentation] [15] Documentation generation completed for: help
[2025-07-03 01:44:25.602] [INFO] [Documentation] [15] Help system documentation generated
[2025-07-03 01:44:25.638] [INFO] [Documentation] [15] Documentation system initialized
[2025-07-03 01:44:26.336] [INFO] [Application] [15] Initializing enhanced UI components...
[2025-07-03 01:44:26.676] [DEBUG] [Application] [15] Theme applied to controls
[2025-07-03 01:44:26.726] [DEBUG] [Application] [15] Modern theme applied successfully
[2025-07-03 01:44:27.051] [DEBUG] [Application] [15] Field validation initialized
[2025-07-03 01:44:27.166] [DEBUG] [Application] [15] Responsive layout initialized
[2025-07-03 01:44:27.694] [DEBUG] [Application] [15] Workflow step 'Connection' status: Pending
[2025-07-03 01:44:27.926] [INFO] [Application] [15] Enhanced UI initialization completed
[2025-07-03 01:44:28.051] [INFO] [Application] [15] Welcome! Please connect to Jira to begin.
[2025-07-03 01:44:28.122] [INFO] [Application] [15] OnboardingFromJiraGUI started successfully
[2025-07-03 01:44:28.224] [INFO] [Application] [15] [OK] Monitoring system initialized
[2025-07-03 01:44:29.404] [DEBUG] [Application] [15] Auto-completion setup for LastNameBox
[2025-07-03 01:44:29.460] [DEBUG] [Application] [15] Auto-completion setup for JobTitleBox
[2025-07-03 01:44:29.508] [DEBUG] [Application] [15] Auto-completion setup for FirstNameBox
[2025-07-03 01:44:29.556] [DEBUG] [Application] [15] Auto-completion setup for DepartmentBox
[2025-07-03 01:44:29.599] [DEBUG] [Application] [15] Auto-completion setup for ModelAccountBox
[2025-07-03 01:44:29.641] [DEBUG] [Application] [15] Auto-completion integration completed
[2025-07-03 01:44:29.789] [INFO] [Application] [15] Modern UI Integration completed successfully
[2025-07-03 01:50:27.189] [DEBUG] [Testing] [17] Test registered: ConfigurationValidation in suite Unit
[2025-07-03 01:50:27.399] [DEBUG] [Testing] [17] Test registered: ConfigurationValidation in Unit
[2025-07-03 01:50:27.414] [DEBUG] [Testing] [17] Test registered: LoggingSystem in suite Unit
[2025-07-03 01:50:27.556] [DEBUG] [Testing] [17] Test registered: LoggingSystem in Unit
[2025-07-03 01:50:27.585] [DEBUG] [Testing] [17] Test registered: VersionManager in suite Unit
[2025-07-03 01:50:27.611] [DEBUG] [Testing] [17] Test registered: VersionManager in Unit
[2025-07-03 01:50:27.644] [DEBUG] [Testing] [17] Test registered: JiraConnection in suite Integration
[2025-07-03 01:50:27.672] [DEBUG] [Testing] [17] Test registered: JiraConnection in Integration
[2025-07-03 01:50:27.705] [DEBUG] [Testing] [17] Test registered: ActiveDirectoryConnection in suite Integration
[2025-07-03 01:50:27.731] [DEBUG] [Testing] [17] Test registered: ActiveDirectoryConnection in Integration
[2025-07-03 01:50:27.761] [DEBUG] [Testing] [17] Test registered: StartupTime in suite Performance
[2025-07-03 01:50:27.787] [DEBUG] [Testing] [17] Test registered: StartupTime in Performance
[2025-07-03 01:50:27.819] [DEBUG] [Testing] [17] Test registered: MemoryUsage in suite Performance
[2025-07-03 01:50:27.848] [DEBUG] [Testing] [17] Test registered: MemoryUsage in Performance
[2025-07-03 01:50:27.877] [DEBUG] [Testing] [17] Test registered: CredentialHandling in suite Security
[2025-07-03 01:50:27.904] [DEBUG] [Testing] [17] Test registered: CredentialHandling in Security
[2025-07-03 01:50:27.933] [DEBUG] [Testing] [17] Test registered: InputValidation in suite Security
[2025-07-03 01:50:27.974] [DEBUG] [Testing] [17] Test registered: InputValidation in Security
[2025-07-03 01:50:28.005] [DEBUG] [Testing] [17] Test registered: WindowCreation in suite UI
[2025-07-03 01:50:28.028] [DEBUG] [Testing] [17] Test registered: WindowCreation in UI
[2025-07-03 01:50:28.052] [INFO] [Testing] [17] Core tests initialized
[2025-07-03 01:50:28.152] [INFO] [Documentation] [17] Initializing documentation system
[2025-07-03 01:50:28.227] [INFO] [Documentation] [17] Starting documentation generation for: api
[2025-07-03 01:50:28.639] [ERROR] [Documentation] [17] Documentation generation failed: The term 'if' is not recognized as the name of a cmdlet, function, script file, or operable program. Check the spelling of the name, or if a path was included, verify that the path is correct and try again. | Stack: Write-StructuredLog:6703 -> :8646 -> New-Documentation:9077 -> New-APIDocumentation:9083 -> Initialize-DocumentationSystem:9458 -> OnboardingFromJiraGUI.ps1:9472 -> <ScriptBlock>:1
[2025-07-03 01:50:28.777] [INFO] [Documentation] [17] Documentation generation completed for: api
[2025-07-03 01:50:28.814] [INFO] [Documentation] [17] API documentation generated: 0 files
[2025-07-03 01:50:28.850] [INFO] [Documentation] [17] Starting documentation generation for: help
[2025-07-03 01:50:28.903] [INFO] [Documentation] [17] Documentation generation completed for: help
[2025-07-03 01:50:28.920] [INFO] [Documentation] [17] Help system documentation generated
[2025-07-03 01:50:28.952] [INFO] [Documentation] [17] Documentation system initialized
[2025-07-03 01:50:29.276] [INFO] [Application] [17] Initializing enhanced UI components...
[2025-07-03 01:50:29.431] [DEBUG] [Application] [17] Theme applied to controls
[2025-07-03 01:50:29.454] [DEBUG] [Application] [17] Modern theme applied successfully
[2025-07-03 01:50:29.503] [DEBUG] [Application] [17] Field validation initialized
[2025-07-03 01:50:29.597] [DEBUG] [Application] [17] Responsive layout initialized
[2025-07-03 01:50:29.788] [DEBUG] [Application] [17] Workflow step 'Connection' status: Pending
[2025-07-03 01:50:29.869] [INFO] [Application] [17] Enhanced UI initialization completed
[2025-07-03 01:50:29.918] [INFO] [Application] [17] Welcome! Please connect to Jira to begin.
[2025-07-03 01:50:29.968] [INFO] [Application] [17] OnboardingFromJiraGUI started successfully
[2025-07-03 01:50:30.016] [INFO] [Application] [17] [OK] Monitoring system initialized
[2025-07-03 01:50:30.080] [INFO] [Audit] [17] Audit: akinje performed PermissionInit on UserPermissions - Success | Context: SessionId=Unknown; Action=PermissionInit; Timestamp=07/03/2025 01:50:30; Result=Success; User=akinje; Detected=Standard user privileges; Target=UserPermissions; PrivilegeLevel=Standard
[2025-07-03 01:50:30.137] [INFO] [Application] [17] [OK] User permissions initialized for JERAGM\akinje
[2025-07-03 01:50:30.162] [DEBUG] [Application] [17]  Cache cleanup scheduled every 30 minutes
[2025-07-03 01:50:30.194] [INFO] [Application] [17] Applying Modern UI Integration...
[2025-07-03 01:50:30.298] [DEBUG] [Application] [17] Auto-completion setup for LastNameBox
[2025-07-03 01:50:30.319] [DEBUG] [Application] [17] Auto-completion setup for JobTitleBox
[2025-07-03 01:50:30.336] [DEBUG] [Application] [17] Auto-completion setup for FirstNameBox
[2025-07-03 01:50:30.357] [DEBUG] [Application] [17] Auto-completion setup for DepartmentBox
[2025-07-03 01:50:30.374] [DEBUG] [Application] [17] Auto-completion setup for ModelAccountBox
[2025-07-03 01:50:30.389] [DEBUG] [Application] [17] Auto-completion integration completed
[2025-07-03 01:50:30.459] [INFO] [Application] [17] Modern UI Integration completed successfully
[2025-07-03 01:51:07.840] [INFO] [Application] [17] Step 1: Connect to Jira
[2025-07-03 01:51:07.861] [DEBUG] [Application] [17] Workflow step 'Connection' status: InProgress
[2025-07-03 01:51:07.940] [INFO] [Application] [17] Connecting to https://jeragm.atlassian.net...
[2025-07-03 01:51:10.108] [DEBUG] [Application] [17] Workflow step 'Connection' status: Completed
[2025-07-03 01:51:10.122] [INFO] [Application] [17] Successfully connected to Jira
[2025-07-03 01:51:18.212] [INFO] [Application] [17] Step 2: Fetch Ticket Data
[2025-07-03 01:51:18.500] [DEBUG] [Application] [17] Workflow step 'FetchData' status: InProgress
[2025-07-03 01:51:18.577] [INFO] [Application] [17] Fetching data for ticket TESTIT-49342...
[2025-07-03 01:51:20.876] [DEBUG] [Application] [17] Cached ticket: TESTIT-49342
[2025-07-03 01:51:20.913] [DEBUG] [Application] [17] Issue object properties: customfield_10553, customfield_10058, customfield_10305, customfield_10149, customfield_10723, customfield_10928, customfield_10194, customfield_10363, customfield_10750, customfield_10147, customfield_11280, customfield_10729, customfield_10153, customfield_10369, customfield_10145, customfield_10783, customfield_10727, duedate, customfield_10151, customfield_10367, customfield_10370, customfield_10486, customfield_10373, worklog, customfield_10157, customfield_10371, customfield_10008, customfield_10155, customfield_10519, customfield_10202, customfield_10306, customfield_10380, customfield_10062, ID, customfield_10375, customfield_10072, customfield_10069, customfield_10685, customfield_11251, customfield_10200, customfield_10187, customfield_11113, customfield_11245, components, customfield_11253, Transition, customfield_11250, customfield_11247, resolutiondate, customfield_10101, LastViewed, customfield_10385, customfield_11249, customfield_11279, customfield_10344, customfield_10209, customfield_10189, customfield_11212, customfield_10651, Project, customfield_10211, customfield_10105, customfield_11214, customfield_10012, customfield_10213, customfield_10395, customfield_10684, customfield_10116, customfield_10172, timeoriginalestimate, customfield_10203, customfield_10174, customfield_10025, customfield_10336, customfield_10065, customfield_10330, customfield_10223, customfield_10027, customfield_10686, fixVersions, customfield_10126, customfield_10182, customfield_10356, progress, customfield_10128, customfield_10042, customfield_10180, customfield_10562, customfield_10196, customfield_10858, customfield_10629, customfield_10261, customfield_10004, customfield_10049, customfield_10627, aggregatetimeestimate, customfield_10663, customfield_10185, customfield_10237, customfield_10557, customfield_10081, timespent, security, customfield_10057, aggregateprogress, aggregatetimespent, customfield_10314, environment, customfield_10722, customfield_10195, customfield_10239, customfield_10368, customfield_10229, customfield_10037, issuelinks, customfield_10348, customfield_10056, customfield_10131, customfield_10199, customfield_10054, Status, customfield_10618, customfield_10751, customfield_10161, Comment, Key, customfield_11254, customfield_10063, customfield_10060, customfield_10066, customfield_10226, customfield_10068, labels, customfield_10520, customfield_10489, statuscategorychangedate, customfield_10160, customfield_10222, customfield_10354, customfield_11252, customfield_10159, customfield_10393, customfield_11248, RestUrl, resolution, customfield_10384, customfield_10216, customfield_10379, customfield_10208, customfield_10386, customfield_10341, customfield_10132, customfield_10559, customfield_10171, customfield_10013, customfield_10100, customfield_10212, customfield_10204, customfield_10725, customfield_10117, customfield_10173, customfield_10011, customfield_10075, customfield_10398, customfield_10115, customfield_10347, customfield_10337, customfield_10321, customfield_10177, customfield_10215, customfield_10224, customfield_10352, Assignee, customfield_10130, customfield_10717, customfield_10129, customfield_10181, customfield_10000, customfield_10453, customfield_11181, customfield_10178, customfield_10029, customfield_10276, watches, customfield_10560, customfield_10133, customfield_10120, customfield_10102, customfield_10634, customfield_10249, customfield_10320, customfield_10048, customfield_11278, customfield_10346, customfield_10383, customfield_10184, customfield_11281, customfield_10726, customfield_10030, priority, customfield_10235, customfield_10720, customfield_10134, customfield_10047, customfield_10190, customfield_10721, customfield_10045, customfield_10448, customfield_10238, customfield_10882, customfield_10250, customfield_10198, customfield_10118, customfield_10619, customfield_10197, customfield_10251, customfield_10318, customfield_10055, Summary, Description, customfield_10447, customfield_10304, customfield_10255, customfield_10059, customfield_10053, customfield_10148, customfield_10382, customfield_10193, customfield_10488, attachment, customfield_10728, customfield_10225, customfield_10158, workratio, customfield_10372, customfield_10206, customfield_10156, customfield_10664, customfield_10201, customfield_10154, customfield_10521, statusCategory, Created, customfield_10064, issuetype, customfield_10254, customfield_10374, customfield_10326, customfield_10342, customfield_10317, customfield_10554, customfield_10227, customfield_10210, customfield_10214, customfield_10391, customfield_11246, customfield_10585, customfield_10070, customfield_11182, customfield_10630, customfield_10135, aggregatetimeoriginalestimate, customfield_10631, customfield_10010, customfield_10061, customfield_10009, customfield_11215, timeestimate, customfield_10662, customfield_11213, customfield_10556, customfield_10028, customfield_10635, customfield_10396, customfield_10275, customfield_10719, timetracking, customfield_10175, customfield_10024, Creator, customfield_10381, customfield_10023, Updated, customfield_11179, customfield_10127, customfield_10183, customfield_11122, customfield_10561, customfield_10043, customfield_10322, customfield_10046, customfield_10192, customfield_10041, Reporter, customfield_10044, customfield_10628, customfield_10335, customfield_10040, subtasks, customfield_10191, customfield_10626, customfield_10343, versions, customfield_10186, HttpUrl, customfield_10319, customfield_10303, customfield_10001, customfield_10555, customfield_10252, customfield_10050
[2025-07-03 01:51:20.931] [DEBUG] [Application] [17] Found Comment property with 18 comments
[2025-07-03 01:51:20.943] [INFO] [Application] [17] Starting data extraction from ticket TESTIT-49342
[2025-07-03 01:51:20.982] [DEBUG] [Application] [17] Extracted data - Name: 'Test1 Test2', Job: 'Intern', Dept: 'IT', Model: 'test', Location: ''
[2025-07-03 01:51:21.382] [DEBUG] [Application] [17] Generating SAM account name for Test1 Test2
[2025-07-03 01:51:21.397] [DEBUG] [Application] [17] Checking if SAM account name 'test2t' exists
[2025-07-03 01:51:21.414] [INFO] [Application] [17] Generated SAM account name: test2t (could not verify uniqueness)
[2025-07-03 01:51:21.615] [DEBUG] [Application] [17] Generated from Jira ticket - SAM: 31 32 33 test2t, UPN: <EMAIL> (based on Test1.Test2), Email: <EMAIL>
[2025-07-03 01:51:21.644] [INFO] [Application] [17] Data extraction completed. Please verify the populated fields.
[2025-07-03 01:51:21.662] [DEBUG] [Application] [17] Workflow step 'FetchData' status: Completed
[2025-07-03 01:51:21.685] [INFO] [Application] [17] Step 3: Validate Information
[2025-07-03 01:51:21.703] [DEBUG] [Application] [17] Workflow step 'Validation' status: InProgress
[2025-07-03 01:51:21.716] [INFO] [Application] [17] Successfully fetched and populated data from ticket TESTIT-49342
[2025-07-03 01:53:02.275] [DEBUG] [Testing] [16] Test registered: ConfigurationValidation in suite Unit
[2025-07-03 01:53:02.468] [DEBUG] [Testing] [16] Test registered: ConfigurationValidation in Unit
[2025-07-03 01:53:02.479] [DEBUG] [Testing] [16] Test registered: LoggingSystem in suite Unit
[2025-07-03 01:53:02.491] [DEBUG] [Testing] [16] Test registered: LoggingSystem in Unit
[2025-07-03 01:53:02.501] [DEBUG] [Testing] [16] Test registered: VersionManager in suite Unit
[2025-07-03 01:53:02.515] [DEBUG] [Testing] [16] Test registered: VersionManager in Unit
[2025-07-03 01:53:02.529] [DEBUG] [Testing] [16] Test registered: JiraConnection in suite Integration
[2025-07-03 01:53:02.543] [DEBUG] [Testing] [16] Test registered: JiraConnection in Integration
[2025-07-03 01:53:02.554] [DEBUG] [Testing] [16] Test registered: ActiveDirectoryConnection in suite Integration
[2025-07-03 01:53:02.567] [DEBUG] [Testing] [16] Test registered: ActiveDirectoryConnection in Integration
[2025-07-03 01:53:02.584] [DEBUG] [Testing] [16] Test registered: StartupTime in suite Performance
[2025-07-03 01:53:02.612] [DEBUG] [Testing] [16] Test registered: StartupTime in Performance
[2025-07-03 01:53:02.634] [DEBUG] [Testing] [16] Test registered: MemoryUsage in suite Performance
[2025-07-03 01:53:02.651] [DEBUG] [Testing] [16] Test registered: MemoryUsage in Performance
[2025-07-03 01:53:02.669] [DEBUG] [Testing] [16] Test registered: CredentialHandling in suite Security
[2025-07-03 01:53:02.694] [DEBUG] [Testing] [16] Test registered: CredentialHandling in Security
[2025-07-03 01:53:02.715] [DEBUG] [Testing] [16] Test registered: InputValidation in suite Security
[2025-07-03 01:53:02.748] [DEBUG] [Testing] [16] Test registered: InputValidation in Security
[2025-07-03 01:53:02.767] [DEBUG] [Testing] [16] Test registered: WindowCreation in suite UI
[2025-07-03 01:53:02.795] [DEBUG] [Testing] [16] Test registered: WindowCreation in UI
[2025-07-03 01:53:02.818] [INFO] [Testing] [16] Core tests initialized
[2025-07-03 01:53:02.963] [INFO] [Documentation] [16] Initializing documentation system
[2025-07-03 01:53:03.139] [INFO] [Documentation] [16] Starting documentation generation for: api
[2025-07-03 01:53:03.276] [ERROR] [Documentation] [16] Documentation generation failed: Method invocation failed because [DocumentationGenerator] does not contain a method named 'ExtractParameterName'. | Stack: Write-StructuredLog:6703 -> :8646 -> New-Documentation:9093 -> New-APIDocumentation:9099 -> Initialize-DocumentationSystem:9474 -> OnboardingFromJiraGUI.ps1:9488 -> <ScriptBlock>:1
[2025-07-03 01:53:03.349] [INFO] [Documentation] [16] Documentation generation completed for: api
[2025-07-03 01:53:03.401] [INFO] [Documentation] [16] API documentation generated: 0 files
[2025-07-03 01:53:03.423] [INFO] [Documentation] [16] Starting documentation generation for: help
[2025-07-03 01:53:03.469] [INFO] [Documentation] [16] Documentation generation completed for: help
[2025-07-03 01:53:03.502] [INFO] [Documentation] [16] Help system documentation generated
[2025-07-03 01:53:03.526] [INFO] [Documentation] [16] Documentation system initialized
[2025-07-03 01:53:03.725] [INFO] [Application] [16] Initializing enhanced UI components...
[2025-07-03 01:53:03.814] [DEBUG] [Application] [16] Theme applied to controls
[2025-07-03 01:53:03.831] [DEBUG] [Application] [16] Modern theme applied successfully
[2025-07-03 01:53:04.009] [DEBUG] [Application] [16] Field validation initialized
[2025-07-03 01:53:04.066] [DEBUG] [Application] [16] Responsive layout initialized
[2025-07-03 01:53:04.194] [DEBUG] [Application] [16] Workflow step 'Connection' status: Pending
[2025-07-03 01:53:04.249] [INFO] [Application] [16] Enhanced UI initialization completed
[2025-07-03 01:53:04.299] [INFO] [Application] [16] Welcome! Please connect to Jira to begin.
[2025-07-03 01:53:04.321] [INFO] [Application] [16] OnboardingFromJiraGUI started successfully
[2025-07-03 01:53:04.353] [INFO] [Application] [16] [OK] Monitoring system initialized
[2025-07-03 01:53:04.404] [INFO] [Audit] [16] Audit: akinje performed PermissionInit on UserPermissions - Success | Context: SessionId=Unknown; Action=PermissionInit; Timestamp=07/03/2025 01:53:04; Result=Success; User=akinje; Detected=Standard user privileges; Target=UserPermissions; PrivilegeLevel=Standard
[2025-07-03 01:53:04.476] [INFO] [Application] [16] [OK] User permissions initialized for JERAGM\akinje
[2025-07-03 01:53:04.502] [DEBUG] [Application] [16]  Cache cleanup scheduled every 30 minutes
[2025-07-03 01:53:04.535] [INFO] [Application] [16] Applying Modern UI Integration...
[2025-07-03 01:53:04.645] [DEBUG] [Application] [16] Auto-completion setup for LastNameBox
[2025-07-03 01:53:04.664] [DEBUG] [Application] [16] Auto-completion setup for JobTitleBox
[2025-07-03 01:53:04.695] [DEBUG] [Application] [16] Auto-completion setup for FirstNameBox
[2025-07-03 01:53:04.716] [DEBUG] [Application] [16] Auto-completion setup for DepartmentBox
[2025-07-03 01:53:04.741] [DEBUG] [Application] [16] Auto-completion setup for ModelAccountBox
[2025-07-03 01:53:04.761] [DEBUG] [Application] [16] Auto-completion integration completed
[2025-07-03 01:53:04.836] [INFO] [Application] [16] Modern UI Integration completed successfully
[2025-07-03 01:57:18.475] [DEBUG] [Testing] [17] Test registered: ConfigurationValidation in suite Unit
[2025-07-03 01:57:18.720] [DEBUG] [Testing] [17] Test registered: ConfigurationValidation in Unit
[2025-07-03 01:57:18.733] [DEBUG] [Testing] [17] Test registered: LoggingSystem in suite Unit
[2025-07-03 01:57:18.749] [DEBUG] [Testing] [17] Test registered: LoggingSystem in Unit
[2025-07-03 01:57:18.759] [DEBUG] [Testing] [17] Test registered: VersionManager in suite Unit
[2025-07-03 01:57:18.773] [DEBUG] [Testing] [17] Test registered: VersionManager in Unit
[2025-07-03 01:57:18.788] [DEBUG] [Testing] [17] Test registered: JiraConnection in suite Integration
[2025-07-03 01:57:18.805] [DEBUG] [Testing] [17] Test registered: JiraConnection in Integration
[2025-07-03 01:57:18.825] [DEBUG] [Testing] [17] Test registered: ActiveDirectoryConnection in suite Integration
[2025-07-03 01:57:18.843] [DEBUG] [Testing] [17] Test registered: ActiveDirectoryConnection in Integration
[2025-07-03 01:57:18.874] [DEBUG] [Testing] [17] Test registered: StartupTime in suite Performance
[2025-07-03 01:57:18.892] [DEBUG] [Testing] [17] Test registered: StartupTime in Performance
[2025-07-03 01:57:18.916] [DEBUG] [Testing] [17] Test registered: MemoryUsage in suite Performance
[2025-07-03 01:57:18.934] [DEBUG] [Testing] [17] Test registered: MemoryUsage in Performance
[2025-07-03 01:57:18.952] [DEBUG] [Testing] [17] Test registered: CredentialHandling in suite Security
[2025-07-03 01:57:18.970] [DEBUG] [Testing] [17] Test registered: CredentialHandling in Security
[2025-07-03 01:57:18.988] [DEBUG] [Testing] [17] Test registered: InputValidation in suite Security
[2025-07-03 01:57:19.016] [DEBUG] [Testing] [17] Test registered: InputValidation in Security
[2025-07-03 01:57:19.039] [DEBUG] [Testing] [17] Test registered: WindowCreation in suite UI
[2025-07-03 01:57:19.055] [DEBUG] [Testing] [17] Test registered: WindowCreation in UI
[2025-07-03 01:57:19.079] [INFO] [Testing] [17] Core tests initialized
[2025-07-03 01:57:19.121] [INFO] [Documentation] [17] Initializing documentation system
[2025-07-03 01:57:19.157] [INFO] [Documentation] [17] Starting documentation generation for: api
[2025-07-03 01:57:19.267] [ERROR] [Documentation] [17] Documentation generation failed: Method invocation failed because [DocumentationGenerator] does not contain a method named 'ExtractParameterName'. | Stack: Write-StructuredLog:6717 -> :8660 -> New-Documentation:9107 -> New-APIDocumentation:9113 -> Initialize-DocumentationSystem:9488 -> OnboardingFromJiraGUI.ps1:9502 -> <ScriptBlock>:1
[2025-07-03 01:57:19.434] [INFO] [Documentation] [17] Documentation generation completed for: api
[2025-07-03 01:57:19.502] [INFO] [Documentation] [17] API documentation generated: 0 files
[2025-07-03 01:57:19.531] [INFO] [Documentation] [17] Starting documentation generation for: help
[2025-07-03 01:57:19.624] [INFO] [Documentation] [17] Documentation generation completed for: help
[2025-07-03 01:57:19.675] [INFO] [Documentation] [17] Help system documentation generated
[2025-07-03 01:57:19.712] [INFO] [Documentation] [17] Documentation system initialized
[2025-07-03 01:57:19.881] [INFO] [Application] [17] Initializing enhanced UI components...
[2025-07-03 01:57:19.982] [DEBUG] [Application] [17] Theme applied to controls
[2025-07-03 01:57:20.032] [DEBUG] [Application] [17] Modern theme applied successfully
[2025-07-03 01:57:20.066] [DEBUG] [Application] [17] Field validation initialized
[2025-07-03 01:57:20.141] [DEBUG] [Application] [17] Responsive layout initialized
[2025-07-03 01:57:20.333] [DEBUG] [Application] [17] Workflow step 'Connection' status: Pending
[2025-07-03 01:57:20.426] [INFO] [Application] [17] Enhanced UI initialization completed
[2025-07-03 01:57:20.471] [INFO] [Application] [17] Welcome! Please connect to Jira to begin.
[2025-07-03 01:57:20.491] [INFO] [Application] [17] OnboardingFromJiraGUI started successfully
[2025-07-03 01:57:20.518] [INFO] [Application] [17] [OK] Monitoring system initialized
[2025-07-03 01:57:20.590] [INFO] [Audit] [17] Audit: akinje performed PermissionInit on UserPermissions - Success | Context: SessionId=Unknown; Action=PermissionInit; Timestamp=07/03/2025 01:57:20; Result=Success; User=akinje; Detected=Standard user privileges; Target=UserPermissions; PrivilegeLevel=Standard
[2025-07-03 01:57:20.649] [INFO] [Application] [17] [OK] User permissions initialized for JERAGM\akinje
[2025-07-03 01:57:20.676] [DEBUG] [Application] [17]  Cache cleanup scheduled every 30 minutes
[2025-07-03 01:57:20.695] [INFO] [Application] [17] Applying Modern UI Integration...
[2025-07-03 01:57:20.773] [DEBUG] [Application] [17] Auto-completion setup for LastNameBox
[2025-07-03 01:57:20.788] [DEBUG] [Application] [17] Auto-completion setup for JobTitleBox
[2025-07-03 01:57:20.806] [DEBUG] [Application] [17] Auto-completion setup for FirstNameBox
[2025-07-03 01:57:20.824] [DEBUG] [Application] [17] Auto-completion setup for DepartmentBox
[2025-07-03 01:57:20.841] [DEBUG] [Application] [17] Auto-completion setup for ModelAccountBox
[2025-07-03 01:57:20.866] [DEBUG] [Application] [17] Auto-completion integration completed
[2025-07-03 01:57:20.917] [INFO] [Application] [17] Modern UI Integration completed successfully
[2025-07-03 02:06:30.922] [DEBUG] [Testing] [15] Test registered: ConfigurationValidation in suite Unit
[2025-07-03 02:06:31.209] [DEBUG] [Testing] [15] Test registered: ConfigurationValidation in Unit
[2025-07-03 02:06:31.228] [DEBUG] [Testing] [15] Test registered: LoggingSystem in suite Unit
[2025-07-03 02:06:31.252] [DEBUG] [Testing] [15] Test registered: LoggingSystem in Unit
[2025-07-03 02:06:31.277] [DEBUG] [Testing] [15] Test registered: VersionManager in suite Unit
[2025-07-03 02:06:31.305] [DEBUG] [Testing] [15] Test registered: VersionManager in Unit
[2025-07-03 02:06:31.329] [DEBUG] [Testing] [15] Test registered: JiraConnection in suite Integration
[2025-07-03 02:06:31.352] [DEBUG] [Testing] [15] Test registered: JiraConnection in Integration
[2025-07-03 02:06:31.376] [DEBUG] [Testing] [15] Test registered: ActiveDirectoryConnection in suite Integration
[2025-07-03 02:06:31.451] [DEBUG] [Testing] [15] Test registered: ActiveDirectoryConnection in Integration
[2025-07-03 02:06:31.477] [DEBUG] [Testing] [15] Test registered: StartupTime in suite Performance
[2025-07-03 02:06:31.511] [DEBUG] [Testing] [15] Test registered: StartupTime in Performance
[2025-07-03 02:06:31.545] [DEBUG] [Testing] [15] Test registered: MemoryUsage in suite Performance
[2025-07-03 02:06:31.572] [DEBUG] [Testing] [15] Test registered: MemoryUsage in Performance
[2025-07-03 02:06:31.599] [DEBUG] [Testing] [15] Test registered: CredentialHandling in suite Security
[2025-07-03 02:06:31.629] [DEBUG] [Testing] [15] Test registered: CredentialHandling in Security
[2025-07-03 02:06:31.661] [DEBUG] [Testing] [15] Test registered: InputValidation in suite Security
[2025-07-03 02:06:31.691] [DEBUG] [Testing] [15] Test registered: InputValidation in Security
[2025-07-03 02:06:31.716] [DEBUG] [Testing] [15] Test registered: WindowCreation in suite UI
[2025-07-03 02:06:31.744] [DEBUG] [Testing] [15] Test registered: WindowCreation in UI
[2025-07-03 02:06:31.778] [INFO] [Testing] [15] Core tests initialized
[2025-07-03 02:06:31.864] [INFO] [Documentation] [15] Initializing documentation system
[2025-07-03 02:06:31.937] [INFO] [Documentation] [15] Starting documentation generation for: api
[2025-07-03 02:06:32.157] [ERROR] [Documentation] [15] Documentation generation failed: Method invocation failed because [DocumentationGenerator] does not contain a method named 'ExtractParameterName'. | Stack: Write-StructuredLog:6717 -> :8660 -> New-Documentation:9107 -> New-APIDocumentation:9113 -> Initialize-DocumentationSystem:9488 -> OnboardingFromJiraGUI.ps1:9502
[2025-07-03 02:06:32.274] [INFO] [Documentation] [15] Documentation generation completed for: api
[2025-07-03 02:06:32.313] [INFO] [Documentation] [15] API documentation generated: 0 files
[2025-07-03 02:06:32.354] [INFO] [Documentation] [15] Starting documentation generation for: help
[2025-07-03 02:06:32.413] [INFO] [Documentation] [15] Documentation generation completed for: help
[2025-07-03 02:06:32.444] [INFO] [Documentation] [15] Help system documentation generated
[2025-07-03 02:06:32.479] [INFO] [Documentation] [15] Documentation system initialized
[2025-07-03 02:06:32.845] [INFO] [Application] [15] Initializing enhanced UI components...
[2025-07-03 02:06:32.949] [DEBUG] [Application] [15] Theme applied to controls
[2025-07-03 02:06:32.964] [DEBUG] [Application] [15] Modern theme applied successfully
[2025-07-03 02:06:32.999] [DEBUG] [Application] [15] Field validation initialized
[2025-07-03 02:06:33.074] [DEBUG] [Application] [15] Responsive layout initialized
[2025-07-03 02:06:33.186] [DEBUG] [Application] [15] Workflow step 'Connection' status: Pending
[2025-07-03 02:06:33.225] [INFO] [Application] [15] Enhanced UI initialization completed
[2025-07-03 02:06:33.260] [INFO] [Application] [15] Welcome! Please connect to Jira to begin.
[2025-07-03 02:06:33.278] [INFO] [Application] [15] OnboardingFromJiraGUI started successfully
[2025-07-03 02:06:33.346] [INFO] [Application] [15] [OK] Monitoring system initialized
[2025-07-03 02:06:33.440] [INFO] [Audit] [15] Audit: akinje performed PermissionInit on UserPermissions - Success | Context: SessionId=Unknown; Action=PermissionInit; Timestamp=07/03/2025 02:06:33; Result=Success; User=akinje; Detected=Standard user privileges; Target=UserPermissions; PrivilegeLevel=Standard
[2025-07-03 02:06:33.480] [INFO] [Application] [15] [OK] User permissions initialized for JERAGM\akinje
[2025-07-03 02:06:33.509] [DEBUG] [Application] [15]  Cache cleanup scheduled every 30 minutes
[2025-07-03 02:06:33.526] [INFO] [Application] [15] Applying Modern UI Integration...
[2025-07-03 02:06:33.599] [DEBUG] [Application] [15] Auto-completion setup for LastNameBox
[2025-07-03 02:06:33.612] [DEBUG] [Application] [15] Auto-completion setup for JobTitleBox
[2025-07-03 02:06:33.630] [DEBUG] [Application] [15] Auto-completion setup for FirstNameBox
[2025-07-03 02:06:33.646] [DEBUG] [Application] [15] Auto-completion setup for DepartmentBox
[2025-07-03 02:06:33.663] [DEBUG] [Application] [15] Auto-completion setup for ModelAccountBox
[2025-07-03 02:06:33.678] [DEBUG] [Application] [15] Auto-completion integration completed
[2025-07-03 02:06:33.737] [INFO] [Application] [15] Modern UI Integration completed successfully
[2025-07-03 02:10:10.889] [DEBUG] [Testing] [18] Test registered: ConfigurationValidation in suite Unit
[2025-07-03 02:10:11.067] [DEBUG] [Testing] [18] Test registered: ConfigurationValidation in Unit
[2025-07-03 02:10:11.083] [DEBUG] [Testing] [18] Test registered: LoggingSystem in suite Unit
[2025-07-03 02:10:11.104] [DEBUG] [Testing] [18] Test registered: LoggingSystem in Unit
[2025-07-03 02:10:11.207] [DEBUG] [Testing] [18] Test registered: VersionManager in suite Unit
[2025-07-03 02:10:11.220] [DEBUG] [Testing] [18] Test registered: VersionManager in Unit
[2025-07-03 02:10:11.239] [DEBUG] [Testing] [18] Test registered: JiraConnection in suite Integration
[2025-07-03 02:10:11.256] [DEBUG] [Testing] [18] Test registered: JiraConnection in Integration
[2025-07-03 02:10:11.278] [DEBUG] [Testing] [18] Test registered: ActiveDirectoryConnection in suite Integration
[2025-07-03 02:10:11.300] [DEBUG] [Testing] [18] Test registered: ActiveDirectoryConnection in Integration
[2025-07-03 02:10:11.325] [DEBUG] [Testing] [18] Test registered: StartupTime in suite Performance
[2025-07-03 02:10:11.345] [DEBUG] [Testing] [18] Test registered: StartupTime in Performance
[2025-07-03 02:10:11.364] [DEBUG] [Testing] [18] Test registered: MemoryUsage in suite Performance
[2025-07-03 02:10:11.383] [DEBUG] [Testing] [18] Test registered: MemoryUsage in Performance
[2025-07-03 02:10:11.400] [DEBUG] [Testing] [18] Test registered: CredentialHandling in suite Security
[2025-07-03 02:10:11.416] [DEBUG] [Testing] [18] Test registered: CredentialHandling in Security
[2025-07-03 02:10:11.432] [DEBUG] [Testing] [18] Test registered: InputValidation in suite Security
[2025-07-03 02:10:11.450] [DEBUG] [Testing] [18] Test registered: InputValidation in Security
[2025-07-03 02:10:11.471] [DEBUG] [Testing] [18] Test registered: WindowCreation in suite UI
[2025-07-03 02:10:11.486] [DEBUG] [Testing] [18] Test registered: WindowCreation in UI
[2025-07-03 02:10:11.505] [INFO] [Testing] [18] Core tests initialized
[2025-07-03 02:10:11.553] [INFO] [Documentation] [18] Initializing documentation system
[2025-07-03 02:10:11.592] [INFO] [Documentation] [18] Starting documentation generation for: api
[2025-07-03 02:10:12.104] [INFO] [Documentation] [18] Documentation saved: Documentation\API_Reference.html
[2025-07-03 02:10:12.165] [INFO] [Documentation] [18] Documentation saved: Documentation\API_Reference.md
[2025-07-03 02:10:12.208] [INFO] [Documentation] [18] Documentation generation completed for: api
[2025-07-03 02:10:12.242] [INFO] [Documentation] [18] API documentation generated: 2 files
[2025-07-03 02:10:12.271] [INFO] [Documentation] [18] Starting documentation generation for: help
[2025-07-03 02:10:12.305] [INFO] [Documentation] [18] Documentation generation completed for: help
[2025-07-03 02:10:12.320] [INFO] [Documentation] [18] Help system documentation generated
[2025-07-03 02:10:12.337] [INFO] [Documentation] [18] Documentation system initialized
[2025-07-03 02:10:12.529] [INFO] [Application] [18] Initializing enhanced UI components...
[2025-07-03 02:10:12.762] [DEBUG] [Application] [18] Theme applied to controls
[2025-07-03 02:10:12.872] [DEBUG] [Application] [18] Modern theme applied successfully
[2025-07-03 02:10:12.962] [DEBUG] [Application] [18] Field validation initialized
[2025-07-03 02:10:13.041] [DEBUG] [Application] [18] Responsive layout initialized
[2025-07-03 02:10:13.270] [DEBUG] [Application] [18] Workflow step 'Connection' status: Pending
[2025-07-03 02:10:13.346] [INFO] [Application] [18] Enhanced UI initialization completed
[2025-07-03 02:10:13.386] [INFO] [Application] [18] Welcome! Please connect to Jira to begin.
[2025-07-03 02:10:13.406] [INFO] [Application] [18] OnboardingFromJiraGUI started successfully
[2025-07-03 02:10:13.459] [INFO] [Application] [18] [OK] Monitoring system initialized
[2025-07-03 02:10:13.509] [INFO] [Audit] [18] Audit: akinje performed PermissionInit on UserPermissions - Success | Context: SessionId=Unknown; Action=PermissionInit; Timestamp=07/03/2025 02:10:13; Result=Success; User=akinje; Detected=Standard user privileges; Target=UserPermissions; PrivilegeLevel=Standard
[2025-07-03 02:10:13.560] [INFO] [Application] [18] [OK] User permissions initialized for JERAGM\akinje
[2025-07-03 02:10:13.581] [DEBUG] [Application] [18]  Cache cleanup scheduled every 30 minutes
[2025-07-03 02:10:13.606] [INFO] [Application] [18] Applying Modern UI Integration...
[2025-07-03 02:10:13.729] [DEBUG] [Application] [18] Auto-completion setup for LastNameBox
[2025-07-03 02:10:13.751] [DEBUG] [Application] [18] Auto-completion setup for JobTitleBox
[2025-07-03 02:10:13.769] [DEBUG] [Application] [18] Auto-completion setup for FirstNameBox
[2025-07-03 02:10:13.790] [DEBUG] [Application] [18] Auto-completion setup for DepartmentBox
[2025-07-03 02:10:13.815] [DEBUG] [Application] [18] Auto-completion setup for ModelAccountBox
[2025-07-03 02:10:13.837] [DEBUG] [Application] [18] Auto-completion integration completed
[2025-07-03 02:10:13.895] [INFO] [Application] [18] Modern UI Integration completed successfully
[2025-07-03 02:10:51.798] [DEBUG] [Testing] [16] Test registered: ConfigurationValidation in suite Unit
[2025-07-03 02:10:51.982] [DEBUG] [Testing] [16] Test registered: ConfigurationValidation in Unit
[2025-07-03 02:10:52.087] [DEBUG] [Testing] [16] Test registered: LoggingSystem in suite Unit
[2025-07-03 02:10:52.099] [DEBUG] [Testing] [16] Test registered: LoggingSystem in Unit
[2025-07-03 02:10:52.112] [DEBUG] [Testing] [16] Test registered: VersionManager in suite Unit
[2025-07-03 02:10:52.127] [DEBUG] [Testing] [16] Test registered: VersionManager in Unit
[2025-07-03 02:10:52.139] [DEBUG] [Testing] [16] Test registered: JiraConnection in suite Integration
[2025-07-03 02:10:52.149] [DEBUG] [Testing] [16] Test registered: JiraConnection in Integration
[2025-07-03 02:10:52.163] [DEBUG] [Testing] [16] Test registered: ActiveDirectoryConnection in suite Integration
[2025-07-03 02:10:52.182] [DEBUG] [Testing] [16] Test registered: ActiveDirectoryConnection in Integration
[2025-07-03 02:10:52.205] [DEBUG] [Testing] [16] Test registered: StartupTime in suite Performance
[2025-07-03 02:10:52.236] [DEBUG] [Testing] [16] Test registered: StartupTime in Performance
[2025-07-03 02:10:52.256] [DEBUG] [Testing] [16] Test registered: MemoryUsage in suite Performance
[2025-07-03 02:10:52.273] [DEBUG] [Testing] [16] Test registered: MemoryUsage in Performance
[2025-07-03 02:10:52.290] [DEBUG] [Testing] [16] Test registered: CredentialHandling in suite Security
[2025-07-03 02:10:52.316] [DEBUG] [Testing] [16] Test registered: CredentialHandling in Security
[2025-07-03 02:10:52.336] [DEBUG] [Testing] [16] Test registered: InputValidation in suite Security
[2025-07-03 02:10:52.362] [DEBUG] [Testing] [16] Test registered: InputValidation in Security
[2025-07-03 02:10:52.382] [DEBUG] [Testing] [16] Test registered: WindowCreation in suite UI
[2025-07-03 02:10:52.406] [DEBUG] [Testing] [16] Test registered: WindowCreation in UI
[2025-07-03 02:10:52.430] [INFO] [Testing] [16] Core tests initialized
[2025-07-03 02:10:52.490] [INFO] [Documentation] [16] Initializing documentation system
[2025-07-03 02:10:52.570] [INFO] [Documentation] [16] Starting documentation generation for: api
[2025-07-03 02:10:53.162] [INFO] [Documentation] [16] Documentation saved: Documentation\API_Reference.html
[2025-07-03 02:10:53.202] [INFO] [Documentation] [16] Documentation saved: Documentation\API_Reference.md
[2025-07-03 02:10:53.248] [INFO] [Documentation] [16] Documentation generation completed for: api
[2025-07-03 02:10:53.272] [INFO] [Documentation] [16] API documentation generated: 2 files
[2025-07-03 02:10:53.295] [INFO] [Documentation] [16] Starting documentation generation for: help
[2025-07-03 02:10:53.319] [INFO] [Documentation] [16] Documentation generation completed for: help
[2025-07-03 02:10:53.333] [INFO] [Documentation] [16] Help system documentation generated
[2025-07-03 02:10:53.349] [INFO] [Documentation] [16] Documentation system initialized
[2025-07-03 02:10:53.520] [INFO] [Application] [16] Initializing enhanced UI components...
[2025-07-03 02:10:53.599] [DEBUG] [Application] [16] Theme applied to controls
[2025-07-03 02:10:53.657] [DEBUG] [Application] [16] Modern theme applied successfully
[2025-07-03 02:10:53.715] [DEBUG] [Application] [16] Field validation initialized
[2025-07-03 02:10:53.773] [DEBUG] [Application] [16] Responsive layout initialized
[2025-07-03 02:10:53.897] [DEBUG] [Application] [16] Workflow step 'Connection' status: Pending
[2025-07-03 02:10:53.949] [INFO] [Application] [16] Enhanced UI initialization completed
[2025-07-03 02:10:54.007] [INFO] [Application] [16] Welcome! Please connect to Jira to begin.
[2025-07-03 02:10:54.043] [INFO] [Application] [16] OnboardingFromJiraGUI started successfully
[2025-07-03 02:10:54.082] [INFO] [Application] [16] [OK] Monitoring system initialized
[2025-07-03 02:10:54.146] [INFO] [Audit] [16] Audit: akinje performed PermissionInit on UserPermissions - Success | Context: SessionId=Unknown; Action=PermissionInit; Timestamp=07/03/2025 02:10:54; Result=Success; User=akinje; Detected=Standard user privileges; Target=UserPermissions; PrivilegeLevel=Standard
[2025-07-03 02:10:54.186] [INFO] [Application] [16] [OK] User permissions initialized for JERAGM\akinje
[2025-07-03 02:10:54.239] [DEBUG] [Application] [16]  Cache cleanup scheduled every 30 minutes
[2025-07-03 02:10:54.282] [INFO] [Application] [16] Applying Modern UI Integration...
[2025-07-03 02:10:54.462] [DEBUG] [Application] [16] Auto-completion setup for LastNameBox
[2025-07-03 02:10:54.487] [DEBUG] [Application] [16] Auto-completion setup for JobTitleBox
[2025-07-03 02:10:54.521] [DEBUG] [Application] [16] Auto-completion setup for FirstNameBox
[2025-07-03 02:10:54.548] [DEBUG] [Application] [16] Auto-completion setup for DepartmentBox
[2025-07-03 02:10:54.578] [DEBUG] [Application] [16] Auto-completion setup for ModelAccountBox
[2025-07-03 02:10:54.600] [DEBUG] [Application] [16] Auto-completion integration completed
[2025-07-03 02:10:54.686] [INFO] [Application] [16] Modern UI Integration completed successfully
[2025-07-03 02:16:23.093] [DEBUG] [Testing] [15] Test registered: ConfigurationValidation in suite Unit
[2025-07-03 02:16:23.241] [DEBUG] [Testing] [15] Test registered: ConfigurationValidation in Unit
[2025-07-03 02:16:23.255] [DEBUG] [Testing] [15] Test registered: LoggingSystem in suite Unit
[2025-07-03 02:16:23.265] [DEBUG] [Testing] [15] Test registered: LoggingSystem in Unit
[2025-07-03 02:16:23.278] [DEBUG] [Testing] [15] Test registered: VersionManager in suite Unit
[2025-07-03 02:16:23.290] [DEBUG] [Testing] [15] Test registered: VersionManager in Unit
[2025-07-03 02:16:23.301] [DEBUG] [Testing] [15] Test registered: JiraConnection in suite Integration
[2025-07-03 02:16:23.311] [DEBUG] [Testing] [15] Test registered: JiraConnection in Integration
[2025-07-03 02:16:23.322] [DEBUG] [Testing] [15] Test registered: ActiveDirectoryConnection in suite Integration
[2025-07-03 02:16:23.332] [DEBUG] [Testing] [15] Test registered: ActiveDirectoryConnection in Integration
[2025-07-03 02:16:23.346] [DEBUG] [Testing] [15] Test registered: StartupTime in suite Performance
[2025-07-03 02:16:23.434] [DEBUG] [Testing] [15] Test registered: StartupTime in Performance
[2025-07-03 02:16:23.442] [DEBUG] [Testing] [15] Test registered: MemoryUsage in suite Performance
[2025-07-03 02:16:23.451] [DEBUG] [Testing] [15] Test registered: MemoryUsage in Performance
[2025-07-03 02:16:23.460] [DEBUG] [Testing] [15] Test registered: CredentialHandling in suite Security
[2025-07-03 02:16:23.470] [DEBUG] [Testing] [15] Test registered: CredentialHandling in Security
[2025-07-03 02:16:23.483] [DEBUG] [Testing] [15] Test registered: InputValidation in suite Security
[2025-07-03 02:16:23.496] [DEBUG] [Testing] [15] Test registered: InputValidation in Security
[2025-07-03 02:16:23.508] [DEBUG] [Testing] [15] Test registered: WindowCreation in suite UI
[2025-07-03 02:16:23.517] [DEBUG] [Testing] [15] Test registered: WindowCreation in UI
[2025-07-03 02:16:23.529] [INFO] [Testing] [15] Core tests initialized
[2025-07-03 02:16:23.569] [INFO] [Documentation] [15] Initializing documentation system
[2025-07-03 02:16:23.597] [INFO] [Documentation] [15] Starting documentation generation for: api
[2025-07-03 02:16:24.148] [INFO] [Documentation] [15] Documentation saved: Documentation\API_Reference.html
[2025-07-03 02:16:24.183] [INFO] [Documentation] [15] Documentation saved: Documentation\API_Reference.md
[2025-07-03 02:16:24.211] [INFO] [Documentation] [15] Documentation generation completed for: api
[2025-07-03 02:16:24.238] [INFO] [Documentation] [15] API documentation generated: 2 files
[2025-07-03 02:16:24.251] [INFO] [Documentation] [15] Starting documentation generation for: help
[2025-07-03 02:16:24.271] [INFO] [Documentation] [15] Documentation generation completed for: help
[2025-07-03 02:16:24.282] [INFO] [Documentation] [15] Help system documentation generated
[2025-07-03 02:16:24.291] [INFO] [Documentation] [15] Documentation system initialized
[2025-07-03 02:16:24.411] [INFO] [Application] [15] Initializing enhanced UI components...
[2025-07-03 02:16:24.479] [DEBUG] [Application] [15] Theme applied to controls
[2025-07-03 02:16:24.534] [DEBUG] [Application] [15] Modern theme applied successfully
[2025-07-03 02:16:24.571] [DEBUG] [Application] [15] Field validation initialized
[2025-07-03 02:16:24.607] [DEBUG] [Application] [15] Responsive layout initialized
[2025-07-03 02:16:24.721] [DEBUG] [Application] [15] Workflow step 'Connection' status: Pending
[2025-07-03 02:16:24.783] [INFO] [Application] [15] Enhanced UI initialization completed
[2025-07-03 02:16:24.826] [INFO] [Application] [15] Welcome! Please connect to Jira to begin.
[2025-07-03 02:16:24.843] [INFO] [Application] [15] OnboardingFromJiraGUI started successfully
[2025-07-03 02:16:24.868] [INFO] [Application] [15] [OK] Monitoring system initialized
[2025-07-03 02:16:24.913] [INFO] [Audit] [15] Audit: akinje performed PermissionInit on UserPermissions - Success | Context: SessionId=Unknown; Action=PermissionInit; Timestamp=07/03/2025 02:16:24; Result=Success; User=akinje; Detected=Standard user privileges; Target=UserPermissions; PrivilegeLevel=Standard
[2025-07-03 02:16:24.939] [INFO] [Application] [15] [OK] User permissions initialized for JERAGM\akinje
[2025-07-03 02:16:24.957] [DEBUG] [Application] [15]  Cache cleanup scheduled every 30 minutes
[2025-07-03 02:16:24.972] [INFO] [Application] [15] Applying Modern UI Integration...
[2025-07-03 02:16:25.069] [DEBUG] [Application] [15] Auto-completion setup for LastNameBox
[2025-07-03 02:16:25.082] [DEBUG] [Application] [15] Auto-completion setup for JobTitleBox
[2025-07-03 02:16:25.092] [DEBUG] [Application] [15] Auto-completion setup for FirstNameBox
[2025-07-03 02:16:25.102] [DEBUG] [Application] [15] Auto-completion setup for DepartmentBox
[2025-07-03 02:16:25.113] [DEBUG] [Application] [15] Auto-completion setup for ModelAccountBox
[2025-07-03 02:16:25.124] [DEBUG] [Application] [15] Auto-completion integration completed
[2025-07-03 02:16:25.169] [INFO] [Application] [15] Modern UI Integration completed successfully
[2025-07-03 02:17:27.462] [DEBUG] [Testing] [15] Test registered: ConfigurationValidation in suite Unit
[2025-07-03 02:17:27.615] [DEBUG] [Testing] [15] Test registered: ConfigurationValidation in Unit
[2025-07-03 02:17:27.632] [DEBUG] [Testing] [15] Test registered: LoggingSystem in suite Unit
[2025-07-03 02:17:27.647] [DEBUG] [Testing] [15] Test registered: LoggingSystem in Unit
[2025-07-03 02:17:27.657] [DEBUG] [Testing] [15] Test registered: VersionManager in suite Unit
[2025-07-03 02:17:27.673] [DEBUG] [Testing] [15] Test registered: VersionManager in Unit
[2025-07-03 02:17:27.688] [DEBUG] [Testing] [15] Test registered: JiraConnection in suite Integration
[2025-07-03 02:17:27.702] [DEBUG] [Testing] [15] Test registered: JiraConnection in Integration
[2025-07-03 02:17:27.721] [DEBUG] [Testing] [15] Test registered: ActiveDirectoryConnection in suite Integration
[2025-07-03 02:17:27.881] [DEBUG] [Testing] [15] Test registered: ActiveDirectoryConnection in Integration
[2025-07-03 02:17:27.912] [DEBUG] [Testing] [15] Test registered: StartupTime in suite Performance
[2025-07-03 02:17:27.951] [DEBUG] [Testing] [15] Test registered: StartupTime in Performance
[2025-07-03 02:17:27.967] [DEBUG] [Testing] [15] Test registered: MemoryUsage in suite Performance
[2025-07-03 02:17:27.982] [DEBUG] [Testing] [15] Test registered: MemoryUsage in Performance
[2025-07-03 02:17:27.996] [DEBUG] [Testing] [15] Test registered: CredentialHandling in suite Security
[2025-07-03 02:17:28.012] [DEBUG] [Testing] [15] Test registered: CredentialHandling in Security
[2025-07-03 02:17:28.026] [DEBUG] [Testing] [15] Test registered: InputValidation in suite Security
[2025-07-03 02:17:28.048] [DEBUG] [Testing] [15] Test registered: InputValidation in Security
[2025-07-03 02:17:28.068] [DEBUG] [Testing] [15] Test registered: WindowCreation in suite UI
[2025-07-03 02:17:28.089] [DEBUG] [Testing] [15] Test registered: WindowCreation in UI
[2025-07-03 02:17:28.112] [INFO] [Testing] [15] Core tests initialized
[2025-07-03 02:17:28.176] [INFO] [Documentation] [15] Initializing documentation system
[2025-07-03 02:17:28.211] [INFO] [Documentation] [15] Starting documentation generation for: api
[2025-07-03 02:17:28.684] [INFO] [Documentation] [15] Documentation saved: Documentation\API_Reference.html
[2025-07-03 02:17:28.723] [INFO] [Documentation] [15] Documentation saved: Documentation\API_Reference.md
[2025-07-03 02:17:28.753] [INFO] [Documentation] [15] Documentation generation completed for: api
[2025-07-03 02:17:28.774] [INFO] [Documentation] [15] API documentation generated: 2 files
[2025-07-03 02:17:28.789] [INFO] [Documentation] [15] Starting documentation generation for: help
[2025-07-03 02:17:28.814] [INFO] [Documentation] [15] Documentation generation completed for: help
[2025-07-03 02:17:28.825] [INFO] [Documentation] [15] Help system documentation generated
[2025-07-03 02:17:28.839] [INFO] [Documentation] [15] Documentation system initialized
[2025-07-03 02:17:28.949] [INFO] [Application] [15] Initializing enhanced UI components...
[2025-07-03 02:17:29.018] [DEBUG] [Application] [15] Theme applied to controls
[2025-07-03 02:17:29.080] [DEBUG] [Application] [15] Modern theme applied successfully
[2025-07-03 02:17:29.106] [DEBUG] [Application] [15] Field validation initialized
[2025-07-03 02:17:29.155] [DEBUG] [Application] [15] Responsive layout initialized
[2025-07-03 02:17:29.234] [DEBUG] [Application] [15] Workflow step 'Connection' status: Pending
[2025-07-03 02:17:29.291] [INFO] [Application] [15] Enhanced UI initialization completed
[2025-07-03 02:17:29.335] [INFO] [Application] [15] Welcome! Please connect to Jira to begin.
[2025-07-03 02:17:29.352] [INFO] [Application] [15] OnboardingFromJiraGUI started successfully
[2025-07-03 02:17:29.376] [INFO] [Application] [15] [OK] Monitoring system initialized
[2025-07-03 02:17:29.413] [INFO] [Audit] [15] Audit: akinje performed PermissionInit on UserPermissions - Success | Context: SessionId=Unknown; Action=PermissionInit; Timestamp=07/03/2025 02:17:29; Result=Success; User=akinje; Detected=Standard user privileges; Target=UserPermissions; PrivilegeLevel=Standard
[2025-07-03 02:17:29.435] [INFO] [Application] [15] [OK] User permissions initialized for JERAGM\akinje
[2025-07-03 02:17:29.453] [DEBUG] [Application] [15]  Cache cleanup scheduled every 30 minutes
[2025-07-03 02:17:29.469] [INFO] [Application] [15] Applying Modern UI Integration...
[2025-07-03 02:17:29.551] [DEBUG] [Application] [15] Auto-completion setup for LastNameBox
[2025-07-03 02:17:29.563] [DEBUG] [Application] [15] Auto-completion setup for JobTitleBox
[2025-07-03 02:17:29.575] [DEBUG] [Application] [15] Auto-completion setup for FirstNameBox
[2025-07-03 02:17:29.586] [DEBUG] [Application] [15] Auto-completion setup for DepartmentBox
[2025-07-03 02:17:29.598] [DEBUG] [Application] [15] Auto-completion setup for ModelAccountBox
[2025-07-03 02:17:29.611] [DEBUG] [Application] [15] Auto-completion integration completed
[2025-07-03 02:17:29.649] [INFO] [Application] [15] Modern UI Integration completed successfully
[2025-07-03 02:18:47.017] [DEBUG] [Testing] [16] Test registered: ConfigurationValidation in suite Unit
[2025-07-03 02:18:47.136] [DEBUG] [Testing] [16] Test registered: ConfigurationValidation in Unit
[2025-07-03 02:18:47.147] [DEBUG] [Testing] [16] Test registered: LoggingSystem in suite Unit
[2025-07-03 02:18:47.155] [DEBUG] [Testing] [16] Test registered: LoggingSystem in Unit
[2025-07-03 02:18:47.165] [DEBUG] [Testing] [16] Test registered: VersionManager in suite Unit
[2025-07-03 02:18:47.175] [DEBUG] [Testing] [16] Test registered: VersionManager in Unit
[2025-07-03 02:18:47.186] [DEBUG] [Testing] [16] Test registered: JiraConnection in suite Integration
[2025-07-03 02:18:47.194] [DEBUG] [Testing] [16] Test registered: JiraConnection in Integration
[2025-07-03 02:18:47.208] [DEBUG] [Testing] [16] Test registered: ActiveDirectoryConnection in suite Integration
[2025-07-03 02:18:47.218] [DEBUG] [Testing] [16] Test registered: ActiveDirectoryConnection in Integration
[2025-07-03 02:18:47.241] [DEBUG] [Testing] [16] Test registered: StartupTime in suite Performance
[2025-07-03 02:18:47.353] [DEBUG] [Testing] [16] Test registered: StartupTime in Performance
[2025-07-03 02:18:47.368] [DEBUG] [Testing] [16] Test registered: MemoryUsage in suite Performance
[2025-07-03 02:18:47.381] [DEBUG] [Testing] [16] Test registered: MemoryUsage in Performance
[2025-07-03 02:18:47.394] [DEBUG] [Testing] [16] Test registered: CredentialHandling in suite Security
[2025-07-03 02:18:47.413] [DEBUG] [Testing] [16] Test registered: CredentialHandling in Security
[2025-07-03 02:18:47.431] [DEBUG] [Testing] [16] Test registered: InputValidation in suite Security
[2025-07-03 02:18:47.457] [DEBUG] [Testing] [16] Test registered: InputValidation in Security
[2025-07-03 02:18:47.478] [DEBUG] [Testing] [16] Test registered: WindowCreation in suite UI
[2025-07-03 02:18:47.495] [DEBUG] [Testing] [16] Test registered: WindowCreation in UI
[2025-07-03 02:18:47.511] [INFO] [Testing] [16] Core tests initialized
[2025-07-03 02:18:47.546] [INFO] [Documentation] [16] Initializing documentation system
[2025-07-03 02:18:47.569] [INFO] [Documentation] [16] Starting documentation generation for: api
[2025-07-03 02:18:47.964] [INFO] [Documentation] [16] Documentation saved: Documentation\API_Reference.html
[2025-07-03 02:18:47.999] [INFO] [Documentation] [16] Documentation saved: Documentation\API_Reference.md
[2025-07-03 02:18:48.021] [INFO] [Documentation] [16] Documentation generation completed for: api
[2025-07-03 02:18:48.043] [INFO] [Documentation] [16] API documentation generated: 2 files
[2025-07-03 02:18:48.055] [INFO] [Documentation] [16] Starting documentation generation for: help
[2025-07-03 02:18:48.072] [INFO] [Documentation] [16] Documentation generation completed for: help
[2025-07-03 02:18:48.085] [INFO] [Documentation] [16] Help system documentation generated
[2025-07-03 02:18:48.100] [INFO] [Documentation] [16] Documentation system initialized
[2025-07-03 02:18:48.198] [INFO] [Application] [16] Initializing enhanced UI components...
[2025-07-03 02:18:48.253] [DEBUG] [Application] [16] Theme applied to controls
[2025-07-03 02:18:48.305] [DEBUG] [Application] [16] Modern theme applied successfully
[2025-07-03 02:18:48.418] [DEBUG] [Application] [16] Field validation initialized
[2025-07-03 02:18:48.452] [DEBUG] [Application] [16] Responsive layout initialized
[2025-07-03 02:18:48.539] [DEBUG] [Application] [16] Workflow step 'Connection' status: Pending
[2025-07-03 02:18:48.586] [INFO] [Application] [16] Enhanced UI initialization completed
[2025-07-03 02:18:48.629] [INFO] [Application] [16] Welcome! Please connect to Jira to begin.
[2025-07-03 02:18:48.655] [INFO] [Application] [16] OnboardingFromJiraGUI started successfully
[2025-07-03 02:18:48.682] [INFO] [Application] [16] [OK] Monitoring system initialized
[2025-07-03 02:18:48.722] [INFO] [Audit] [16] Audit: akinje performed PermissionInit on UserPermissions - Success | Context: SessionId=Unknown; Action=PermissionInit; Timestamp=07/03/2025 02:18:48; Result=Success; User=akinje; Detected=Standard user privileges; Target=UserPermissions; PrivilegeLevel=Standard
[2025-07-03 02:18:48.745] [INFO] [Application] [16] [OK] User permissions initialized for JERAGM\akinje
[2025-07-03 02:18:48.765] [DEBUG] [Application] [16]  Cache cleanup scheduled every 30 minutes
[2025-07-03 02:18:48.782] [INFO] [Application] [16] Applying Modern UI Integration...
[2025-07-03 02:18:48.868] [DEBUG] [Application] [16] Auto-completion setup for LastNameBox
[2025-07-03 02:18:48.879] [DEBUG] [Application] [16] Auto-completion setup for JobTitleBox
[2025-07-03 02:18:48.897] [DEBUG] [Application] [16] Auto-completion setup for FirstNameBox
[2025-07-03 02:18:48.913] [DEBUG] [Application] [16] Auto-completion setup for DepartmentBox
[2025-07-03 02:18:48.929] [DEBUG] [Application] [16] Auto-completion setup for ModelAccountBox
[2025-07-03 02:18:48.945] [DEBUG] [Application] [16] Auto-completion integration completed
[2025-07-03 02:18:48.985] [INFO] [Application] [16] Modern UI Integration completed successfully
[2025-07-03 07:57:18.665] [DEBUG] [Testing] [17] Test registered: ConfigurationValidation in suite Unit
[2025-07-03 07:57:18.936] [DEBUG] [Testing] [17] Test registered: ConfigurationValidation in Unit
[2025-07-03 07:57:18.953] [DEBUG] [Testing] [17] Test registered: LoggingSystem in suite Unit
[2025-07-03 07:57:18.969] [DEBUG] [Testing] [17] Test registered: LoggingSystem in Unit
[2025-07-03 07:57:18.984] [DEBUG] [Testing] [17] Test registered: VersionManager in suite Unit
[2025-07-03 07:57:19.005] [DEBUG] [Testing] [17] Test registered: VersionManager in Unit
[2025-07-03 07:57:19.023] [DEBUG] [Testing] [17] Test registered: JiraConnection in suite Integration
[2025-07-03 07:57:19.039] [DEBUG] [Testing] [17] Test registered: JiraConnection in Integration
[2025-07-03 07:57:19.053] [DEBUG] [Testing] [17] Test registered: ActiveDirectoryConnection in suite Integration
[2025-07-03 07:57:19.071] [DEBUG] [Testing] [17] Test registered: ActiveDirectoryConnection in Integration
[2025-07-03 07:57:19.194] [DEBUG] [Testing] [17] Test registered: StartupTime in suite Performance
[2025-07-03 07:57:19.209] [DEBUG] [Testing] [17] Test registered: StartupTime in Performance
[2025-07-03 07:57:19.226] [DEBUG] [Testing] [17] Test registered: MemoryUsage in suite Performance
[2025-07-03 07:57:19.241] [DEBUG] [Testing] [17] Test registered: MemoryUsage in Performance
[2025-07-03 07:57:19.259] [DEBUG] [Testing] [17] Test registered: CredentialHandling in suite Security
[2025-07-03 07:57:19.278] [DEBUG] [Testing] [17] Test registered: CredentialHandling in Security
[2025-07-03 07:57:19.304] [DEBUG] [Testing] [17] Test registered: InputValidation in suite Security
[2025-07-03 07:57:19.331] [DEBUG] [Testing] [17] Test registered: InputValidation in Security
[2025-07-03 07:57:19.355] [DEBUG] [Testing] [17] Test registered: WindowCreation in suite UI
[2025-07-03 07:57:19.386] [DEBUG] [Testing] [17] Test registered: WindowCreation in UI
[2025-07-03 07:57:19.410] [INFO] [Testing] [17] Core tests initialized
[2025-07-03 07:57:19.485] [INFO] [Documentation] [17] Initializing documentation system
[2025-07-03 07:57:19.551] [INFO] [Documentation] [17] Starting documentation generation for: api
[2025-07-03 07:57:20.180] [INFO] [Documentation] [17] Documentation saved: Documentation\API_Reference.html
[2025-07-03 07:57:20.236] [INFO] [Documentation] [17] Documentation saved: Documentation\API_Reference.md
[2025-07-03 07:57:20.288] [INFO] [Documentation] [17] Documentation generation completed for: api
[2025-07-03 07:57:20.348] [INFO] [Documentation] [17] API documentation generated: 2 files
[2025-07-03 07:57:20.444] [INFO] [Documentation] [17] Starting documentation generation for: help
[2025-07-03 07:57:20.502] [INFO] [Documentation] [17] Documentation generation completed for: help
[2025-07-03 07:57:20.540] [INFO] [Documentation] [17] Help system documentation generated
[2025-07-03 07:57:20.573] [INFO] [Documentation] [17] Documentation system initialized
[2025-07-03 07:57:20.862] [INFO] [Application] [17] Initializing enhanced UI components...
[2025-07-03 07:57:20.938] [DEBUG] [Application] [17] Theme applied to controls
[2025-07-03 07:57:20.955] [DEBUG] [Application] [17] Modern theme applied successfully
[2025-07-03 07:57:21.034] [DEBUG] [Application] [17] Field validation initialized
[2025-07-03 07:57:21.101] [DEBUG] [Application] [17] Responsive layout initialized
[2025-07-03 07:57:21.317] [DEBUG] [Application] [17] Workflow step 'Connection' status: Pending
[2025-07-03 07:57:21.455] [INFO] [Application] [17] Enhanced UI initialization completed
[2025-07-03 07:57:21.540] [INFO] [Application] [17] Welcome! Please connect to Jira to begin.
[2025-07-03 07:57:21.564] [INFO] [Application] [17] OnboardingFromJiraGUI started successfully
[2025-07-03 07:57:21.590] [INFO] [Application] [17] [OK] Monitoring system initialized
[2025-07-03 07:57:21.663] [INFO] [Audit] [17] Audit: akinje performed PermissionInit on UserPermissions - Success | Context: SessionId=Unknown; Action=PermissionInit; Timestamp=07/03/2025 07:57:21; Result=Success; User=akinje; Detected=Standard user privileges; Target=UserPermissions; PrivilegeLevel=Standard
[2025-07-03 07:57:21.717] [INFO] [Application] [17] [OK] User permissions initialized for JERAGM\akinje
[2025-07-03 07:57:21.786] [DEBUG] [Application] [17]  Cache cleanup scheduled every 30 minutes
[2025-07-03 07:57:21.807] [INFO] [Application] [17] Applying Modern UI Integration...
[2025-07-03 07:57:21.955] [DEBUG] [Application] [17] Auto-completion setup for LastNameBox
[2025-07-03 07:57:21.974] [DEBUG] [Application] [17] Auto-completion setup for JobTitleBox
[2025-07-03 07:57:22.001] [DEBUG] [Application] [17] Auto-completion setup for FirstNameBox
[2025-07-03 07:57:22.033] [DEBUG] [Application] [17] Auto-completion setup for DepartmentBox
[2025-07-03 07:57:22.067] [DEBUG] [Application] [17] Auto-completion setup for ModelAccountBox
[2025-07-03 07:57:22.098] [DEBUG] [Application] [17] Auto-completion integration completed
[2025-07-03 07:57:22.176] [INFO] [Application] [17] Modern UI Integration completed successfully
[2025-07-03 07:57:52.957] [INFO] [Application] [17] Step 1: Connect to Jira
[2025-07-03 07:57:52.967] [DEBUG] [Application] [17] Workflow step 'Connection' status: InProgress
[2025-07-03 07:57:53.005] [INFO] [Application] [17] Connecting to https://jeragm.atlassian.net...
[2025-07-03 07:57:54.688] [DEBUG] [Application] [17] Workflow step 'Connection' status: Completed
[2025-07-03 07:57:54.688] [INFO] [Application] [17] Successfully connected to Jira
[2025-07-03 09:40:43.165] [DEBUG] [Testing] [15] Test registered: ConfigurationValidation in suite Unit
[2025-07-03 09:40:43.448] [DEBUG] [Testing] [15] Test registered: ConfigurationValidation in Unit
[2025-07-03 09:40:43.466] [DEBUG] [Testing] [15] Test registered: LoggingSystem in suite Unit
[2025-07-03 09:40:43.488] [DEBUG] [Testing] [15] Test registered: LoggingSystem in Unit
[2025-07-03 09:40:43.506] [DEBUG] [Testing] [15] Test registered: VersionManager in suite Unit
[2025-07-03 09:40:43.530] [DEBUG] [Testing] [15] Test registered: VersionManager in Unit
[2025-07-03 09:40:43.552] [DEBUG] [Testing] [15] Test registered: JiraConnection in suite Integration
[2025-07-03 09:40:43.570] [DEBUG] [Testing] [15] Test registered: JiraConnection in Integration
[2025-07-03 09:40:43.601] [DEBUG] [Testing] [15] Test registered: ActiveDirectoryConnection in suite Integration
[2025-07-03 09:40:43.641] [DEBUG] [Testing] [15] Test registered: ActiveDirectoryConnection in Integration
[2025-07-03 09:40:43.673] [DEBUG] [Testing] [15] Test registered: StartupTime in suite Performance
[2025-07-03 09:40:43.696] [DEBUG] [Testing] [15] Test registered: StartupTime in Performance
[2025-07-03 09:40:43.740] [DEBUG] [Testing] [15] Test registered: MemoryUsage in suite Performance
[2025-07-03 09:40:43.768] [DEBUG] [Testing] [15] Test registered: MemoryUsage in Performance
[2025-07-03 09:40:43.792] [DEBUG] [Testing] [15] Test registered: CredentialHandling in suite Security
[2025-07-03 09:40:43.830] [DEBUG] [Testing] [15] Test registered: CredentialHandling in Security
[2025-07-03 09:40:43.862] [DEBUG] [Testing] [15] Test registered: InputValidation in suite Security
[2025-07-03 09:40:43.902] [DEBUG] [Testing] [15] Test registered: InputValidation in Security
[2025-07-03 09:40:43.937] [DEBUG] [Testing] [15] Test registered: WindowCreation in suite UI
[2025-07-03 09:40:43.979] [DEBUG] [Testing] [15] Test registered: WindowCreation in UI
[2025-07-03 09:40:44.011] [INFO] [Testing] [15] Core tests initialized
[2025-07-03 09:40:44.127] [INFO] [Documentation] [15] Initializing documentation system
[2025-07-03 09:40:44.334] [INFO] [Documentation] [15] Starting documentation generation for: api
[2025-07-03 09:40:45.665] [INFO] [Documentation] [15] Documentation saved: Documentation\API_Reference.html
[2025-07-03 09:40:45.771] [INFO] [Documentation] [15] Documentation saved: Documentation\API_Reference.md
[2025-07-03 09:40:45.834] [INFO] [Documentation] [15] Documentation generation completed for: api
[2025-07-03 09:40:45.932] [INFO] [Documentation] [15] API documentation generated: 2 files
[2025-07-03 09:40:46.027] [INFO] [Documentation] [15] Starting documentation generation for: help
[2025-07-03 09:40:46.174] [INFO] [Documentation] [15] Documentation generation completed for: help
[2025-07-03 09:40:46.212] [INFO] [Documentation] [15] Help system documentation generated
[2025-07-03 09:40:46.241] [INFO] [Documentation] [15] Documentation system initialized
[2025-07-03 09:40:46.557] [INFO] [Application] [15] Initializing enhanced UI components...
[2025-07-03 09:40:46.757] [DEBUG] [Application] [15] Theme applied to controls
[2025-07-03 09:40:46.840] [DEBUG] [Application] [15] Modern theme applied successfully
[2025-07-03 09:40:46.932] [DEBUG] [Application] [15] Field validation initialized
[2025-07-03 09:40:47.009] [DEBUG] [Application] [15] Responsive layout initialized
[2025-07-03 09:40:47.243] [DEBUG] [Application] [15] Workflow step 'Connection' status: Pending
[2025-07-03 09:40:47.356] [INFO] [Application] [15] Enhanced UI initialization completed
[2025-07-03 09:40:47.483] [INFO] [Application] [15] Welcome! Please connect to Jira to begin.
[2025-07-03 09:40:47.528] [INFO] [Application] [15] OnboardingFromJiraGUI started successfully
[2025-07-03 09:40:47.626] [INFO] [Application] [15] [OK] Monitoring system initialized
[2025-07-03 09:40:47.750] [INFO] [Audit] [15] Audit: akinje performed PermissionInit on UserPermissions - Success | Context: SessionId=Unknown; Action=PermissionInit; Timestamp=07/03/2025 09:40:47; Result=Success; User=akinje; Detected=Standard user privileges; Target=UserPermissions; PrivilegeLevel=Standard
[2025-07-03 09:40:47.809] [INFO] [Application] [15] [OK] User permissions initialized for JERAGM\akinje
[2025-07-03 09:40:47.844] [DEBUG] [Application] [15]  Cache cleanup scheduled every 30 minutes
[2025-07-03 09:40:47.889] [INFO] [Application] [15] Applying Modern UI Integration...
[2025-07-03 09:40:48.082] [DEBUG] [Application] [15] Auto-completion setup for LastNameBox
[2025-07-03 09:40:48.110] [DEBUG] [Application] [15] Auto-completion setup for JobTitleBox
[2025-07-03 09:40:48.143] [DEBUG] [Application] [15] Auto-completion setup for FirstNameBox
[2025-07-03 09:40:48.183] [DEBUG] [Application] [15] Auto-completion setup for DepartmentBox
[2025-07-03 09:40:48.220] [DEBUG] [Application] [15] Auto-completion setup for ModelAccountBox
[2025-07-03 09:40:48.247] [DEBUG] [Application] [15] Auto-completion integration completed
[2025-07-03 09:40:48.340] [INFO] [Application] [15] Modern UI Integration completed successfully
[2025-07-03 09:41:09.483] [INFO] [Application] [15] Step 1: Connect to Jira
[2025-07-03 09:41:09.522] [DEBUG] [Application] [15] Workflow step 'Connection' status: InProgress
[2025-07-03 09:41:09.563] [INFO] [Application] [15] Connecting to https://jeragm.atlassian.net...
[2025-07-03 09:41:13.155] [DEBUG] [Application] [15] Workflow step 'Connection' status: Completed
[2025-07-03 09:41:13.197] [INFO] [Application] [15] Successfully connected to Jira
[2025-07-03 09:41:16.007] [INFO] [Application] [15] Step 2: Fetch Ticket Data
[2025-07-03 09:41:16.087] [DEBUG] [Application] [15] Workflow step 'FetchData' status: InProgress
[2025-07-03 09:41:16.136] [INFO] [Application] [15] Fetching data for ticket TESTIT-49342...
[2025-07-03 09:41:22.604] [DEBUG] [Application] [15] Cached ticket: TESTIT-49342
[2025-07-03 09:41:22.732] [DEBUG] [Application] [15] Issue object properties: customfield_10553, customfield_10058, customfield_10305, customfield_10149, customfield_10723, customfield_10928, customfield_10194, customfield_10363, customfield_10750, customfield_10147, customfield_11280, customfield_10729, customfield_10153, customfield_10369, customfield_10145, customfield_10783, customfield_10727, duedate, customfield_10151, customfield_10367, customfield_10370, customfield_10486, customfield_10373, worklog, customfield_10157, customfield_10371, customfield_10008, customfield_10155, customfield_10519, customfield_10202, customfield_10306, customfield_10380, customfield_10062, ID, customfield_10375, customfield_10072, customfield_10069, customfield_10685, customfield_11251, customfield_10200, customfield_10187, customfield_11113, customfield_11245, components, customfield_11253, Transition, customfield_11250, customfield_11247, resolutiondate, customfield_10101, LastViewed, customfield_10385, customfield_11249, customfield_11279, customfield_10344, customfield_10209, customfield_10189, customfield_11212, customfield_10651, Project, customfield_10211, customfield_10105, customfield_11214, customfield_10012, customfield_10213, customfield_10395, customfield_10684, customfield_10116, customfield_10172, timeoriginalestimate, customfield_10203, customfield_10174, customfield_10025, customfield_10336, customfield_10065, customfield_10330, customfield_10223, customfield_10027, customfield_10686, fixVersions, customfield_10126, customfield_10182, customfield_10356, progress, customfield_10128, customfield_10042, customfield_10180, customfield_10562, customfield_10196, customfield_10858, customfield_10629, customfield_10261, customfield_10004, customfield_10049, customfield_10627, aggregatetimeestimate, customfield_10663, customfield_10185, customfield_10237, customfield_10557, customfield_10081, timespent, security, customfield_10057, aggregateprogress, aggregatetimespent, customfield_10314, environment, customfield_10722, customfield_10195, customfield_10239, customfield_10368, customfield_10229, customfield_10037, issuelinks, customfield_10348, customfield_10056, customfield_10131, customfield_10199, customfield_10054, Status, customfield_10618, customfield_10751, customfield_10161, Comment, Key, customfield_11254, customfield_10063, customfield_10060, customfield_10066, customfield_10226, customfield_10068, labels, customfield_10520, customfield_10489, statuscategorychangedate, customfield_10160, customfield_10222, customfield_10354, customfield_11252, customfield_10159, customfield_10393, customfield_11248, RestUrl, resolution, customfield_10384, customfield_10216, customfield_10379, customfield_10208, customfield_10386, customfield_10341, customfield_10132, customfield_10559, customfield_10171, customfield_10013, customfield_10100, customfield_10212, customfield_10204, customfield_10725, customfield_10117, customfield_10173, customfield_10011, customfield_10075, customfield_10398, customfield_10115, customfield_10347, customfield_10337, customfield_10321, customfield_10177, customfield_10215, customfield_10224, customfield_10352, Assignee, customfield_10130, customfield_10717, customfield_10129, customfield_10181, customfield_10000, customfield_10453, customfield_11181, customfield_10178, customfield_10029, customfield_10276, watches, customfield_10560, customfield_10133, customfield_10120, customfield_10102, customfield_10634, customfield_10249, customfield_10320, customfield_10048, customfield_11278, customfield_10346, customfield_10383, customfield_10184, customfield_11281, customfield_10726, customfield_10030, priority, customfield_10235, customfield_10720, customfield_10134, customfield_10047, customfield_10190, customfield_10721, customfield_10045, customfield_10448, customfield_10238, customfield_10882, customfield_10250, customfield_10198, customfield_10118, customfield_10619, customfield_10197, customfield_10251, customfield_10318, customfield_10055, Summary, Description, customfield_10447, customfield_10304, customfield_10255, customfield_10059, customfield_10053, customfield_10148, customfield_10382, customfield_10193, customfield_10488, attachment, customfield_10728, customfield_10225, customfield_10158, workratio, customfield_10372, customfield_10206, customfield_10156, customfield_10664, customfield_10201, customfield_10154, customfield_10521, statusCategory, Created, customfield_10064, issuetype, customfield_10254, customfield_10374, customfield_10326, customfield_10342, customfield_10317, customfield_10554, customfield_10227, customfield_10210, customfield_10214, customfield_10391, customfield_11246, customfield_10585, customfield_10070, customfield_11182, customfield_10630, customfield_10135, aggregatetimeoriginalestimate, customfield_10631, customfield_10010, customfield_10061, customfield_11215, timeestimate, customfield_10662, customfield_11213, customfield_10556, customfield_10028, customfield_10635, customfield_10396, customfield_10275, customfield_10719, timetracking, customfield_10175, customfield_10024, Creator, customfield_10381, customfield_10023, Updated, customfield_11179, customfield_10127, customfield_10183, customfield_11122, customfield_10561, customfield_10043, customfield_10322, customfield_10046, customfield_10192, customfield_10041, Reporter, customfield_10044, customfield_10628, customfield_10335, customfield_10040, subtasks, customfield_10191, customfield_10626, customfield_10343, versions, customfield_10186, HttpUrl, customfield_10319, customfield_10303, customfield_10001, customfield_10555, customfield_10252, customfield_10050
[2025-07-03 09:41:22.817] [DEBUG] [Application] [15] Found Comment property with 18 comments
[2025-07-03 09:41:22.871] [INFO] [Application] [15] Starting data extraction from ticket TESTIT-49342
[2025-07-03 09:41:23.039] [DEBUG] [Application] [15] Extracted data - Name: 'Test1 Test2', Job: 'Intern', Dept: 'IT', Model: 'test', Location: ''
[2025-07-03 09:41:23.997] [DEBUG] [Application] [15] Generating SAM account name for Test1 Test2
[2025-07-03 09:41:24.136] [DEBUG] [Application] [15] Checking if SAM account name 'test2t' exists
[2025-07-03 09:41:24.202] [INFO] [Application] [15] Generated SAM account name: test2t (simulation mode - could not verify uniqueness)
[2025-07-03 09:41:24.269] [DEBUG] [Application] [15] SAM account generated successfully
[2025-07-03 09:41:24.773] [DEBUG] [Application] [15] Generated account details from Jira ticket successfully
[2025-07-03 09:41:24.922] [INFO] [Application] [15] Data extraction completed. Please verify the populated fields.
[2025-07-03 09:41:24.960] [DEBUG] [Application] [15] Workflow step 'FetchData' status: Completed
[2025-07-03 09:41:25.010] [INFO] [Application] [15] Step 3: Validate Information
[2025-07-03 09:41:25.043] [DEBUG] [Application] [15] Workflow step 'Validation' status: InProgress
[2025-07-03 09:41:25.085] [INFO] [Application] [15] Successfully fetched and populated data from ticket TESTIT-49342
[2025-07-03 09:46:02.514] [DEBUG] [Testing] [15] Test registered: ConfigurationValidation in suite Unit
[2025-07-03 09:46:02.844] [DEBUG] [Testing] [15] Test registered: ConfigurationValidation in Unit
[2025-07-03 09:46:02.930] [DEBUG] [Testing] [15] Test registered: LoggingSystem in suite Unit
[2025-07-03 09:46:02.962] [DEBUG] [Testing] [15] Test registered: LoggingSystem in Unit
[2025-07-03 09:46:03.001] [DEBUG] [Testing] [15] Test registered: VersionManager in suite Unit
[2025-07-03 09:46:03.041] [DEBUG] [Testing] [15] Test registered: VersionManager in Unit
[2025-07-03 09:46:03.087] [DEBUG] [Testing] [15] Test registered: JiraConnection in suite Integration
[2025-07-03 09:46:03.117] [DEBUG] [Testing] [15] Test registered: JiraConnection in Integration
[2025-07-03 09:46:03.151] [DEBUG] [Testing] [15] Test registered: ActiveDirectoryConnection in suite Integration
[2025-07-03 09:46:03.184] [DEBUG] [Testing] [15] Test registered: ActiveDirectoryConnection in Integration
[2025-07-03 09:46:03.218] [DEBUG] [Testing] [15] Test registered: StartupTime in suite Performance
[2025-07-03 09:46:03.249] [DEBUG] [Testing] [15] Test registered: StartupTime in Performance
[2025-07-03 09:46:03.282] [DEBUG] [Testing] [15] Test registered: MemoryUsage in suite Performance
[2025-07-03 09:46:03.315] [DEBUG] [Testing] [15] Test registered: MemoryUsage in Performance
[2025-07-03 09:46:03.349] [DEBUG] [Testing] [15] Test registered: CredentialHandling in suite Security
[2025-07-03 09:46:03.383] [DEBUG] [Testing] [15] Test registered: CredentialHandling in Security
[2025-07-03 09:46:03.417] [DEBUG] [Testing] [15] Test registered: InputValidation in suite Security
[2025-07-03 09:46:03.451] [DEBUG] [Testing] [15] Test registered: InputValidation in Security
[2025-07-03 09:46:03.498] [DEBUG] [Testing] [15] Test registered: WindowCreation in suite UI
[2025-07-03 09:46:03.539] [DEBUG] [Testing] [15] Test registered: WindowCreation in UI
[2025-07-03 09:46:03.576] [INFO] [Testing] [15] Core tests initialized
[2025-07-03 09:46:03.728] [INFO] [Documentation] [15] Initializing documentation system
[2025-07-03 09:46:03.850] [INFO] [Documentation] [15] Starting documentation generation for: api
[2025-07-03 09:46:05.357] [INFO] [Documentation] [15] Documentation saved: Documentation\API_Reference.html
[2025-07-03 09:46:05.456] [INFO] [Documentation] [15] Documentation saved: Documentation\API_Reference.md
[2025-07-03 09:46:05.535] [INFO] [Documentation] [15] Documentation generation completed for: api
[2025-07-03 09:46:05.625] [INFO] [Documentation] [15] API documentation generated: 2 files
[2025-07-03 09:46:05.676] [INFO] [Documentation] [15] Starting documentation generation for: help
[2025-07-03 09:46:05.748] [INFO] [Documentation] [15] Documentation generation completed for: help
[2025-07-03 09:46:05.781] [INFO] [Documentation] [15] Help system documentation generated
[2025-07-03 09:46:05.816] [INFO] [Documentation] [15] Documentation system initialized
[2025-07-03 09:46:06.308] [INFO] [Application] [15] Initializing enhanced UI components...
[2025-07-03 09:46:06.551] [DEBUG] [Application] [15] Theme applied to controls
[2025-07-03 09:46:06.697] [DEBUG] [Application] [15] Modern theme applied successfully
[2025-07-03 09:46:06.880] [DEBUG] [Application] [15] Field validation initialized
[2025-07-03 09:46:07.021] [DEBUG] [Application] [15] Responsive layout initialized
[2025-07-03 09:46:07.436] [DEBUG] [Application] [15] Workflow step 'Connection' status: Pending
[2025-07-03 09:46:07.635] [INFO] [Application] [15] Enhanced UI initialization completed
[2025-07-03 09:46:07.789] [INFO] [Application] [15] Welcome! Please connect to Jira to begin.
[2025-07-03 09:46:07.860] [INFO] [Application] [15] OnboardingFromJiraGUI started successfully
[2025-07-03 09:46:07.936] [INFO] [Application] [15] [OK] Monitoring system initialized
[2025-07-03 09:46:08.121] [INFO] [Audit] [15] Audit: akinje performed PermissionInit on UserPermissions - Success | Context: SessionId=Unknown; Action=PermissionInit; Timestamp=07/03/2025 09:46:08; Result=Success; User=akinje; Detected=Standard user privileges; Target=UserPermissions; PrivilegeLevel=Standard
[2025-07-03 09:46:08.248] [INFO] [Application] [15] [OK] User permissions initialized for JERAGM\akinje
[2025-07-03 09:46:08.401] [DEBUG] [Application] [15]  Cache cleanup scheduled every 30 minutes
[2025-07-03 09:46:08.455] [INFO] [Application] [15] Applying Modern UI Integration...
[2025-07-03 09:46:08.753] [DEBUG] [Application] [15] Auto-completion setup for LastNameBox
[2025-07-03 09:46:08.825] [DEBUG] [Application] [15] Auto-completion setup for JobTitleBox
[2025-07-03 09:46:08.921] [DEBUG] [Application] [15] Auto-completion setup for FirstNameBox
[2025-07-03 09:46:08.994] [DEBUG] [Application] [15] Auto-completion setup for DepartmentBox
[2025-07-03 09:46:09.105] [DEBUG] [Application] [15] Auto-completion setup for ModelAccountBox
[2025-07-03 09:46:09.558] [DEBUG] [Application] [15] Auto-completion integration completed
[2025-07-03 09:46:09.807] [INFO] [Application] [15] Modern UI Integration completed successfully
[2025-07-03 09:46:39.571] [INFO] [Application] [15] Step 1: Connect to Jira
[2025-07-03 09:46:39.596] [DEBUG] [Application] [15] Workflow step 'Connection' status: InProgress
[2025-07-03 09:46:39.697] [INFO] [Application] [15] Connecting to https://jeragm.atlassian.net...
[2025-07-03 09:46:43.726] [DEBUG] [Application] [15] Workflow step 'Connection' status: Completed
[2025-07-03 09:46:43.948] [INFO] [Application] [15] Successfully connected to Jira
[2025-07-03 09:46:46.471] [INFO] [Application] [15] Step 2: Fetch Ticket Data
[2025-07-03 09:46:46.508] [DEBUG] [Application] [15] Workflow step 'FetchData' status: InProgress
[2025-07-03 09:46:46.606] [INFO] [Application] [15] Fetching data for ticket TESTIT-49342...
[2025-07-03 09:46:51.826] [DEBUG] [Application] [15] Cached ticket: TESTIT-49342
[2025-07-03 09:46:51.893] [DEBUG] [Application] [15] Issue object properties: customfield_10553, customfield_10058, customfield_10305, customfield_10149, customfield_10723, customfield_10928, customfield_10194, customfield_10363, customfield_10750, customfield_10147, customfield_11280, customfield_10729, customfield_10153, customfield_10369, customfield_10145, customfield_10783, customfield_10727, duedate, customfield_10151, customfield_10367, customfield_10370, customfield_10486, customfield_10373, worklog, customfield_10157, customfield_10371, customfield_10008, customfield_10155, customfield_10519, customfield_10202, customfield_10306, customfield_10380, customfield_10062, ID, customfield_10375, customfield_10072, customfield_10069, customfield_10685, customfield_11251, customfield_10200, customfield_10187, customfield_11113, customfield_11245, components, customfield_11253, Transition, customfield_11250, customfield_11247, resolutiondate, customfield_10101, LastViewed, customfield_10385, customfield_11249, customfield_11279, customfield_10344, customfield_10209, customfield_10189, customfield_11212, customfield_10651, Project, customfield_10211, customfield_10105, customfield_11214, customfield_10012, customfield_10213, customfield_10395, customfield_10684, customfield_10116, customfield_10172, timeoriginalestimate, customfield_10203, customfield_10174, customfield_10025, customfield_10336, customfield_10065, customfield_10330, customfield_10223, customfield_10027, customfield_10686, fixVersions, customfield_10126, customfield_10182, customfield_10356, progress, customfield_10128, customfield_10042, customfield_10180, customfield_10562, customfield_10196, customfield_10858, customfield_10629, customfield_10261, customfield_10004, customfield_10049, customfield_10627, aggregatetimeestimate, customfield_10663, customfield_10185, customfield_10237, customfield_10557, customfield_10081, timespent, security, customfield_10057, aggregateprogress, aggregatetimespent, customfield_10314, environment, customfield_10722, customfield_10195, customfield_10239, customfield_10368, customfield_10229, customfield_10037, issuelinks, customfield_10348, customfield_10056, customfield_10131, customfield_10199, customfield_10054, Status, customfield_10618, customfield_10751, customfield_10161, Comment, Key, customfield_11254, customfield_10063, customfield_10060, customfield_10066, customfield_10226, customfield_10068, labels, customfield_10520, customfield_10489, statuscategorychangedate, customfield_10160, customfield_10222, customfield_10354, customfield_11252, customfield_10159, customfield_10393, customfield_11248, RestUrl, resolution, customfield_10384, customfield_10216, customfield_10379, customfield_10208, customfield_10386, customfield_10341, customfield_10132, customfield_10559, customfield_10171, customfield_10013, customfield_10100, customfield_10212, customfield_10204, customfield_10725, customfield_10117, customfield_10173, customfield_10011, customfield_10075, customfield_10398, customfield_10115, customfield_10347, customfield_10337, customfield_10321, customfield_10177, customfield_10215, customfield_10224, customfield_10352, Assignee, customfield_10130, customfield_10717, customfield_10129, customfield_10181, customfield_10000, customfield_10453, customfield_11181, customfield_10178, customfield_10029, customfield_10276, watches, customfield_10560, customfield_10133, customfield_10120, customfield_10102, customfield_10634, customfield_10249, customfield_10320, customfield_10048, customfield_11278, customfield_10346, customfield_10383, customfield_10184, customfield_11281, customfield_10726, customfield_10030, priority, customfield_10235, customfield_10720, customfield_10134, customfield_10047, customfield_10190, customfield_10721, customfield_10045, customfield_10448, customfield_10238, customfield_10882, customfield_10250, customfield_10198, customfield_10118, customfield_10619, customfield_10197, customfield_10251, customfield_10318, customfield_10055, Summary, Description, customfield_10447, customfield_10304, customfield_10255, customfield_10059, customfield_10053, customfield_10148, customfield_10382, customfield_10193, customfield_10488, attachment, customfield_10728, customfield_10225, customfield_10158, workratio, customfield_10372, customfield_10206, customfield_10156, customfield_10664, customfield_10201, customfield_10154, customfield_10521, statusCategory, Created, customfield_10064, issuetype, customfield_10254, customfield_10374, customfield_10326, customfield_10342, customfield_10317, customfield_10554, customfield_10227, customfield_10210, customfield_10214, customfield_10391, customfield_11246, customfield_10585, customfield_10070, customfield_11182, customfield_10630, customfield_10135, aggregatetimeoriginalestimate, customfield_10631, customfield_10010, customfield_10061, customfield_11215, timeestimate, customfield_10662, customfield_11213, customfield_10556, customfield_10028, customfield_10635, customfield_10396, customfield_10275, customfield_10719, timetracking, customfield_10175, customfield_10024, Creator, customfield_10381, customfield_10023, Updated, customfield_11179, customfield_10127, customfield_10183, customfield_11122, customfield_10561, customfield_10043, customfield_10322, customfield_10046, customfield_10192, customfield_10041, Reporter, customfield_10044, customfield_10628, customfield_10335, customfield_10040, subtasks, customfield_10191, customfield_10626, customfield_10343, versions, customfield_10186, HttpUrl, customfield_10319, customfield_10303, customfield_10001, customfield_10555, customfield_10252, customfield_10050
[2025-07-03 09:46:51.946] [DEBUG] [Application] [15] Found Comment property with 18 comments
[2025-07-03 09:46:51.983] [INFO] [Application] [15] Starting data extraction from ticket TESTIT-49342
[2025-07-03 09:46:52.069] [DEBUG] [Application] [15] Extracted data - Name: 'Test1 Test2', Job: 'Intern', Dept: 'IT', Model: 'test', Location: ''
[2025-07-03 09:46:52.655] [DEBUG] [Application] [15] Generating SAM account name for Test1 Test2
[2025-07-03 09:46:52.696] [DEBUG] [Application] [15] Checking if SAM account name 'test2t' exists
[2025-07-03 09:46:52.737] [INFO] [Application] [15] Generated SAM account name: test2t (simulation mode - could not verify uniqueness)
[2025-07-03 09:46:52.784] [DEBUG] [Application] [15] DIAGNOSTIC: generatedSam = '31 32 33 test2t'
[2025-07-03 09:46:52.815] [DEBUG] [Application] [15] DIAGNOSTIC: samForGui = '31 32 33 test2t'
[2025-07-03 09:46:52.849] [DEBUG] [Application] [15] DIAGNOSTIC: samForGui length = 15
[2025-07-03 09:46:52.886] [DEBUG] [Application] [15] DIAGNOSTIC: samForGui bytes = 51 49 32 51 50 32 51 51 32 116 101 115 116 50 116
[2025-07-03 09:46:53.000] [DEBUG] [Application] [15] DIAGNOSTIC: SamBox.Text after assignment = '31 32 33 test2t'
[2025-07-03 09:46:53.049] [DEBUG] [Application] [15] DIAGNOSTIC: SamBox.Text length = 15
[2025-07-03 09:46:53.241] [DEBUG] [Application] [15] Generated account details from Jira ticket successfully
[2025-07-03 09:46:53.389] [INFO] [Application] [15] Data extraction completed. Please verify the populated fields.
[2025-07-03 09:46:53.462] [DEBUG] [Application] [15] Workflow step 'FetchData' status: Completed
[2025-07-03 09:46:53.520] [INFO] [Application] [15] Step 3: Validate Information
[2025-07-03 09:46:53.560] [DEBUG] [Application] [15] Workflow step 'Validation' status: InProgress
[2025-07-03 09:46:53.600] [INFO] [Application] [15] Successfully fetched and populated data from ticket TESTIT-49342
